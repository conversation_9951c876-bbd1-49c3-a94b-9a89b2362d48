/**
 * 远期房态API - 使用模拟数据
 */

// 模拟楼栋数据
const mockBuildings = [
  { id: 1, name: 'A栋', code: 'A' },
  { id: 2, name: 'B栋', code: 'B' },
  { id: 3, name: 'C栋', code: 'C' }
]

// 模拟楼层数据
const mockFloors = {
  1: [ // A栋
    { id: 11, name: '1楼', buildingId: 1 },
    { id: 12, name: '2楼', buildingId: 1 },
    { id: 13, name: '3楼', buildingId: 1 },
    { id: 14, name: '4楼', buildingId: 1 }
  ],
  2: [ // B栋
    { id: 21, name: '1楼', buildingId: 2 },
    { id: 22, name: '2楼', buildingId: 2 },
    { id: 23, name: '3楼', buildingId: 2 }
  ],
  3: [ // C栋
    { id: 31, name: '1楼', buildingId: 3 },
    { id: 32, name: '2楼', buildingId: 3 }
  ]
}

// 模拟房间数据
const mockRooms = [
  // A栋房间
  { id: 101, roomNumber: 'A101', roomTypeName: '标准间', buildingId: 1, buildingName: 'A栋', floorId: 11, floorName: '1楼' },
  { id: 102, roomNumber: 'A102', roomTypeName: '标准间', buildingId: 1, buildingName: 'A栋', floorId: 11, floorName: '1楼' },
  { id: 103, roomNumber: 'A103', roomTypeName: '豪华间', buildingId: 1, buildingName: 'A栋', floorId: 11, floorName: '1楼' },
  { id: 201, roomNumber: 'A201', roomTypeName: '标准间', buildingId: 1, buildingName: 'A栋', floorId: 12, floorName: '2楼' },
  { id: 202, roomNumber: 'A202', roomTypeName: '标准间', buildingId: 1, buildingName: 'A栋', floorId: 12, floorName: '2楼' },
  { id: 203, roomNumber: 'A203', roomTypeName: '套房', buildingId: 1, buildingName: 'A栋', floorId: 12, floorName: '2楼' },
  { id: 301, roomNumber: 'A301', roomTypeName: '豪华间', buildingId: 1, buildingName: 'A栋', floorId: 13, floorName: '3楼' },
  { id: 302, roomNumber: 'A302', roomTypeName: '豪华间', buildingId: 1, buildingName: 'A栋', floorId: 13, floorName: '3楼' },
  { id: 401, roomNumber: 'A401', roomTypeName: '总统套房', buildingId: 1, buildingName: 'A栋', floorId: 14, floorName: '4楼' },
  
  // B栋房间
  { id: 501, roomNumber: 'B101', roomTypeName: '标准间', buildingId: 2, buildingName: 'B栋', floorId: 21, floorName: '1楼' },
  { id: 502, roomNumber: 'B102', roomTypeName: '标准间', buildingId: 2, buildingName: 'B栋', floorId: 21, floorName: '1楼' },
  { id: 503, roomNumber: 'B103', roomTypeName: '豪华间', buildingId: 2, buildingName: 'B栋', floorId: 21, floorName: '1楼' },
  { id: 601, roomNumber: 'B201', roomTypeName: '标准间', buildingId: 2, buildingName: 'B栋', floorId: 22, floorName: '2楼' },
  { id: 602, roomNumber: 'B202', roomTypeName: '豪华间', buildingId: 2, buildingName: 'B栋', floorId: 22, floorName: '2楼' },
  { id: 701, roomNumber: 'B301', roomTypeName: '套房', buildingId: 2, buildingName: 'B栋', floorId: 23, floorName: '3楼' },
  
  // C栋房间
  { id: 801, roomNumber: 'C101', roomTypeName: '经济间', buildingId: 3, buildingName: 'C栋', floorId: 31, floorName: '1楼' },
  { id: 802, roomNumber: 'C102', roomTypeName: '经济间', buildingId: 3, buildingName: 'C栋', floorId: 31, floorName: '1楼' },
  { id: 803, roomNumber: 'C103', roomTypeName: '标准间', buildingId: 3, buildingName: 'C栋', floorId: 31, floorName: '1楼' },
  { id: 901, roomNumber: 'C201', roomTypeName: '标准间', buildingId: 3, buildingName: 'C栋', floorId: 32, floorName: '2楼' },
  { id: 902, roomNumber: 'C202', roomTypeName: '豪华间', buildingId: 3, buildingName: 'C栋', floorId: 32, floorName: '2楼' }
]

// 模拟客人姓名
const mockGuestNames = [
  '张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十',
  '郑十一', '王十二', '冯十三', '陈十四', '褚十五', '卫十六',
  '蒋十七', '沈十八', '韩十九', '杨二十', '朱二十一', '秦二十二'
]

// 房间状态类型
const statusTypes = ['available', 'occupied', 'reserved', 'maintenance', 'cleaning', 'out-of-order']

/**
 * 获取楼栋列表
 */
export function getBuildingList() {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve([...mockBuildings])
    }, 200)
  })
}

/**
 * 获取楼层列表
 * @param {number} buildingId - 楼栋ID
 */
export function getFloorList(buildingId) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const floors = mockFloors[buildingId] || []
      resolve([...floors])
    }, 150)
  })
}

/**
 * 获取房间列表
 */
export function getRoomList() {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve([...mockRooms])
    }, 300)
  })
}

/**
 * 生成随机房间状态
 * @param {string} date - 日期字符串
 * @param {number} roomId - 房间ID
 */
function generateRandomStatus(date, roomId) {
  // 使用日期和房间ID作为种子，确保相同输入产生相同输出
  const seed = date.replace(/-/g, '') + roomId.toString()
  const hash = seed.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0)
    return a & a
  }, 0)
  
  const random = Math.abs(hash) / 2147483647
  
  // 根据日期调整概率
  const dateObj = new Date(date)
  const dayOfWeek = dateObj.getDay()
  const isWeekend = dayOfWeek === 0 || dayOfWeek === 6
  
  let status = 'available'
  let guestName = ''
  
  if (isWeekend) {
    // 周末入住率更高
    if (random < 0.6) {
      status = 'occupied'
      guestName = mockGuestNames[Math.floor(random * mockGuestNames.length)]
    } else if (random < 0.8) {
      status = 'reserved'
    } else if (random < 0.85) {
      status = 'maintenance'
    } else if (random < 0.9) {
      status = 'cleaning'
    } else if (random < 0.92) {
      status = 'out-of-order'
    }
  } else {
    // 工作日
    if (random < 0.4) {
      status = 'occupied'
      guestName = mockGuestNames[Math.floor(random * mockGuestNames.length)]
    } else if (random < 0.6) {
      status = 'reserved'
    } else if (random < 0.65) {
      status = 'maintenance'
    } else if (random < 0.7) {
      status = 'cleaning'
    } else if (random < 0.72) {
      status = 'out-of-order'
    }
  }
  
  return {
    status,
    guestName,
    checkIn: status === 'occupied' ? '14:00' : '',
    checkOut: status === 'occupied' ? '12:00' : '',
    notes: status === 'maintenance' ? '空调维修' : status === 'cleaning' ? '深度清洁' : ''
  }
}

/**
 * 获取远期房态数据
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始日期
 * @param {string} params.endDate - 结束日期
 * @param {Array} params.roomIds - 房间ID列表
 * @param {number} params.buildingId - 楼栋ID（可选）
 * @param {number} params.floorId - 楼层ID（可选）
 */
export function getFutureRoomStatusData(params) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const { startDate, endDate, roomIds = [] } = params
      const statusData = {}
      
      // 生成日期列表
      const dates = []
      const start = new Date(startDate)
      const end = new Date(endDate)
      
      for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
        dates.push(d.toISOString().split('T')[0])
      }
      
      // 为每个房间生成每一天的状态
      roomIds.forEach(roomId => {
        statusData[roomId] = {}
        dates.forEach(date => {
          statusData[roomId][date] = generateRandomStatus(date, roomId)
        })
      })
      
      resolve(statusData)
    }, 500)
  })
}

/**
 * 获取房间状态统计
 * @param {Object} params - 查询参数
 */
export function getRoomStatusStats(params) {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟统计数据
      const stats = {
        available: Math.floor(Math.random() * 50) + 20,
        occupied: Math.floor(Math.random() * 40) + 15,
        reserved: Math.floor(Math.random() * 30) + 10,
        maintenance: Math.floor(Math.random() * 5) + 1,
        cleaning: Math.floor(Math.random() * 8) + 2,
        outOfOrder: Math.floor(Math.random() * 3)
      }
      
      resolve(stats)
    }, 200)
  })
}

/**
 * 更新房间状态
 * @param {Object} params - 更新参数
 * @param {number} params.roomId - 房间ID
 * @param {string} params.date - 日期
 * @param {string} params.status - 新状态
 * @param {Object} params.data - 其他数据
 */
export function updateRoomStatus(params) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      // 模拟更新操作
      const success = Math.random() > 0.1 // 90% 成功率
      
      if (success) {
        resolve({
          success: true,
          message: '房间状态更新成功'
        })
      } else {
        reject(new Error('更新失败，请重试'))
      }
    }, 300)
  })
}
