<template>
  <CommonPage>
    <template #action>
      <NButton v-permission="'AddMini'" type="primary" @click="getLink()">
        <i class="i-material-symbols:add mr-4 text-18" />
        绑定新小程序
      </NButton>
    </template>
    <MeCrud
      ref="$table"
      v-model:query-items="queryItems"
      :scroll-x="1200"
      :columns="columns"
      :get-data="getList"
    >
      <MeQueryItem label="小程序名称" :label-width="80">
        <n-input
          v-model:value="queryItems.name"
          type="text"
          placeholder="小程序名称"
          clearable
        />
      </MeQueryItem>

      <MeQueryItem label="appId" :label-width="50">
        <n-input
          v-model:value="queryItems.appid"
          type="text"
          placeholder="小程序appid"
          clearable
        />
      </MeQueryItem>

      <MeQueryItem label="状态" :label-width="50">
        <n-select
          v-model:value="queryItems.enable"
          clearable
          :options="[
            { label: '启用', value: 1 },
            { label: '停用', value: 0 },
          ]"
        />
      </MeQueryItem>
    </MeCrud>

    <n-modal v-model:show="showModal">
      <n-card
        style="width: 600px"
        title="授权小程序"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <template #header-extra>
          <NButton type="primary" @click="toAuth">
            点击授权小程序
          </NButton>
        </template>
      </n-card>
    </n-modal>

    <n-modal v-model:show="showModal2">
      <n-card
        style="width: 1200px"
        title="选择一个模板上传后再进行审核"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
            <n-card
            :bordered="true"
            style="width: 100%;margin-bottom: 30px;"
            title="线上版本信息">
              <div>版本号:{{ lineDetail.release_version }}</div>
              <div>线上版本描述:{{ lineDetail.release_desc }}</div>
              <div>发布线上版的时间:{{ formatTime(lineDetail.release_time) }}</div>
              <n-button type="error" size="small" class="mt-5" @click="openCallBackModal">
                版本回退
              </n-button>
            </n-card>
            <n-data-table
              size="large"
              :columns="columnsModel"
              :data="data"
              :pagination="paginationModel"
            >
              <template #loading></template>
            </n-data-table>

          <p>请选择一个版本提交进行审核</p>

            <n-data-table
              size="large"
              :columns="columnsUploaded"
              :data="uploadedData"
              :pagination="paginationUploaded"
            />

      </n-card>
    </n-modal>

    <n-modal v-model:show="showModalPlugin">
      <n-card
        style="width: 1200px"
        title="插件管理" :bordered="false" size="huge" role="dialog" aria-modal="true"
      >
        <div class="m10">
          <NButton type="primary" @click="tongtong()">
            通通锁插件申请
          </NButton>
          <NButton type="primary" class="ml-10" @click="yaya()">
            丫丫锁插件申请
          </NButton>
        </div>
        <n-data-table
          size="large"
          :columns="columnsPlugin"
          :data="dataPlugin"
          :pagination="paginationPlugin"
        />
      </n-card>
    </n-modal>

    <n-modal v-model:show="showModalQRCode">
      <n-card
        style="width: 1200px"
        title="体验版二维码" :bordered="false" size="huge" role="dialog" aria-modal="true"
      >
      <n-image
    width="200"
    :src="QrURL"
  />
      </n-card>
    </n-modal>

    <!-- 版本回退弹窗 -->
  <VersionRollbackModal
    v-model:show="showRollbackModal"
    :appid="authorizer_appid"
  />

  <PrivacySettingModal
    v-model:show="showPrivacyModal"
    :appid="authorizer_appid"
  />
  </CommonPage>
</template>
<script setup>
import dayjs from 'dayjs';
import { NAvatar, NButton, NSwitch, NTabs, NTabPane } from 'naive-ui'
import { onActivated,onMounted, ref,watch   } from 'vue';
import { onBeforeRouteUpdate,useRoute } from 'vue-router';

import { useCrud } from '@/composables'
import { useUserStore } from '@/store'
import VersionRollbackModal from '@/components/me/VersionRollbackModal.vue'
import PrivacySettingModal from '@/components/me/PrivacySettingModal.vue';
// 导入 onBeforeRouteUpdate 钩子
import api from './api'

defineOptions({ name: 'WxMgt' })
const $table = ref(null)
const roles = ref([])
const link = ref('');
const showModal = ref(false);
const showModal2 = ref(false);
const value = ref(2);
api.getAllRoles().then(({ data = [] }) => (roles.value = data))
/** QueryBar筛选参数（可选） */
const queryItems = ref({})
const userStore = useUserStore()

onMounted(() => {
  getList()
  $table.value?.handleSearch()

})

const {
  modalForm,
  handleDelete,
  handleSave,
} = useCrud({
  name: '扫码绑定小程序',
  options:{ title:'扫码授权' },
  initForm: { enable: true },
  doCreate: api.create,
  doDelete: api.delete,
  doUpdate: api.update,
  refresh: () => $table.value?.handleSearch(),
})

const columns = [
  { type: 'selection', fixed: 'left' },
  {
    title: 'logo',
    key: 'head_img',
    width: 60,
    render: ({ head_img }) =>
      h(NAvatar, {
        size: 'medium',
        src: head_img,
      }),
  },
  { title: '小程序名称', key: 'signature', width: 150, ellipsis: { tooltip: true } },
  {
    title: '小程序appId',
    key: 'authorizer_appid',
    width: 140,
    ellipsis: { tooltip: true }
  },
  {
    title: '认证状态',
    key: 'verify_type_info',
    width: 80,
    ellipsis: { tooltip: true }
  },
  {
    title: '授权状态',
    key: 'idc',
    width: 80,
    ellipsis: { tooltip: true }
  },

  {
    title: '线上版本',
    key: 'onLine_version',
    width: 80,
    ellipsis: { tooltip: true }
  },
  {
    title: '已授权插件',
    key: 'plugin_status',
    width: 120,
    ellipsis: { tooltip: true }
  },
  {
    title: '操作',
    key: 'actions',
    width: 320,
    align: 'right',
    fixed: 'right',
    hideInExcel: true,
    render(row) {
      return [
      h(
            NButton,
            {
              size: 'small',
              type: 'info',
              style: 'margin-left: 12px;',
              onClick: () => getLink(row)
            },
            {
              default: () => '授权小程序',
              icon: () => h('i', { class: 'i-ic:baseline-link-off text-14' }),
            },
          ),
      h(
            NButton,
            {
              size: 'small',
              type: 'info',
              style: 'margin-left: 12px;',
              onClick: () => openYsModal(row)
            },
            {
              default: () => '隐私协议',
              icon: () => h('i', { class: 'i-ic:baseline-link-off text-14' }),
            },
          ),
        h(
            NButton,
            {
              size: 'small',
              type: 'primary',
              style: 'margin-left: 12px;',
              onClick: () => openModalPlugin(row),
            },
            {
              default: () => '插件管理',
              icon: () => h('i', { class: 'i-material-symbols:settings-account-box-outline text-14' }),
            },
          ),
          h(
            NButton,
            {
              size: 'small',
              type: 'success',
              style: 'margin-left: 12px;',
              onClick: () => openModal(row)
            },
            {
              default: () => '上传代码',
              icon: () => h('i', { class: 'i-material-symbols:backup text-14' }),
            },
          ),

          // h(
          //   NButton,
          //   {
          //     size: 'small',
          //     type: 'error',
          //     style: 'margin-left: 12px;',
          //     onClick: () => handleDelete({ title: '解除绑定', id: row.authorizer_appid, onOk: onDelete }),
          //   },
          //   {
          //     default: () => '解除绑定',
          //     icon: () => h('i', { class: 'i-material-symbols:delete-outline text-14' }),
          //   },
          // )
      ]
    },
  },
]

// 隐私协议

const showPrivacyModal = ref(false)
function openYsModal(row) {
  authorizer_appid.value = row.authorizer_appid;
  showPrivacyModal.value = true; // 显示隐私协议弹窗
}
const showRollbackModal = ref(false)
function openCallBackModal(row) {
  authorizer_appid.value = row.authorizer_appid;
  showRollbackModal.value = true; // 显示回退弹窗
}

const columnsModel  = [
  {
     title: "模板ID",
     key: "template_id"
   },
   {
     title: "草稿ID",
     key: "draft_id"
   },
   {
     title: "版本号",
     key: "user_version"
   },
   {
     title: "上传时间",
     key: "create_time"
   },
   {
     title: "备注",
     key: "user_desc"
   },
   {
     title: "模板类型",
     key: "template_type"
   },
  //  {
  //    title: "审核状态",
  //    key: "audit_status"
  //  },
   {
     title: "驳回原因",
     key: "reason"
   },
   {
     title: "操作",
     key: "actions",
     render(row) {
       return h(
         NButton,
         {
           size: "small",
           type: "info",
           onClick: () => uploadCode(row),
           loading:uploadCodeLoading.value
         },
         { default: () => "上传代码" }
       );
     }
   }
 ];

 const uploadedData = ref([]);
const paginationUploaded = ref({ pageSize: 10 });
const paginationModel = ref({ pageSize: 10 });

// 已上传代码表格列定义
const columnsUploaded = [
{
    title: "id",
    key: "id"
  },
  {
    title: "版本号",
    key: "version"
  },
  {
    title: "创建时间",
    key: "create_time"
  },
  // {
  //   title: "上传时间",
  //   key: "upload_time"
  // },
  {
    title: "appid",
    key: "appid"
  },
  {
    title: "审核状态",
    key: "status",
    width: 200
  },
  {
    title: "操作",
    key: "actions",
    render(row) {
      return [h(
        NButton,
        {
          size: "small",
          type: "success",
          onClick: () => getQrCode(row)
        },
        { default: () => "获取体验版二维码" }
      ),
      h(
        NButton,
        {
          size: "small",
          type: "success",
          style: "margin-left: 12px;",
          loading: commitCodeLoading.value,
          disabled: row.status == '审核中'||row.status == '审核通过',
          onClick: () => submitAudit(row)
        },
        { default: () => row.status == '审核中'?'正在审核':"提交审核" }
      ),
      h(
        NButton,
        {
          size: "small",
          type: "error",
          style: "margin-left: 12px;",
          loading: releaseCodeLoading.value,
          disabled: row.status != '审核通过',
          onClick: () => releaseCode(row)
        },
        { default: () => "发布上线" }
      )]
    }
  }
];

// 获取已上传代码列表
async function getUploadedCodeList() {
  try {
    // 假设有一个获取已上传代码列表的API
    // 如果没有对应API，需要在api.js中添加
    const response = await api.getCommitList({appid: authorizer_appid.value});
    if (response && response.data) {
      const list = response.data || [];

      uploadedData.value = list.map((item, index) => ({
        key: index,
        id: item.id,
        appid: item.appid,
        version: item.version,
        upload_time: item.upload_time?dayjs(item.upload_time * 1000).format('YYYY-MM-DD HH:mm:ss'):'暂未提交审核',
        create_time: dayjs(item.create_time * 1000).format('YYYY-MM-DD HH:mm:ss'),
        status: formatStatus(item.status)+`${item.reason&&item.status==1?'(原因:'+item.reason+')':''}`,
      }));
    }
  } catch (error) {
    $message.error('获取已上传代码列表失败');
  }
}

function formatTime(timestamp) {
  return dayjs(timestamp * 1000).format('YYYY-MM-DD HH:mm:ss');
}

// 格式化状态
function formatStatus(status) {
  const statusMap = {
    0: '审核通过',
    1: '审核被拒绝',
    2: '审核中',
    3: '已撤回',
    4:'未提交审核',
    5:'已发布'
  };
  return statusMap[status] || '未知状态';
}

const releaseCodeLoading = ref(false);
// 发布代码
async function releaseCode(row) {
  try {
    releaseCodeLoading.value = true;
    // 假设有一个发布代码的API
    // 如果没有对应API，需要在api.js中添加
    const response = await api.releaseCode({
      appid: authorizer_appid.value});
    if (response && response.code === 0) {
      $message.success('发布成功');
      releaseCodeLoading.value = false;
      // 刷新列表
      await getUploadedCodeList();
      await getNetLine()
    }
  }catch (error) {
    releaseCodeLoading.value = false;
    $message.error('发布失败');
  }
}

// 提交审核
const commitCodeLoading = ref(false);
async function submitAudit(row) {
  try {
    // 假设有一个提交审核的API
    // 如果没有对应API，需要在api.js中添加
    commitCodeLoading.value = true;
    const response = await api.submitUpload({
      appid: authorizer_appid.value });
    if (response && response.code === 0) {
      commitCodeLoading.value = false;
      $message.success('提交审核成功');
      // 刷新列表
      await getUploadedCodeList();
    } else {
      commitCodeLoading.value = false;
      $message.error('提交审核失败：' + (response.msg || '未知错误'));
    }
  } catch (error) {
    commitCodeLoading.value = false;
    if(error.error.data.errcode== 85009){
      $message.error('正在审核中,请勿重复提交');
    }else{
      $message.error('提交审核失败：' + (error.error.message || '未知错误'));
    }

  }
}

async function createData() {
 const datalist = await api.getModelList();
 const list = datalist.data.list;


 const data = [];

 list.forEach((item, index) => {
  const column = {
   key:0,
   template_id: '',
   draft_id: "",
   user_version: "",
   create_time: "",
   user_desc: "",
   template_type: "",
   audit_status: "",
   reason: ""
 }
   column.key = index; // 假设每个项目都有一个唯一的 key 属性
   column.template_id = item.template_id; // 假设每个项目都有一个 title 属性
   column.draft_id = item.draft_id; // 假设每个项目都有一个 title 属性
   column.user_version = item.user_version; // 假设每个项目都有一个 title 属性
   column.create_time = dayjs(item.create_time*1000).format('YYYY-MM-DD HH:mm:ss'); // 假设每个项目都有一个 title 属性
   column.user_desc = item.user_desc; // 假设每个项目都有一个 title 属性
   column.template_type = item.template_type===0?'普通模板':'标准模板'; // 假设每个项目都有一个 title 属性
   column.audit_status = item.audit_status===0?'未提审核':item.audit_status==1?'审核中':item.audit_status==2?'审核驳回':item.audit_status==3?'审核通过':item.audit_status==4?'提审中':'提审失败'; // 假设每个项目都有一个 title 属性
   column.reason = item.reason; // 假设每个项目都有一个 title 属性
   data.push(column);
 })

 return data;
}

const data = ref([]); // 假设这是你的数据;
const QrURL = ref('');

onActivated(() => {
  getList()
  $table.value?.handleSearch()
})

async function getList() {

  return await api.getMiniList({appid:userStore.userInfo.platformList[0].appid,userId:userStore.userInfo.id});
}
const uploadCodeLoading = ref(false)
async function uploadCode(row) {
  try {
    uploadCodeLoading.value = true;
    const params ={
      appid:authorizer_appid.value,
      template_id:row.template_id,
      user_version:row.user_version,
      user_desc:row.user_desc,
      ext_json:"{\"requiredPrivateInfos\":[\"getLocation\"],\"ext\":{\"host_url\":\"https://try-hotel.hanwuxi.cn\"}}"
    }

    let ext_json = JSON.parse(params.ext_json);
    ext_json.ext.host_url = userStore.userInfo.baseUrl; // 替换为你的新值;
    params.ext_json = JSON.stringify(ext_json);
    const data  = await api.uploadCode(params); // 使用 async/await 替代.then

    if (data.code == 0) {
      await getUploadedCodeList();
      uploadCodeLoading.value = false;

      $message.success('提交代码成功');
    }
  } catch (error) {
    uploadCodeLoading.value = false;
    $message.error('提交代码失败，请重试');
  }
}
const showModalQRCode = ref(false)
async function getQrCode(row) {
  try {
    const data = await api.getQrCode({
      appid: authorizer_appid.value
    });
    if (data.code == 0) {
      $message.success('获取二维码成功');
      QrURL.value = data.data;
      showModalQRCode.value = true;
    }
  }catch (error) {
    $message.error('获取二维码失败，请重试');
  }
}
const lineDetail = ref(null);
async function getNetLine() {
  try {
    const data = await api.getNetLine({
      appid: authorizer_appid.value
    });
    if (data.code == 0) {
      if(data.data.release_info){
        lineDetail.value = data.data.release_info;
      }

    }
  }catch {
      $message.error('获取线上版本信息错误，请重试');
    }
}

// 上传代码弹窗
const authorizer_appid = ref('');
async function openModal(row) {
  authorizer_appid.value = row.authorizer_appid;
  data.value = await createData();
  await getUploadedCodeList();
  await getNetLine();
  showModal2.value = true;
}

// 插件

const  columnsPlugin = [
  {
    title: 'logo',
    key: 'headimgurl',
    width: 60,
    render: ({ headimgurl }) =>
      h(NAvatar, {
        size: 'medium',
        src: headimgurl,
      }),
  },
  {
     title: "插件appid",
     key: "appid"
   },

   {
     title: "插件名称",
     key: "nickname"
   },
   {
     title: "申请结果",
     key: "reason"
   },
   {
     title: "插件版本",
     key: "version"
   },
   {
     title: "状态",
     key: "status"
   },
   {
     title: "操作",
     key: "actions",
     render(row) {
       return [h(
         NButton,
         {
           size: "small",
           type: "success",
           onClick: () => updatePlugin(row)
         },
         { default: () => "更新插件" }
       ),
       h(
         NButton,
         {
           size: "small",
           type: "error",
           style:'margin-left: 12px;',
           onClick: () => delPlugin(row)
         },
         { default: () => "解绑插件" }
       ),
      ]
     }
   }
 ];

async function createDataPlugin() {
  const list = await api.getPluginStatus({appid:authorizer_appid.value}); // 使用 async/await 替代.then

 const data = [];
 list.data.forEach((item, index) => {
  const column = {
   key:0,
   appid: '',
   version: "",
   headimgurl: "",
   nickname: "",
   status: "",
   reason: ""
 }
   column.key = index; // 假设每个项目都有一个唯一的 key 属性
   column.appid = item.appid; // 假设每个项目都有一个 title 属性
   column.version = item.version; // 假设每个项目都有一个 title 属性
   column.headimgurl =  item.headimgurl; // 假设每个项目都有一个 title 属性
   column.nickname = item.nickname; // 假设每个项目都有一个 title 属性
   column.status = item.status==1?'申请中':item.status==2?'审核通过':item.status==3?'审核拒绝':'申请超时'; // 假设每个项目都有一个 title 属性
   column.reason = item.reason; // 假设每个项目都有一个 title 属性
   data.push(column);
 })
 return data;
}

// 插件弹窗
const showModalPlugin = ref(false);
const dataPlugin = ref([]); // 假设这是你的数据;

async function openModalPlugin(row) {
  authorizer_appid.value = row.authorizer_appid;
  dataPlugin.value = await createDataPlugin();
  showModalPlugin.value = true;
}

async function tongtong() {
  const params = {appid:authorizer_appid.value,plugin_appid:'wx43d5971c94455481'}
   const data = await api.applyPluginStatus(params)
  if(data.code == 0){
        $message.success('插件授权成功');
        // 重新获取数据并刷新表格
        dataPlugin.value = await createDataPlugin();
      }
}

async function yaya() {
  const params = {appid:authorizer_appid.value,plugin_appid:'wx252c9e1868833f3d'}
   const data = await api.applyPluginStatus(params)
  if(data.code == 0){
        $message.success('插件授权成功');
        // 重新获取数据并刷新表格
        dataPlugin.value = await createDataPlugin();
      }
}

// 更新插件
async function updatePlugin(rowData) {
    const params = {appid:authorizer_appid.value,plugin_appid:rowData.appid}

    const data = await api.updatePluginStatus(params);
      if(data.code == 0){
        $message.success('更新成功');
        // 重新获取数据并刷新表格
        dataPlugin.value = await createDataPlugin();
      }

}

// 删除插件
async function delPlugin(rowData) {
    const params = {appid:authorizer_appid.value,plugin_appid:rowData.appid}; // 补充分号
    const data = await api.delPluginStatus(params); // 接口返回值保存到 data
    if (data.code == 0) { // 改为使用 data 变量
        $message.success('删除成功');
        // 重新获取数据并刷新表格
        dataPlugin.value = await createDataPlugin();
    }
}

const route = useRoute()

// 监听当前路由路径变化（改为深度监听整个 route 对象）
watch(
  () => route,
  (newRoute) => {
    if (newRoute.path === '/wx/manager') {
      getList()
    }
  },
  { deep: true } // 新增：深度监听路由对象变化
)

// 新增：路由更新前守卫（处理从子页面返回的场景）

onBeforeRouteUpdate((to, _from, next) => {
  if (to.path === '/wx/manager') {
    getList()
  }
  next()
})

async function handleEnable(row) {
  row.enableLoading = true
  try {
    await api.update({ id: row.id, enable: !row.enable })
    row.enableLoading = false
    $message.success('操作成功')
    $table.value?.handleSearch()
  }
  catch (error) {
    row.enableLoading = false
  }
}

async function getLink() {
  showModal.value = true;
  try {
    const { data } = await api.create(); // 使用 async/await 替代 .then

    link.value = data; // 假设接口返回的 data 是链接字符串
    $message.success('链接获取成功');
  } catch (error) {
    $message.error('链接获取失败，请重试');
  }
}

function toAuth() {
  // window.open(link.value, '_blank'); // 打开链接在新标签页中
  window.location.href = link.value; // 直接跳转到链接


}

function onSave() {
  handleSave()
}

function onDelete() {
  handleDelete()
}
</script>
