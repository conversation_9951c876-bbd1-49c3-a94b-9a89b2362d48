<template>
  <n-modal
    v-model:show="visible"
    :mask-closable="false"
    :close-on-esc="false"
    class="order-detail-modal"
    transform-origin="center"
  >
    <div class="order-detail-wrapper">
      <!-- 主要内容区域 -->
      <div class="modal-content">
        <!-- 全局加载状态 -->
        <div v-show="loading" class="global-loading">
          <n-spin size="large">
            <template #description>加载订单详情中...</template>
          </n-spin>
        </div>

        <!-- 数据为空状态 -->
        <div v-show="!loading && (!finalOrderData || Object.keys(finalOrderData).length === 0)" class="empty-state">
          <n-empty description="暂无订单数据" />
        </div>

        <!-- 左右分栏布局 -->
        <div v-show="!loading && finalOrderData && Object.keys(finalOrderData).length > 0" class="main-layout" :class="{ 'sidebar-collapsed': !sidebarVisible, 'no-connect-rooms': shouldHideConnectSidebar }">
          <!-- 左侧联房侧边栏 -->
          <div v-show="!shouldHideConnectSidebar" class="connect-sidebar" :class="{ collapsed: !sidebarVisible }">
            <div class="sidebar-header">
              <div class="header-content">
                <i class="i-mdi:home-group"></i>
                <span v-if="sidebarVisible">联房房间 ({{ connectRooms.length }})</span>
              </div>
              <n-button
                quaternary
                circle
                size="small"
                @click="toggleSidebar"
                class="toggle-button"
                :title="sidebarVisible ? '隐藏联房区域' : '显示联房区域'"
              >
                <template #icon>
                  <i :class="sidebarVisible ? 'i-mdi:chevron-left' : 'i-mdi:chevron-right'"></i>
                </template>
              </n-button>
            </div>
            <!-- 收起后的全局浮动展开按钮（兜底） -->
            <button
              v-if="!sidebarVisible && !shouldHideConnectSidebar"
              class="sidebar-fab"
              type="button"
              aria-label="展开联房侧栏"
              @click="toggleSidebar"
              title="展开联房侧栏"
            >
              <i class="i-mdi:chevron-right"></i>
              <span class="fab-text">联房</span>
            </button>

            <!-- 有联房数据时显示 -->
            <div v-show="connectRooms.length > 0">
              <!-- 联房财务明细跳转 -->
              <div class="connect-finance-link" v-show="sidebarVisible">
                <n-button
                  type="info"
                  size="small"
                  block
                  @click="handleViewConnectFinance"
                  class="connect-finance-button"
                >
                  <template #icon>
                    <i class="i-mdi:finance"></i>
                  </template>
                  查看联房财务明细
                </n-button>
              </div>
              <!-- 可滚动的房间列表 -->
              <div class="room-list-container" v-show="sidebarVisible">
                <div class="room-list">
                  <div
                    v-for="room in connectRooms"
                    :key="`expanded-${room?.bill_id || room?.id || 'unknown'}`"
                    :class="['room-item', { active: currentRoomBillId === (room?.bill_id || room?.id), loading: roomSwitchLoading === (room?.bill_id || room?.id) }]"
                    @click="switchRoom(room?.bill_id || room?.id)"
                  >
                    <div class="room-content">
                      <div class="room-number">{{ (room && room.room_number) || '-' }}</div>
                      <div class="room-info">
                        <div class="room-type">{{ room?.room_type_name || '标准间' }}</div>
                        <div class="room-status">
                          <n-tag v-show="room?.main_room" size="tiny" type="success">主房</n-tag>
                          <n-tag v-show="room && !room.main_room" size="tiny" type="info">联房</n-tag>
                        </div>
                      </div>
                      <div class="room-price">¥{{ room?.room_price || 0 }}</div>
                    </div>
                    <!-- 房间切换加载状态 -->
                    <div v-show="roomSwitchLoading === room?.bill_id" class="room-loading">
                      <n-spin size="small" />
                    </div>
                  </div>
                </div>
              </div>
              <!-- 折叠状态下的房间指示器 -->
              <div class="collapsed-indicators" v-show="!sidebarVisible">
                <div
                  v-for="room in connectRooms"
                  :key="`collapsed-${room?.bill_id || room?.id || 'unknown'}`"
                  :class="['room-indicator', { active: currentRoomBillId === (room?.bill_id || room?.id) }]"
                  @click="switchRoom(room?.bill_id || room?.id)"
                  :title="`房间 ${(room && room.room_number) || '-'}`"
                >
                  {{ (room && room.room_number) || '-' }}
                </div>
              </div>
            </div>

            <!-- 没有联房数据时显示 -->
            <div v-show="connectRooms.length === 0">
              <!-- 展开状态：显示单房间信息 -->
              <div class="single-room-info" v-show="sidebarVisible">
                <div class="info-section">
                  <div class="info-title">
                    <i class="i-mdi:home"></i>
                    <span>单间订单</span>
                  </div>
                  <div class="info-content">
                    <p class="info-text">此订单仅包含单个房间</p>
                    <p class="info-text">无联房信息</p>
                  </div>
                </div>
              </div>
              <!-- 收起状态：显示单房间图标 -->
              <div class="single-room-collapsed" v-show="!sidebarVisible">
                <div class="single-room-indicator active">
                  <i class="i-mdi:home"></i>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧：主要内容区域 -->
          <div class="main-panel">
            <!-- 顶部信息栏 -->
            <div class="top-info-bar">

              <div class="order-basic-info">
                <span class="order-id">id: {{ finalOrderData?.id || finalOrderData?.bill_id || '-' }}</span>
                <span class="order-number">订单号: {{ finalOrderData?.bill_code || finalOrderData?.order_no || '-' }}</span>
                <n-tag :type="getOrderStatusType(finalOrderData?.bill_status)" class="order-status">
                  {{ getOrderStatusText(finalOrderData?.bill_status) }}
                </n-tag>
              </div>
              <div class="top-actions">
                <n-button type="error" size="medium" @click="handleClose" class="close-button">
                  <template #icon><i class="i-mdi:close"></i></template>
                  关闭
                </n-button>
              </div>
            </div>

            <!-- Tab导航栏 -->
            <div class="tab-navigation">
              <n-tabs v-model:value="activeTab" type="line" size="medium" class="order-detail-tabs">
                <n-tab-pane name="details" tab="详情">
                  <template #tab>
                    <div class="tab-item">
                      <i class="i-mdi:file-document-outline"></i>
                      <span>详情</span>
                    </div>
                  </template>
                </n-tab-pane>
                <n-tab-pane name="guests" tab="入住人">
                  <template #tab>
                    <div class="tab-item">
                      <i class="i-mdi:account-group"></i>
                      <span>入住人</span>
                      <n-tag size="tiny" type="info" class="tab-count">{{ guestsCount }}</n-tag>
                    </div>
                  </template>
                </n-tab-pane>
                <n-tab-pane name="logs" tab="日志">
                  <template #tab>
                    <div class="tab-item">
                      <i class="i-mdi:history"></i>
                      <span>日志</span>
                      <n-tag size="tiny" type="info" class="tab-count">{{ logsCount }}</n-tag>
                    </div>
                  </template>
                </n-tab-pane>
                <n-tab-pane name="supermarket" tab="超市">
                  <template #tab>
                    <div class="tab-item">
                      <i class="i-mdi:shopping"></i>
                      <span>超市</span>
                      <n-tag size="tiny" type="info" class="tab-count">{{ supermarketCount }}</n-tag>
                    </div>
                  </template>
                </n-tab-pane>
                <n-tab-pane name="rental" tab="物品租借">
                  <template #tab>
                    <div class="tab-item">
                      <i class="i-mdi:package-variant"></i>
                      <span>物品租借</span>
                      <n-tag size="tiny" type="info" class="tab-count">{{ rentalCount }}</n-tag>
                    </div>
                  </template>
                </n-tab-pane>
                <n-tab-pane name="compensation" tab="物品赔付">
                  <template #tab>
                    <div class="tab-item">
                      <i class="i-mdi:alert-circle-outline"></i>
                      <span>物品赔付</span>
                      <n-tag size="tiny" type="info" class="tab-count">{{ compensationCount }}</n-tag>
                    </div>
                  </template>
                </n-tab-pane>
              </n-tabs>
            </div>

            <!-- 内容区域 - 基于tab切换 -->
            <div class="content-area">
              <!-- 详情Tab内容 -->
              <div v-show="activeTab === 'details'" class="tab-content-wrapper">
              <!-- 骨架屏加载状态 -->
              <div v-show="contentLoading" class="skeleton-loading">
                <!-- 订单概览骨架屏 -->
                <div class="skeleton-overview-section">
                  <div class="skeleton-row">
                    <div class="skeleton-card" v-for="i in 3" :key="i">
                      <div class="skeleton-header">
                        <div class="skeleton-icon"></div>
                        <div class="skeleton-title"></div>
                      </div>
                      <div class="skeleton-body">
                        <div class="skeleton-line" v-for="j in 3" :key="j"></div>
                      </div>
                    </div>
                  </div>
                  <div class="skeleton-row">
                    <div class="skeleton-card" v-for="i in 3" :key="i + 3">
                      <div class="skeleton-header">
                        <div class="skeleton-icon"></div>
                        <div class="skeleton-title"></div>
                      </div>
                      <div class="skeleton-body">
                        <div class="skeleton-line" v-for="j in 2" :key="j"></div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 操作区域骨架屏 -->
                <div class="skeleton-actions-section">
                  <div class="skeleton-action-group" v-for="i in 2" :key="i">
                    <div class="skeleton-group-header"></div>
                    <div class="skeleton-buttons">
                      <div class="skeleton-button" v-for="j in 4" :key="j"></div>
                    </div>
                  </div>
                </div>

                <!-- 表格骨架屏 -->
                <div class="skeleton-table">
                  <div class="skeleton-table-header">
                    <div class="skeleton-th" v-for="i in 5" :key="i"></div>
                  </div>
                  <div class="skeleton-table-body">
                    <div class="skeleton-tr" v-for="i in 3" :key="i">
                      <div class="skeleton-td" v-for="j in 5" :key="j"></div>
                    </div>
                  </div>
                </div>

                <!-- 加载提示 -->
                <div class="skeleton-loading-tip">
                  <n-spin size="small" />
                  <span>正在切换到房间 {{ getRoomNumber(roomSwitchLoading) }}...</span>
                </div>
              </div>

              <!-- 主要内容 -->
              <div v-show="!contentLoading" class="scrollable-content">
                <!-- 全局空值检查 -->
                <div v-if="!finalOrderData" class="no-data-message">
                  <div class="no-data-content">
                    <i class="i-mdi:information-outline"></i>
                    <span>暂无订单数据</span>
                  </div>
                </div>

                <!-- 订单概览区域 - 重新设计的紧凑布局 -->
                <div v-else class="order-overview-section">
                  <!-- 第一行：房间和价格信息 -->
                  <div class="overview-row">
                    <!-- 房间信息卡片 -->
                    <div class="overview-card room-card">
                      <div class="card-header">
                        <i class="i-mdi:home"></i>
                        <span class="card-title">房间信息</span>
                      </div>
                      <div class="card-body">
                        <div class="room-info-layout">
                          <!-- 主要房间信息 - 突出显示 -->
                          <div class="room-primary">
                            <span class="room-number">{{ (finalOrderData?.room_number) || '-' }}</span>
                            <span class="room-type">{{ (finalOrderData?.room_type_name) || '-' }}</span>
                          </div>

                          <!-- 详细信息 - 紧凑网格布局 -->
                          <div class="room-details">
                            <div class="detail-row">
                              <span class="detail-item">楼层: {{ getFloorInfo() }}</span>
                              <span class="detail-item">房费: ¥{{ (finalOrderData && finalOrderData.room_amount) || 0 }}</span>
                            </div>
                            <div class="detail-row">
                              <span class="detail-item">销售: {{ (finalOrderData && finalOrderData.room_sale_type_name) || '-' }}</span>
                              <span class="detail-item">天数: {{ (finalOrderData && finalOrderData.stay_time) || calculateDays() }}天</span>
                            </div>
                            <div class="detail-row">
                              <span class="detail-item">服务: {{ getServiceInfo() }}</span>
                              <span class="detail-item">方案: {{ getPriceInfo() }}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 财务概览卡片 -->
                    <div class="overview-card finance-card">
                      <div class="card-header">
                        <i class="i-mdi:currency-cny"></i>
                        <span class="card-title">财务概览</span>
                      </div>
                      <div class="card-body">
                        <div class="finance-layout">
                          <!-- 主要余额 - 突出显示 -->
                          <div class="balance-primary">
                            <span class="balance-label">余额</span>
                            <span class="balance-amount" :class="getBalanceClass()">¥{{ billFundData.balance_amount || (finalOrderData && finalOrderData.bill_balance) || 0 }}</span>
                          </div>

                          <!-- 财务详情 - 水平紧凑布局 -->
                          <div class="finance-details-compact">
                            <span class="finance-item">总额: ¥{{ (finalOrderData && finalOrderData.bill_amount) || 0 }}</span>
                            <span class="finance-item">已付: ¥{{ (finalOrderData && finalOrderData.already_pay) || 0 }}</span>
                            <span class="finance-item">押金: ¥{{ (finalOrderData && finalOrderData.cash_pledge) || 0 }}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 时间信息卡片 -->
                    <div class="overview-card time-card">
                      <div class="card-header">
                        <i class="i-mdi:clock-outline"></i>
                        <span class="card-title">时间信息</span>
                      </div>
                      <div class="card-body">
                        <div class="time-layout">
                          <!-- 时间信息分组显示 -->
                          <div class="time-group">
                            <div class="time-pair">
                              <span class="time-compact">预住: {{ formatDateTime(finalOrderData && finalOrderData.enter_time_plan) }}</span>
                              <span class="time-compact">入住: {{ formatDateTime(finalOrderData && finalOrderData.enter_time) }}</span>
                            </div>
                            <div class="time-pair">
                              <span class="time-compact">预离: {{ formatDateTime(finalOrderData && finalOrderData.leave_time_plan) }}</span>
                              <span class="time-compact">离店: {{ formatDateTime(finalOrderData && finalOrderData.leave_time) || '-' }}</span>
                            </div>
                          </div>

                          <!-- 入住天数突出显示 -->
                          <div class="stay-duration">
                            <span class="duration-value">{{ (finalOrderData && finalOrderData.stay_time) || calculateDays() }}</span>
                            <span class="duration-unit">天</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 第二行：客户、入住人员和硬件信息 -->
                  <div class="overview-row">
                    <!-- 联系人信息卡片 -->
                    <div class="overview-card customer-card">
                      <div class="card-header">
                        <i class="i-mdi:account"></i>
                        <span class="card-title">联系人信息</span>
                      </div>
                      <div class="card-body">
                        <div class="contact-layout">
                          <!-- 主要联系信息 -->
                          <div class="contact-primary">
                            <span class="contact-name">{{ (finalOrderData && finalOrderData.link_man) || '-' }}</span>
                            <span class="contact-phone">{{ (finalOrderData && finalOrderData.link_phone) || '-' }}</span>
                          </div>

                          <!-- 会员信息 -->
                          <div class="member-info">
                            <span class="member-item">等级: {{ (finalOrderData && finalOrderData.grade_name) || '-' }}</span>
                            <span class="member-item">会员: {{ (finalOrderData && finalOrderData.member_user_name) || '-' }}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 入住人员卡片 -->
                    <div class="overview-card guests-card">
                      <div class="card-header">
                        <i class="i-mdi:account-group"></i>
                        <span class="card-title">入住人员</span>
                        <n-button size="tiny" type="primary" @click="handleAddGuest" class="add-guest-btn">
                          <template #icon><i class="i-mdi:plus"></i></template>
                        </n-button>
                      </div>
                      <div class="card-body">
                        <div v-show="finalOrderData && finalOrderData.users && finalOrderData.users.length > 0" class="guests-list">
                          <div v-for="(user, index) in (finalOrderData && finalOrderData.users) || []" :key="`user-${user.id || user.name || index}`" class="guest-item-compact">
                            <div class="guest-info-compact">
                              <!-- 主要信息一行显示 -->
                              <div class="guest-main-line">
                                <span class="guest-name-compact">{{ user.name || '-' }}</span>
                                <div class="guest-badges-compact">
                                  <n-tag v-show="user.is_main" type="success" size="tiny">主</n-tag>
                                  <n-tag v-show="!user.is_main" type="default" size="tiny">同</n-tag>
                                  <n-tag v-show="user.send_password" type="info" size="tiny">
                                    <i class="i-mdi:key"></i>
                                  </n-tag>
                                </div>
                              </div>
                              <!-- 详细信息紧凑显示 -->
                              <div class="guest-details-compact">
                                <span class="guest-contact">{{ user.phone || '-' }} | {{ formatIdCard(user.identification_number) }}</span>
                              </div>
                            </div>
                            <n-button size="tiny" type="error" @click="handleDeleteGuest(user)" class="remove-guest-btn">
                              <i class="i-mdi:delete"></i>
                            </n-button>
                          </div>
                        </div>
                        <div v-show="!finalOrderData || !finalOrderData.users || finalOrderData.users.length === 0" class="no-guests">暂无入住人员</div>
                      </div>
                    </div>

                    <!-- 订单信息卡片 -->
                    <div class="overview-card status-card">
                      <div class="card-header">
                        <i class="i-mdi:information"></i>
                        <span class="card-title">订单信息</span>
                      </div>
                      <div class="card-body">
                        <div class="order-layout">
                          <!-- 订单状态和标识 -->
                          <div class="order-status-line">
                            <n-tag :type="getOrderStatusType(finalOrderData && finalOrderData.bill_status)" size="medium" class="status-tag-compact">
                              {{ getOrderStatusText(finalOrderData && finalOrderData.bill_status) }}
                            </n-tag>
                            <div class="order-badges-compact">
                              <n-tag v-if="finalOrderData && finalOrderData.secrecy" type="warning" size="tiny">
                                <i class="i-mdi:eye-off"></i>
                              </n-tag>
                              <n-tag v-if="finalOrderData && finalOrderData.team_bill" type="info" size="tiny">
                                <i class="i-mdi:account-group"></i>
                              </n-tag>
                            </div>
                          </div>

                          <!-- 订单详情 - 紧凑三列布局 -->
                          <div class="order-details-compact">
                            <div class="detail-column">
                              <span class="order-detail-item">ID: {{ finalOrderData?.id || finalOrderData?.bill_id || '-' }}</span>
                              <span class="order-detail-item">来源: {{ finalOrderData?.bill_source_name || '-' }}</span>
                            </div>
                            <div class="detail-column">
                              <span class="order-detail-item">订单号: {{ (finalOrderData && finalOrderData.bill_code) || '-' }}</span>
                              <span class="order-detail-item">分销: {{ getDistributorName() }}</span>
                            </div>
                            <div class="detail-column">
                              <span class="order-detail-item">外部号: {{ (finalOrderData && finalOrderData.other_bill_code) || '-' }}</span>
                              <span class="order-detail-item">备注: {{ (finalOrderData && finalOrderData.memo) || '-' }}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 第三行：硬件详情信息 -->
                  <div class="overview-row hardware-row">
                    <!-- 硬件详情卡片 -->
                    <div class="overview-card hardware-card">
                      <div class="card-header">
                        <i class="i-mdi:devices"></i>
                        <span class="card-title">硬件详情</span>
                        <div class="hardware-summary">
                          <n-tag v-show="getHardwareCount().total > 0" type="info" size="tiny">
                            {{ getHardwareCount().total }}个设备
                          </n-tag>
                        </div>
                      </div>
                      <div class="card-body">
                        <!-- Tab切换模式 -->
                        <n-tabs v-if="getHardwareCount().total > 0" type="line" size="small" class="hardware-tabs">
                          <!-- 智能门锁Tab -->
                          <n-tab-pane v-if="finalOrderData && finalOrderData.hardware_list?.lock_list?.length > 0" name="locks" tab="智能门锁">
                            <template #tab>
                              <div class="tab-header">
                                <i class="i-mdi:lock"></i>
                                <span>智能门锁</span>
                                <n-tag type="success" size="tiny" class="tab-count">{{ (finalOrderData && finalOrderData.hardware_list && finalOrderData.hardware_list.lock_list && finalOrderData.hardware_list.lock_list.length) || 0 }}</n-tag>
                              </div>
                            </template>
                            <div class="tab-content">
                              <div class="hardware-devices">
                                <div v-for="(lock, index) in (finalOrderData && finalOrderData.hardware_list && finalOrderData.hardware_list.lock_list) || []" :key="`lock-${lock.lock_id || lock.id || index}`" class="device-item lock-item">
                                  <div class="device-main">
                                    <span class="device-name">{{ lock.lock_alias || lock.lock_name || '-' }}</span>
                                    <div class="device-status">
                                      <n-tag v-if="lock.electric_quantity !== undefined"
                                             :type="getBatteryType(lock.electric_quantity)"
                                             size="tiny">
                                        <i class="i-mdi:battery"></i>{{ lock.electric_quantity }}%
                                      </n-tag>
                                      <n-tag type="default" size="tiny">{{ lock.lock_type_name || '门锁' }}</n-tag>
                                    </div>
                                  </div>
                                  <div class="device-details">
                                    <span class="device-info">ID: {{ lock.lock_id }}</span>
                                    <span class="device-info">MAC: {{ formatMacAddress(lock.lock_mac) }}</span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </n-tab-pane>

                          <!-- WiFi网络Tab -->
                          <n-tab-pane v-if="finalOrderData && finalOrderData.hardware_list?.wifi_list?.length > 0" name="wifi" tab="WiFi网络">
                            <template #tab>
                              <div class="tab-header">
                                <i class="i-mdi:wifi"></i>
                                <span>WiFi网络</span>
                                <n-tag type="info" size="tiny" class="tab-count">{{ (finalOrderData && finalOrderData.hardware_list && finalOrderData.hardware_list.wifi_list && finalOrderData.hardware_list.wifi_list.length) || 0 }}</n-tag>
                              </div>
                            </template>
                            <div class="tab-content">
                              <div class="hardware-devices">
                                <div v-for="(wifi, index) in (finalOrderData && finalOrderData.hardware_list && finalOrderData.hardware_list.wifi_list) || []" :key="`wifi-${wifi.id || wifi.ssid || index}`" class="device-item">
                                  <div class="device-main">
                                    <span class="device-name">{{ wifi.wifi_name || wifi.ssid || 'WiFi网络' }}</span>
                                    <div class="device-status">
                                      <n-tag :type="getWifiStatusType(wifi.status)" size="tiny">
                                        <i class="i-mdi:wifi"></i>{{ getWifiStatusText(wifi.status) }}
                                      </n-tag>
                                      <n-tag v-if="wifi.password" type="warning" size="tiny">
                                        <i class="i-mdi:lock"></i>加密
                                      </n-tag>
                                    </div>
                                  </div>
                                  <div class="device-details">
                                    <span class="device-info">SSID: {{ wifi.ssid || '-' }}</span>
                                    <span class="device-info">连接次数: {{ wifi.connect_times || 0 }}</span>
                                    <span v-if="wifi.password && showWifiPassword" class="device-info">密码: {{ wifi.password }}</span>
                                    <span v-else-if="wifi.password" class="device-info wifi-password-toggle" @click="toggleWifiPassword">
                                      密码: ******* <i class="i-mdi:eye"></i>
                                    </span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </n-tab-pane>

                          <!-- 取电开关Tab -->
                          <n-tab-pane v-if="finalOrderData && finalOrderData.hardware_list?.power_switch?.length > 0" name="power" tab="取电开关">
                            <template #tab>
                              <div class="tab-header">
                                <i class="i-mdi:power-socket"></i>
                                <span>取电开关</span>
                                <n-tag type="warning" size="tiny" class="tab-count">{{ (finalOrderData && finalOrderData.hardware_list && finalOrderData.hardware_list.power_switch && finalOrderData.hardware_list.power_switch.length) || 0 }}</n-tag>
                              </div>
                            </template>
                            <div class="tab-content">
                              <div class="hardware-devices">
                                <div v-for="(power, index) in (finalOrderData && finalOrderData.hardware_list && finalOrderData.hardware_list.power_switch) || []" :key="`power-${power.id || power.device_name || index}`" class="device-item">
                                  <div class="device-main">
                                    <span class="device-name">{{ power.device_name || '取电开关' }}</span>
                                    <div class="device-status">
                                      <n-tag :type="power.status === 'on' ? 'success' : 'default'" size="tiny">
                                        <i class="i-mdi:power"></i>{{ power.status === 'on' ? '开启' : '关闭' }}
                                      </n-tag>
                                    </div>
                                  </div>
                                  <div class="device-details">
                                    <span class="device-info">位置: {{ power.location || '-' }}</span>
                                    <span class="device-info">功率: {{ power.power || '-' }}</span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </n-tab-pane>
                        </n-tabs>

                        <!-- 无硬件设备提示 -->
                        <div v-else class="no-hardware">
                          <i class="i-mdi:devices-off"></i>
                          <span>暂无硬件设备</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 第一排操作区域：房间管理 + 打印二维码 -->
                <div class="primary-actions-section">
                  <!-- 房间管理操作 -->
                  <div class="action-group room-actions">
                    <div class="group-header">
                      <i class="i-mdi:home-edit"></i>
                      <span class="group-title">房间管理</span>
                    </div>
                    <div class="action-buttons">
                      <n-button size="small" @click="handleChangeRoom">
                        <template #icon><i class="i-mdi:swap-horizontal"></i></template>
                        更换房型
                      </n-button>
                      <n-button size="small" @click="handleContinueStay">
                        <template #icon><i class="i-mdi:calendar-plus"></i></template>
                        续房
                      </n-button>
                      <n-button size="small" @click="handleCheckout">
                        <template #icon><i class="i-mdi:logout"></i></template>
                        退房
                      </n-button>
                      <n-button size="small" @click="handleConnectRoom">
                        <template #icon><i class="i-mdi:home-group"></i></template>
                        联房
                      </n-button>
                    </div>
                  </div>

                  <!-- 打印二维码操作 -->
                  <div class="action-group print-actions">
                    <div class="group-header">
                      <i class="i-mdi:printer"></i>
                      <span class="group-title">打印与二维码</span>
                    </div>
                    <div class="action-buttons">
                      <n-button type="primary" size="small" @click="handlePrint">
                        <template #icon><i class="i-mdi:printer"></i></template>
                        打印订单
                      </n-button>
                      <n-button type="success" size="small" @click="handleGenerateKey">
                        <template #icon><i class="i-mdi:qrcode"></i></template>
                        生成钥匙二维码
                      </n-button>
                      <n-button type="info" size="small" @click="handleGenerateKeyWithCode">
                        <template #icon><i class="i-mdi:qrcode-plus"></i></template>
                        取单生成钥匙二维码
                      </n-button>
                      <n-button type="warning" size="small" @click="handleGenerateKeyWithCodeN31C">
                        <template #icon><i class="i-mdi:qrcode-scan"></i></template>
                        双H-N31C钥匙二维码
                      </n-button>
                    </div>
                  </div>
                </div>

                <!-- 第二排操作区域：财务操作 -->
                <div class="finance-operations-section">
                  <!-- 快速财务操作 -->
                  <div class="action-group finance-actions">
                    <div class="group-header">
                      <i class="i-mdi:cash-multiple"></i>
                      <span class="group-title">快速财务操作</span>
                    </div>
                    <div class="action-buttons">
                      <n-button type="success" size="small" @click="handleCollect">
                        <template #icon><i class="i-mdi:cash-plus"></i></template>
                        收款
                      </n-button>
                      <n-button type="warning" size="small" @click="handleHangAccount">
                        <template #icon><i class="i-mdi:account-clock"></i></template>
                        挂账
                      </n-button>
                      <n-button type="info" size="small" @click="handleTransfer">
                        <template #icon><i class="i-mdi:bank-transfer"></i></template>
                        过账
                      </n-button>
                      <n-button type="error" size="small" @click="handleWriteOff">
                        <template #icon><i class="i-mdi:cash-remove"></i></template>
                        冲销
                      </n-button>
                    </div>
                  </div>

                  <!-- 高级财务操作 -->
                  <div class="action-group advanced-finance-actions">
                    <div class="group-header">
                      <i class="i-mdi:finance"></i>
                      <span class="group-title">高级财务操作</span>
                    </div>
                    <div class="action-buttons">
                      <n-button size="small" @click="handleOriginalPrice">
                        <template #icon><i class="i-mdi:cash-plus"></i></template>
                        原价追款
                      </n-button>
                      <n-button size="small" @click="handleCustomPrice">
                        <template #icon><i class="i-mdi:cash-edit"></i></template>
                        自定义追款
                      </n-button>
                      <n-button size="small" @click="handleFreeOrder">
                        <template #icon><i class="i-mdi:gift"></i></template>
                        免单
                      </n-button>
                      <n-button size="small" @click="handleDiscount">
                        <template #icon><i class="i-mdi:percent"></i></template>
                        折扣
                      </n-button>
                      <n-button size="small" @click="handleWalkAccount">
                        <template #icon><i class="i-mdi:account-arrow-right"></i></template>
                        走账
                      </n-button>
                      <n-button size="small" @click="handleSettlement">
                        <template #icon><i class="i-mdi:calculator"></i></template>
                        结账
                      </n-button>
                      <n-button type="error" size="small" @click="handleTransferToDelinquent">
                        <template #icon><i class="i-mdi:alert-circle"></i></template>
                        转为拖结态
                      </n-button>
                    </div>
                  </div>
                </div>

                <!-- 财务明细卡片 - 包含筛选和表格 -->
                <div class="finance-details-card">
                  <!-- 卡片头部 - 标题和筛选 -->
                  <div class="card-header">
                    <div class="header-left">
                      <i class="i-mdi:table-large"></i>
                      <span class="card-title">财务明细</span>
                    </div>
                    <div class="header-right">
                      <n-button-group size="small">
                        <n-button
                          :type="financeFilter === 'all' ? 'primary' : 'default'"
                          @click="setFinanceFilter('all')"
                        >全部</n-button>
                        <n-button
                          :type="financeFilter === 'consume' ? 'primary' : 'default'"
                          @click="setFinanceFilter('consume')"
                        >消费</n-button>
                        <n-button
                          :type="financeFilter === 'collect' ? 'primary' : 'default'"
                          @click="setFinanceFilter('collect')"
                        >收款</n-button>
                      </n-button-group>
                    </div>
                  </div>

                  <!-- 卡片内容 - 表格 -->
                  <div class="card-content">
                    <div class="table-container">
                      <n-data-table
                        :columns="financeColumns"
                        :data="billFundData.list || []"
                        :loading="financeLoading"
                        :pagination="financePagination"
                        size="small"
                        :scroll-x="1200"
                        :max-height="400"
                        @update:page="handleFinancePageChange"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

              <!-- 入住人Tab内容 -->
              <div v-show="activeTab === 'guests'" class="tab-content-wrapper">
                <div class="guests-content">
                  <!-- 顶部操作区域 -->
                  <div class="guests-header">
                    <div class="guests-tips">
                      <div class="tips-content">
                        <i class="i-mdi:information-outline"></i>
                        <div class="tips-text">
                          <div class="tips-title">操作提醒</div>
                          <div class="tips-list">
                            <span class="tip-item">1、待入住、入住中和走结订单有编辑权限</span>
                            <span class="tip-item">2、走结订单不能编辑入住状态</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="guests-actions">
                      <n-button type="success" @click="handleAddGuest">
                        <template #icon><i class="i-mdi:plus"></i></template>
                        添加入住人
                      </n-button>
                    </div>
                  </div>

                  <!-- 入住人列表 -->
                  <div v-if="finalOrderData && finalOrderData.users && finalOrderData.users.length > 0" class="guests-table">
                    <div class="guests-table-header">
                      <div class="header-cell">住客信息</div>
                      <div class="header-cell">证件照片</div>
                      <div class="header-cell">用户照片</div>
                      <div class="header-cell">人脸认证情况</div>
                      <div class="header-cell">入住状态</div>
                      <div class="header-cell">是否已上传公安</div>
                      <div class="header-cell">操作</div>
                    </div>

                    <div class="guests-table-body">
                      <div
                        v-for="(user, index) in (finalOrderData && finalOrderData.users) || []"
                        :key="`user-${user.id || index}`"
                        class="guest-row"
                      >
                        <!-- 住客信息 -->
                        <div class="guest-cell guest-info-cell">
                          <div class="guest-name">{{ user.name || '-' }}</div>
                          <div class="guest-details">
                            <div class="detail-row">
                              <span class="detail-label">性别:</span>
                              <span class="detail-value">{{ user.gender === 1 ? '男' : user.gender === 2 ? '女' : '未知' }}</span>
                            </div>
                            <div class="detail-row">
                              <span class="detail-label">民族:</span>
                              <span class="detail-value">{{ user.nation || '-' }}</span>
                            </div>
                            <div class="detail-row">
                              <span class="detail-label">证件:</span>
                              <span class="detail-value">{{ getIdentificationTypeName(user.identification_type) }}</span>
                            </div>
                            <div class="detail-row">
                              <span class="detail-label">号码:</span>
                              <span class="detail-value">{{ formatIdCard(user.identification_number) }}</span>
                            </div>
                            <div class="detail-row">
                              <span class="detail-label">手机:</span>
                              <span class="detail-value">{{ user.phone || '-' }}</span>
                            </div>
                          </div>
                        </div>

                        <!-- 证件照片 -->
                        <div class="guest-cell photo-cell">
                          <div class="photo-container">
                            <div v-if="user.id_card_front_image" class="photo-item">
                              <img :src="user.id_card_front_image" alt="身份证正面" class="id-photo" />
                              <span class="photo-label">正面</span>
                            </div>
                            <div v-if="user.id_card_back_image" class="photo-item">
                              <img :src="user.id_card_back_image" alt="身份证背面" class="id-photo" />
                              <span class="photo-label">背面</span>
                            </div>
                            <div v-if="!user.id_card_front_image && !user.id_card_back_image" class="no-photo">
                              <i class="i-mdi:image-off"></i>
                              <span>未上传</span>
                            </div>
                          </div>
                        </div>

                        <!-- 用户照片 -->
                        <div class="guest-cell photo-cell">
                          <div class="photo-container">
                            <div v-if="user.avatar || user.face_image" class="photo-item">
                              <img :src="user.avatar || user.face_image" alt="用户照片" class="user-photo" />
                              <span class="photo-label">头像</span>
                            </div>
                            <div v-else class="no-photo">
                              <i class="i-mdi:account-off"></i>
                              <span>未上传</span>
                            </div>
                          </div>
                        </div>

                        <!-- 人脸认证情况 -->
                        <div class="guest-cell status-cell">
                          <div class="status-container">
                            <div
                              v-if="user.authentication === 1"
                              class="status-item success clickable"
                              @click="handleFaceVerification(user)"
                            >
                              <i class="i-mdi:check-circle"></i>
                              <span>已认证</span>
                            </div>
                            <div
                              v-else
                              class="status-item error clickable"
                              @click="handleFaceVerification(user)"
                            >
                              <i class="i-mdi:close-circle"></i>
                              <span>未认证</span>
                            </div>
                          </div>
                        </div>

                        <!-- 入住状态 -->
                        <div class="guest-cell status-cell">
                          <div class="status-container">
                            <div v-if="user.is_main" class="status-item primary">
                              <i class="i-mdi:account-star"></i>
                              <span>主住客</span>
                            </div>
                            <div v-else class="status-item default">
                              <i class="i-mdi:account"></i>
                              <span>同住人</span>
                            </div>
                            <div v-if="user.user_status === 1" class="status-item success">
                              <i class="i-mdi:home-check"></i>
                              <span>入住中</span>
                            </div>
                            <div v-else-if="user.user_status === 2" class="status-item info">
                              <i class="i-mdi:home-export-outline"></i>
                              <span>已离店</span>
                            </div>
                            <div v-else class="status-item warning">
                              <i class="i-mdi:home-clock"></i>
                              <span>未入住</span>
                            </div>
                            <div v-if="user.send_password === 1" class="status-item info">
                              <i class="i-mdi:key"></i>
                              <span>已发密码</span>
                            </div>
                          </div>
                        </div>

                        <!-- 是否已上传公安 -->
                        <div class="guest-cell status-cell">
                          <div class="status-container">
                            <div v-if="user.is_upload === 1" class="status-item success">
                              <i class="i-mdi:shield-check"></i>
                              <span>已上传</span>
                            </div>
                            <div v-else class="status-item error">
                              <i class="i-mdi:shield-off"></i>
                              <span>未上传</span>
                            </div>
                          </div>
                        </div>

                        <!-- 操作 -->
                        <div class="guest-cell actions-cell">
                          <div class="action-buttons">
                            <n-button size="small" type="primary" @click="handleViewMemberInfo(user)">
                              <template #icon><i class="i-mdi:account-details"></i></template>
                              查看会员信息
                            </n-button>
                            <n-button size="small" @click="handleEditGuest(user)">
                              <template #icon><i class="i-mdi:edit"></i></template>
                              编辑
                            </n-button>
                            <n-button size="small" type="error" @click="handleDeleteGuest(user)">
                              <template #icon><i class="i-mdi:delete"></i></template>
                              删除
                            </n-button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div v-else class="empty-guests">
                    <n-empty description="暂无入住人员">
                      <template #icon>
                        <i class="i-mdi:account-group"></i>
                      </template>
                      <template #extra>
                        <n-button type="primary" @click="handleAddGuest">
                          添加入住人
                        </n-button>
                      </template>
                    </n-empty>
                  </div>
                </div>
              </div>

              <!-- 日志Tab内容 -->
              <div v-show="activeTab === 'logs'" class="tab-content-wrapper">
                <div class="logs-content">
                  <div class="logs-header">
                    <div class="logs-title">
                      <h3>操作日志</h3>
                      <n-tag size="small" type="info">共 {{ logsCount }} 条</n-tag>
                    </div>
                    <div class="logs-controls">
                      <n-tabs
                        v-model:value="logFilter"
                        type="line"
                        size="small"
                        @update:value="refreshLogs"
                      >
                        <n-tab-pane
                          v-for="opt in logFilterOptions"
                          :key="`log-type-${opt.value}`"
                          :name="opt.value"
                          :tab="opt.label"
                        />
                      </n-tabs>
                      <n-button @click="refreshLogs" :loading="logsLoading" size="small">
                        <template #icon><i class="i-mdi:refresh"></i></template>
                        刷新
                      </n-button>
                    </div>
                  </div>

                  <div class="logs-list">
                    <div class="logs-scroll-area">
                      <n-spin :show="logsLoading">
                        <div class="log-items">
                          <div
                            v-for="log in logs"
                            :key="log.id || log.log_id"
                            class="log-item"
                          >
                            <div class="log-item-header">
                              <div class="log-meta">
                                <div class="log-icon-wrapper">
                                  <i :class="getLogIcon(log.type)" class="log-icon"></i>
                                </div>
                                <div class="log-info">
                                  <div class="log-title-row">
                                    <span class="log-title">{{ log.title || '操作记录' }}</span>
                                    <n-tag size="tiny" :type="getLogType(log.type)">
                                      {{ getLogTypeLabel(log.type) }}
                                    </n-tag>
                                  </div>
                                  <div class="log-time">
                                    <i class="i-mdi:clock-outline"></i>
                                    {{ formatLogTime(log.create_time || log.created_at) }}
                                  </div>
                                </div>
                              </div>
                              <div class="log-actions">
                                <n-button
                                  text
                                  size="small"
                                  @click="toggleLogDetails(log.id)"
                                >
                                  <template #icon>
                                    <i :class="expandedLogs.has(log.id) ? 'i-mdi:chevron-up' : 'i-mdi:chevron-down'"></i>
                                  </template>
                                  详情
                                </n-button>
                              </div>
                            </div>

                            <div class="log-content">
                              {{ log.content || log.remark || '-' }}
                            </div>

                            <n-collapse-transition :show="expandedLogs.has(log.id)">
                              <div class="log-details">
                                <div class="detail-row">
                                  <span class="detail-label">操作人员：</span>
                                  <span class="detail-value">{{ log.admin_name || log.operator_name || log.operator || '系统' }}</span>
                                </div>
                                <div class="detail-row" v-if="log.ip || log.operator_ip">
                                  <span class="detail-label">IP地址：</span>
                                  <span class="detail-value">{{ log.ip || log.operator_ip }}</span>
                                </div>
                                <div class="detail-row" v-if="log.brower">
                                  <span class="detail-label">浏览器：</span>
                                  <span class="detail-value">{{ log.brower }}</span>
                                </div>
                                <div class="detail-row">
                                  <span class="detail-label">记录ID：</span>
                                  <span class="detail-value">#{{ log.id }}</span>
                                </div>
                                <div class="detail-row" v-if="log.status !== undefined">
                                  <span class="detail-label">状态：</span>
                                  <n-tag size="tiny" :type="log.status === 1 ? 'success' : 'warning'">
                                    {{ log.status === 1 ? '成功' : '待处理' }}
                                  </n-tag>
                                </div>
                              </div>
                            </n-collapse-transition>
                          </div>
                        </div>

                        <div v-if="logs.length === 0 && !logsLoading" class="empty-logs">
                          <n-empty description="暂无操作日志">
                            <template #icon>
                              <i class="i-mdi:history"></i>
                            </template>
                          </n-empty>
                        </div>
                      </n-spin>
                    </div>

                    <!-- 固定在底部的分页 -->
                    <div v-if="logs.length > 0" class="logs-footer-fixed">
                      <div class="pagination-info">
                        <span class="info-text">
                          显示第 {{ (logsPagination.page - 1) * logsPagination.pageSize + 1 }} -
                          {{ Math.min(logsPagination.page * logsPagination.pageSize, logsPagination.itemCount) }} 条，
                          共 {{ logsPagination.itemCount }} 条记录
                        </span>
                      </div>
                      <n-pagination
                        :page="logsPagination.page"
                        :page-size="logsPagination.pageSize"
                        :item-count="logsPagination.itemCount"
                        :page-sizes="logsPagination.pageSizes"
                        :show-size-picker="logsPagination.showSizePicker"
                        :show-quick-jumper="true"
                        @update:page="handleLogsPageChange"
                        @update:page-size="handleLogsPageSizeChange"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <!-- 超市Tab内容 -->
              <div v-show="activeTab === 'supermarket'" class="tab-content-wrapper">
                <div class="supermarket-content">
                  <div class="supermarket-header">
                    <h3>超市消费</h3>
                    <n-button type="primary" @click="handleAddSupermarketItem">
                      <template #icon><i class="i-mdi:plus"></i></template>
                      添加商品
                    </n-button>
                  </div>

                  <div class="supermarket-list">
                    <n-empty description="超市消费功能开发中">
                      <template #icon>
                        <i class="i-mdi:shopping"></i>
                      </template>
                      <template #extra>
                        <n-button type="primary" @click="handleAddSupermarketItem">
                          添加商品消费
                        </n-button>
                      </template>
                    </n-empty>
                  </div>
                </div>
              </div>

              <!-- 物品租借Tab内容 -->
              <div v-show="activeTab === 'rental'" class="tab-content-wrapper">
                <div class="rental-content">
                  <div class="rental-header">
                    <h3>物品租借</h3>
                    <n-button type="primary" @click="handleAddRentalItem">
                      <template #icon><i class="i-mdi:plus"></i></template>
                      添加租借
                    </n-button>
                  </div>

                  <div class="rental-list">
                    <n-empty description="物品租借功能开发中">
                      <template #icon>
                        <i class="i-mdi:package-variant"></i>
                      </template>
                      <template #extra>
                        <n-button type="primary" @click="handleAddRentalItem">
                          添加租借物品
                        </n-button>
                      </template>
                    </n-empty>
                  </div>
                </div>
              </div>

              <!-- 物品赔付Tab内容 -->
              <div v-show="activeTab === 'compensation'" class="tab-content-wrapper">
                <div class="compensation-content">
                  <div class="compensation-header">
                    <h3>物品赔付</h3>
                    <n-button type="primary" @click="handleAddCompensationItem">
                      <template #icon><i class="i-mdi:plus"></i></template>
                      添加赔付
                    </n-button>
                  </div>

                  <div class="compensation-list">
                    <n-empty description="物品赔付功能开发中">
                      <template #icon>
                        <i class="i-mdi:alert-circle-outline"></i>
                      </template>
                      <template #extra>
                        <n-button type="primary" @click="handleAddCompensationItem">
                          添加赔付项目
                        </n-button>
                      </template>
                    </n-empty>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      <!-- 加载状态 -->
      <div v-show="loading" class="loading-container">
        <n-spin size="large">
          <template #description>
            加载订单详情中...
          </template>
        </n-spin>
      </div>

      <!-- 无数据状态 -->
      <div v-show="!loading && (!finalOrderData || Object.keys(finalOrderData).length === 0)" class="no-data-container">
        <n-empty description="暂无订单数据">
          <template #icon>
            <i class="i-mdi:receipt-text-outline"></i>
          </template>
        </n-empty>
      </div>
    </div>
    </div>
  </n-modal>

  <!-- 人脸认证弹框 -->
  <n-modal
    v-model:show="faceVerificationModal.show"
    preset="card"
    title="人脸认证"
    class="face-verification-modal"
    :style="{ width: '800px', maxWidth: '90vw' }"
    :mask-closable="false"
  >
    <div v-if="faceVerificationModal.user" class="face-verification-content">
      <!-- 用户基本信息 -->
      <div class="user-info-header">
        <h3>{{ faceVerificationModal.user.name || '未知姓名' }}</h3>
        <div class="user-details">
          <span>身份证号：{{ formatIdCard(faceVerificationModal.user.identification_number) }}</span>
          <span>手机号：{{ faceVerificationModal.user.phone || '-' }}</span>
        </div>
      </div>

      <!-- 照片对比区域 -->
      <div class="photos-comparison">
        <!-- 身份证照片 -->
        <div class="photo-section">
          <h4>身份证照片</h4>
          <div class="id-photos">
            <div v-if="faceVerificationModal.user.id_card_front_image" class="photo-item">
              <img
                :src="faceVerificationModal.user.id_card_front_image"
                alt="身份证正面"
                class="verification-photo"
                @click="previewImage(faceVerificationModal.user.id_card_front_image)"
              />
              <div class="photo-actions">
                <span class="photo-label">正面</span>
                <n-button
                  size="tiny"
                  type="primary"
                  @click="downloadImage(faceVerificationModal.user.id_card_front_image, '身份证正面')"
                >
                  <template #icon><i class="i-mdi:download"></i></template>
                  下载
                </n-button>
              </div>
            </div>
            <div v-if="faceVerificationModal.user.id_card_back_image" class="photo-item">
              <img
                :src="faceVerificationModal.user.id_card_back_image"
                alt="身份证背面"
                class="verification-photo"
                @click="previewImage(faceVerificationModal.user.id_card_back_image)"
              />
              <div class="photo-actions">
                <span class="photo-label">背面</span>
                <n-button
                  size="tiny"
                  type="primary"
                  @click="downloadImage(faceVerificationModal.user.id_card_back_image, '身份证背面')"
                >
                  <template #icon><i class="i-mdi:download"></i></template>
                  下载
                </n-button>
              </div>
            </div>
            <div v-if="!faceVerificationModal.user.id_card_front_image && !faceVerificationModal.user.id_card_back_image" class="no-photo">
              <i class="i-mdi:image-off"></i>
              <span>暂无身份证照片</span>
            </div>
          </div>
        </div>

        <!-- 人脸照片 -->
        <div class="photo-section">
          <h4>人脸照片</h4>
          <div class="face-photos">
            <div v-if="faceVerificationModal.user.avatar || faceVerificationModal.user.face_image" class="photo-item">
              <img
                :src="faceVerificationModal.user.avatar || faceVerificationModal.user.face_image"
                alt="人脸照片"
                class="verification-photo face-photo"
                @click="previewImage(faceVerificationModal.user.avatar || faceVerificationModal.user.face_image)"
              />
              <div class="photo-actions">
                <span class="photo-label">人脸</span>
                <n-button
                  size="tiny"
                  type="primary"
                  @click="downloadImage(faceVerificationModal.user.avatar || faceVerificationModal.user.face_image, '人脸照片')"
                >
                  <template #icon><i class="i-mdi:download"></i></template>
                  下载
                </n-button>
              </div>
            </div>
            <div v-else class="no-photo">
              <i class="i-mdi:account-off"></i>
              <span>暂无人脸照片</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 认证状态和操作 -->
      <div class="verification-actions">
        <div class="current-status">
          <span class="status-label">当前状态：</span>
          <n-tag
            :type="faceVerificationModal.user.authentication === 1 ? 'success' : 'error'"
          >
            {{ faceVerificationModal.user.authentication === 1 ? '已认证' : '未认证' }}
          </n-tag>
        </div>
        <div class="action-buttons">
          <n-button
            type="success"
            @click="updateVerificationStatus(true)"
            :loading="faceVerificationModal.loading"
          >
            <template #icon><i class="i-mdi:check-circle"></i></template>
            认证通过
          </n-button>
          <n-button
            type="error"
            @click="updateVerificationStatus(false)"
            :loading="faceVerificationModal.loading"
          >
            <template #icon><i class="i-mdi:close-circle"></i></template>
            认证不通过
          </n-button>
        </div>
      </div>
    </div>
  </n-modal>

  <!-- 编辑入住人弹框 -->
  <n-modal
    v-model:show="editGuestModal.show"
    preset="card"
    title="编辑入住人"
    class="edit-guest-modal"
    :style="{ width: '600px', maxWidth: '90vw' }"
    :mask-closable="false"
  >
    <div class="edit-guest-content">
      <n-form
        ref="editGuestFormRef"
        :model="editGuestModal.form"
        :rules="addGuestRules"
        label-placement="left"
        label-width="120px"
        require-mark-placement="left"
      >
        <!-- 姓名 - 带模糊搜索 -->
        <n-form-item label="姓名" path="name" required>
          <n-auto-complete
            v-model:value="editGuestModal.form.name"
            :options="nameSearchOptions"
            :loading="nameSearchLoading"
            placeholder="请输入姓名"
            @update:value="handleNameSearch"
            @select="handleNameSelect"
            clearable
          >
            <template #suffix>
              <n-button size="small" type="primary" @click="handleIdVerification">
                证
              </n-button>
            </template>
          </n-auto-complete>
        </n-form-item>

        <!-- 性别 -->
        <n-form-item label="性别" path="gender" required>
          <n-select
            v-model:value="editGuestModal.form.gender"
            :options="genderOptions"
            placeholder="请选择性别"
          />
        </n-form-item>

        <!-- 电话 -->
        <n-form-item label="电话" path="phone">
          <n-input
            v-model:value="editGuestModal.form.phone"
            placeholder="请输入手机号码"
            maxlength="11"
          />
        </n-form-item>

        <!-- 证件类型 -->
        <n-form-item label="证件类型" path="identification_type" required>
          <n-select
            v-model:value="editGuestModal.form.identification_type"
            :options="idTypeOptions"
            placeholder="请选择证件类型"
          />
        </n-form-item>

        <!-- 证件号码 -->
        <n-form-item label="证件号码" path="identification_number">
          <n-input
            v-model:value="editGuestModal.form.identification_number"
            placeholder="请输入证件号码"
            maxlength="30"
          />
        </n-form-item>

        <!-- 民族 -->
        <n-form-item label="民族" path="nation_id">
          <n-select
            v-model:value="editGuestModal.form.nation_id"
            :options="nationOptions"
            placeholder="请选择民族"
            filterable
          />
        </n-form-item>

        <!-- 户籍地址 -->
        <n-form-item label="户籍地址" path="address">
          <n-input
            v-model:value="editGuestModal.form.address"
            placeholder="请输入户籍地址"
            maxlength="200"
          />
        </n-form-item>

        <!-- 是否已人脸认证 -->
        <n-form-item label="是否已人脸认证">
          <n-radio-group v-model:value="editGuestModal.form.id_authentication" class="radio-group-spacing">
            <n-radio :value="1">是</n-radio>
            <n-radio :value="0">否</n-radio>
          </n-radio-group>
        </n-form-item>

        <!-- 是否已上传公安 -->
        <n-form-item label="是否已上传公安">
          <n-radio-group v-model:value="editGuestModal.form.is_upload" class="radio-group-spacing">
            <n-radio :value="1">是</n-radio>
            <n-radio :value="0">否</n-radio>
          </n-radio-group>
        </n-form-item>
      </n-form>

      <!-- 操作按钮 -->
      <div class="modal-actions">
        <n-button @click="handleCancelEditGuest">关闭</n-button>
        <n-button
          type="primary"
          @click="handleSubmitEditGuest"
          :loading="editGuestModal.loading"
        >
          提交
        </n-button>
      </div>
    </div>
  </n-modal>

  <!-- 会员信息弹窗 -->
  <MemberInfoModal
    :show="memberInfoModal.show"
    :common-code="memberInfoModal.commonCode"
    @update:show="memberInfoModal.show = $event"
  />

  <!-- 编辑入住状态弹框 -->
  <n-modal
    v-model:show="editStatusModal.show"
    preset="card"
    title="编辑入住状态"
    class="edit-status-modal"
    :style="{ width: '500px', maxWidth: '90vw' }"
    :mask-closable="false"
  >
    <div v-if="editStatusModal.user" class="edit-status-content">
      <!-- 用户基本信息 -->
      <div class="user-info-section">
        <h4>{{ editStatusModal.user.name || '未知姓名' }}</h4>
        <div class="user-basic-info">
          <span>身份证号：{{ formatIdCard(editStatusModal.user.identification_number) }}</span>
          <span>手机号：{{ editStatusModal.user.phone || '-' }}</span>
        </div>
      </div>

      <!-- 当前状态 -->
      <div class="current-status-section">
        <div class="status-row">
          <span class="status-label">当前入住状态：</span>
          <n-tag
            :type="editStatusModal.user.user_status === 1 ? 'success' :
                  editStatusModal.user.user_status === 2 ? 'info' : 'warning'"
          >
            {{ editStatusModal.user.user_status === 1 ? '入住中' :
               editStatusModal.user.user_status === 2 ? '已离店' : '未入住' }}
          </n-tag>
        </div>
      </div>

      <!-- 状态选择 -->
      <div class="status-select-section">
        <div class="form-item">
          <label class="form-label">修改为：</label>
          <n-select
            v-model:value="editStatusModal.newStatus"
            :options="userStatusOptions"
            placeholder="请选择入住状态"
            class="status-select"
          />
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="modal-actions">
        <n-button @click="editStatusModal.show = false">取消</n-button>
        <n-button
          type="primary"
          @click="updateUserStatus"
          :loading="editStatusModal.loading"
          :disabled="!editStatusModal.newStatus && editStatusModal.newStatus !== 0"
        >
          确认修改
        </n-button>
      </div>
    </div>
  </n-modal>

  <!-- 添加入住人弹框 -->
  <n-modal
    v-model:show="addGuestModal.show"
    preset="card"
    title="添加入住人"
    class="add-guest-modal"
    :style="{ width: '600px', maxWidth: '90vw' }"
    :mask-closable="false"
  >
    <div class="add-guest-content">
      <n-form
        ref="addGuestFormRef"
        :model="addGuestModal.form"
        :rules="addGuestRules"
        label-placement="left"
        label-width="120px"
        require-mark-placement="left"
      >
        <!-- 姓名 - 带模糊搜索 -->
        <n-form-item label="姓名" path="name" required>
          <n-auto-complete
            v-model:value="addGuestModal.form.name"
            :options="nameSearchOptions"
            :loading="nameSearchLoading"
            placeholder="请输入姓名"
            @update:value="handleNameSearch"
            @select="handleAddGuestNameSelect"
            clearable
          >
            <template #suffix>
              <n-button size="small" type="success" @click="handleIdVerification">
                证
              </n-button>
            </template>
          </n-auto-complete>
        </n-form-item>

        <!-- 性别 -->
        <n-form-item label="性别" path="gender" required>
          <n-select
            v-model:value="addGuestModal.form.gender"
            :options="genderOptions"
            placeholder="请选择性别"
          />
        </n-form-item>

        <!-- 电话 -->
        <n-form-item label="电话" path="phone">
          <n-input
            v-model:value="addGuestModal.form.phone"
            placeholder="请输入手机号码"
            maxlength="11"
          />
        </n-form-item>

        <!-- 证件类型 -->
        <n-form-item label="证件类型" path="identification_type" required>
          <n-select
            v-model:value="addGuestModal.form.identification_type"
            :options="idTypeOptions"
            placeholder="请选择证件类型"
          />
        </n-form-item>

        <!-- 证件号码 -->
        <n-form-item label="证件号码" path="identification_number">
          <n-input
            v-model:value="addGuestModal.form.identification_number"
            placeholder="请输入证件号码"
            maxlength="30"
          />
        </n-form-item>

        <!-- 民族 -->
        <n-form-item label="民族" path="nation_id">
          <n-select
            v-model:value="addGuestModal.form.nation_id"
            :options="nationOptions"
            placeholder="请选择民族"
            filterable
          />
        </n-form-item>

        <!-- 户籍地址 -->
        <n-form-item label="户籍地址" path="address">
          <n-input
            v-model:value="addGuestModal.form.address"
            placeholder="请输入户籍地址"
            maxlength="200"
          />
        </n-form-item>

        <!-- 是否已人脸认证 -->
        <n-form-item label="是否已人脸认证">
          <n-radio-group v-model:value="addGuestModal.form.id_authentication" class="radio-group-spacing">
            <n-radio :value="1">是</n-radio>
            <n-radio :value="0">否</n-radio>
          </n-radio-group>
        </n-form-item>

        <!-- 是否已上传公安 -->
        <n-form-item label="是否已上传公安">
          <n-radio-group v-model:value="addGuestModal.form.is_upload" class="radio-group-spacing">
            <n-radio :value="1">是</n-radio>
            <n-radio :value="0">否</n-radio>
          </n-radio-group>
        </n-form-item>
      </n-form>

      <!-- 操作按钮 -->
      <div class="modal-actions">
        <n-button @click="handleCancelAddGuest">关闭</n-button>
        <n-button
          type="primary"
          @click="handleSubmitAddGuest"
          :loading="addGuestModal.loading"
        >
          提交
        </n-button>
      </div>
    </div>
  </n-modal>
</template>

<script setup>
import { ref, computed, watch, h, onUnmounted, onMounted, nextTick } from 'vue'
import { useMessage, useDialog, NButton } from 'naive-ui'
import { request } from '@/utils/http'
import MemberInfoModal from './MemberInfoModal.vue'

// 使用message和dialog

// 工具：从“姓名 等级 手机号”或“姓名 - 手机号 - 证件号”等字符串中提取纯姓名
function getPureName(input) {
  if (input == null) return ''
  let s = String(input).trim()
  // 统一替换中文破折号、长横等为短横
  s = s.replace(/[—–\u2014\u2013]/g, '-')
  // 寻找最早出现的分隔符位置：" - ", "|", "-", 空格
  const idxs = []
  const pushIdx = (i) => { if (i !== -1) idxs.push(i) }
  pushIdx(s.indexOf(' - '))
  pushIdx(s.indexOf('|'))
  pushIdx(s.indexOf('-'))
  pushIdx(s.indexOf(' '))
  const cut = idxs.length ? Math.min(...idxs) : -1
  if (cut !== -1) {
    s = s.slice(0, cut)
  }
  return s.trim()
}

const message = useMessage()
const dialog = useDialog()

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  orderData: {
    type: Object,
    default: () => ({})
  },
  billId: {
    type: [String, Number],
    default: null
  }
})

const emit = defineEmits(['update:show', 'print', 'export'])
const visible = ref(false)
const loading = ref(false)
const apiOrderData = ref({})

// 组件挂载状态
const isMounted = ref(false)
const isDestroyed = ref(false)

// 新增状态
const billDataTips = ref({})
const billFundData = ref({ list: [], count: 0, balance_amount: 0, consume_amount: 0, pay_amount: 0, cash_pledge_amount: 0 })
const connectFundData = ref({ balance_amount: 0, consume_amount: 0 })
const connectRooms = ref([])
const connectRoomsLoaded = ref(false) // 联房数据是否已加载
const currentRoomBillId = ref(null)
const financeLoading = ref(false)
const financeFilter = ref('all')

// 侧边栏显示控制
const sidebarVisible = ref(true)

// 局部加载状态
// 日志展开状态管理
const expandedLogs = ref(new Set())

// 人脸认证弹框状态
const faceVerificationModal = ref({
  show: false,
  user: null,
  loading: false
})

// 编辑入住状态弹框状态
const editStatusModal = ref({
  show: false,
  user: null,
  newStatus: null,
  loading: false
})

// 会员信息弹窗状态
const memberInfoModal = ref({
  show: false,
  commonCode: ''
})

// 添加入住人弹窗状态
const addGuestModal = ref({
  show: false,
  loading: false,
  form: {
    name: '',
    gender: 0, // 默认未知
    phone: '',
    identification_type: 1,
    identification_number: '',
    address: '0',
    id_authentication: 1,
    is_upload: 1,
    birthday: '1',
    nation_id: 1
  }
})

// 编辑入住人弹窗状态
const editGuestModal = ref({
  show: false,
  loading: false,
  user: null, // 当前编辑的用户
  form: {
    name: '',
    gender: 0, // 默认未知
    phone: '',
    identification_type: 1,
    identification_number: '',
    address: '0',
    id_authentication: 1,
    is_upload: 1,
    birthday: '1',
    nation_id: 1
  }
})

// 姓名搜索相关状态
const nameSearchOptions = ref([])
const nameSearchLoading = ref(false)


// 统一清洗姓名输入：若出现 “姓名 - 手机号 - 证件号”，自动截取 “姓名” 部分
watch(
  () => editGuestModal.value?.form?.name,
  (val) => {
    if (typeof val === 'string') {
      const pure = getPureName(val)
      if (pure !== val) {
        editGuestModal.value.form.name = pure
      }
    }
  }
)

watch(
  () => addGuestModal.value?.form?.name,
  (val) => {
    if (typeof val === 'string') {
      const pure = getPureName(val)
      if (pure !== val) {
        addGuestModal.value.form.name = pure
      }
    }
  }
)

// 入住状态选项
const userStatusOptions = [
  { label: '未入住', value: 0 },
  { label: '入住中', value: 1 },
  { label: '已离店', value: 2 }
]

// 证件类型列表
const identificationTypes = ref([])

// 性别选项
const genderOptions = [
  { label: '未知', value: 0 },
  { label: '男', value: 1 },
  { label: '女', value: 2 }
]

// 证件类型选项 - 从接口获取
const idTypeOptions = computed(() => {
  return identificationTypes.value.map(type => ({
    label: type.name || type.label,
    value: type.id || type.value
  }))
})

// 民族选项
const nationOptions = [
  { label: '汉族', value: 1 },
  { label: '蒙古族', value: 2 },
  { label: '回族', value: 3 },
  { label: '藏族', value: 4 },
  { label: '维吾尔族', value: 5 },
  { label: '苗族', value: 6 },
  { label: '彝族', value: 7 },
  { label: '壮族', value: 8 },
  { label: '布依族', value: 9 },
  { label: '朝鲜族', value: 10 },
  { label: '满族', value: 11 },
  { label: '侗族', value: 12 },
  { label: '瑶族', value: 13 },
  { label: '白族', value: 14 },
  { label: '土家族', value: 15 },
  { label: '哈尼族', value: 16 },
  { label: '哈萨克族', value: 17 },
  { label: '傣族', value: 18 },
  { label: '黎族', value: 19 },
  { label: '傈僳族', value: 20 },
  { label: '佤族', value: 21 },
  { label: '畲族', value: 22 },
  { label: '高山族', value: 23 },
  { label: '拉祜族', value: 24 },
  { label: '水族', value: 25 },
  { label: '东乡族', value: 26 },
  { label: '纳西族', value: 27 },
  { label: '景颇族', value: 28 },
  { label: '柯尔克孜族', value: 29 },
  { label: '土族', value: 30 },
  { label: '达斡尔族', value: 31 },
  { label: '仫佬族', value: 32 },
  { label: '羌族', value: 33 },
  { label: '布朗族', value: 34 },
  { label: '撒拉族', value: 35 },
  { label: '毛南族', value: 36 },
  { label: '仡佬族', value: 37 },
  { label: '锡伯族', value: 38 },
  { label: '阿昌族', value: 39 },
  { label: '普米族', value: 40 },
  { label: '塔吉克族', value: 41 },
  { label: '怒族', value: 42 },
  { label: '乌孜别克族', value: 43 },
  { label: '俄罗斯族', value: 44 },
  { label: '鄂温克族', value: 45 },
  { label: '德昂族', value: 46 },
  { label: '保安族', value: 47 },
  { label: '裕固族', value: 48 },
  { label: '京族', value: 49 },
  { label: '塔塔尔族', value: 50 },
  { label: '独龙族', value: 51 },
  { label: '鄂伦春族', value: 52 },
  { label: '赫哲族', value: 53 },
  { label: '门巴族', value: 54 },
  { label: '珞巴族', value: 55 },
  { label: '基诺族', value: 56 }
]

// 添加入住人表单验证规则
const addGuestRules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 1, max: 50, message: '姓名长度应为1-50个字符', trigger: 'blur' }
  ],
  gender: [
    {
      validator: (rule, value) => {
        if (value === undefined || value === null || value === '') {
          return new Error('请选择性别')
        }
        return true
      },
      trigger: 'change'
    }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  identification_type: [
    {
      validator: (rule, value) => {
        if (value === undefined || value === null || value === '') {
          return new Error('请选择证件类型')
        }
        return true
      },
      trigger: 'change'
    }
  ],
  identification_number: [
    { min: 1, max: 30, message: '证件号码长度应为1-30个字符', trigger: 'blur' }
  ]
}

// 表单引用
const addGuestFormRef = ref(null)
const editGuestFormRef = ref(null)

// 获取证件类型列表
async function fetchIdentificationTypes() {
  try {
    const response = await request.get('/admin/CommonData/getIdentificationTypeList')
    if (response.code === 0 && response.data) {
      identificationTypes.value = response.data
    } else if (response.code === 886) {
      message.error('登录已过期，请重新登录')
      // 这里可以添加跳转到登录页的逻辑
    } else {
      console.error('获取证件类型列表失败:', response.message)
    }
  } catch (error) {
    console.error('获取证件类型列表失败:', error)
  }
}

// 获取证件类型名称
function getIdentificationTypeName(typeId) {
  if (!typeId || !identificationTypes.value.length) return '身份证'
  const type = identificationTypes.value.find(item => item.id === typeId || item.value === typeId)
  return type ? type.name || type.label : '身份证'
}

// 全局接口状态码处理
function handleApiResponse(response, successCallback, errorMessage = '操作失败') {
  if (response.code === 0) {
    // 成功
    if (successCallback) successCallback(response)
    return true
  } else if (response.code === 886) {
    // 登录过期
    message.error('登录已过期，请重新登录')
    // 这里可以添加跳转到登录页的逻辑
    return false
  } else {
    // 其他错误
    message.error(response.message || errorMessage)
    return false
  }
}

// 判断是否为最近的日志（24小时内）
function isRecentLog(timestamp) {
  if (!timestamp) return false
  const logTime = Number(timestamp) < 1e12 ? Number(timestamp) * 1000 : Number(timestamp)
  const now = Date.now()
  return (now - logTime) < 24 * 60 * 60 * 1000
}

// 切换日志详情展开状态
function toggleLogDetails(logId) {
  if (expandedLogs.value.has(logId)) {
    expandedLogs.value.delete(logId)
  } else {
    expandedLogs.value.add(logId)
  }
}

// 分页处理函数
function handleLogsPageChange(page) {
  logsPagination.value.page = page
  const bid = currentRoomBillId.value || props.billId || finalOrderData.value?.bill_id || finalOrderData.value?.id
  fetchRoomLogs(bid, logFilter.value)
}

function handleLogsPageSizeChange(size) {
  logsPagination.value.pageSize = size
  logsPagination.value.page = 1
  const bid = currentRoomBillId.value || props.billId || finalOrderData.value?.bill_id || finalOrderData.value?.id
  fetchRoomLogs(bid, logFilter.value)
}
const contentLoading = ref(false)
const roomSwitchLoading = ref(null) // 存储正在切换的房间ID

// WiFi密码显示控制
const showWifiPassword = ref(false)

// 硬件展开控制
const hardwareExpanded = ref(false)
const locksExpanded = ref(false)

// Tab切换控制
const activeTab = ref('details')
const guestsCount = computed(() => (finalOrderData.value?.users?.length) || 0)
const supermarketCount = computed(() => (finalOrderData.value?.supermarket_items?.length) || 0)
const rentalCount = computed(() => (finalOrderData.value?.rental_items?.length) || 0)
const compensationCount = computed(() => (finalOrderData.value?.compensation_items?.length) || 0)

// 日志相关数据
const logs = ref([])
const logsLoading = ref(false)
const logsPagination = ref({
  page: 1,
  pageSize: 20,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100]
})

// 日志数量（从账单提示数据中获取优先显示）
const logsCount = computed(() => {
  const tips = billDataTips.value || {}
  return tips.log_total ?? tips.logs_total ?? tips.logsCount ?? tips.log_count ?? tips.count ?? 0
})

const logFilter = ref('')
const logFilterOptions = [
  { label: '全部', value: '' },
  { label: '订房日志', value: '1' },
  { label: '财务日志', value: '2' },
  { label: '其他日志', value: '3' }
]

// 获取房间号的辅助函数
function getRoomNumber(billId) {
  if (!billId) return ''
  const room = connectRooms.value.find(r => r.bill_id === billId)
  return room?.room_number || billId
}

// 财务明细表格列定义
const financeColumns = [
  {
    title: '流水号',
    key: 'id',
    width: 80
  },
  {
    title: '时间',
    key: 'create_time',
    width: 120,
    render: (row) => formatDateTime(row.create_time)
  },
  {
    title: '消费项目',
    key: 'name',
    width: 120
  },
  {
    title: '类型',
    key: 'type_name',
    width: 80
  },
  {
    title: '单价',
    key: 'price',
    width: 80,
    render: (row) => `¥${row.price || 0}`
  },
  {
    title: '数量',
    key: 'num',
    width: 60
  },
  {
    title: '金额',
    key: 'amount',
    width: 80,
    render: (row) => `¥${row.amount || 0}`
  },
  {
    title: '操作员',
    key: 'operator_name',
    width: 80
  },
  {
    title: '备注',
    key: 'remark',
    width: 120
  },
  {
    title: '操作',
    key: 'actions',
    width: 80,
    render: (row) => h('div', [
      h(NButton, {
        size: 'tiny',
        type: 'error',
        onClick: () => handleDeleteFinanceItem(row.id)
      }, { default: () => '删除' })
    ])
  }
]

// 财务明细分页
const financePagination = ref({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  showQuickJumper: true,
  prefix: ({ itemCount }) => `共 ${itemCount} 条`
})

// 数据缓存
const dataCache = new Map()

// 计算属性
const finalOrderData = computed(() => {
  // 确保返回值永远不是 null 或 undefined
  const apiData = apiOrderData.value || {}
  const propData = props.orderData || {}

  // 如果 apiOrderData 有数据，优先使用它
  if (Object.keys(apiData).length > 0) {
    return apiData
  }

  // 否则使用 props.orderData，如果也没有则返回空对象
  return Object.keys(propData).length > 0 ? propData : {}
})

// 判断是否应该隐藏联房侧边栏
const shouldHideConnectSidebar = computed(() => {
  // 如果还在加载中，不隐藏（显示加载状态）
  if (loading.value || contentLoading.value) {
    return false
  }

  // 如果没有订单数据，隐藏
  if (!finalOrderData.value || Object.keys(finalOrderData.value).length === 0) {
    return true
  }

  // 基于 getBillDetail 接口的 connect_bills 数据判断
  // 如果 finalOrderData 中有 connect_bills 且不为空，显示侧边栏
  if (finalOrderData.value.connect_bills && Array.isArray(finalOrderData.value.connect_bills)) {
    return finalOrderData.value.connect_bills.length === 0
  }

  // 如果 connectRooms 已加载，使用其数据
  if (connectRoomsLoaded.value) {
    return connectRooms.value.length === 0
  }

  // 默认不隐藏（等待数据加载）
  return false
})

// 格式化日期时间
function formatDateTime(dateTime) {
  if (!dateTime) return '-'
  // 如果是时间戳，转换为毫秒
  if (typeof dateTime === 'number') {
    dateTime = dateTime * 1000
  }
  const date = new Date(dateTime)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取楼层信息
function getFloorInfo() {
  if (!finalOrderData.value) return '-'
  const floorNumber = finalOrderData.value.floor_number
  const buildingNumber = finalOrderData.value.building_number
  if (floorNumber && buildingNumber) {
    return `${buildingNumber}栋${floorNumber}楼`
  }
  return finalOrderData.value.floor_name || '-'
}

// 获取服务信息
function getServiceInfo() {
  if (!finalOrderData.value) return '-'
  const roomService = finalOrderData.value.room_service
  if (roomService && roomService.service_name) {
    return roomService.service_name
  }
  return '-'
}

// 获取价格信息
function getPriceInfo() {
  if (!finalOrderData.value) return '-'
  const saleTypeName = finalOrderData.value.room_sale_type_name
  const saleTypeSign = finalOrderData.value.room_sale_type_sign
  if (saleTypeName) {
    return saleTypeSign ? `${saleTypeName}(${saleTypeSign})` : saleTypeName
  }
  return '-'
}

// 格式化身份证号（脱敏处理）
function formatIdCard(idCard) {
  if (!idCard || idCard === 'xxxx') return '-'
  if (idCard.length >= 8) {
    return idCard.substring(0, 4) + '****' + idCard.substring(idCard.length - 4)
  }
  return idCard
}

// 获取分销人信息
function getDistributorName() {
  if (!finalOrderData.value) return '-'
  const distributionUser = finalOrderData.value.distribution_user
  if (distributionUser && distributionUser.name) {
    return distributionUser.name
  }
  return finalOrderData.value.distribution_user_name || '-'
}

// 获取硬件设备总数
function getHardwareCount() {
  if (!finalOrderData.value) return { locks: 0, wifi: 0, power: 0, total: 0 }
  const hardwareList = finalOrderData.value.hardware_list || {}
  const lockCount = hardwareList.lock_list?.length || 0
  const wifiCount = hardwareList.wifi_list?.length || 0
  const powerCount = hardwareList.power_switch?.length || 0

  return {
    lock: lockCount,
    wifi: wifiCount,
    power: powerCount,
    total: lockCount + wifiCount + powerCount
  }
}

// 获取电池电量类型
function getBatteryType(quantity) {
  if (quantity >= 80) return 'success'
  if (quantity >= 50) return 'warning'
  if (quantity >= 20) return 'error'
  return 'default'
}

// 格式化MAC地址
function formatMacAddress(mac) {
  if (!mac) return '-'
  // 确保MAC地址格式统一
  return mac.toUpperCase()
}

// 获取WiFi状态类型
function getWifiStatusType(status) {
  if (status === 1) return 'success' // 启用
  return 'default' // 禁用
}

// 获取WiFi状态文本
function getWifiStatusText(status) {
  if (status === 1) return '可用'
  return '禁用'
}

// 切换WiFi密码显示
function toggleWifiPassword() {
  showWifiPassword.value = !showWifiPassword.value
}

// 硬件展开控制
function toggleHardwareExpanded() {
  hardwareExpanded.value = !hardwareExpanded.value
}

function toggleLocksExpanded() {
  locksExpanded.value = !locksExpanded.value
}

// 获取显示的门锁列表（默认显示前2个）
function getDisplayedLocks() {
  if (!finalOrderData.value) return []
  const locks = finalOrderData.value.hardware_list?.lock_list || []
  if (locksExpanded.value || locks.length <= 2) {
    return locks
  }
  return locks.slice(0, 2)
}

// 检查是否有其他硬件设备
function hasOtherHardware() {
  if (!finalOrderData.value) return false
  const hardwareList = finalOrderData.value.hardware_list || {}
  const wifiCount = hardwareList.wifi_list?.length || 0
  const powerCount = hardwareList.power_switch?.length || 0
  return wifiCount > 0 || powerCount > 0
}

// 获取其他硬件设备数量
function getOtherHardwareCount() {
  if (!finalOrderData.value) return 0
  const hardwareList = finalOrderData.value.hardware_list || {}
  const wifiCount = hardwareList.wifi_list?.length || 0
  const powerCount = hardwareList.power_switch?.length || 0
  return wifiCount + powerCount
}

// 计算入住天数
function calculateDays() {
  if (!finalOrderData.value || !finalOrderData.value.enter_time || !finalOrderData.value.leave_time_plan) return 0
  const enterTime = new Date(finalOrderData.value.enter_time)
  const leaveTime = new Date(finalOrderData.value.leave_time_plan)
  const diffTime = Math.abs(leaveTime - enterTime)
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays
}

// 获取余额样式类
function getBalanceClass() {
  const balance = billFundData.value.balance_amount || 0
  if (balance > 0) return 'success'
  if (balance < 0) return 'error'
  return 'default'
}

// 获取订单状态类型
function getOrderStatusType(status) {
  const statusMap = {
    1: 'info',     // 预订
    2: 'success',  // 入住
    3: 'warning',  // 退房
    4: 'error'     // 取消
  }
  return statusMap[status] || 'default'
}

// 获取订单状态文本
function getOrderStatusText(status) {
  const statusMap = {
    //  1：代付款，2待确认3：待入住4：入住中5：已完成6：待取消7：已取消，8申请取消，9预定未到，10走结
    1: '待付款',
    2: '待确认',
    3: '待入住',
    4: '入住中',
    5: '已完成',
    6: '待取消',
    7: '已取消',
    8: '申请取消',
    9: '预定未到',
    10: '走结'

  }
  return statusMap[status] || '未知'
}

// 获取支付状态类型
function getPaymentStatusType(status) {
  const statusMap = {
    1: 'warning',  // 未支付
    2: 'success',  // 已支付
    3: 'info'      // 部分支付
  }
  return statusMap[status] || 'default'
}

// 获取支付状态文本
function getPaymentStatusText(status) {
  const statusMap = {
    1: '未支付',
    2: '已支付',
    3: '部分支付'
  }
  return statusMap[status] || '未知'
}

// API调用函数
async function fetchOrderDetail(billId) {
  // 检查组件状态
  if (isDestroyed.value || !isMounted.value) {
    console.log('OrderDetailModal: fetchOrderDetail 组件未挂载或已销毁')
    return
  }

  try {
    const response = await request.post('/admin/RoomBill/getRoomBillDetail', { bill_id: billId })

    // 再次检查组件状态
    if (isDestroyed.value || !isMounted.value) {
      console.log('OrderDetailModal: API 响应后组件状态已变化')
      return
    }

    if (response && response.data) {
      // Use nextTick to ensure safe DOM updates and avoid vnode errors
      if (!isDestroyed.value && isMounted.value) {
        await nextTick(() => {
          if (!isDestroyed.value && isMounted.value) {
            apiOrderData.value = response.data
            currentRoomBillId.value = billId

            // 直接使用 getBillDetail 接口返回的 connect_bills 数据
            if (response.data.connect_bills && Array.isArray(response.data.connect_bills)) {
              connectRooms.value = response.data.connect_bills
              connectRoomsLoaded.value = true

              // 确保当前房间ID正确设置
              if (response.data.connect_bills.length > 0) {
                // 优先使用联房列表中匹配的房间ID
                const currentRoom = response.data.connect_bills.find(room =>
                  room.bill_id === billId || room.id === billId
                )
                if (currentRoom) {
                  currentRoomBillId.value = currentRoom.bill_id || currentRoom.id
                } else {
                  // 如果没有匹配的，使用第一个房间的ID
                  currentRoomBillId.value = response.data.connect_bills[0].bill_id || response.data.connect_bills[0].id
                }
              }

              // 如果只有一间房，自动收起侧边栏
              if (response.data.connect_bills.length === 1) {
                sidebarVisible.value = false
              }
            } else {
              connectRooms.value = []
              connectRoomsLoaded.value = true

              // 没有联房数据（单房间），设置当前房间ID并自动收起侧边栏
              currentRoomBillId.value = billId
              sidebarVisible.value = false
            }
          }
        })
      }
    } else {
      console.log('OrderDetailModal: API 响应数据格式错误', response)
      if (isMounted.value) {
        message.error('订单详情数据格式错误')
      }
    }
  } catch (error) {
    console.error('OrderDetailModal: 获取订单详情失败', error)
    if (isMounted.value) {
      message.error('获取订单详情失败')
    }
    throw error
  }
}

async function fetchBillDataTips(billId) {
  if (isDestroyed.value || !isMounted.value) {
    return
  }

  try {
    const response = await request.post('/admin/RoomBill/getBillDataTips', { bill_id: billId })
    if (response && response.data && !isDestroyed.value && isMounted.value) {
      await nextTick(() => {
        if (!isDestroyed.value && isMounted.value) {
          billDataTips.value = response.data
        }
      })
    }
  } catch (error) {

  }
}



async function fetchBillFund() {
  if (!currentRoomBillId.value || isDestroyed.value || !isMounted.value) {

    return
  }

  try {
    if (isMounted.value) {
      financeLoading.value = true
    }

    const params = {
      bill_id: currentRoomBillId.value,
      page: financePagination.value.page,
      limit: financePagination.value.pageSize
    }

    if (financeFilter.value !== 'all') {
      params.type = financeFilter.value
    }

    const response = await request.post('/admin/RoomBill/getRoomBillFund', params)

    if (response && response.data && !isDestroyed.value && isMounted.value) {
      await nextTick(() => {
        if (!isDestroyed.value && isMounted.value) {
          billFundData.value = {
            list: response.data.list || [],
            count: response.data.count || 0,
            balance_amount: response.data.balance_amount || 0,
            consume_amount: response.data.consume_amount || 0,
            pay_amount: response.data.pay_amount || 0,
            cash_pledge_amount: response.data.cash_pledge_amount || 0
          }

          // 更新分页信息
          financePagination.value.itemCount = response.data.count || 0

        }
      })
    }
  } catch (error) {

    if (isMounted.value) {
      message.error('获取财务明细失败')
    }
  } finally {
    if (isMounted.value) {
      financeLoading.value = false
    }
  }
}

// 获取门锁配置
async function fetchTtLockConfig(billId) {
  if (isDestroyed.value || !isMounted.value) {
    return
  }

  try {
    const response = await request.post('/admin/TtLock/getTtLockConfig', { bill_id: billId })
    if (response && response.data && !isDestroyed.value && isMounted.value) {
      await nextTick(() => {
        if (!isDestroyed.value && isMounted.value) {
          // 这里可以存储门锁配置数据，根据实际需要处理
          console.log('门锁配置数据:', response.data)
        }
      })
    }
  } catch (error) {
    console.error('获取门锁配置失败:', error)
  }
}

// 设置财务筛选
function setFinanceFilter(filter) {
  financeFilter.value = filter
  financePagination.value.page = 1
  fetchBillFund()
}

// 切换房间 - 使用局部加载状态
async function switchRoom(billId) {

  if (currentRoomBillId.value === billId || isDestroyed.value || !isMounted.value) {

    return
  }

  // 防止重复点击
  if (roomSwitchLoading.value === billId) {
    return
  }

  try {
    // 设置房间切换加载状态
    roomSwitchLoading.value = billId

    // 先设置加载状态，然后等待一个tick确保DOM更新
    if (isMounted.value) {
      contentLoading.value = true

      // 清空当前数据，避免新旧数据混合
      apiOrderData.value = {}
      await nextTick()
    }

    // 重新获取订单详情
    await fetchOrderDetail(billId)

    // 等待一个tick确保主数据已经更新到DOM
    await nextTick()

    // 重新获取财务数据
    if (!isDestroyed.value && isMounted.value) {
      await Promise.all([
        fetchBillDataTips(billId),
        fetchBillFund()
      ])
    }

  } catch (error) {

    if (isMounted.value) {
      message.error('切换房间失败')
    }
  } finally {
    // 清除加载状态
    roomSwitchLoading.value = null
    if (isMounted.value) {
      contentLoading.value = false
    }
  }
}

// 查看联房财务明细
function handleViewConnectFinance() {
  message.info('查看联房财务明细')
}

// 关闭弹窗
function handleClose() {
  emit('update:show', false)
}

// 切换侧边栏显示隐藏
function toggleSidebar() {
  sidebarVisible.value = !sidebarVisible.value
}

// 财务明细分页处理
function handleFinancePageChange(page) {
  financePagination.value.page = page
  fetchBillFund()
}

// 删除财务明细项
function handleDeleteFinanceItem(id) {
  message.info(`删除财务明细项 ${id}`)
  // TODO: 实现删除财务明细功能后，在成功回调中调用 handleOperationSuccess('删除财务明细')
}

// 操作按钮事件处理
function handlePrint() {
  message.info('打印订单')
  // 打印操作通常不会产生日志，无需调用 handleOperationSuccess
}
function handleGenerateKey() {
  message.info('生成钥匙二维码')
  // TODO: 实现生成钥匙二维码功能后，在成功回调中调用 handleOperationSuccess('生成钥匙二维码')
}
function handleGenerateKeyWithCode() {
  message.info('取单生成钥匙二维码')
  // TODO: 实现取单生成钥匙二维码功能后，在成功回调中调用 handleOperationSuccess('取单生成钥匙二维码')
}
function handleGenerateKeyWithCodeN31C() {
  message.info('取单生成钥匙二维码（双H-N31C）')
  // TODO: 实现双H-N31C钥匙二维码功能后，在成功回调中调用 handleOperationSuccess('双H-N31C钥匙二维码')
}
function handleChangeRoom() {
  message.info('更换房型')
  // TODO: 实现更换房型功能后，在成功回调中调用 handleOperationSuccess('更换房型')
}
function handleContinueStay() {
  message.info('续房')
  // TODO: 实现续房功能后，在成功回调中调用 handleOperationSuccess('续房')
}
function handleCheckout() {
  message.info('退房')
  // TODO: 实现退房功能后，在成功回调中调用 handleOperationSuccess('退房')
}
function handleConnectRoom() {
  message.info('联房')
  // TODO: 实现联房功能后，在成功回调中调用 handleOperationSuccess('联房')
}

// 规范化日志时间：兼容秒/毫秒
function formatLogTime(ts) {
  if (!ts) return '-'
  const num = Number(ts)
  const ms = num < 1e12 ? num * 1000 : num
  return formatDateTime(ms)
}

// 入住人相关处理函数
function handleViewGuest(user) {
  // 查看入住人详情
  window.$message?.info(`查看入住人: ${user.name}`)
}

function handleEditGuest(user) {
  // 编辑入住人信息
  editGuestModal.value.user = user
  // 填充表单数据，确保所有必填字段都有值
  editGuestModal.value.form = {
    name: user.name || '',
    gender: user.gender !== undefined && user.gender !== null ? user.gender : 0, // 确保性别有值，默认未知
    phone: user.phone || '',
    identification_type: user.identification_type !== undefined && user.identification_type !== null ? user.identification_type : 1, // 确保证件类型有值
    identification_number: user.identification_number || '',
    address: user.address || '0',
    id_authentication: user.authentication !== undefined && user.authentication !== null ? user.authentication : 1,
    is_upload: user.is_upload !== undefined && user.is_upload !== null ? user.is_upload : 1,
    birthday: user.birthday || '1',
    nation_id: user.nation_id || 1
  }

  // 确保表单字段都有有效值，避免验证失败
  console.log('编辑用户表单数据:', editGuestModal.value.form)

  editGuestModal.value.show = true
}

// 删除入住人
async function handleDeleteGuest(user) {
  try {
    // 确认删除
    const confirmed = await new Promise((resolve) => {
      dialog.warning({
        title: '确认删除',
        content: `确定要删除入住人 "${user.name}" 吗？此操作不可撤销。`,
        positiveText: '确定删除',
        negativeText: '取消',
        onPositiveClick: () => resolve(true),
        onNegativeClick: () => resolve(false),
        onClose: () => resolve(false)
      })
    })

    if (!confirmed) {
      return
    }

    // 调用删除接口
    const response = await request.post('/admin/RoomBill/delRoomBillUser', {
      room_user_id: user.id
    })

    if (response.code === 0) {
      message.success('删除入住人成功')
      // 刷新入住人数据
      await fetchRoomBillUser()
      // 刷新日志数量
      await refreshLogsCount()
    } else if (response.code === 886) {
      message.error('登录已过期，请重新登录')
    } else {
      message.error(response.message || '删除失败')
    }
  } catch (error) {
    console.error('删除入住人失败:', error)
    message.error('删除失败，请重试')
  }
}

// 打开添加入住人弹窗
function handleAddGuest() {
  // 重置表单
  addGuestModal.value.form = {
    name: '',
    gender: 0, // 默认未知
    phone: '',
    identification_type: 1,
    identification_number: '',
    address: '0',
    id_authentication: 1,
    is_upload: 1,
    birthday: '1',
    nation_id: 1
  }
  addGuestModal.value.show = true
}

// 取消添加入住人
function handleCancelAddGuest() {
  addGuestModal.value.show = false
}

// 取消编辑入住人
function handleCancelEditGuest() {
  editGuestModal.value.show = false
  editGuestModal.value.user = null
}

// 身份证验证
function handleIdVerification() {
  message.info('身份证验证功能')
}

// 姓名模糊搜索
async function handleNameSearch(value) {
  // 统一清洗：如果带有“ - ”，只保留姓名部分
  let keyword = value || ''
  const sep = ' - '
  const idx = keyword.indexOf(sep)
  if (idx !== -1) {
    keyword = keyword.slice(0, idx)
    // 同步回填输入框，确保只显示姓名
    if (editGuestModal.value?.show) editGuestModal.value.form.name = keyword
    if (addGuestModal.value?.show) addGuestModal.value.form.name = keyword
  }

  if (!keyword || keyword.length < 1) {
    nameSearchOptions.value = []
    return
  }

  try {
    nameSearchLoading.value = true
    const response = await request.post('/admin/RoomBill/getRoomUserInfo', {
      page: 1,
      limit: 10,
      search_word: keyword
    })

    if (response.code === 0 && response.data && response.data.list) {
      // 将搜索结果转换为选项格式
      // 下拉展示：姓名 等级 手机号；选择后仅回填姓名
      nameSearchOptions.value = response.data.list.map(user => {
        const grade = user.level_name || user.grade_name || user.member_level_name || user.grade || ''
        const phone = user.phone || ''
        const display = [user.name, grade, phone].filter(Boolean).join(' ')
        const option = {
          label: display,
          value: user.name, // 选择后只回填姓名
          user: user // 保存完整的用户信息供自动填充（用于添加时同步其他字段）
        }
        console.log('构造选项:', option)
        return option
      })
      console.log('最终选项列表:', nameSearchOptions.value)
    } else {
      nameSearchOptions.value = []
    }
  } catch (error) {
    console.error('搜索用户失败:', error)
    nameSearchOptions.value = []
  } finally {
    nameSearchLoading.value = false
  }
}

// 处理姓名选择 - 自动填充用户信息
function handleNameSelect(value, option) {
  console.log('handleNameSelect 被调用:', { value, option })

  // 统一取纯姓名，防止把附加信息写入输入框
  const pickedName = getPureName(option?.user?.name ?? option?.value ?? value)
  editGuestModal.value.form.name = pickedName

  if (option && option.user) {
    const user = option.user
    console.log('选择的用户信息:', user)

    // 根据接口返回的数据结构自动填充字段（与添加入住人保持一致）
    // 性别：gender (0=未知, 1=男, 2=女)
    if (user.gender !== undefined && user.gender !== null) {
      editGuestModal.value.form.gender = user.gender
    }

    // 手机号：phone
    if (user.phone && user.phone.trim()) {
      editGuestModal.value.form.phone = user.phone.trim()
    }

    // 证件类型：identification_type
    if (user.identification_type !== undefined && user.identification_type !== null) {
      editGuestModal.value.form.identification_type = user.identification_type
    }

    // 证件号码：identification_number
    if (user.identification_number && user.identification_number.trim() && user.identification_number !== 'xxxx') {
      editGuestModal.value.form.identification_number = user.identification_number.trim()
    }

    // 户籍地址：address
    if (user.address && user.address !== '0') {
      editGuestModal.value.form.address = user.address
    }

    // 人脸认证状态：authentication
    if (user.authentication !== undefined && user.authentication !== null) {
      editGuestModal.value.form.id_authentication = user.authentication
    }

    // 上传公安状态：is_upload
    if (user.is_upload !== undefined && user.is_upload !== null) {
      editGuestModal.value.form.is_upload = user.is_upload
    }

    // 生日：birthday
    if (user.birthday && user.birthday !== '1') {
      editGuestModal.value.form.birthday = user.birthday
    }

    // 民族ID：nation_id
    if (user.nation_id !== undefined && user.nation_id !== null) {
      editGuestModal.value.form.nation_id = user.nation_id
    }

    console.log('编辑入住人自动填充后的表单数据:', editGuestModal.value.form)
    message.success('已自动填充用户信息')
  } else {
    console.log('没有找到用户信息或选项无效')
  }
}

// 处理添加入住人姓名选择 - 自动填充用户信息
function handleAddGuestNameSelect(value, option) {
  console.log('handleAddGuestNameSelect 被调用:', { value, option })
  console.log('当前表单状态:', addGuestModal.value.form)
  console.log('当前搜索选项列表:', nameSearchOptions.value)

  // 统一取纯姓名，防止把附加信息写入输入框
  const pickedName = getPureName(value)
  console.log('设置姓名:', pickedName)
  addGuestModal.value.form.name = pickedName

  // 如果 option 为 undefined，尝试从搜索选项中找到对应的用户数据
  let user = null
  if (option && option.user) {
    user = option.user
    console.log('从 option 获取用户信息:', user)
  } else {
    // 兜底：从搜索选项中查找匹配的用户
    console.log('option 无效，尝试从搜索选项中查找用户数据')
    const matchedOption = nameSearchOptions.value.find(opt => opt.value === pickedName || opt.label.includes(pickedName))
    if (matchedOption && matchedOption.user) {
      user = matchedOption.user
      console.log('从搜索选项中找到用户信息:', user)
    } else {
      console.log('未找到匹配的用户数据')
    }
  }

  if (user) {
    console.log('选择的用户信息:', user)
    console.log('用户手机号详情:', { phone: user.phone, phoneType: typeof user.phone, phoneValue: JSON.stringify(user.phone) })

    // 根据接口返回的数据结构自动填充字段
    // 性别：gender (0=未知, 1=男, 2=女)
    if (user.gender !== undefined && user.gender !== null) {
      addGuestModal.value.form.gender = user.gender
    }

    // 手机号：phone
    console.log('检查手机号:', { phone: user.phone, type: typeof user.phone })
    if (user.phone && String(user.phone).trim()) {
      const phoneValue = String(user.phone).trim()
      console.log('设置手机号:', phoneValue)
      addGuestModal.value.form.phone = phoneValue
    } else {
      console.log('手机号为空或无效:', user.phone)
    }

    // 证件类型：identification_type
    console.log('检查证件类型:', { identification_type: user.identification_type, type: typeof user.identification_type })
    if (user.identification_type !== undefined && user.identification_type !== null) {
      console.log('设置证件类型:', user.identification_type)
      addGuestModal.value.form.identification_type = user.identification_type
    } else {
      console.log('证件类型为空或无效:', user.identification_type)
    }

    // 证件号码：identification_number
    console.log('检查证件号码:', { identification_number: user.identification_number, type: typeof user.identification_number })
    if (user.identification_number && String(user.identification_number).trim() && user.identification_number !== 'xxxx') {
      const idNumber = String(user.identification_number).trim()
      console.log('设置证件号码:', idNumber)
      addGuestModal.value.form.identification_number = idNumber
    } else {
      console.log('证件号码为空或无效:', user.identification_number)
    }

    // 户籍地址：address
    if (user.address && user.address !== '0') {
      addGuestModal.value.form.address = user.address
    }

    // 人脸认证状态：authentication
    if (user.authentication !== undefined && user.authentication !== null) {
      addGuestModal.value.form.id_authentication = user.authentication
    }

    // 上传公安状态：is_upload
    if (user.is_upload !== undefined && user.is_upload !== null) {
      addGuestModal.value.form.is_upload = user.is_upload
    }

    // 生日：birthday
    if (user.birthday && user.birthday !== '1') {
      addGuestModal.value.form.birthday = user.birthday
    }

    // 民族ID：nation_id
    if (user.nation_id !== undefined && user.nation_id !== null) {
      addGuestModal.value.form.nation_id = user.nation_id
    }

    console.log('添加入住人自动填充后的表单数据:', addGuestModal.value.form)
    message.success('已自动填充用户信息')
  } else {
    console.log('没有找到用户信息或选项无效')
  }
}

// 提交添加入住人
async function handleSubmitAddGuest() {
  try {
    // 表单验证
    await addGuestFormRef.value?.validate()

    addGuestModal.value.loading = true

    // 使用通用函数提交
    const response = await submitUserInfo(addGuestModal.value.form, false)

    if (response.code === 0) {
      message.success('添加入住人成功')
      addGuestModal.value.show = false
      // 刷新入住人数据
      await fetchRoomBillUser()
      // 刷新日志数量
      await refreshLogsCount()
    } else if (response.code === 886) {
      message.error('登录已过期，请重新登录')
    } else {
      message.error(response.message || '添加失败')
    }
  } catch (error) {
    if (error?.errors) {
      // 表单验证错误
      return
    }
    console.error('添加入住人失败:', error)
    message.error('添加失败，请重试')
  } finally {
    addGuestModal.value.loading = false
  }
}

// 通用的用户信息提交函数
async function submitUserInfo(formData, isEdit = false, userId = null) {
  const params = new URLSearchParams()

  // 如果是编辑，需要传入用户ID
  if (isEdit && userId) {
    params.append('id', userId)
  }

  // 基础参数
  params.append('name', formData.name)
  params.append('gender', formData.gender)
  params.append('phone', formData.phone || '')
  params.append('identification_type', formData.identification_type)
  params.append('identification_number', formData.identification_number || '')
  params.append('address', formData.address || '0')
  params.append('id_authentication', formData.id_authentication)
  params.append('is_upload', formData.is_upload)
  params.append('bill_id', props.billId || finalOrderData.value?.bill_id || finalOrderData.value?.id)
  params.append('birthday', formData.birthday || '1')

  // 如果是编辑，需要添加额外的参数
  if (isEdit && editGuestModal.value.user) {
    const user = editGuestModal.value.user
    params.append('is_main', user.is_main || 0)
    params.append('nation_id', formData.nation_id || 1)
    params.append('create_time', user.create_time || '')
    params.append('authentication', formData.id_authentication)
    params.append('common_code', user.common_code || '')
    params.append('send_password', user.send_password || 0)
    params.append('shop_id_zong', user.shop_id_zong || 36)
    params.append('user_status', user.user_status || 0)
    params.append('accnt', user.accnt || '')
    params.append('nation', user.nation || '汉族')
  }

  // 调用接口
  return await request.post('/admin/RoomBill/updateRoomBillUser', params, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 提交编辑入住人
async function handleSubmitEditGuest() {
  try {
    // 表单验证
    await editGuestFormRef.value?.validate()

    editGuestModal.value.loading = true

    // 使用通用函数提交
    const response = await submitUserInfo(
      editGuestModal.value.form,
      true,
      editGuestModal.value.user.id
    )

    if (response.code === 0) {
      message.success('编辑入住人成功')
      editGuestModal.value.show = false
      // 刷新入住人数据
      await fetchRoomBillUser()
      // 刷新日志数量
      await refreshLogsCount()
    } else if (response.code === 886) {
      message.error('登录已过期，请重新登录')
    } else {
      message.error(response.message || '编辑失败')
    }
  } catch (error) {
    if (error?.errors) {
      // 表单验证错误
      return
    }
    console.error('编辑入住人失败:', error)
    message.error('编辑失败，请重试')
  } finally {
    editGuestModal.value.loading = false
  }
}



// 查看会员信息
function handleViewMemberInfo(user) {
  if (!user.common_code) {
    message.warning('该用户没有会员信息')
    return
  }

  memberInfoModal.value.commonCode = user.common_code
  memberInfoModal.value.show = true
}

// 编辑入住状态
function handleEditGuestStatus(user) {
  editStatusModal.value.user = user
  editStatusModal.value.newStatus = user.user_status
  editStatusModal.value.show = true
}

// 更新入住状态
async function updateUserStatus() {
  if (!editStatusModal.value.user) return

  editStatusModal.value.loading = true

  try {
    // 构建请求参数，包含用户的所有信息
    const requestData = {
      accnt: editStatusModal.value.user.accnt || null,
      address: editStatusModal.value.user.address || "0",
      authentication: editStatusModal.value.user.authentication || 0,
      bill_id: editStatusModal.value.user.bill_id,
      birthday: editStatusModal.value.user.birthday || "",
      common_code: editStatusModal.value.user.common_code || "",
      create_time: editStatusModal.value.user.create_time,
      gender: editStatusModal.value.user.gender || 0,
      id: editStatusModal.value.user.id,
      identification_number: editStatusModal.value.user.identification_number || "",
      identification_type: editStatusModal.value.user.identification_type || 1,
      is_main: editStatusModal.value.user.is_main || 0,
      is_upload: editStatusModal.value.user.is_upload || 0,
      name: editStatusModal.value.user.name || "",
      nation: editStatusModal.value.user.nation || "",
      nation_id: editStatusModal.value.user.nation_id || 1,
      phone: editStatusModal.value.user.phone || "",
      send_password: editStatusModal.value.user.send_password || 0,
      shop_id_zong: editStatusModal.value.user.shop_id_zong,
      user_status: editStatusModal.value.newStatus
    }

    const response = await request.post('/admin/RoomBill/updateRoomBillUser', requestData)

    if (response.code === 0) {
      // 更新本地数据
      editStatusModal.value.user.user_status = editStatusModal.value.newStatus

      // 更新原始数据中的用户信息
      const userIndex = finalOrderData.value.users?.findIndex(u => u.id === editStatusModal.value.user.id)
      if (userIndex !== -1) {
        finalOrderData.value.users[userIndex].user_status = editStatusModal.value.newStatus
      }

      const statusText = userStatusOptions.find(opt => opt.value === editStatusModal.value.newStatus)?.label || '未知'
      message.success(`入住状态已更新为：${statusText}`)
      editStatusModal.value.show = false
      // 刷新日志数量
      await refreshLogsCount()
    } else if (response.code === 886) {
      message.error('登录已过期，请重新登录')
      // 这里可以添加跳转到登录页的逻辑
    } else {
      message.error(response.message || '更新失败')
    }
  } catch (error) {
    console.error('更新入住状态失败:', error)
    message.error('更新失败，请重试')
  } finally {
    editStatusModal.value.loading = false
  }
}

// 人脸认证相关函数
function handleFaceVerification(user) {
  faceVerificationModal.value.user = user
  faceVerificationModal.value.show = true
}

function previewImage(imageUrl) {
  // 图片预览功能
  window.open(imageUrl, '_blank')
}

function downloadImage(imageUrl, filename) {
  // 图片下载功能
  const link = document.createElement('a')
  link.href = imageUrl
  link.download = `${filename}_${Date.now()}.jpg`
  link.target = '_blank'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  message.success('图片下载已开始')
}

async function updateVerificationStatus(isVerified) {
  if (!faceVerificationModal.value.user) return

  faceVerificationModal.value.loading = true

  try {
    const user = faceVerificationModal.value.user

    // 构建请求参数，包含用户的所有信息
    const requestData = {
      id: user.id,
      bill_id: user.bill_id,
      name: user.name || '',
      gender: user.gender || 1,
      is_main: user.is_main || 0,
      identification_type: user.identification_type || 1,
      identification_number: user.identification_number || '',
      phone: user.phone || '',
      nation_id: user.nation_id || 1,
      create_time: user.create_time,
      authentication: isVerified ? 1 : 0,
      common_code: user.common_code || '',
      birthday: user.birthday || '',
      address: user.address || '0',
      is_upload: user.is_upload || 1,
      send_password: user.send_password || 0,
      shop_id_zong: user.shop_id_zong || 36,
      user_status: user.user_status || 1,
      accnt: user.accnt || '',
      nation: user.nation || '汉族',
      bill_room_room_id: user.bill_room_room_id || user.id
    }

    // 调用API更新认证状态
    const response = await request.post('/admin/RoomBill/updateRoomBillUser', requestData)

    if (response.code === 0) {
      // 更新本地数据
      faceVerificationModal.value.user.authentication = isVerified ? 1 : 0

      // 更新原始数据中的用户信息
      const userIndex = finalOrderData.value.users?.findIndex(u => u.id === faceVerificationModal.value.user.id)
      if (userIndex !== -1) {
        finalOrderData.value.users[userIndex].authentication = isVerified ? 1 : 0
      }

      message.success(isVerified ? '认证通过设置成功' : '认证不通过设置成功')
      faceVerificationModal.value.show = false
      // 刷新日志数量
      await refreshLogsCount()
    } else if (response.code === 886) {
      message.error('登录已过期，请重新登录')
      // 这里可以添加跳转到登录页的逻辑
    } else {
      message.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('更新认证状态失败:', error)
    message.error('操作失败，请重试')
  } finally {
    faceVerificationModal.value.loading = false
  }
}



// 通用的操作成功后处理函数
async function handleOperationSuccess(operationName) {
  // 刷新日志数量
  await refreshLogsCount()
  // 可以在这里添加其他需要在操作成功后执行的逻辑
  console.log(`${operationName} 操作完成，已刷新日志数量`)
}

// 财务操作
function handleCollect() {
  message.info('收款')
  // TODO: 实现收款功能后，在成功回调中调用 handleOperationSuccess('收款')
}
function handleHangAccount() {
  message.info('挂账')
  // TODO: 实现挂账功能后，在成功回调中调用 handleOperationSuccess('挂账')
}
function handleTransfer() {
  message.info('过账')
  // TODO: 实现过账功能后，在成功回调中调用 handleOperationSuccess('过账')
}
function handleOriginalPrice() {
  message.info('原价追款')
  // TODO: 实现原价追款功能后，在成功回调中调用 handleOperationSuccess('原价追款')
}
function handleCustomPrice() {
  message.info('自定义追款')
  // TODO: 实现自定义追款功能后，在成功回调中调用 handleOperationSuccess('自定义追款')
}
function handleWriteOff() {
  message.info('冲销')
  // TODO: 实现冲销功能后，在成功回调中调用 handleOperationSuccess('冲销')
}
function handleFreeOrder() {
  message.info('免单')
  // TODO: 实现免单功能后，在成功回调中调用 handleOperationSuccess('免单')
}
function handleDiscount() {
  message.info('折扣')
  // TODO: 实现折扣功能后，在成功回调中调用 handleOperationSuccess('折扣')
}
function handleWalkAccount() {
  message.info('走账')
  // TODO: 实现走账功能后，在成功回调中调用 handleOperationSuccess('走账')
}
function handleSettlement() {
  message.info('结账')
  // TODO: 实现结账功能后，在成功回调中调用 handleOperationSuccess('结账')
}
function handleTransferToDelinquent() {
  message.info('转为拖结态')
  // TODO: 实现转为拖结态功能后，在成功回调中调用 handleOperationSuccess('转为拖结态')
}
// 获取订单日志（列表）
async function fetchRoomLogs(billId, type = '') {
  if (!billId || isDestroyed.value || !isMounted.value) return
  try {
    logsLoading.value = true
    const params = {
      bill_id: billId,
      page: logsPagination.value.page,
      limit: logsPagination.value.pageSize
    }
    if (type !== '') params.type = type
    const res = await request.post('/admin/RoomBill/getRoomLog', params)
    const data = res?.data || {}
    const list = Array.isArray(data.list) ? data.list : []
    logs.value = list
    logsPagination.value.itemCount = Number(data.count) || list.length || 0

    // 更新billDataTips中的日志数量，确保tab上的数量显示正确
    if (billDataTips.value && data.count !== undefined) {
      billDataTips.value.log_total = Number(data.count)
    }
  } catch (err) {

    logs.value = []
    logsPagination.value.itemCount = 0
    // 错误时也要更新billDataTips中的日志数量
    if (billDataTips.value) {
      billDataTips.value.log_total = 0
    }
  } finally {
    logsLoading.value = false
  }
}

// Tab相关处理函数


function refreshLogs() {
  // 切换类型重置到第一页
  logsPagination.value.page = 1
  const bid = currentRoomBillId.value || props.billId || finalOrderData.value?.bill_id || finalOrderData.value?.id
  fetchRoomLogs(bid, logFilter.value)
}

// 刷新日志数量 - 用于编辑操作后更新tab上的数量显示
async function refreshLogsCount() {
  const bid = currentRoomBillId.value || props.billId || finalOrderData.value?.bill_id || finalOrderData.value?.id
  if (!bid || isDestroyed.value || !isMounted.value) return

  try {
    // 只获取第一页数据来获取总数量，不影响当前显示的日志列表
    const params = {
      bill_id: bid,
      page: 1,
      limit: 1 // 只需要获取count，所以limit设为1即可
    }
    const res = await request.post('/admin/RoomBill/getRoomLog', params)
    const data = res?.data || {}

    // 更新billDataTips中的日志数量，确保tab上的数量显示正确
    if (billDataTips.value && data.count !== undefined) {
      billDataTips.value.log_total = Number(data.count)
    }
  } catch (err) {
    // 静默处理错误，不影响用户操作
    console.warn('刷新日志数量失败:', err)
  }
}

// 获取入住人信息
async function fetchRoomBillUser(billId) {
  if (isDestroyed.value || !isMounted.value) {
    return
  }

  if (!billId) {
    billId = currentRoomBillId.value || props.billId || finalOrderData.value?.bill_id || finalOrderData.value?.id
  }

  if (!billId) {
    return
  }

  try {
    const response = await request.post('/admin/RoomBill/getRoomBillUser', { bill_id: billId })

    // 再次检查组件状态
    if (isDestroyed.value || !isMounted.value) {
      return
    }

    if (response && response.data) {
      // 使用nextTick确保安全的DOM更新
      await nextTick(() => {
        if (!isDestroyed.value && isMounted.value) {
          // 更新入住人信息
          if (finalOrderData.value && Array.isArray(response.data)) {
            finalOrderData.value.users = response.data
          } else if (finalOrderData.value && response.data.users && Array.isArray(response.data.users)) {
            finalOrderData.value.users = response.data.users
          }
        }
      })
    }
  } catch (error) {
    console.error('获取入住人信息失败:', error)
    if (isMounted.value) {
      message.error('获取入住人信息失败')
    }
  }
}

// 刷新订单详情数据
async function refreshOrderDetails() {
  if (!isMounted.value || isDestroyed.value) {
    return
  }

  let billId = props.billId
  if (!billId && props.orderData) {
    billId = props.orderData.bill_id || props.orderData.id
  }

  if (!billId) {
    return
  }

  try {
    // 刷新主要订单详情
    await fetchOrderDetail(billId)

    // 刷新辅助数据
    if (isMounted.value && !isDestroyed.value) {
      await Promise.all([
        fetchBillDataTips(currentRoomBillId.value),
        fetchBillFund(),
        fetchTtLockConfig(currentRoomBillId.value)
      ])
    }
  } catch (error) {
    console.error('刷新订单详情失败:', error)
  }
}
function getLogTypeLabel(type) {
  const t = String(type)
  const map = { 'order': '订房', 'finance': '财务', 'room': '房态', 'system': '系统', '1': '订房', '2': '财务', '3': '其他', '': '全部' }
  return map[t] || '其他'
}

function getLogType(type) {
  const t = String(type)
  const typeMap = {
    'order': 'success', '1': 'success',
    'finance': 'warning', '2': 'warning',
    'room': 'info', '3': 'info',
    'system': 'default'
  }
  return typeMap[t] || 'default'
}

function getLogIcon(type) {
  const t = String(type)
  const iconMap = {
    'order': 'i-mdi:file-document', '1': 'i-mdi:file-document',
    'finance': 'i-mdi:cash', '2': 'i-mdi:cash',
    'room': 'i-mdi:home', '3': 'i-mdi:home',
    'system': 'i-mdi:cog'
  }
  return iconMap[t] || 'i-mdi:information'
}

function handleAddSupermarketItem() {
  message.info('添加超市商品')
  // TODO: 实现添加超市商品功能后，在成功回调中调用 handleOperationSuccess('添加超市商品')
}
function handleAddRentalItem() {
  message.info('添加租借物品')
  // TODO: 实现添加租借物品功能后，在成功回调中调用 handleOperationSuccess('添加租借物品')
}
function handleAddCompensationItem() {
  message.info('添加赔付项目')
  // TODO: 实现添加赔付项目功能后，在成功回调中调用 handleOperationSuccess('添加赔付项目')
}

// State cleanup function
function cleanupState() {
  apiOrderData.value = {}
  billDataTips.value = {}
  billFundData.value = { list: [], count: 0, balance_amount: 0, consume_amount: 0, pay_amount: 0, cash_pledge_amount: 0 }
  connectRooms.value = []
  connectRoomsLoaded.value = false
  currentRoomBillId.value = null

  // Reset loading and filter states
  loading.value = false
  contentLoading.value = false
  roomSwitchLoading.value = null
  financeFilter.value = 'all'
  financePagination.value.page = 1

  // Reset UI state
  sidebarVisible.value = true
  hardwareExpanded.value = false
  locksExpanded.value = false
  showWifiPassword.value = false

  // Reset tab state - 重置到默认详情tab
  activeTab.value = 'details'

  // Reset logs state
  logs.value = []
  logsLoading.value = false
  logFilter.value = ''
  logsPagination.value.page = 1
  expandedLogs.value.clear() // 清空展开的日志详情

}

// Centralized function for initial data loading
async function loadInitialData() {
  if (!isMounted.value || isDestroyed.value) {
    console.log('OrderDetailModal: 组件未挂载或已销毁，跳过数据加载')
    return
  }

  // 确保每次打开时都重置到详情tab
  activeTab.value = 'details'

  let billId = props.billId
  if (!billId && props.orderData) {
    billId = props.orderData.bill_id || props.orderData.id
  }

  if (!billId) {
    console.log('OrderDetailModal: 没有找到有效的 billId，跳过数据加载')
    return
  }

  loading.value = true

  try {
    // Fetch the main detail first, as other calls might depend on it
    await fetchOrderDetail(billId)

    // After the main detail is loaded, fetch auxiliary data in parallel
    if (isMounted.value && !isDestroyed.value) {
      await Promise.all([
        fetchBillDataTips(currentRoomBillId.value),
        fetchBillFund(),
        fetchTtLockConfig(currentRoomBillId.value)
      ])
    }

  } catch (error) {
    console.error('OrderDetailModal: 加载订单数据失败', error)
    if (isMounted.value) {
      message.error('加载订单数据失败，请重试')
    }
  } finally {
    if (isMounted.value) {
      loading.value = false
    }
  }
}

// Watch for the modal visibility
watch(() => props.show, (newVal) => {
  visible.value = newVal
  if (newVal) {
    // When modal opens, load data
    loadInitialData()

    // 获取证件类型列表
    if (identificationTypes.value.length === 0) {
      fetchIdentificationTypes()
    }
  } else {
    // When modal closes, clean up state to prevent stale data issues
    cleanupState()
  }
}, { immediate: true })
// 当切换到“日志”主Tab时，自动加载一次
watch(activeTab, (val) => {
  if (val === 'logs') {
    refreshLogs()
  } else if (val === 'details') {
    // 刷新详情数据
    refreshOrderDetails()
  } else if (val === 'guests') {
    // 刷新入住人数据 - 专门调用getRoomBillUser接口
    fetchRoomBillUser()
  }
})

// 当日志类型切换时，确保触发加载（双保险）
watch(logFilter, () => {
  if (activeTab.value === 'logs') {
    refreshLogs()
  }
})

// Component mounted
onMounted(() => {
  isMounted.value = true
  isDestroyed.value = false

  // If the modal is already set to show on mount, start loading data
  if (props.show) {
    loadInitialData()
  }
})

// Component unmounted
onUnmounted(() => {
  isDestroyed.value = true
  isMounted.value = false
  cleanupState()
  dataCache.clear() // Also clear long-term cache on unmount

})
</script>

<style scoped>
/* 弹窗样式 - 铺满整个页面 */
.global-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.order-detail-modal {
  width: 100vw !important;
  height: 100vh !important;
  max-width: none !important;
  max-height: none !important;
  top: 0 !important;
  left: 0 !important;
  transform: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

.order-detail-wrapper {
  background: #f8fafc;
  border-radius: 0;
  overflow: hidden;
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
}

/* 模态框内容 */
.modal-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0; /* 确保flex子元素可以收缩 */
}

/* 主布局 */
.main-layout {
  display: flex;
  height: 100%;
  position: relative; /* 让浮动展开按钮进行绝对定位 */
  transition: all 0.3s ease;
}

.main-layout.no-sidebar .connect-sidebar {
  width: 80px; /* 即使没有联房数据也显示侧边栏，只是缩小宽度 */
}

.main-layout.sidebar-collapsed .connect-sidebar {
  width: 80px;
}

/* 没有联房时的布局 */
.main-layout.no-connect-rooms .main-panel {
  margin-left: 0;
  width: 100%;
}

/* 联房侧边栏 */
.connect-sidebar {
  width: 280px;
  background: #fff;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  height: 100%;
  max-height: 100vh;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.connect-sidebar.collapsed {
  width: 60px;
}

.sidebar-header {
  padding: 1rem;
  border-bottom: 1px solid #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8fafc;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #1f2937;
}

.toggle-button {
  flex-shrink: 0;
}

/* 联房财务明细跳转 */
.connect-finance-link {
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #f3f4f6;
}

.connect-finance-button {
  width: 100%;
}

/* 房间列表容器 */
.room-list-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 确保flex子元素可以收缩 */
}

/* 房间列表 */
.room-list {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0.375rem;
  min-height: 0; /* 确保可以收缩 */
  /* 优化滚动性能 */
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* 房间列表滚动条样式 - 更美观的设计 */
.room-list::-webkit-scrollbar {
  width: 6px;
}

.room-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
  margin: 4px 0;
}

.room-list::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.room-list::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.4);
}

.room-list::-webkit-scrollbar-thumb:active {
  background: rgba(0, 0, 0, 0.6);
}

/* Firefox滚动条样式 */
.room-list {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) rgba(0, 0, 0, 0.05);
}

.room-item {
  position: relative;
  margin-bottom: 0.375rem;
  background: #f8fafc;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  overflow: hidden;
}

.room-content {
  padding: 0.5rem;
}

.room-item.loading {
  pointer-events: none;
}

.room-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 5;
}

.room-item:last-child {
  margin-bottom: 0;
}

.room-item:hover {
  background: #e5e7eb;
}

.room-item.active {
  background: #dbeafe;
  border-color: #3b82f6;
}

.room-number {
  font-weight: 600;
  font-size: 1rem;
  color: #1f2937;
  margin-bottom: 0.125rem;
}

/* 浮动展开按钮（兜底） - 增强可见性 */
.sidebar-fab {
  position: fixed;
  top: 50%;
  left: 8px;
  transform: translateY(-50%);
  width: 48px;
  height: 80px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: none;
  border-radius: 0 12px 12px 0;
  box-shadow:
    0 4px 16px rgba(59, 130, 246, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  cursor: pointer;
  z-index: 1000;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: fabPulse 2s ease-in-out infinite;
}

.sidebar-fab:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-50%) translateX(4px);
  box-shadow:
    0 6px 20px rgba(59, 130, 246, 0.4),
    0 4px 12px rgba(0, 0, 0, 0.15);
}

.sidebar-fab i {
  font-size: 20px;
  color: white;
}

.sidebar-fab .fab-text {
  font-size: 10px;
  color: white;
  font-weight: 500;
  writing-mode: vertical-rl;
  text-orientation: mixed;
}

@keyframes fabPulse {
  0%, 100% {
    box-shadow:
      0 4px 16px rgba(59, 130, 246, 0.3),
      0 2px 8px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow:
      0 6px 20px rgba(59, 130, 246, 0.5),
      0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

@media (max-width: 768px) {
  .sidebar-fab { left: 6px; top: 10px; width: 34px; height: 34px; }
}

.room-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.125rem;
}

.room-type {
  font-size: 0.75rem;
  color: #6b7280;
}

.room-status {
  display: flex;
  align-items: center;
}

.room-price {
  font-weight: 600;
  font-size: 0.875rem;
  color: #059669;
}

/* 房间列表容器 - 添加过渡动画 */
.room-list-container {
  transition: opacity 0.2s ease, visibility 0.2s ease;
}

.room-list-container[style*="display: none"] {
  opacity: 0;
  visibility: hidden;
}

/* 折叠状态指示器 - 添加过渡动画 */
.collapsed-indicators {
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  transition: opacity 0.2s ease, visibility 0.2s ease;
}

.collapsed-indicators[style*="display: none"] {
  opacity: 0;
  visibility: hidden;
}

.room-indicator {
  width: 60px;
  height: 36px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
}

.room-indicator:hover {
  background: #e5e7eb;
}

.room-indicator.active {
  background: #3b82f6;
  color: white;
}

/* 主面板 */
.main-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0; /* 确保flex子元素可以收缩 */
  max-height: 100%; /* 确保不超出父容器 */
  height: 100%; /* 明确设置高度 */
}

/* 顶部信息栏 */
.top-info-bar {
  background: #fff;
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Tab导航栏样式 */
.tab-navigation {
  background: white;
  border-bottom: 1px solid rgba(var(--primary-color-rgb), 0.12);
  flex-shrink: 0;
}

.order-detail-tabs {
  padding: 0 1rem;
}

.order-detail-tabs :deep(.n-tabs-nav) {
  background: transparent;
  border-bottom: none;
}

.order-detail-tabs :deep(.n-tabs-tab) {
  padding: 0.75rem 1rem;
  font-weight: 500;
  color: #6b7280;
  transition: all 0.2s ease;
}

.order-detail-tabs :deep(.n-tabs-tab:hover) {
  color: var(--primary-color);
}

.order-detail-tabs :deep(.n-tabs-tab--active) {
  color: var(--primary-color);
  font-weight: 600;
}

.tab-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.tab-item i {
  font-size: 1rem;
}

/* Tab计数样式 */
.tab-count {
  margin-left: 6px;
  line-height: 1;
}

/* Tab内容区域样式 */
.tab-content-wrapper {
  flex: 1;
  overflow: hidden; /* 确保内容不溢出 */
  padding: 1.5rem;
  min-height: 0; /* 确保可以收缩 */
  display: flex;
  flex-direction: column;
}

/* 入住人Tab样式 */
.guests-content {
  max-width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.guests-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
  flex-shrink: 0;
}

.guests-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
}

.guests-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.guest-card {
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  transition: all 0.2s ease;
}

.guest-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.guest-info {
  flex: 1;
}

.guest-main {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.75rem;
}

.guest-name {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
}

.guest-badges {
  display: flex;
  gap: 0.5rem;
}

.guest-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.5rem;
}

.detail-item {
  display: flex;
  gap: 0.5rem;
}

.detail-item .label {
  color: #6b7280;
  font-size: 0.875rem;
  min-width: 60px;
}

.detail-item .value {
  color: #1f2937;
  font-size: 0.875rem;
  font-weight: 500;
}

.guest-actions {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

.empty-guests {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

/* 日志Tab样式 */

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
  flex-shrink: 0;
}

.logs-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
}

.log-filters {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

/* 日志样式重构 - 简洁版本 */
.logs-content {
  padding: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 16px 0;
  border-bottom: 1px solid #e5e7eb;
  flex-shrink: 0;
}

.logs-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logs-title h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
}

.logs-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.logs-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.logs-scroll-area {
  flex: 1;
  overflow-y: auto;
  padding-right: 4px; /* 为滚动条留出空间 */
}

.logs-footer-fixed {
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-top: 1px solid #e5e7eb;
  margin-top: 16px;
  background: white;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

.log-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding-bottom: 8px; /* 为底部留出一些空间 */
}

.log-item {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s ease;
}

.log-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.log-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.log-meta {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex: 1;
}

.log-icon-wrapper {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  background: #f3f4f6;
}

.log-icon {
  font-size: 16px;
  color: #6b7280;
}

.log-info {
  flex: 1;
  min-width: 0;
}

.log-title-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  flex-wrap: wrap;
}

.log-title {
  font-weight: 600;
  color: #1f2937;
  font-size: 14px;
}

.log-time {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #6b7280;
  font-size: 12px;
}

.log-time i {
  font-size: 12px;
}

.log-actions {
  flex-shrink: 0;
}

.log-content {
  color: #374151;
  line-height: 1.5;
  font-size: 14px;
  white-space: pre-wrap;
  word-break: break-word;
  margin-bottom: 8px;
}

.log-details {
  background: #f9fafb;
  border-radius: 6px;
  padding: 12px;
  margin-top: 12px;
  border-top: 1px solid #e5e7eb;
}

.detail-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
  font-size: 13px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  color: #6b7280;
  font-weight: 500;
  min-width: 60px;
}

.detail-value {
  color: #1f2937;
  font-weight: 500;
}

.pagination-info {
  color: #6b7280;
  font-size: 14px;
}

.empty-logs {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

@media (max-width: 768px) {
  .logs-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .logs-controls {
    justify-content: space-between;
  }

  .log-meta {
    flex-direction: column;
    gap: 8px;
  }

  .log-icon-wrapper {
    align-self: flex-start;
  }

  .logs-footer-fixed {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
    padding: 12px 0;
  }

  .pagination-info {
    text-align: center;
  }
}

/* 入住人Tab样式 */
.guests-content {
  padding: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 顶部操作区域 */
.guests-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px 20px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  gap: 20px;
}

.guests-tips {
  flex: 1;
  display: flex;
  align-items: flex-start;
}

.tips-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px 16px;
  background: #e0f2fe;
  border: 1px solid #0891b2;
  border-radius: 8px;
  max-width: 600px;
}

.tips-content > i {
  color: #0891b2;
  font-size: 18px;
  margin-top: 2px;
  flex-shrink: 0;
}

.tips-text {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.tips-title {
  font-weight: 600;
  color: #0c4a6e;
  font-size: 14px;
}

.tips-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.tip-item {
  color: #0c4a6e;
  font-size: 13px;
  line-height: 1.4;
}

.guests-actions {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.guests-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.guests-table-header {
  display: grid;
  grid-template-columns: 2fr 1.5fr 1fr 1.2fr 1.2fr 1.2fr 1.5fr;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.header-cell {
  padding: 16px 12px;
  text-align: center;
  border-right: 1px solid #e5e7eb;
}

.header-cell:last-child {
  border-right: none;
}

.guests-table-body {
  flex: 1;
  overflow-y: auto;
}

.guest-row {
  display: grid;
  grid-template-columns: 2fr 1.5fr 1fr 1.2fr 1.2fr 1.2fr 1.5fr;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s ease;
}

.guest-row:hover {
  background: #f9fafb;
}

.guest-cell {
  padding: 16px 12px;
  border-right: 1px solid #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.guest-cell:last-child {
  border-right: none;
}

.guest-info-cell {
  align-items: flex-start;
  justify-content: flex-start;
  flex-direction: column;
  gap: 8px;
}

.guest-name {
  font-weight: 600;
  color: #1f2937;
  font-size: 16px;
  margin-bottom: 8px;
}

.guest-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
}

.detail-row {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
}

.detail-label {
  color: #6b7280;
  min-width: 40px;
  font-weight: 500;
}

.detail-value {
  color: #374151;
  font-weight: 500;
}

.photo-cell {
  flex-direction: column;
  gap: 8px;
}

.photo-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.photo-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.id-photo, .user-photo {
  width: 60px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.id-photo:hover, .user-photo:hover {
  transform: scale(1.1);
}

.user-photo {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.photo-label {
  font-size: 11px;
  color: #6b7280;
  font-weight: 500;
}

.no-photo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  color: #9ca3af;
  font-size: 12px;
}

.no-photo i {
  font-size: 24px;
}

.status-cell {
  flex-direction: column;
  gap: 6px;
}

.status-container {
  display: flex;
  flex-direction: column;
  gap: 6px;
  align-items: center;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.status-item.success {
  background: #dcfce7;
  color: #166534;
}

.status-item.warning {
  background: #fef3c7;
  color: #92400e;
}

.status-item.error {
  background: #fee2e2;
  color: #991b1b;
}

.status-item.primary {
  background: #dbeafe;
  color: #1e40af;
}

.status-item.default {
  background: #f3f4f6;
  color: #6b7280;
}

.status-item.info {
  background: #e0f2fe;
  color: #0c4a6e;
}

.status-item.clickable {
  cursor: pointer;
  transition: all 0.2s ease;
}

.status-item.clickable:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.actions-cell {
  flex-direction: column;
  gap: 6px;
}

.action-buttons {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.empty-guests {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

@media (max-width: 1200px) {
  .guests-table-header,
  .guest-row {
    grid-template-columns: 1.5fr 1fr 0.8fr 1fr 1fr 1fr 1.2fr;
  }

  .guest-name {
    font-size: 14px;
  }

  .detail-row {
    font-size: 12px;
  }

  .id-photo, .user-photo {
    width: 50px;
    height: 35px;
  }

  .user-photo {
    width: 35px;
    height: 35px;
  }
}

@media (max-width: 768px) {
  .guests-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
    padding: 16px;
  }

  .tips-content {
    padding: 12px;
  }

  .tips-title {
    font-size: 13px;
  }

  .tip-item {
    font-size: 12px;
  }

  .guests-actions {
    justify-content: center;
  }

  .guests-table-header,
  .guest-row {
    grid-template-columns: 1fr;
    gap: 0;
  }

  .guest-cell {
    border-right: none;
    border-bottom: 1px solid #f3f4f6;
    padding: 12px;
  }

  .header-cell {
    display: none;
  }

  .guest-row {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 12px;
    background: white;
  }

  .guest-cell::before {
    content: attr(data-label);
    font-weight: 600;
    color: #6b7280;
    margin-bottom: 4px;
    display: block;
    font-size: 12px;
  }

  .action-buttons {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
  }
}

/* 人脸认证弹框样式 */
.face-verification-modal .n-card {
  max-height: 90vh;
  overflow-y: auto;
}

.face-verification-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.user-info-header {
  text-align: center;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.user-info-header h3 {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
}

.user-details {
  display: flex;
  justify-content: center;
  gap: 24px;
  color: #6b7280;
  font-size: 14px;
}

.photos-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.photo-section h4 {
  margin: 0 0 16px 0;
  color: #374151;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
}

.id-photos, .face-photos {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
}

.photo-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  transition: all 0.2s ease;
}

.photo-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.verification-photo {
  width: 200px;
  height: 120px;
  object-fit: cover;
  border-radius: 6px;
  border: 1px solid #d1d5db;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.verification-photo:hover {
  transform: scale(1.05);
}

.verification-photo.face-photo {
  width: 120px;
  height: 120px;
  border-radius: 50%;
}

.photo-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.photo-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.no-photo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 40px 20px;
  color: #9ca3af;
  font-size: 14px;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  background: #f9fafb;
}

.no-photo i {
  font-size: 32px;
}

.verification-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.current-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-label {
  font-weight: 500;
  color: #374151;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

@media (max-width: 768px) {
  .photos-comparison {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .user-details {
    flex-direction: column;
    gap: 8px;
  }

  .verification-photo {
    width: 150px;
    height: 90px;
  }

  .verification-photo.face-photo {
    width: 90px;
    height: 90px;
  }

  .verification-actions {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .action-buttons {
    justify-content: center;
  }
}

/* 编辑入住状态弹框样式 */
.edit-status-modal .n-card {
  max-height: 90vh;
  overflow-y: auto;
}

.edit-status-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.user-info-section {
  text-align: center;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.user-info-section h4 {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.user-basic-info {
  display: flex;
  justify-content: center;
  gap: 24px;
  color: #6b7280;
  font-size: 14px;
}

.current-status-section {
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.status-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.status-select-section {
  padding: 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.form-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.form-label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
  min-width: 60px;
}

.status-select {
  flex: 1;
  min-width: 200px;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

@media (max-width: 768px) {
  .user-basic-info {
    flex-direction: column;
    gap: 8px;
  }

  .form-item {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .form-label {
    min-width: auto;
  }

  .modal-actions {
    flex-direction: column;
    align-items: stretch;
  }
}

.empty-logs {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

/* 超市、租借、赔付Tab样式 */
.supermarket-content,
.rental-content,
.compensation-content {
  max-width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.supermarket-header,
.rental-header,
.compensation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
  flex-shrink: 0;
}

.supermarket-header h3,
.rental-header h3,
.compensation-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
}

.supermarket-list,
.rental-list,
.compensation-list {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.order-basic-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.order-id, .order-number {
  font-size: 0.875rem;
  color: #6b7280;
}

.order-status {
  margin-left: 0.5rem;
}

.close-button {
  font-weight: 600;
  padding: 0.5rem 1rem;
}

/* 内容区域 - 修复滚动问题 */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  min-height: 0; /* 确保可以收缩 */
  max-height: 100%; /* 确保不超出父容器 */
  overflow: hidden; /* 防止内容溢出 */
}

/* 内容加载状态 */
.content-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

/* 可滚动内容区域 - 彻底修复滚动 */
.scrollable-content {
  flex: 1;
  overflow-y: auto !important;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
  padding-right: 1.5rem; /* 为滚动条留出空间 */
  min-height: 0; /* 确保可以收缩 */
  max-height: 100%; /* 确保不超出父容器 */
  /* 优化滚动性能 */
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  /* 确保滚动条可见 */
  scrollbar-width: auto;
  scrollbar-color: #d1d5db #f3f4f6;
}

/* Webkit浏览器滚动条样式 */
.scrollable-content::-webkit-scrollbar {
  width: 8px;
}

.scrollable-content::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 4px;
}

.scrollable-content::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 4px;
}

.scrollable-content::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* 滚动条样式 - 与左侧保持一致 */
.scrollable-content::-webkit-scrollbar {
  width: 6px;
}

.scrollable-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
  margin: 4px 0;
}

.scrollable-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.scrollable-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.4);
}

.scrollable-content::-webkit-scrollbar-thumb:active {
  background: rgba(0, 0, 0, 0.6);
}

/* Firefox滚动条样式 */
.scrollable-content {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) rgba(0, 0, 0, 0.05);
}

/* 骨架屏加载样式 */
.skeleton-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  z-index: 10;
  padding: 1rem;
  overflow-y: auto;
}

/* 骨架屏动画 - 修复闪光效果 */
@keyframes skeleton-shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* 骨架屏基础样式 */
.skeleton-loading .skeleton-element {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: skeleton-shimmer 2s ease-in-out infinite;
  border-radius: 4px;
}

/* 为所有骨架屏元素添加统一的动画 */
.skeleton-icon,
.skeleton-title,
.skeleton-line,
.skeleton-group-header,
.skeleton-button,
.skeleton-th,
.skeleton-td {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%) !important;
  background-size: 200px 100% !important;
  animation: skeleton-shimmer 2s ease-in-out infinite !important;
  border-radius: 4px !important;
}

/* 骨架屏概览区域 */
.skeleton-overview-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.skeleton-row {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.75rem;
}

.skeleton-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  padding: 0;
}

.skeleton-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
}

.skeleton-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
}

.skeleton-title {
  width: 80px;
  height: 14px;
}

.skeleton-body {
  padding: 0.75rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.skeleton-line {
  height: 12px;
  border-radius: 2px;
}

.skeleton-line:nth-child(1) { width: 100%; }
.skeleton-line:nth-child(2) { width: 80%; }
.skeleton-line:nth-child(3) { width: 60%; }

/* 骨架屏操作区域 */
.skeleton-actions-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.skeleton-action-group {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.skeleton-group-header {
  height: 40px;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  margin: 0;
}

.skeleton-buttons {
  padding: 0.75rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.skeleton-button {
  width: 80px;
  height: 28px;
  border-radius: 4px;
}

/* 骨架屏表格 */
.skeleton-table {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  margin-bottom: 1rem;
}

.skeleton-table-header {
  display: flex;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  padding: 0.75rem;
  gap: 1rem;
}

.skeleton-th {
  flex: 1;
  height: 16px;
}

.skeleton-table-body {
  padding: 0.75rem;
}

.skeleton-tr {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.75rem;
}

.skeleton-tr:last-child {
  margin-bottom: 0;
}

.skeleton-td {
  flex: 1;
  height: 14px;
}

/* 骨架屏加载提示 */
.skeleton-loading-tip {
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.95);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  font-size: 0.875rem;
  color: #6b7280;
  z-index: 20;
}

.skeleton-loading-tip span {
  background: none !important;
  animation: none !important;
}

/* 订单概览区域 - 紧凑布局 */
.order-overview-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem; /* 减少行间距 */
  margin-bottom: 0.75rem; /* 减少底部边距 */
}

.overview-row {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem; /* 减少卡片间距 */
  height: 160px; /* 设置固定高度 */
}

.overview-card {
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  /* 优化高度 - 设置最大高度限制 */
  max-height: 180px;
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 0.5rem; /* 统一间距 */
  padding: 0.5rem 0.75rem; /* 统一内边距 */
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  flex-shrink: 0;
  min-height: 28px; /* 统一最小高度 */
  height: 28px; /* 固定统一高度 */
}

.card-header i {
  font-size: 1rem; /* 统一图标尺寸 */
  color: #3b82f6;
  display: inline-flex; /* 使用inline-flex确保对齐 */
  align-items: center;
  justify-content: center;
  width: 18px; /* 统一图标宽度 */
  height: 18px; /* 统一图标高度 */
  flex-shrink: 0; /* 防止图标收缩 */
  line-height: 1; /* 确保图标垂直居中 */
}

.card-title {
  font-weight: 600;
  color: #1f2937;
  font-size: 0.875rem; /* 统一字体尺寸 */
  line-height: 1; /* 统一行高确保对齐 */
  display: inline-flex; /* 确保与图标水平对齐 */
  align-items: center;
}

.card-body {
  padding: 0.5rem 0.625rem; /* 减少内边距 */
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 房间信息卡片 - 全新紧凑布局 */
.room-card .room-info-layout {
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
  height: 100%;
}

.room-card .room-primary {
  display: flex;
  align-items: baseline;
  gap: 0.5rem;
  padding-bottom: 0.25rem;
  border-bottom: 1px solid #e5e7eb;
}

.room-card .room-number {
  font-size: 1.125rem;
  font-weight: 700;
  color: #3b82f6;
  line-height: 1.2;
}

.room-card .room-type {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1f2937;
  line-height: 1.2;
}

.room-card .room-details {
  display: flex;
  flex-direction: column;
  gap: 0.1875rem;
  flex: 1;
}

.room-card .detail-row {
  display: flex;
  justify-content: space-between;
  gap: 0.5rem;
}

.room-card .detail-item {
  font-size: 0.6875rem;
  color: #4b5563;
  line-height: 1.3;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 财务概览卡片 - 全新紧凑布局 */
.finance-card .finance-layout {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  height: 100%;
}

.finance-card .balance-primary {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.125rem;
  padding: 0.5rem;
  background: #f8fafc;
  border-radius: 4px;
  border-bottom: 2px solid #e5e7eb;
}

.finance-card .balance-label {
  font-size: 0.6875rem;
  color: #6b7280;
  font-weight: 500;
  line-height: 1.2;
}

.finance-card .balance-amount {
  font-size: 1.375rem;
  font-weight: 700;
  line-height: 1.1;
}

.finance-card .finance-details-compact {
  display: flex;
  justify-content: space-between;
  gap: 0.25rem;
  flex: 1;
  align-items: center;
}

.finance-card .finance-item {
  font-size: 0.6875rem;
  color: #4b5563;
  line-height: 1.3;
  text-align: center;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 时间信息卡片 - 全新紧凑布局 */
.time-card .time-layout {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  height: 100%;
}

.time-card .time-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

.time-card .time-pair {
  display: flex;
  /* flex-direction: column; */
  justify-content: space-between;
  gap: 0.125rem;
}

.time-card .time-compact {
  font-size: 0.6875rem;
  color: #4b5563;
  line-height: 1.3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.time-card .stay-duration {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 0.25rem;
  padding: 0.375rem;
  background: #f0f9ff;
  border-radius: 4px;
  border: 1px solid #bae6fd;
}

.time-card .duration-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: #0369a1;
  line-height: 1.1;
}

.time-card .duration-unit {
  font-size: 0.75rem;
  color: #0369a1;
  font-weight: 500;
  line-height: 1.2;
}

/* 联系人信息卡片 - 全新紧凑布局 */
.customer-card .contact-layout {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  height: 100%;
}

.customer-card .contact-primary {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding-bottom: 0.375rem;
  border-bottom: 1px solid #e5e7eb;
}

.customer-card .contact-name {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.2;
}

.customer-card .contact-phone {
  font-size: 0.8125rem;
  color: #4b5563;
  font-family: monospace;
  line-height: 1.3;
}

.customer-card .member-info {
  display: flex;
  flex-direction: column;
  gap: 0.1875rem;
  flex: 1;
}

.customer-card .member-item {
  font-size: 0.6875rem;
  color: #4b5563;
  line-height: 1.3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 入住人员卡片 - 全新紧凑布局 */
.guests-card .card-header {
  justify-content: space-between;
}

.guests-card .add-guest-btn {
  margin-left: auto;
}

.guests-card .guests-list {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  max-height: 120px;
  overflow-y: auto;
  flex: 1;
}

.guests-card .guest-item-compact {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0.375rem;
  background: #f8fafc;
  border-radius: 3px;
  border: 1px solid #e5e7eb;
}

.guests-card .guest-info-compact {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
  min-height: 0;
}

.guests-card .guest-main-line {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.25rem;
}

.guests-card .guest-name-compact {
  font-weight: 600;
  color: #1f2937;
  font-size: 0.8125rem;
  line-height: 1.2;
}

.guests-card .guest-badges-compact {
  display: flex;
  gap: 0.125rem;
  flex-shrink: 0;
}

.guests-card .guest-details-compact {
  margin-top: 0.125rem;
}

.guests-card .guest-contact {
  font-size: 0.6875rem;
  color: #6b7280;
  font-family: monospace;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.guests-card .no-guests {
  text-align: center;
  color: #6b7280;
  font-size: 0.8125rem;
  padding: 0.75rem;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 订单信息卡片 - 全新紧凑布局 */
.status-card .order-layout {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  height: 100%;
}

.status-card .order-status-line {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.375rem;
  padding-bottom: 0.375rem;
  border-bottom: 1px solid #e5e7eb;
}

.status-card .status-tag-compact {
  font-weight: 600;
  font-size: 0.8125rem;
}

.status-card .order-badges-compact {
  display: flex;
  gap: 0.125rem;
  flex-shrink: 0;
}

.status-card .order-details-compact {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.375rem;
  flex: 1;
}

.status-card .detail-column {
  display: flex;
  flex-direction: column;
  gap: 0.1875rem;
}

.status-card .order-detail-item {
  font-size: 0.6875rem;
  color: #4b5563;
  line-height: 1.3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 硬件详情卡片 - Tab模式布局 */
.hardware-row {
  grid-template-columns: 1fr; /* 硬件卡片占满整行 */
}

.hardware-card .card-header {
  justify-content: space-between;
}

.hardware-card .hardware-summary {
  margin-left: auto;
}

/* Tab模式样式 */
.hardware-tabs {
  height: 100%;
  max-height: 200px; /* 增加最大高度以适应tab */
}

.hardware-tabs .n-tabs-nav {
  padding: 0 0.5rem;
}

.hardware-tabs .n-tabs-tab {
  padding: 0.25rem 0.5rem;
}

/* Tab面板优化 */
.hardware-tabs :deep(.n-tab-pane) {
  padding: 0.5rem;
  max-height: 200px; /* 增加最大高度 */
  overflow-y: auto; /* 启用垂直滚动 */
  overflow-x: hidden;
}

/* 兼容性：直接选择器 */
.hardware-tabs .n-tab-pane {
  padding: 0.5rem;
  max-height: 200px;
  overflow-y: auto; /* 启用垂直滚动 */
  overflow-x: hidden;
}

/* 硬件tabs滚动条样式 */
.hardware-tabs :deep(.n-tab-pane)::-webkit-scrollbar,
.hardware-tabs .n-tab-pane::-webkit-scrollbar {
  width: 6px;
}

.hardware-tabs :deep(.n-tab-pane)::-webkit-scrollbar-track,
.hardware-tabs .n-tab-pane::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 3px;
}

.hardware-tabs :deep(.n-tab-pane)::-webkit-scrollbar-thumb,
.hardware-tabs .n-tab-pane::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.hardware-tabs :deep(.n-tab-pane)::-webkit-scrollbar-thumb:hover,
.hardware-tabs .n-tab-pane::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* 硬件设备列表样式 */

.tab-header {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
}

.tab-header i {
  font-size: 0.875rem;
}

.tab-count {
  margin-left: 0.25rem;
}

.tab-content {
  height: auto; /* 改为自动高度 */
  min-height: 80px; /* 设置最小高度 */
  max-height: 160px; /* 设置最大高度以确保滚动 */
  overflow-y: auto; /* 启用垂直滚动 */
  overflow-x: hidden;
  padding: 0.25rem; /* 添加内边距 */
  /* 滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f3f4f6;
}

/* Tab内容区域的滚动条样式 */
.tab-content::-webkit-scrollbar {
  width: 6px;
}

.tab-content::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 3px;
}

.tab-content::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.tab-content::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* 硬件卡片样式 */

.hardware-card .hardware-section {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.hardware-card .hardware-section-header {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding-bottom: 0.125rem;
  border-bottom: 1px solid #e5e7eb;
}

.hardware-card .hardware-section-header i {
  font-size: 0.875rem;
  color: #6b7280;
}

.hardware-card .section-title {
  font-size: 0.75rem;
  font-weight: 600;
  color: #374151;
  line-height: 1.2;
}

.hardware-card .device-count {
  font-size: 0.6875rem;
  color: #6b7280;
  line-height: 1.2;
}

.hardware-card .hardware-devices {
  display: flex;
  flex-direction: column;
  gap: 0.375rem; /* 增加设备间距 */
  padding: 0.25rem 0; /* 添加上下内边距 */
}

.hardware-card .device-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem; /* 增加内部间距 */
  padding: 0.5rem; /* 增加内边距 */
  background: #f8fafc;
  border-radius: 6px; /* 增加圆角 */
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease; /* 添加过渡效果 */
}

.hardware-card .device-item:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.hardware-card .device-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.25rem;
}

.hardware-card .device-name {
  font-size: 0.8125rem;
  font-weight: 500;
  color: #1f2937;
  line-height: 1.2;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.hardware-card .device-status {
  display: flex;
  gap: 0.125rem;
  flex-shrink: 0;
}

.hardware-card .device-details {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.hardware-card .device-info {
  font-size: 0.6875rem;
  color: #6b7280;
  font-family: monospace;
  line-height: 1.2;
}

.hardware-card .wifi-password-toggle {
  cursor: pointer;
  color: #3b82f6;
  transition: color 0.2s;
}

.hardware-card .wifi-password-toggle:hover {
  color: #1d4ed8;
}

.hardware-card .wifi-password-toggle i {
  margin-left: 0.25rem;
  font-size: 0.75rem;
}

.hardware-card .no-hardware {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  padding: 1rem;
  color: #6b7280;
  font-size: 0.8125rem;
  flex: 1;
}

.hardware-card .no-hardware i {
  font-size: 1.5rem;
  opacity: 0.5;
}

/* 硬件展开功能样式 */
.hardware-card .priority-section {
  border: 2px solid #10b981;
  border-radius: 8px;
  padding: 0.75rem;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
  margin-bottom: 0.75rem;
}

.hardware-card .priority-tag {
  margin-left: 0.5rem;
}

/* 门锁设备特殊样式 */
.hardware-card .lock-item {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.08) 0%, #f8fafc 100%);
  border: 1px solid rgba(16, 185, 129, 0.3);
  position: relative;
}

.hardware-card .lock-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: #10b981;
  border-radius: 3px 0 0 3px;
}

.hardware-card .lock-item:hover {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.12) 0%, #f1f5f9 100%);
  border-color: rgba(16, 185, 129, 0.4);
}

.hardware-card .section-expand {
  margin-top: 0.5rem;
  text-align: center;
}

.hardware-card .section-expand-btn {
  font-size: 0.75rem;
  color: #10b981;
}

.hardware-card .section-toggle {
  margin-left: auto;
  font-size: 0.75rem;
}

.hardware-card .expandable-content {
  animation: expandIn 0.3s ease-out;
  overflow: hidden;
}

.hardware-card .collapsed .expandable-content {
  display: none;
}

@keyframes expandIn {
  from {
    opacity: 0;
    max-height: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    max-height: 500px;
    transform: translateY(0);
  }
}

.hardware-card .hardware-subsection {
  margin-bottom: 0.75rem;
  padding: 0.5rem;
  background: rgba(var(--primary-color-rgb), 0.03);
  border-radius: 6px;
  border: 1px solid rgba(var(--primary-color-rgb), 0.1);
}

.hardware-card .subsection-header {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding-bottom: 0.25rem;
  border-bottom: 1px solid rgba(var(--primary-color-rgb), 0.15);
  margin-bottom: 0.5rem;
}

.hardware-card .subsection-header i {
  font-size: 0.875rem;
  color: var(--primary-color);
}

.hardware-card .subsection-title {
  font-size: 0.75rem;
  font-weight: 600;
  color: #1e40af;
  line-height: 1.2;
}

.hardware-card .expand-toggle {
  margin-left: 0.5rem;
  font-size: 0.75rem;
}

/* 第一排操作区域：房间管理 + 打印二维码 */
.primary-actions-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

/* 第二排操作区域：财务操作 */
.finance-operations-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

/* 操作区域响应式设计 */
@media (max-width: 1024px) {
  .primary-actions-section,
  .finance-operations-section {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
}

@media (max-width: 768px) {
  .action-group .action-buttons {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  .action-group .action-buttons .n-button {
    width: 100%;
    justify-content: center;
  }
}

.action-group {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.group-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
}

.group-header i {
  font-size: 1.25rem;
  color: #3b82f6;
}

.group-title {
  font-weight: 600;
  color: #1f2937;
  font-size: 0.875rem;
}

.action-group .action-buttons {
  padding: 0.75rem;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

/* 财务明细卡片 */
.finance-details-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 500px; /* 确保表格有足够的显示空间 */
}

.finance-details-card .card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  flex-shrink: 0; /* 防止头部被压缩 */
}

.finance-details-card .header-left {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.finance-details-card .header-left i {
  font-size: 1.125rem;
  color: #6366f1;
}

.finance-details-card .card-title {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.finance-details-card .header-right {
  display: flex;
  align-items: center;
}

.finance-details-card .card-content {
  flex: 1;
  padding: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.finance-details-card .table-container {
  flex: 1;
  width: 100%;
  overflow: hidden;
}

/* 财务明细表格样式优化 */
.finance-details-card .n-data-table {
  height: 100%;
}

.finance-details-card .n-data-table .n-data-table-wrapper {
  height: 100%;
}

.finance-details-card .n-data-table .n-data-table-base-table {
  height: 100%;
}

.finance-details-card .n-data-table .n-data-table-base-table-body {
  max-height: 350px;
  overflow-y: auto;
}

/* 表格滚动条样式 */
.finance-details-card .n-data-table-base-table-body::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.finance-details-card .n-data-table-base-table-body::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.finance-details-card .n-data-table-base-table-body::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.finance-details-card .n-data-table-base-table-body::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.4);
}

/* 表格行样式 */
.finance-details-card .n-data-table-tr {
  transition: background-color 0.2s ease;
}

.finance-details-card .n-data-table-tr:hover {
  background-color: #f8fafc;
}

/* 表格头部固定 */
.finance-details-card .n-data-table-thead {
  position: sticky;
  top: 0;
  z-index: 1;
  background: #fff;
}

/* 高级操作区域 */
.advanced-actions-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .overview-row {
    grid-template-columns: 1fr 1fr;
  }

  .overview-row .overview-card:nth-child(3) {
    grid-column: 1 / -1;
  }

  .primary-actions-section,
  .finance-operations-section {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .overview-row {
    grid-template-columns: 1fr;
  }

  .main-layout.sidebar-collapsed .main-panel {
    margin-left: 0;
  }

  .connect-sidebar.collapsed {
    width: 70px; /* 移动端稍微窄一点，但不隐藏 */
  }
}

.card-icon {
  font-size: 1.5rem;
  color: #3b82f6;
  margin-bottom: 0.25rem;
}

.card-title {
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.25rem 0;
}

.info-item.highlight {
  background: #f0f9ff;
  padding: 0.5rem;
  border-radius: 4px;
  border-left: 3px solid #3b82f6;
}

.info-item .label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.info-item .value {
  font-weight: 600;
  color: #1f2937;
}

.info-item.highlight .value {
  color: #1d4ed8;
  font-size: 1rem;
}

/* 入住人员相关样式 */
.guests-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.guest-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem;
  background: #f8fafc;
  border-radius: 4px;
}

.guest-name {
  font-weight: 500;
  color: #1f2937;
}

.no-guests {
  text-align: center;
  color: #9ca3af;
  font-style: italic;
  padding: 1rem;
}

/* 财务操作区域 */
.finance-operations-section {
  margin-bottom: 1rem;
}

.finance-operations-row {
  display: grid;
  grid-template-columns: 300px 1fr auto;
  gap: 1rem;
  align-items: start;
}

/* 余额统计卡片 */
.balance-summary-card {
  background: #fff;
  border-radius: 8px;
  padding: 0.5rem 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: fit-content;
}

/* 快速财务操作卡片 */
.quick-finance-actions-card {
  background: #fff;
  border-radius: 8px;
  padding: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: fit-content;
}

/* 余额统计 */
.balance-summary {
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f3f4f6;
}

.balance-main {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.balance-label {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
}

.balance-amount {
  font-size: 1.5rem;
  font-weight: 700;
}

.balance-amount.success {
  color: #10b981;
}

.balance-amount.error {
  color: #ef4444;
}

.balance-amount.default {
  color: #6b7280;
}

.balance-details {
  display: flex;
  gap: 1rem;
}

.balance-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
}

.balance-item .label {
  color: #6b7280;
}

.balance-item .value {
  font-weight: 600;
  color: #1f2937;
}

/* 快速财务操作 */
.quick-finance-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

/* 财务筛选 */
.finance-filters {
  background: #fff;
  border-radius: 8px;
  padding: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-width: fit-content;
  height: fit-content;
}

.filter-label {
  font-weight: 500;
  color: #6b7280;
}

/* 操作按钮区域 */
.action-buttons {
  background: #fff;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-section {
  margin-bottom: 1rem;
}

.action-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.75rem;
  font-size: 0.875rem;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.5rem;
}

/* 添加入住人弹窗样式优化 */
.add-guest-modal .add-guest-content .n-form {
  margin-bottom: 1.5rem;
}

.add-guest-modal .add-guest-content .modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

/* 添加入住人弹窗表单项样式优化 */
.add-guest-modal .n-form-item {
  margin-bottom: 20px;
}

.add-guest-modal .n-form-item-label {
  white-space: nowrap;
  min-width: 120px;
  flex-shrink: 0;
}

/* 特别处理较长标签的表单项 */
.add-guest-modal .n-form-item-label__text {
  overflow: visible;
  text-overflow: unset;
}

/* 底部表格 */

/* 添加与编辑弹窗的单选按钮行使用同一间距样式 */
.add-guest-modal .radio-group-spacing { display:flex; gap:20px; align-items:center; }

.bottom-table {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 加载和空状态 */
.loading-container,
.no-data-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: #f8fafc;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .info-cards-row {
    grid-template-columns: repeat(3, 1fr);
  }

  .finance-operations-row {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
}

@media (max-width: 768px) {
  .info-cards-row {
    grid-template-columns: repeat(2, 1fr);
  }

  .action-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .info-cards-row {
    grid-template-columns: 1fr;
  }

  .action-grid {
    grid-template-columns: 1fr;
  }
}

/* 添加入住人弹窗样式 */
.add-guest-modal .add-guest-content .n-form {
  margin-bottom: 1.5rem;
}

.add-guest-modal .add-guest-content .modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

/* 编辑入住人弹窗样式 */
.edit-guest-modal .edit-guest-content .n-form {
  margin-bottom: 1.5rem;
}

.edit-guest-modal .edit-guest-content .modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

/* 姓名搜索自动完成样式 */
.edit-guest-modal .n-auto-complete {
  width: 100%;
}

.edit-guest-modal .n-auto-complete .n-input {
  width: 100%;
}

/* 单选按钮组样式 - 防止文字换行挤压 */
.radio-group-spacing {
  display: flex;
  gap: 20px;
  align-items: center;
  flex-wrap: nowrap;
}

.radio-group-spacing .n-radio {
  white-space: nowrap;
  margin-right: 0;
  flex-shrink: 0;
}

.radio-group-spacing .n-radio .n-radio__label {
  padding-left: 8px;
}

/* 编辑弹窗表单项样式优化 */
.edit-guest-modal .n-form-item {
  margin-bottom: 20px;
}

.edit-guest-modal .n-form-item-label {
  white-space: nowrap;
  min-width: 120px; /* 增加标签宽度以适应较长的文字 */
  flex-shrink: 0; /* 防止标签被压缩 */
}

/* 特别处理较长标签的表单项 */
.edit-guest-modal .n-form-item-label__text {
  overflow: visible;
  text-overflow: unset;
}
</style>

<style>
/* 订单详情模态框专用全局样式 - 使用更具体的选择器避免冲突 */
.n-modal.order-detail-modal .n-modal {
  width: 100vw !important;
  height: 100vh !important;
  max-width: none !important;
  max-height: none !important;
  top: 0 !important;
  left: 0 !important;
  transform: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

.n-modal.order-detail-modal .n-modal-container {
  width: 100vw !important;
  height: 100vh !important;
  padding: 0 !important;
}

/* 全局加载状态 */
.global-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60vh;
  min-height: 400px;
}

/* 空状态 */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60vh;
  min-height: 400px;
}

/* 单房间信息样式 */
.single-room-info {
  padding: 1rem;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.info-section {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
}

.info-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.75rem;
}

.info-title i {
  font-size: 1rem;
  color: #3b82f6;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-text {
  font-size: 0.75rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

/* 单房间收起状态 */
.single-room-collapsed {
  padding: 1rem 0.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.single-room-indicator {
  width: 60px;
  height: 60px;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  transition: all 0.2s ease;
  cursor: pointer;
}

.single-room-indicator.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.single-room-indicator i {
  font-size: 1.5rem;
}

.n-modal.order-detail-modal .n-modal-body-wrapper {
  width: 100vw !important;
  height: 100vh !important;
  padding: 0 !important;
}

.order-detail-modal .n-card {
  width: 100vw !important;
  height: 100vh !important;
  border-radius: 0 !important;
  margin: 0 !important;
}

.order-detail-modal .n-card__content {
  padding: 0 !important;
  height: 100vh !important;
}
</style>
