{
  "prettier.enable": false,
  "editor.formatOnSave": false,

  // Auto fix
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "never"
  },

  // Enable eslint for supported languages (excluding TypeScript)
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "vue",
    "html",
    "markdown",
    "json",
    "jsonc"
  ],
  "i18n-ally.localesPaths": [
    "src/i18n",
    "src/i18n/locales"
  ],
  "[vue]": {
    "editor.codeActionsOnSave": {
      "source.organizeImports": "never"
    }
  },
  "[javascript]": {
    "editor.maxTokenizationLineLength": 2500,
    "editor.codeActionsOnSave": {
      "source.organizeImports": "never"
    }
  }
}
