<template>
  <div class="hotel-brand-container">
    <!-- 酒店Logo区域 -->
    <div class="logo-section">
      <div class="logo-container">
        <img v-if="hotelLogo" :src="hotelLogo" alt="酒店Logo" class="hotel-logo">
        <div v-else class="default-logo">
          <TheLogo />
        </div>
      </div>
    </div>

    <!-- 酒店信息区域 -->
    <div
      v-show="!appStore.collapsed"
      class="hotel-info-section"
    >
      <div class="hotel-brand">
        <div class="hotel-name-row">
          <h1 class="hotel-name">{{ hotelName }}</h1>
          <!-- 账号切换按钮 -->
          <div v-if="hasChildAccounts" class="account-switcher">
            <AccountSwitcher />
          </div>
        </div>
        <div class="brand-tagline">
          <span class="tagline-text">智慧管理</span>
          <span class="tagline-dot">•</span>
          <span class="tagline-text">卓越服务</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useAppStore, useUserStore } from '@/store'
import { getLoginField } from '@/utils/loginData'
import AccountSwitcher from '@/components/AccountSwitcher.vue'
import { useLoginData } from '@/composables/useLoginData'

const appStore = useAppStore()
const userStore = useUserStore()

// 使用响应式登录数据
const { hotelInfo, hasChildAccounts } = useLoginData()

// 酒店Logo - 优先使用cover_pic字段
const hotelLogo = computed(() => {
  return getLoginField('cover_pic') ||
         getLoginField('shop_logo') ||
         ''
})

// 酒店名称 - 使用响应式的酒店信息
const hotelName = computed(() => {
  return hotelInfo.value.name || '智慧酒店管理系统'
})

// hasChildAccounts 已从 useLoginData 获取，无需重复定义
</script>

<style scoped>
.hotel-brand-container {
  display: flex;
  align-items: center;
  padding: 8px 6px; /* 减少内边距 */
  height: 44px; /* 进一步减少高度以完美对齐 */
  background: linear-gradient(135deg,
    rgba(var(--primary-color-rgb), 0.5) 0%,
    rgba(var(--primary-color-rgb), 0.45) 15%,
    rgba(var(--primary-color-rgb), 0.4) 30%,
    rgba(255, 255, 255, 0.75) 50%,
    rgba(var(--primary-color-rgb), 0.4) 70%,
    rgba(var(--primary-color-rgb), 0.45) 85%,
    rgba(var(--primary-color-rgb), 0.5) 100%
  );
  backdrop-filter: blur(15px);
  border-bottom: 1px solid rgba(var(--primary-color-rgb), 0.6);
  box-shadow: 0 4px 20px rgba(var(--primary-color-rgb), 0.35), 0 2px 10px rgba(var(--primary-color-rgb), 0.25);
  position: relative;
  overflow: hidden;
  /* width: 100%; */
}

/* 添加跟随主题色的光效 */
.hotel-brand-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(var(--primary-color-rgb), 0.15) 30%,
    rgba(var(--primary-color-rgb), 0.12) 50%,
    rgba(var(--primary-color-rgb), 0.15) 70%,
    transparent 100%
  );
  pointer-events: none;
}

.logo-section {
  position: relative;
  z-index: 3; /* 提高层级确保在光效之上 */
  padding-left: 0; /* 靠左对齐 */
}

.logo-container {
  width: 28px; /* 减小Logo尺寸以适应更小的高度 */
  height: 28px;
  border-radius: 6px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgb(var(--primary-color)) 0%, rgba(var(--primary-color), 0.85) 100%);
  box-shadow:
    0 2px 6px rgba(var(--primary-color), 0.25),
    0 1px 2px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  position: relative;
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.8); /* 添加白色边框 */
}

.hotel-logo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

/* 图片悬停效果 */
.logo-container:hover .hotel-logo {
  transform: scale(1.1);
}

.default-logo {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.hotel-info-section {
  margin-left: 2px; /* 调整间距 */
  flex: 1;
  min-width: 0;
  position: relative;
  z-index: 2;
}

.hotel-brand {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.hotel-name-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.5rem;
}

.hotel-name {
  font-size: 14px; /* 进一步减小字体以适应更小高度 */
  font-weight: 700;
  line-height: 1.1;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  background: linear-gradient(135deg, #2A5DAA 0%, #1E4A8C 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.02em;
  flex: 1;
}

.account-switcher {
  flex-shrink: 0;
  margin-left: 8px;
}

.hotel-switch-btn {
  padding: 2px 6px;
  height: 24px;
  font-size: 10px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 2px;
  flex-shrink: 0;
}

.hotel-switch-btn i {
  font-size: 12px;
}

.brand-tagline {
  display: flex;
  align-items: center;
  gap: 6px;
  opacity: 0.8;
}

.tagline-text {
  font-size: 9px; /* 进一步减小字体以适应更小高度 */
  font-weight: 500;
  color: var(--text-color-2);
  letter-spacing: 0.5px;
}

.tagline-dot {
  font-size: 8px;
  color: rgb(var(--primary-color));
  opacity: 0.6;
}

/* 悬停效果 */
.hotel-brand-container:hover .logo-container {
  transform: scale(1.05);
  box-shadow:
    0 6px 16px rgba(var(--primary-color), 0.3),
    0 2px 6px rgba(0, 0, 0, 0.15);
}

/* 暗色主题适配 - 跟随主题色 */
.dark .hotel-brand-container {
  background: linear-gradient(135deg,
    rgba(var(--primary-color-rgb), 0.55) 0%,
    rgba(var(--primary-color-rgb), 0.5) 15%,
    rgba(var(--primary-color-rgb), 0.45) 30%,
    rgba(31, 41, 55, 0.8) 50%,
    rgba(var(--primary-color-rgb), 0.45) 70%,
    rgba(var(--primary-color-rgb), 0.5) 85%,
    rgba(var(--primary-color-rgb), 0.55) 100%
  ) !important;
  border-bottom: 1px solid rgba(var(--primary-color-rgb), 0.7) !important;
  box-shadow: 0 4px 20px rgba(var(--primary-color-rgb), 0.4), 0 2px 10px rgba(var(--primary-color-rgb), 0.3);
}

.dark .hotel-brand-container::before {
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(var(--primary-color-rgb), 0.2) 30%,
    rgba(var(--primary-color-rgb), 0.15) 50%,
    rgba(var(--primary-color-rgb), 0.2) 70%,
    transparent 100%
  );
}

.dark .hotel-name {
  color: var(--text-color-1);
}

.dark .tagline-text {
  color: var(--text-color-3);
}

.dark .logo-container {
  border-color: rgba(75, 85, 99, 0.6);
}

/* 小屏幕优化 - 减少Logo区域高度 */
@media (max-width: 1440px) and (max-height: 900px) {
  .hotel-brand-container {
    height: 60px !important; /* 保持与顶部头部一致 */
    padding: 12px 6px !important;
  }

  .logo-container {
    width: 32px !important;
    height: 32px !important;
  }

  .hotel-name {
    font-size: 14px !important;
  }

  .tagline-text {
    font-size: 10px !important;
  }

  .hotel-info-section {
    margin-left: 1px !important;
  }
}

/* 移动端进一步优化 */
@media (max-width: 768px) {
  .hotel-brand-container {
    height: 60px !important; /* 保持与顶部头部一致 */
    padding: 12px 6px !important;
  }

  .logo-container {
    width: 28px !important;
    height: 28px !important;
  }

  .hotel-name {
    font-size: 12px !important;
  }
}

</style>
