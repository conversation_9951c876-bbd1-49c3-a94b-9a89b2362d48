/**
 * 网站图标管理工具
 * 用于动态更新网站的favicon图标
 */

/**
 * 更新网站图标
 * @param {string} iconUrl - 图标URL
 */
export function updateFavicon(iconUrl) {
  try {
    if (!iconUrl) {

      return
    }

    // 移除现有的favicon链接
    const existingLinks = document.querySelectorAll('link[rel*="icon"]')
    existingLinks.forEach(link => link.remove())

    // 创建新的favicon链接
    const link = document.createElement('link')
    link.rel = 'icon'
    link.type = 'image/png'
    link.href = iconUrl
    
    // 添加到head中
    document.head.appendChild(link)

  } catch (error) {

  }
}

/**
 * 重置网站图标为默认图标
 */
export function resetFavicon() {
  updateFavicon('/favicon.png')
}

/**
 * 从登录数据更新网站图标
 */
export function updateFaviconFromLogin() {
  try {
    // 动态导入避免循环依赖
    import('@/utils/loginData').then(({ getWebConfig }) => {
      const webConfig = getWebConfig()
      const customLogo = webConfig.set_web_logo
      
      if (customLogo) {
        updateFavicon(customLogo)
      } else {
        resetFavicon()
      }
    }).catch(error => {

      resetFavicon()
    })
  } catch (error) {

    resetFavicon()
  }
}

/**
 * 监听登录状态变化，自动更新图标
 */
export function setupFaviconWatcher() {
  // 页面加载时更新一次
  updateFaviconFromLogin()
  
  // 监听存储变化
  window.addEventListener('storage', (event) => {
    if (event.key === 'loginResponseData' || event.key === 'systemConfig') {
      updateFaviconFromLogin()
    }
  })
}

export default {
  updateFavicon,
  resetFavicon,
  updateFaviconFromLogin,
  setupFaviconWatcher
}
