<template>
  <n-modal
    v-model:show="visible"
    preset="card"
    title="会员信息"
    class="member-info-modal"
    :style="{ width: '1000px', maxWidth: '95vw', height: '80vh' }"
    :mask-closable="false"
    @update:show="handleClose"
  >
    <div v-if="loading" class="loading-container">
      <n-spin size="large">
        <template #description>加载会员信息中...</template>
      </n-spin>
    </div>

    <div v-else-if="memberInfo && Object.keys(memberInfo).length > 0" class="member-info-content">
      <!-- 操作按钮 - 移到右上角 -->
      <div class="action-buttons-section">
        <n-button type="success" size="tiny" @click="openEditBasicInfoModal">编辑基本信息</n-button>
        <n-button type="info" size="tiny" @click="openEditMemberGradeModal">编辑会员等级</n-button>
        <n-button type="primary" size="tiny" @click="openOperationLogModal">操作日志</n-button>
        <n-button
          v-if="!isBlocked"
          type="error"
          size="tiny"
          @click="handleSetBlock(true)"
          :loading="blockLoading"
        >
          置黑名单
        </n-button>
        <n-button
          v-if="isBlocked"
          type="success"
          size="tiny"
          @click="handleSetBlock(false)"
          :loading="blockLoading"
        >
          解除黑名单
        </n-button>
      </div>

      <!-- 顶部卡片区域 -->
      <div class="top-cards-section">
        <!-- 连锁酒店卡片 -->
        <div class="card-section chain-hotels">
          <div class="card-header-row">
            <h3 class="section-title">跨店卡</h3>
            <div class="card-actions">
              <n-button size="tiny" type="success" @click="handleRecharge('chain')">
                <template #icon><i class="i-mdi:cash-plus"></i></template>
                充值
              </n-button>
            </div>
          </div>
          <div class="cards-grid cards-grid--chain">
            <div class="info-card">
              <div class="card-content">
                <div class="card-main-value">{{ chainTotal }}</div>
                <div class="card-label">总余额</div>
                <div class="subpairs">
                  <span class="pair">本金 <b>{{ chainPrincipal }}</b></span>
                  <span class="pair">赠送 <b>{{ chainGift }}</b></span>
                </div>
              </div>
              <div class="card-detail-btn">
                <n-button size="tiny" text @click="handleViewCardDetails('chain', 'balance')">
                  <template #icon><i class="i-mdi:eye-outline"></i></template>
                  明细
                </n-button>
              </div>
            </div>
            <div class="info-card">
              <div class="card-content">
                <div class="card-main-value">{{ memberInfo.total_amount || 2385 }}</div>
                <div class="card-label">累计金额</div>
              </div>
              <div class="card-detail-btn">
                <n-button size="tiny" text @click="handleViewPaymentDetails">
                  <template #icon><i class="i-mdi:receipt-text-outline"></i></template>
                  明细
                </n-button>
              </div>
            </div>
            <div class="info-card">
              <div class="card-content">
                <div class="card-main-value">{{ memberInfo.points || 35956 }}</div>
                <div class="card-label">累计积分</div>
              </div>
              <div class="card-detail-btn">
                <n-button size="tiny" text @click="handleViewPointDetails">
                  <template #icon><i class="i-mdi:star-outline"></i></template>
                  明细
                </n-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 单店卡片 -->
        <div class="card-section single-store">
          <div class="card-header-row">
            <h3 class="section-title">单店卡</h3>
            <div class="card-actions">
              <n-button size="tiny" type="success" @click="handleRecharge('store')">
                <template #icon><i class="i-mdi:cash-plus"></i></template>
                充值
              </n-button>
            </div>
          </div>
          <div class="cards-grid cards-grid--store">
            <div class="info-card">
              <div class="card-content">
                <div class="card-main-value">{{ storeBalance }}</div>
                <div class="card-label">储值金额</div>
              </div>
              <div class="card-detail-btn">
                <n-button size="tiny" text @click="handleViewStoreBalanceDetails">
                  <template #icon><i class="i-mdi:store-outline"></i></template>
                  明细
                </n-button>
              </div>
            </div>
            <div class="info-card">
              <div class="card-content">
                <div class="card-main-value">{{ storePaidAmount }}</div>
                <div class="card-label">累计实付</div>
              </div>
              <div class="card-detail-btn">
                <n-button size="tiny" text @click="handleViewStorePaidDetails">
                  <template #icon><i class="i-mdi:receipt-text-outline"></i></template>
                  明细
                </n-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 会员基本信息行 -->
      <div class="member-basic-info">
        <div class="basic-info-row">
          <div class="info-item">
            <span class="info-label">等级</span>
            <span class="info-value">{{ memberInfo.grade_name || '普通会员' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">成长值</span>
            <span class="info-value">{{ memberInfo.growth || 28222 }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">卡号</span>
            <span class="info-value">{{ safeDisplayValue(memberInfo.card_number, '20230315180929296') }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">注册时间</span>
            <span class="info-value">{{ formatDateTime(memberInfo.register_time) || '2023-03-15 18:09:25' }}</span>
          </div>
        </div>
        <div class="basic-info-row">
          <div class="info-item">
            <span class="info-label">权益</span>
            <span class="info-value link" @click="openGradeModal">{{ gradeCount }} 项</span>
          </div>
          <div class="info-item">
            <span class="info-label">有效期</span>
            <span class="info-value">{{ formatDateTime(memberInfo.expire_time) || '2026-08-01 00:00:00' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">优惠券</span>
            <span class="info-value link" @click="openCouponModal">{{ couponCount }} 张</span>
          </div>
        </div>
      </div>



      <!-- 客史统计 -->
      <div class="history-stats-section">
        <h3 class="section-title">客史统计</h3>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-label">最近来店门店：</span>
            <span class="stat-value">{{ safeDisplayValue(memberInfo.recent_store, '智慧酒店') }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">最近充值金额：</span>
            <span class="stat-value">{{ memberInfo.recharge?.last_recharge_amount || 310 }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">入住次数：</span>
            <span class="stat-value">{{ memberInfo.checkin_count || 59 }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">平均房价：</span>
            <span class="stat-value">{{ memberInfo.avg_price || 87 }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">最喜欢房型：</span>
            <span class="stat-value">{{ safeDisplayValue(memberInfo.favorite_room_type, '总统套房') }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">最喜欢房间：</span>
            <span class="stat-value">{{ memberInfo.favorite_room || 1001 }}</span>
          </div>
        </div>
      </div>

      <!-- 基本信息 -->
      <div class="basic-info-section">
        <h3 class="section-title">基本信息</h3>
        <div class="basic-info-grid">
          <div class="basic-row">
            <div class="basic-item">
              <span class="basic-label">姓名：</span>
              <span class="basic-value">{{ safeDisplayValue(memberInfo.name, '张迁') }}</span>
            </div>
            <div class="basic-item">
              <span class="basic-label">电话：</span>
              <span class="basic-value">{{ safeDisplayValue(memberInfo.phone, '15972038194') }}</span>
            </div>
            <div class="basic-item">
              <span class="basic-label">性别：</span>
              <span class="basic-value">{{ getGenderText(memberInfo.gender) }}</span>
            </div>
            <div class="basic-item">
              <span class="basic-label">证件：</span>
              <span class="basic-value">{{ formatIdCard(memberInfo.id_card) || '身份证(xxxx)' }}</span>
            </div>
          </div>
          <div class="basic-row">
            <div class="basic-item">
              <span class="basic-label">开卡酒店：</span>
              <span class="basic-value">{{ safeDisplayValue(memberInfo.register_hotel, '智慧酒店') }}</span>
            </div>
            <div class="basic-item">
              <span class="basic-label">销售员：</span>
              <span class="basic-value">{{ getSalespersonName(memberInfo.develop_admin_id) }}</span>
            </div>
            <div class="basic-item">
              <span class="basic-label">发展途径：</span>
              <span class="basic-value">{{ safeDisplayValue(memberInfo.development_channel, '微信小程序') }}</span>
            </div>
            <div class="basic-item">
              <span class="basic-label">喜爱名菜：</span>
              <span class="basic-value">{{ safeDisplayValue(memberInfo.favorite_dish, '吉') }}</span>
            </div>
          </div>
          <div class="basic-row">
            <div class="basic-item">
              <span class="basic-label">民族：</span>
              <span class="basic-value">{{ memberInfo.nation || '' }}</span>
            </div>
            <div class="basic-item">
              <span class="basic-label">生日：</span>
              <span class="basic-value">{{ memberInfo.birthday || '2023/05/30' }}</span>
            </div>
            <div class="basic-item">
              <span class="basic-label">学历：</span>
              <span class="basic-value">{{ memberInfo.education || '' }}</span>
            </div>
            <div class="basic-item">
              <span class="basic-label">车牌：</span>
              <span class="basic-value">{{ memberInfo.license_plate || '' }}</span>
            </div>
          </div>
          <div class="basic-row" v-if="getRemarkText(memberInfo)">
            <div class="basic-item full-width">
              <span class="basic-label">备注：</span>
              <span class="basic-value">{{ getRemarkText(memberInfo) }}</span>
            </div>
          </div>
        </div>
      </div>

    </div>

    <div v-else class="error-container">
      <n-empty description="暂无会员信息">
        <template #icon>
          <i class="i-mdi:account-off"></i>
        </template>
      </n-empty>
    </div>
    <!-- 充值弹窗 -->
    <n-modal
      v-model:show="rechargeModal.show"
      preset="card"
      :title="`充值套餐（${getScopeLabel(rechargeModal.scope)}）`"
      :style="{ width: '720px', maxWidth: '95vw' }"
      :mask-closable="false"
    >
      <div class="recharge-modal-body">
        <div class="recharge-toolbar">
          <n-alert type="info" closable size="small">
            请选择合适的充值套餐，确认后将以当前操作人身份执行充值
          </n-alert>

        </div>
        <div class="recharge-table-wrapper">
          <div class="recharge-grid">
            <n-radio-group v-model:value="selectedRechargeId">
              <div v-for="item in rechargeList" :key="item.id" class="recharge-card-wrapper">
                <n-radio :value="item.id" class="recharge-radio">
                  <div class="recharge-card" :class="{ 'is-recommended': isRecommended(item) }">
                    <div v-if="isRecommended(item)" class="recommend-badge">推荐</div>
                    <div class="recharge-amount">¥{{ formatNumber(item.recharge_money) }}</div>
                    <div class="recharge-gives-summary" v-if="hasGives(item)">
                      <span class="gives-text">{{ getGivesSummary(item) }}</span>
                    </div>
                    <div class="recharge-operator">{{ operatorName }}</div>
                  </div>
                </n-radio>
              </div>
            </n-radio-group>
          </div>
          <div class="recharge-modal-footer">
            <div class="left">
              <span class="muted">操作人：</span>
              <span>{{ operatorName }}</span>
              <template v-if="selectedPlan">
                <n-tag size="small" type="success" class="ml-8">已选套餐：¥{{ selectedPlan.recharge_money }}</n-tag>
              </template>
            </div>
            <div class="right">
              <n-button size="small" @click="rechargeModal.show = false">取消</n-button>
              <n-button size="small" type="primary" :disabled="!selectedPlan" class="ml-8" @click="handleConfirmRecharge">
                确认充值
              </n-button>
            </div>
          </div>
        </div>
      </div>
    </n-modal>

    <!-- 收银台弹窗 -->
    <n-modal
      v-model:show="cashierModal.show"
      preset="card"
      title="收银台"
      :style="{ width: '560px', maxWidth: '95vw' }"
      :mask-closable="false"
    >
      <div class="cashier-modal-body">
        <div class="cashier-summary">
          <div class="row">
            <span class="label">充值金额</span>
            <span class="value">¥{{ selectedPlan?.recharge_money ?? '-' }}</span>
          </div>
          <div class="row">
            <span class="label">赠送</span>
            <div class="gives">
              <span>金额 ¥{{ selectedPlan?.give_money ?? 0 }}</span>
              <span>积分 {{ selectedPlan?.give_point ?? 0 }}</span>
              <span>成长值 {{ selectedPlan?.give_growth ?? 0 }}</span>
            </div>
          </div>
        </div>
        <n-form label-width="72">
          <n-form-item label="账户">
            <n-radio-group v-model:value="cashierForm.account_id">
              <n-spin :show="accountLoading">
                <div v-if="Array.isArray(accountList) && accountList.length" class="account-inline">
                  <n-radio v-for="acc in accountList" :key="acc.id" :value="acc.id" class="account-chip">
                    <i :class="getAccountIcon(acc)" class="chip-icon"></i>
                    <span class="chip-label">{{ getAccountLabel(acc) }}</span>
                  </n-radio>
                </div>
                <n-empty v-else description="暂无账户" size="small" />
              </n-spin>
            </n-radio-group>
          </n-form-item>
          <n-form-item label="支付码">
            <n-input v-model:value="cashierForm.auth_code" placeholder="可扫描或输入支付码（可为空）" />
          </n-form-item>
          <n-form-item label="备注">
            <n-input v-model:value="cashierForm.memo" placeholder="备注信息" />
          </n-form-item>
          <n-form-item>
            <n-checkbox v-model:checked="cashierForm.print">打印收款小票</n-checkbox>
          </n-form-item>
        </n-form>
        <div class="cashier-footer">
          <n-button size="small" tertiary @click="onBackToPlans">返回套餐</n-button>
          <n-button size="small" type="primary" :disabled="!selectedPlan || !cashierForm.account_id" :loading="cashierSubmitting" @click="submitRecharge">确认支付</n-button>


        </div>
      </div>
    </n-modal>
    <!-- 权益列表弹窗（grade_info.right_itererest） -->
    <n-modal v-model:show="gradeModal" preset="card" :title="gradeModalTitle" :style="{ width: '900px', maxWidth: '95vw' }">
      <template v-if="gradeTable.length">
        <n-data-table :columns="gradeColumns" :data="gradeTable" size="small" :bordered="false" />
      </template>
      <n-empty v-else description="暂无权益" />
    </n-modal>

    <!-- 优惠券列表弹窗 -->
    <n-modal v-model:show="couponModal" preset="card" :title="couponModalTitle" :style="{ width: '1000px', maxWidth: '95vw' }">
      <template v-if="couponTable.length || couponPaginationState.itemCount > 0">
        <n-data-table
          :columns="couponColumns"
          :data="couponTable"
          size="small"
          :bordered="false"
          :pagination="couponPagination"
          remote
        />
      </template>
      <n-empty v-else description="暂无优惠券" />
    </n-modal>

    <!-- 余额明细弹窗 -->
    <n-modal
      v-model:show="balanceDetailModal.show"
      preset="card"
      :title="balanceDetailModal.title"
      :style="{ width: '1400px', maxWidth: '95vw', height: '80vh' }"
      :content-style="{ padding: '0', display: 'flex', flexDirection: 'column', height: '100%' }"
    >
      <div class="balance-detail-content">
        <!-- 筛选条件 -->
        <div class="filter-section">
          <div class="filter-row">
            <n-form inline :label-width="60" size="small">
              <n-form-item label="时间:">
                <n-button-group>
                  <n-button size="small" :type="timeFilter === 'today' ? 'primary' : 'default'" @click="setTimeFilter('today')">今天</n-button>
                  <n-button size="small" :type="timeFilter === 'yesterday' ? 'primary' : 'default'" @click="setTimeFilter('yesterday')">昨天</n-button>
                  <n-button size="small" :type="timeFilter === 'week' ? 'primary' : 'default'" @click="setTimeFilter('week')">近7天</n-button>
                  <n-button size="small" :type="timeFilter === 'month' ? 'primary' : 'default'" @click="setTimeFilter('month')">近30天</n-button>
                  <n-button size="small" :type="timeFilter === 'all' ? 'primary' : 'default'" @click="setTimeFilter('all')">全部</n-button>
                  <n-button size="small" :type="timeFilter === 'custom' ? 'primary' : 'default'" @click="setTimeFilter('custom')">自定义</n-button>
                </n-button-group>
              </n-form-item>
              <n-form-item label="资方门店:">
                <n-select v-model:value="filterForm.shop_id" :options="shopOptions" placeholder="全部" clearable style="width: 150px" size="small" @update:value="onFilterChange" />
              </n-form-item>
              <n-form-item label="单据类型:">
                <n-select v-model:value="filterForm.bill_type" :options="billTypeOptions" placeholder="全部" clearable style="width: 150px" size="small" @update:value="onFilterChange" />
              </n-form-item>
            </n-form>
            <div class="filter-actions">
              <n-button type="warning" size="small" @click="openAdjustBalanceModal">
                <template #icon><i class="i-mdi:cash-edit"></i></template>
                手动调整余额
              </n-button>
            </div>
          </div>
        </div>

        <!-- 明细表格容器 -->
        <div class="detail-table-container">
          <div class="detail-table-wrapper">
            <n-data-table
              :columns="balanceDetailColumns"
              :data="balanceDetailList"
              size="small"
              :bordered="false"
              :pagination="false"
              :loading="balanceDetailLoading"
              :scroll-x="1300"
              flex-height
            />
          </div>

          <!-- 固定在底部的分页器 -->
          <div class="detail-pagination-section">
            <n-pagination
              v-model:page="balanceDetailPagination.page"
              v-model:page-size="balanceDetailPagination.pageSize"
              :item-count="balanceDetailPagination.itemCount"
              :page-sizes="balanceDetailPagination.pageSizes"
              :show-size-picker="balanceDetailPagination.showSizePicker"
              :prefix="balanceDetailPagination.prefix"
              @update:page="balanceDetailPagination.onChange"
              @update:page-size="balanceDetailPagination.onUpdatePageSize"
              size="small"
            />
          </div>
        </div>
      </div>
    </n-modal>

    <!-- 手动调整余额弹窗 -->
    <n-modal v-model:show="adjustBalanceModal.show" preset="card" title="手动调整余额" :style="{ width: '400px', maxWidth: '95vw' }">
      <n-form ref="adjustFormRef" :model="adjustBalanceForm" :rules="adjustBalanceRules" label-placement="left" :label-width="80">
        <n-form-item label="调整类型" path="type">
          <n-radio-group v-model:value="adjustBalanceForm.type">
            <n-radio value="1">充值金额</n-radio>
            <n-radio value="2">赠送余额</n-radio>
          </n-radio-group>
        </n-form-item>
        <n-form-item label="调整金额" path="amount">
          <n-input-number
            v-model:value="adjustBalanceForm.amount"
            placeholder="请输入调整金额"
            :precision="2"
            :min="0.01"
            style="width: 100%"
          />
        </n-form-item>
        <n-form-item label="备注说明" path="remark">
          <n-input
            v-model:value="adjustBalanceForm.remark"
            type="textarea"
            placeholder="请输入备注说明"
            :rows="3"
          />
        </n-form-item>
      </n-form>
      <template #footer>
        <div style="display: flex; justify-content: flex-end; gap: 12px;">
          <n-button @click="adjustBalanceModal.show = false">取消</n-button>
          <n-button type="primary" :loading="adjustBalanceLoading" @click="handleAdjustBalance">确认调整</n-button>
        </div>
      </template>
    </n-modal>

    <!-- 打印选择弹窗 -->
    <n-modal v-model:show="printSelectModal.show" preset="card" title="选择打印方式" :style="{ width: '300px', maxWidth: '95vw' }">
      <div class="print-select-content">
        <n-button type="primary" size="large" block @click="handleA4Print" style="margin-bottom: 12px;">
          <template #icon><i class="i-mdi:printer"></i></template>
          A4打印
        </n-button>
        <n-button type="info" size="large" block @click="handleTicketPrint">
          <template #icon><i class="i-mdi:receipt"></i></template>
          小票打印
        </n-button>
      </div>
    </n-modal>

    <!-- A4打印预览弹窗 -->
    <n-modal v-model:show="a4PrintModal.show" preset="card" title="A4打印" :style="{ width: '800px', maxWidth: '95vw', height: '90vh' }">
      <div class="a4-print-content">
        <div class="print-preview" id="printContent">
          <div class="print-header">
            <h2>智慧酒店</h2>
            <h3>(跨店卡充值单)</h3>
          </div>

          <div class="hotel-info">
            地址:四川省成都市青羊区杭州西路176号&nbsp;&nbsp;&nbsp;&nbsp;联系电话:0714-6666677
          </div>

          <div class="member-info-section">
            <div class="info-row">
              <span>会员名：{{ printData.memberName }}</span>
              <span>电话：{{ printData.phone }}</span>
              <span>卡号：{{ printData.cardNumber }}</span>
            </div>
            <div class="info-row">
              <span>充值金额：{{ printData.rechargeAmount }}</span>
              <span>赠送金额：{{ printData.giftAmount }}</span>
              <span>赠送积分：{{ printData.giftPoints }}</span>
            </div>
            <div class="info-row">
              <span>订单号：{{ printData.orderNumber }}</span>
              <span>订单时间：{{ printData.orderTime }}</span>
              <span>备注：</span>
            </div>
          </div>

          <div class="print-footer">
            <div class="footer-row">
              <span>打印时间:{{ printData.printTime }}</span>
              <span>打印人:{{ printData.operator }}</span>
              <span>客户签名：</span>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div style="display: flex; justify-content: center; gap: 12px;">
          <n-button @click="a4PrintModal.show = false">取消</n-button>
          <n-button type="primary" @click="doPrint">
            <template #icon><i class="i-mdi:printer"></i></template>
            打印
          </n-button>
        </div>
      </template>
    </n-modal>

    <!-- 累计金额明细弹窗 -->
    <n-modal
      v-model:show="paymentDetailModal.show"
      preset="card"
      :title="paymentDetailModal.title"
      :style="{ width: '1400px', maxWidth: '95vw', height: '80vh' }"
      :content-style="{ padding: '0', display: 'flex', flexDirection: 'column', height: '100%' }"
    >
      <div class="payment-detail-content">
        <!-- 筛选条件 -->
        <div class="filter-section">
          <div class="filter-row">
            <n-form inline :label-width="60" size="small">
              <n-form-item label="时间:">
                <n-button-group>
                  <n-button size="small" :type="paymentTimeFilter === 'today' ? 'primary' : 'default'" @click="setPaymentTimeFilter('today')">今天</n-button>
                  <n-button size="small" :type="paymentTimeFilter === 'yesterday' ? 'primary' : 'default'" @click="setPaymentTimeFilter('yesterday')">昨天</n-button>
                  <n-button size="small" :type="paymentTimeFilter === 'week' ? 'primary' : 'default'" @click="setPaymentTimeFilter('week')">近7天</n-button>
                  <n-button size="small" :type="paymentTimeFilter === 'month' ? 'primary' : 'default'" @click="setPaymentTimeFilter('month')">近30天</n-button>
                  <n-button size="small" :type="paymentTimeFilter === 'all' ? 'primary' : 'default'" @click="setPaymentTimeFilter('all')">全部</n-button>
                  <n-button size="small" :type="paymentTimeFilter === 'custom' ? 'primary' : 'default'" @click="setPaymentTimeFilter('custom')">自定义</n-button>
                </n-button-group>
              </n-form-item>
              <n-form-item label="发生门店:">
                <n-select v-model:value="paymentFilterForm.shop_id" :options="shopOptions" placeholder="全部" clearable style="width: 150px" size="small" @update:value="onPaymentFilterChange" />
              </n-form-item>
              <n-form-item label="单据类型:">
                <n-select v-model:value="paymentFilterForm.bill_type" :options="billTypeOptions" placeholder="全部" clearable style="width: 150px" size="small" @update:value="onPaymentFilterChange" />
              </n-form-item>
            </n-form>
          </div>
        </div>

        <!-- 明细表格容器 -->
        <div class="detail-table-container">
          <div class="detail-table-wrapper">
            <n-data-table
              :columns="paymentDetailColumns"
              :data="paymentDetailList"
              size="small"
              :bordered="false"
              :pagination="false"
              :loading="paymentDetailLoading"
              :scroll-x="1300"
              flex-height
            />
          </div>

          <!-- 固定在底部的分页器 -->
          <div class="detail-pagination-section">
            <n-pagination
              v-model:page="paymentDetailPagination.page"
              v-model:page-size="paymentDetailPagination.pageSize"
              :item-count="paymentDetailPagination.itemCount"
              :page-sizes="paymentDetailPagination.pageSizes"
              :show-size-picker="paymentDetailPagination.showSizePicker"
              :prefix="paymentDetailPagination.prefix"
              @update:page="paymentDetailPagination.onChange"
              @update:page-size="paymentDetailPagination.onUpdatePageSize"
              size="small"
            />
          </div>
        </div>
      </div>
    </n-modal>

    <!-- 积分明细弹窗 -->
    <n-modal
      v-model:show="pointDetailModal.show"
      preset="card"
      :title="pointDetailModal.title"
      :style="{ width: '1400px', maxWidth: '95vw', height: '80vh' }"
      :content-style="{ padding: '0', display: 'flex', flexDirection: 'column', height: '100%' }"
    >
      <div class="point-detail-content">
        <!-- 筛选条件 -->
        <div class="filter-section">
          <div class="filter-row">
            <n-form inline :label-width="60" size="small">
              <n-form-item label="时间:">
                <n-button-group>
                  <n-button size="small" :type="pointTimeFilter === 'today' ? 'primary' : 'default'" @click="setPointTimeFilter('today')">今天</n-button>
                  <n-button size="small" :type="pointTimeFilter === 'yesterday' ? 'primary' : 'default'" @click="setPointTimeFilter('yesterday')">昨天</n-button>
                  <n-button size="small" :type="pointTimeFilter === 'week' ? 'primary' : 'default'" @click="setPointTimeFilter('week')">近7天</n-button>
                  <n-button size="small" :type="pointTimeFilter === 'month' ? 'primary' : 'default'" @click="setPointTimeFilter('month')">近30天</n-button>
                  <n-button size="small" :type="pointTimeFilter === 'all' ? 'primary' : 'default'" @click="setPointTimeFilter('all')">全部</n-button>
                  <n-button size="small" :type="pointTimeFilter === 'custom' ? 'primary' : 'default'" @click="setPointTimeFilter('custom')">自定义</n-button>
                </n-button-group>
              </n-form-item>
              <n-form-item label="发生门店:">
                <n-select v-model:value="pointFilterForm.shop_id" :options="shopOptions" placeholder="全部" clearable style="width: 150px" size="small" @update:value="onPointFilterChange" />
              </n-form-item>
              <n-form-item label="单据类型:">
                <n-select v-model:value="pointFilterForm.bill_type" :options="billTypeOptions" placeholder="全部" clearable style="width: 150px" size="small" @update:value="onPointFilterChange" />
              </n-form-item>
            </n-form>
            <div class="filter-actions">
              <n-button type="success" size="small" @click="openAdjustPointModal">
                <template #icon><i class="i-mdi:star-plus"></i></template>
                调整积分
              </n-button>
            </div>
          </div>
        </div>

        <!-- 明细表格容器 -->
        <div class="detail-table-container">
          <div class="detail-table-wrapper">
            <n-data-table
              :columns="pointDetailColumns"
              :data="pointDetailList"
              size="small"
              :bordered="false"
              :pagination="false"
              :loading="pointDetailLoading"
              :scroll-x="1300"
              flex-height
            />
          </div>

          <!-- 固定在底部的分页器 -->
          <div class="detail-pagination-section">
            <n-pagination
              v-model:page="pointDetailPagination.page"
              v-model:page-size="pointDetailPagination.pageSize"
              :item-count="pointDetailPagination.itemCount"
              :page-sizes="pointDetailPagination.pageSizes"
              :show-size-picker="pointDetailPagination.showSizePicker"
              :prefix="pointDetailPagination.prefix"
              @update:page="pointDetailPagination.onChange"
              @update:page-size="pointDetailPagination.onUpdatePageSize"
              size="small"
            />
          </div>
        </div>
      </div>
    </n-modal>

    <!-- 调整积分弹窗 -->
    <n-modal v-model:show="adjustPointModal.show" preset="card" title="增减积分" :style="{ width: '400px', maxWidth: '95vw' }">
      <n-form ref="adjustPointFormRef" :model="adjustPointForm" :rules="adjustPointRules" label-placement="left" :label-width="80">
        <n-form-item label="增减数量" path="point">
          <n-input-number
            v-model:value="adjustPointForm.point"
            placeholder="若填负数则为减积分"
            :precision="0"
            style="width: 100%"
          />
          <div style="font-size: 12px; color: #999; margin-top: 4px;">若填负数则为减积分</div>
        </n-form-item>
        <n-form-item label="操作人员">
          <n-input
            :value="userStore.userInfo?.nickname || '1045'"
            readonly
            style="width: 100%"
          />
        </n-form-item>
        <n-form-item label="变更理由" path="remark">
          <n-input
            v-model:value="adjustPointForm.remark"
            type="textarea"
            placeholder="请填写变更理由"
            :rows="3"
          />
        </n-form-item>
      </n-form>
      <template #footer>
        <div style="display: flex; justify-content: flex-end; gap: 12px;">
          <n-button @click="adjustPointModal.show = false">取消</n-button>
          <n-button type="primary" :loading="adjustPointLoading" @click="handleAdjustPoint">提交</n-button>
        </div>
      </template>
    </n-modal>

    <!-- 单店卡余额明细弹窗 -->
    <n-modal
      v-model:show="storeBalanceDetailModal.show"
      preset="card"
      :title="storeBalanceDetailModal.title"
      :style="{ width: '1400px', maxWidth: '95vw', height: '80vh' }"
      :content-style="{ padding: '0', display: 'flex', flexDirection: 'column', height: '100%' }"
    >
      <div class="store-balance-detail-content">
        <!-- 筛选条件 -->
        <div class="filter-section">
          <div class="filter-row">
            <n-form inline :label-width="60" size="small">
              <n-form-item label="时间:">
                <n-button-group>
                  <n-button size="small" :type="storeBalanceTimeFilter === 'today' ? 'primary' : 'default'" @click="setStoreBalanceTimeFilter('today')">今天</n-button>
                  <n-button size="small" :type="storeBalanceTimeFilter === 'yesterday' ? 'primary' : 'default'" @click="setStoreBalanceTimeFilter('yesterday')">昨天</n-button>
                  <n-button size="small" :type="storeBalanceTimeFilter === 'week' ? 'primary' : 'default'" @click="setStoreBalanceTimeFilter('week')">近7天</n-button>
                  <n-button size="small" :type="storeBalanceTimeFilter === 'month' ? 'primary' : 'default'" @click="setStoreBalanceTimeFilter('month')">近30天</n-button>
                  <n-button size="small" :type="storeBalanceTimeFilter === 'all' ? 'primary' : 'default'" @click="setStoreBalanceTimeFilter('all')">全部</n-button>
                  <n-button size="small" :type="storeBalanceTimeFilter === 'custom' ? 'primary' : 'default'" @click="setStoreBalanceTimeFilter('custom')">自定义</n-button>
                </n-button-group>
              </n-form-item>
              <n-form-item label="发生门店:">
                <n-select v-model:value="storeBalanceFilterForm.shop_id" :options="shopOptions" placeholder="全部" clearable style="width: 150px" size="small" @update:value="onStoreBalanceFilterChange" />
              </n-form-item>
              <n-form-item label="单据类型:">
                <n-select v-model:value="storeBalanceFilterForm.bill_type" :options="billTypeOptions" placeholder="全部" clearable style="width: 150px" size="small" @update:value="onStoreBalanceFilterChange" />
              </n-form-item>
            </n-form>
          </div>
        </div>

        <!-- 明细表格容器 -->
        <div class="detail-table-container">
          <div class="detail-table-wrapper">
            <n-data-table
              :columns="storeBalanceDetailColumns"
              :data="storeBalanceDetailList"
              size="small"
              :bordered="false"
              :pagination="false"
              :loading="storeBalanceDetailLoading"
              :scroll-x="1300"
              flex-height
            />
          </div>

          <!-- 固定在底部的分页器 -->
          <div class="detail-pagination-section">
            <n-pagination
              v-model:page="storeBalanceDetailPagination.page"
              v-model:page-size="storeBalanceDetailPagination.pageSize"
              :item-count="storeBalanceDetailPagination.itemCount"
              :page-sizes="storeBalanceDetailPagination.pageSizes"
              :show-size-picker="storeBalanceDetailPagination.showSizePicker"
              :prefix="storeBalanceDetailPagination.prefix"
              @update:page="storeBalanceDetailPagination.onChange"
              @update:page-size="storeBalanceDetailPagination.onUpdatePageSize"
              size="small"
            />
          </div>
        </div>
      </div>
    </n-modal>

    <!-- 单店卡累计实付明细弹窗 -->
    <n-modal
      v-model:show="storePaidDetailModal.show"
      preset="card"
      :title="storePaidDetailModal.title"
      :style="{ width: '1400px', maxWidth: '95vw', height: '80vh' }"
      :content-style="{ padding: '0', display: 'flex', flexDirection: 'column', height: '100%' }"
    >
      <div class="store-paid-detail-content">
        <!-- 筛选条件 -->
        <div class="filter-section">
          <div class="filter-row">
            <n-form inline :label-width="60" size="small">
              <n-form-item label="时间:">
                <n-button-group>
                  <n-button size="small" :type="storePaidTimeFilter === 'today' ? 'primary' : 'default'" @click="setStorePaidTimeFilter('today')">今天</n-button>
                  <n-button size="small" :type="storePaidTimeFilter === 'yesterday' ? 'primary' : 'default'" @click="setStorePaidTimeFilter('yesterday')">昨天</n-button>
                  <n-button size="small" :type="storePaidTimeFilter === 'week' ? 'primary' : 'default'" @click="setStorePaidTimeFilter('week')">近7天</n-button>
                  <n-button size="small" :type="storePaidTimeFilter === 'month' ? 'primary' : 'default'" @click="setStorePaidTimeFilter('month')">近30天</n-button>
                  <n-button size="small" :type="storePaidTimeFilter === 'all' ? 'primary' : 'default'" @click="setStorePaidTimeFilter('all')">全部</n-button>
                  <n-button size="small" :type="storePaidTimeFilter === 'custom' ? 'primary' : 'default'" @click="setStorePaidTimeFilter('custom')">自定义</n-button>
                </n-button-group>
              </n-form-item>
              <n-form-item label="发生门店:">
                <n-select v-model:value="storePaidFilterForm.shop_id" :options="shopOptions" placeholder="全部" clearable style="width: 150px" size="small" @update:value="onStorePaidFilterChange" />
              </n-form-item>
              <n-form-item label="单据类型:">
                <n-select v-model:value="storePaidFilterForm.bill_type" :options="billTypeOptions" placeholder="全部" clearable style="width: 150px" size="small" @update:value="onStorePaidFilterChange" />
              </n-form-item>
            </n-form>
          </div>
        </div>

        <!-- 明细表格容器 -->
        <div class="detail-table-container">
          <div class="detail-table-wrapper">
            <n-data-table
              :columns="storePaidDetailColumns"
              :data="storePaidDetailList"
              size="small"
              :bordered="false"
              :pagination="false"
              :loading="storePaidDetailLoading"
              :scroll-x="1300"
              flex-height
            />
          </div>

          <!-- 固定在底部的分页器 -->
          <div class="detail-pagination-section">
            <n-pagination
              v-model:page="storePaidDetailPagination.page"
              v-model:page-size="storePaidDetailPagination.pageSize"
              :item-count="storePaidDetailPagination.itemCount"
              :page-sizes="storePaidDetailPagination.pageSizes"
              :show-size-picker="storePaidDetailPagination.showSizePicker"
              :prefix="storePaidDetailPagination.prefix"
              @update:page="storePaidDetailPagination.onChange"
              @update:page-size="storePaidDetailPagination.onUpdatePageSize"
              size="small"
            />
          </div>
        </div>
      </div>
    </n-modal>

    <!-- 编辑基本信息弹窗 -->
    <n-modal v-model:show="editBasicInfoModal.show" preset="card" title="编辑基本信息" :style="{ width: '800px', maxWidth: '95vw' }">
      <n-form ref="editBasicInfoFormRef" :model="editBasicInfoForm" :rules="editBasicInfoRules" label-placement="left" :label-width="80">
        <div class="form-row">
          <n-form-item label="姓名" path="name" class="form-item-half">
            <n-input v-model:value="editBasicInfoForm.name" placeholder="请输入姓名" />
          </n-form-item>
          <n-form-item label="手机号" path="phone" class="form-item-half">
            <n-input v-model:value="editBasicInfoForm.phone" placeholder="请输入手机号" />
          </n-form-item>
          <n-form-item label="性别" class="form-item-half">
            <n-select v-model:value="editBasicInfoForm.gender" :options="genderOptions" placeholder="请选择性别" />
          </n-form-item>
        </div>

        <div class="form-row">
          <n-form-item label="证件类型" class="form-item-half">
            <n-select v-model:value="editBasicInfoForm.identification_type" :options="idTypeOptions" placeholder="请选择证件类型" />
          </n-form-item>
          <n-form-item label="证件号码" path="identification_number" class="form-item-half">
            <n-input v-model:value="editBasicInfoForm.identification_number" placeholder="请输入证件号码" />
          </n-form-item>
          <n-form-item label="销售员" class="form-item-half">
            <n-select v-model:value="editBasicInfoForm.develop_admin_id" :options="adminOptions" placeholder="请选择销售员" clearable />
          </n-form-item>
        </div>

        <div class="form-row">
          <n-form-item label="发展途径" class="form-item-half">
            <n-select v-model:value="editBasicInfoForm.user_source" :options="userSourceOptions" placeholder="请选择发展途径" clearable />
          </n-form-item>
          <n-form-item label="民族" class="form-item-half">
            <n-select v-model:value="editBasicInfoForm.nation" :options="nationOptions" placeholder="请选择民族" clearable />
          </n-form-item>
          <n-form-item label="生日" class="form-item-half">
            <n-date-picker v-model:value="editBasicInfoForm.birthday" type="date" placeholder="请选择生日" format="yyyy-MM-dd" />
          </n-form-item>
        </div>

        <div class="form-row">
          <n-form-item label="车牌" class="form-item-full">
            <n-input v-model:value="editBasicInfoForm.car_number" placeholder="请输入车牌号" />
          </n-form-item>
        </div>

        <div class="form-row">
          <n-form-item label="爱好" class="form-item-full">
            <n-input v-model:value="editBasicInfoForm.hobby" placeholder="请输入爱好" />
          </n-form-item>
        </div>

        <div class="form-row">
          <n-form-item label="备注" class="form-item-full">
            <n-input v-model:value="editBasicInfoForm.remark" type="textarea" placeholder="请输入备注" :rows="3" />
          </n-form-item>
        </div>
      </n-form>
      <template #footer>
        <div style="display: flex; justify-content: flex-end; gap: 12px;">
          <n-button @click="editBasicInfoModal.show = false">取消</n-button>
          <n-button type="primary" :loading="editBasicInfoLoading" @click="handleUpdateBasicInfo">确定</n-button>
        </div>
      </template>
    </n-modal>

    <!-- 编辑会员等级弹窗 -->
    <n-modal v-model:show="editMemberGradeModal.show" preset="card" title="编辑会员等级" :style="{ width: '400px', maxWidth: '95vw' }">
      <n-form ref="editMemberGradeFormRef" :model="editMemberGradeForm" :rules="editMemberGradeRules" label-placement="left" :label-width="80">
        <n-form-item label="会员等级" path="grade_id">
          <n-select
            v-model:value="editMemberGradeForm.grade_id"
            :options="memberGradeOptions"
            placeholder="请选择会员等级"
          />
        </n-form-item>
      </n-form>
      <template #footer>
        <div style="display: flex; justify-content: flex-end; gap: 12px;">
          <n-button @click="editMemberGradeModal.show = false">取消</n-button>
          <n-button type="primary" :loading="editMemberGradeLoading" @click="handleUpdateMemberGrade">确定</n-button>
        </div>
      </template>
    </n-modal>

    <!-- 操作日志弹窗 -->
    <n-modal
      v-model:show="operationLogModal.show"
      preset="card"
      :title="`用户操作日志-${memberInfo.name || '未知用户'}`"
      :style="{ width: '1000px', maxWidth: '95vw' }"
      :mask-closable="false"
    >
      <div class="operation-log-content">
        <!-- 筛选条件 -->
        <div class="filter-section">
          <div class="filter-row">
            <n-form inline :label-width="60" size="small">
              <n-form-item label="时间:">
                <n-button-group>
                  <n-button size="small" :type="logTimeFilter === 'unlimited' ? 'primary' : 'default'" @click="setLogTimeFilter('unlimited')">不限</n-button>
                  <n-button size="small" :type="logTimeFilter === 'lastMonth' ? 'primary' : 'default'" @click="setLogTimeFilter('lastMonth')">上月</n-button>
                  <n-button size="small" :type="logTimeFilter === 'nearWeek' ? 'primary' : 'default'" @click="setLogTimeFilter('nearWeek')">近七天</n-button>
                  <n-button size="small" :type="logTimeFilter === 'nearMonth' ? 'primary' : 'default'" @click="setLogTimeFilter('nearMonth')">近三天</n-button>
                  <n-button size="small" :type="logTimeFilter === 'yesterday' ? 'primary' : 'default'" @click="setLogTimeFilter('yesterday')">昨天</n-button>
                  <n-button size="small" :type="logTimeFilter === 'today' ? 'primary' : 'default'" @click="setLogTimeFilter('today')">今天</n-button>
                  <n-button size="small" :type="logTimeFilter === 'thisMonth' ? 'primary' : 'default'" @click="setLogTimeFilter('thisMonth')">本月</n-button>
                  <n-button size="small" :type="logTimeFilter === 'custom' ? 'primary' : 'default'" @click="setLogTimeFilter('custom')">自定义</n-button>
                </n-button-group>
              </n-form-item>
              <n-form-item label="操作类型:">
                <n-select
                  v-model:value="logFilterForm.type"
                  :options="logTypeOptions"
                  placeholder="全部"
                  clearable
                  style="width: 150px"
                  size="small"
                  @update:value="onLogFilterChange"
                />
              </n-form-item>
            </n-form>
          </div>

          <!-- 自定义时间选择 -->
          <div v-if="logTimeFilter === 'custom'" class="custom-time-row">
            <n-form inline size="small">
              <n-form-item label="开始时间:">
                <n-date-picker
                  v-model:value="logFilterForm.start_time"
                  type="datetime"
                  placeholder="选择开始时间"
                  style="width: 200px"
                  @update:value="onLogFilterChange"
                />
              </n-form-item>
              <n-form-item label="结束时间:">
                <n-date-picker
                  v-model:value="logFilterForm.end_time"
                  type="datetime"
                  placeholder="选择结束时间"
                  style="width: 200px"
                  @update:value="onLogFilterChange"
                />
              </n-form-item>
            </n-form>
          </div>
        </div>

        <!-- 日志表格 -->
        <div class="log-table-container">
          <n-data-table
            :columns="operationLogColumns"
            :data="operationLogData"
            :loading="operationLogLoading"
            :pagination="operationLogPagination"
            :scroll-x="800"
            size="small"
            striped
            remote
            flex-height
            style="height: 100%"
          />
        </div>
      </div>
    </n-modal>

  </n-modal>
</template>

<script setup>
import { ref, watch, computed, h } from 'vue'
import { useMessage, useDialog, NTag } from 'naive-ui'

import { useUserStore } from '@/store'
import { request } from '@/utils/http'

// 权益与优惠券数据（来自 getUserInfo）
const gradeInfo = ref([])
const couponsList = ref([])
const gradeModal = ref(false)
const couponModal = ref(false)
const gradeCount = computed(() => Array.isArray(gradeInfo.value) ? gradeInfo.value.length : 0)
const couponCount = computed(() => Array.isArray(couponsList.value) ? couponsList.value.length : 0)
function openGradeModal() { gradeModal.value = true }
async function openCouponModal() {
  couponModal.value = true
  // 重置分页状态
  couponPaginationState.value.page = 1
  couponPaginationState.value.itemCount = 0
  await fetchUserCoupons(1, couponPaginationState.value.pageSize)
}

// 使用message和dialog
const message = useMessage()
const dialog = useDialog()
// 权益弹窗标题与列
const gradeModalTitle = computed(() => {
  const name = memberInfo.value?.name || memberInfo.value?.nickname || ''
  return name ? `会员权益 - ${name}` : '会员权益'
})
const gradeTable = computed(() => {
  const list = Array.isArray(gradeInfo.value) ? gradeInfo.value : []
  return list.map((it, idx) => ({
    key: idx,
    icon: it.icon || it.icon_url || '',
    label: it.label || it.right_label || it.title || '-',
    name: it.name || it.right_name || it.title || '-',
    desc: it.desc || it.right_desc || it.description || '-',
    value: it.value || it.right_value || it.num || it.percent || '',
    status: it.enable ?? it.enabled ?? it.status ?? true
  }))
})
const gradeColumns = [
  { title: '权益图标', key: 'icon', width: 90, render(row){
      const icon = row.icon
      if (typeof icon === 'string' && (icon.startsWith('http://') || icon.startsWith('https://'))) {
        return h('img', { src: icon, style: 'width:24px;height:24px;object-fit:contain;' })
      }
      return h('i', { class: icon || 'i-mdi:checkbox-blank-circle-outline', style: 'font-size:22px;color:#64748b;' })
    }
  },
  { title: '权益名称', key: 'name', minWidth: 140 },
  { title: '权益描述', key: 'desc', minWidth: 260 },
  { title: '值', key: 'value', width: 100 },
  { title: '状态', key: 'status', width: 100, render(row){
      return h('i', { class: row.status ? 'i-mdi:check-circle' : 'i-mdi:close-circle', style: `font-size:18px;${row.status ? 'color:#16a34a' : 'color:#ef4444'}` })
    }
  },
]

// 优惠券表格列
const couponColumns = [
  { title: 'id', key: 'id', width: 80 },
  { title: '名称', key: 'title', minWidth: 140 },
  { title: '类型', key: 'type_name', minWidth: 120 },
  { title: '金额', key: 'amount', width: 100 },
  { title: '使用门槛', key: 'threshold', minWidth: 120 },
  { title: '可用星期', key: 'available_week', minWidth: 120 },
  { title: '时间限制', key: 'time_limit', minWidth: 120 },
  { title: '过期时间', key: 'expire_time', minWidth: 160 },
]

// 优惠券表格数据映射
const couponTable = computed(() => {
  const list = Array.isArray(couponsList.value) ? couponsList.value : []
  return list.map((c) => {
    const typeId = c.type_id ?? c.coupon_info?.type_id
    const typeName = typeId === 1 ? '订房优惠券' : (c.type_name || '优惠券')
    const weekArr = Array.isArray(c.usable_week) ? c.usable_week : []
    const weekText = weekArr.length ? weekArr.map(w => `周${w}`).join('、') : '无限制'
    const timeText = c.usable_time || c.time_limit || '无限制'
    const amount = Number(c.discounts ?? c.amount ?? 0).toFixed(2)
    const threshold = typeof c.use_condition === 'string' || typeof c.use_condition === 'number'
      ? Number(c.use_condition).toFixed(2)
      : (c.threshold_text || '0.00')
    const name = c.coupon_name || c.title || c.coupon_info?.name || '-'

    return {
      id: c.id || c.coupon_id || '-',
      title: name,
      type_name: typeName,
      amount,
      threshold,
      available_week: weekText,
      time_limit: timeText,
      expire_time: c.limit_time ? formatDateTime(c.limit_time) : (c.expire_time || c.expired_at || c.valid_to || '-')
    }
  })
})
// 优惠券分页状态
const couponPaginationState = ref({
  page: 1,
  pageSize: 10,
  itemCount: 0
})

// 优惠券分页配置
const couponPagination = computed(() => ({
  page: couponPaginationState.value.page,
  pageSize: couponPaginationState.value.pageSize,
  itemCount: couponPaginationState.value.itemCount,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  prefix: ({ itemCount }) => `共 ${itemCount} 条`,
  onChange: (page) => {
    couponPaginationState.value.page = page
    fetchUserCoupons(page, couponPaginationState.value.pageSize)
  },
  onUpdatePageSize: (pageSize) => {
    couponPaginationState.value.pageSize = pageSize
    couponPaginationState.value.page = 1
    fetchUserCoupons(1, pageSize)
  }
}))

// 优惠券总数
const couponTableCount = computed(() => couponPaginationState.value.itemCount || couponTable.value.length)

const userStore = useUserStore()

// 优惠券弹窗标题
defineOptions({})
const couponModalTitle = computed(() => {
  const name = memberInfo.value?.name || memberInfo.value?.nickname || ''
  return name ? `可用优惠券列表 - ${name}` : '可用优惠券列表'
})

// 余额明细弹窗相关数据
const balanceDetailModal = ref({
  show: false,
  title: '余额明细',
  scope: 'chain',
  type: 'balance'
})

const timeFilter = ref('all')
const filterForm = ref({
  shop_id: '',
  bill_type: '',
  start_time: '',
  end_time: ''
})

const shopList = ref([])
const billTypeList = ref([])
const balanceDetailList = ref([])
const balanceDetailLoading = ref(false)

// 手动调整余额相关数据
const adjustBalanceModal = ref({
  show: false
})

const adjustBalanceForm = ref({
  type: '1', // 1充值金额，2赠送余额
  amount: null,
  remark: ''
})

const adjustBalanceLoading = ref(false)
const adjustFormRef = ref(null)

// 打印相关数据
const printSelectModal = ref({
  show: false,
  currentRow: null
})

const a4PrintModal = ref({
  show: false
})

const printData = ref({
  memberName: '',
  phone: '',
  cardNumber: '',
  rechargeAmount: '',
  giftAmount: '',
  giftPoints: '',
  orderNumber: '',
  orderTime: '',
  printTime: '',
  operator: ''
})

// 实付款明细相关数据
const paymentDetailModal = ref({
  show: false,
  title: '累计金额明细'
})

const paymentDetailList = ref([])
const paymentDetailLoading = ref(false)
const paymentTimeFilter = ref('all')

const paymentFilterForm = ref({
  shop_id: '',
  bill_type: '',
  start_time: '',
  end_time: ''
})

// 实付款明细分页
const paymentDetailPagination = ref({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  prefix: ({ itemCount }) => `共 ${itemCount} 条`,
  onChange: (page) => {
    paymentDetailPagination.value.page = page
    fetchPaymentRecords()
  },
  onUpdatePageSize: (pageSize) => {
    paymentDetailPagination.value.pageSize = pageSize
    paymentDetailPagination.value.page = 1
    fetchPaymentRecords()
  }
})

// 积分明细相关数据
const pointDetailModal = ref({
  show: false,
  title: '积分明细'
})

const pointDetailList = ref([])
const pointDetailLoading = ref(false)
const pointTimeFilter = ref('all')

const pointFilterForm = ref({
  shop_id: '',
  bill_type: '',
  start_time: '',
  end_time: ''
})

// 积分明细分页
const pointDetailPagination = ref({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  prefix: ({ itemCount }) => `共 ${itemCount} 条`,
  onChange: (page) => {
    pointDetailPagination.value.page = page
    fetchPointRecords()
  },
  onUpdatePageSize: (pageSize) => {
    pointDetailPagination.value.pageSize = pageSize
    pointDetailPagination.value.page = 1
    fetchPointRecords()
  }
})

// 调整积分相关数据
const adjustPointModal = ref({
  show: false
})

const adjustPointForm = ref({
  point: null,
  remark: ''
})

const adjustPointLoading = ref(false)
const adjustPointFormRef = ref(null)

// 调整积分表单验证规则
const adjustPointRules = {
  point: {
    required: true,
    type: 'number',
    message: '请输入有效的积分数量',
    trigger: ['blur', 'change']
  },
  remark: {
    required: true,
    message: '请输入变更理由',
    trigger: 'blur'
  }
}

// 单店卡余额明细相关数据
const storeBalanceDetailModal = ref({
  show: false,
  title: '单店卡余额明细'
})

const storeBalanceDetailList = ref([])
const storeBalanceDetailLoading = ref(false)
const storeBalanceTimeFilter = ref('all')

const storeBalanceFilterForm = ref({
  shop_id: '',
  bill_type: '',
  start_time: '',
  end_time: ''
})

// 单店卡余额明细分页
const storeBalanceDetailPagination = ref({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  prefix: ({ itemCount }) => `共 ${itemCount} 条`,
  onChange: (page) => {
    storeBalanceDetailPagination.value.page = page
    fetchStoreBalanceRecords()
  },
  onUpdatePageSize: (pageSize) => {
    storeBalanceDetailPagination.value.pageSize = pageSize
    storeBalanceDetailPagination.value.page = 1
    fetchStoreBalanceRecords()
  }
})

// 单店卡累计实付明细相关数据
const storePaidDetailModal = ref({
  show: false,
  title: '单店卡累计实付明细'
})

const storePaidDetailList = ref([])
const storePaidDetailLoading = ref(false)
const storePaidTimeFilter = ref('all')

const storePaidFilterForm = ref({
  shop_id: '',
  bill_type: '',
  start_time: '',
  end_time: ''
})

// 单店卡累计实付明细分页
const storePaidDetailPagination = ref({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  prefix: ({ itemCount }) => `共 ${itemCount} 条`,
  onChange: (page) => {
    storePaidDetailPagination.value.page = page
    fetchStorePaidRecords()
  },
  onUpdatePageSize: (pageSize) => {
    storePaidDetailPagination.value.pageSize = pageSize
    storePaidDetailPagination.value.page = 1
    fetchStorePaidRecords()
  }
})

// 调整余额表单验证规则
const adjustBalanceRules = {
  type: {
    required: true,
    message: '请选择调整类型',
    trigger: 'change'
  },
  amount: {
    required: true,
    type: 'number',
    message: '请输入有效的调整金额',
    trigger: ['blur', 'change']
  },
  remark: {
    required: true,
    message: '请输入备注说明',
    trigger: 'blur'
  }
}

// 性别选项
const genderOptions = [
  { label: '男', value: 1 },
  { label: '女', value: 2 }
]

// 证件类型选项
const idTypeOptions = [
  { label: '身份证', value: 1 },
  { label: '护照', value: 2 },
  { label: '军官证', value: 3 },
  { label: '其他', value: 4 }
]

// 销售员选项
const adminOptions = computed(() => [
  { label: '无', value: 0 },
  ...adminList.value.map(admin => {
    // 格式：name(nick_name)
    const name = admin.name || ''
    const nickname = admin.nick_name || admin.nickname || ''
    const label = nickname ? `${name}(${nickname})` : name

    return {
      label: label,
      value: admin.id
    }
  })
])

// 发展途径选项
const userSourceOptions = computed(() =>
  userSourceList.value.map(source => ({
    label: source.source_name,
    value: source.id
  }))
)

// 民族选项
const nationOptions = computed(() =>
  nationList.value.map(nation => ({
    label: nation.nation_name,
    value: nation.id
  }))
)

// 会员等级选项
const memberGradeOptions = computed(() =>
  memberGradeList.value.map(grade => ({
    label: grade.grade_name,
    value: grade.id
  }))
)

// 酒店选项
const shopOptions = computed(() => [
  { label: '全部', value: '' },
  ...shopList.value.map(shop => ({
    label: shop.shop_name,
    value: shop.id
  }))
])

// 单据类型选项
const billTypeOptions = computed(() => [
  { label: '全部', value: '' },
  ...billTypeList.value.map(type => ({
    label: type.bill_type_name,
    value: type.id
  }))
])

// 获取销售员名称
function getSalespersonName(adminId) {
  if (!adminId || adminId === 0) return '无'

  const admin = adminList.value.find(item => item.id === adminId)
  if (!admin) return '无'

  const name = admin.name || ''
  const nickname = admin.nickname || ''
  return nickname ? `${name}(${nickname})` : name
}

// 操作日志类型选项
const logTypeOptions = [
  { label: '全部', value: '' },
  { label: '修改等级', value: 1 },
  { label: '修改信息', value: 2 }
]

// 操作日志表格列
const operationLogColumns = [
  { title: 'id', key: 'id', width: 60 },
  { title: '内容', key: 'manipulation', minWidth: 200 },
  {
    title: '操作人',
    key: 'operator',
    width: 150,
    render: (row) => {
      const name = row.admin_name || ''
      const nickname = row.admin_nickname || ''
      return nickname ? `${name}(${nickname})` : name
    }
  },
  {
    title: '其他信息',
    key: 'other_info',
    width: 200,
    render: (row) => {
      return h('div', { class: 'other-info-cell' }, [
        h('div', `IP: ${row.ip || '-'}`),
        h('div', `位置: ${row.position || '-'}`),
        h('div', `浏览器: ${row.browser || '-'}`)
      ])
    }
  },
  {
    title: '时间',
    key: 'create_time',
    width: 160,
    render: (row) => {
      return formatDateTime(row.create_time) || row.create_time
    }
  }
]

// 余额明细分页
const balanceDetailPagination = ref({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  prefix: ({ itemCount }) => `共 ${itemCount} 条`,
  onChange: (page) => {
    balanceDetailPagination.value.page = page
    fetchBalanceRecords()
  },
  onUpdatePageSize: (pageSize) => {
    balanceDetailPagination.value.pageSize = pageSize
    balanceDetailPagination.value.page = 1
    fetchBalanceRecords()
  }
})

// 余额明细表格列
const balanceDetailColumns = [
  { title: 'id', key: 'id', width: 60 },
  { title: '描述', key: 'change_reason', minWidth: 200 },
  { title: '变动', key: 'change', width: 100, render: (row) => {
    const change = Number(row.change || 0)
    return h('span', {
      style: { color: change >= 0 ? '#16a34a' : '#ef4444', fontWeight: '600' }
    }, change >= 0 ? `+${change}` : `${change}`)
  }},
  { title: '余额', key: 'balance', width: 100 },
  { title: '变动原因', key: 'bill_type_name', minWidth: 150 },
  { title: '订单编号', key: 'bill_id', width: 120 },
  { title: '订单id', key: 'item_detail_id', width: 80 },
  { title: '操作员', key: 'admin_nickname', width: 100 },
  { title: '时间', key: 'create_time', width: 160, render: (row) => {
    return formatDateTime(row.create_time) || row.create_time
  }},
  { title: '操作', key: 'actions', width: 140, render: (row) => {
    const buttons = [
      h('span', {
        class: 'action-btn action-btn--print',
        onClick: () => handlePrint(row)
      }, '打印')
    ]

    // 只有会员充值类型才显示撤销按钮
    if (canRevoke(row)) {
      buttons.push(
        h('span', {
          class: 'action-btn action-btn--revoke',
          onClick: () => handleRevokeRecharge(row)
        }, '撤销')
      )
    }

    return h('div', { class: 'action-buttons' }, buttons)
  }}
]

// 实付款明细表格列
const paymentDetailColumns = [
  { title: 'id', key: 'id', width: 60 },
  { title: '增加', key: 'change', width: 80, render: (row) => {
    const change = Number(row.change || 0)
    return change > 0 ? change : ''
  }},
  { title: '减少', key: 'change', width: 80, render: (row) => {
    const change = Number(row.change || 0)
    return change < 0 ? Math.abs(change) : ''
  }},
  { title: '金额', key: 'change', width: 100, render: (row) => {
    const change = Number(row.change || 0)
    return h('span', {
      style: { color: change >= 0 ? '#16a34a' : '#ef4444', fontWeight: '600' }
    }, change >= 0 ? `+${change}` : `${change}`)
  }},
  { title: '变更原因', key: 'change_reason', minWidth: 200 },
  { title: '订单类型', key: 'bill_type_name', width: 120 },
  { title: '订单id', key: 'item_detail_id', width: 80 },
  { title: '操作员', key: 'admin_nickname', width: 100 },
  { title: '时间', key: 'create_time', width: 160, render: (row) => {
    return formatDateTime(row.create_time) || row.create_time
  }},
  { title: '操作', key: 'actions', width: 80, render: (row) => {
    return h('span', { class: 'action-text' }, '无操作')
  }}
]

// 积分明细表格列
const pointDetailColumns = [
  { title: 'id', key: 'id', width: 60 },
  { title: '增加', key: 'change', width: 80, render: (row) => {
    const change = Number(row.change || 0)
    return change > 0 ? change : ''
  }},
  { title: '减少', key: 'change', width: 80, render: (row) => {
    const change = Number(row.change || 0)
    return change < 0 ? Math.abs(change) : ''
  }},
  { title: '当前值', key: 'points', width: 100 },
  { title: '变更原因', key: 'change_reason', minWidth: 200 },
  { title: '订单类型', key: 'bill_type_name', width: 120 },
  { title: '订单id', key: 'bill_id', width: 80 },
  { title: '操作员', key: 'admin_name', width: 100 },
  { title: '时间', key: 'create_time', width: 160, render: (row) => {
    return formatDateTime(row.create_time) || row.create_time
  }}
]

// 单店卡余额明细表格列（与跨店卡余额明细完全一致）
const storeBalanceDetailColumns = [
  { title: 'id', key: 'id', width: 60 },
  { title: '增加', key: 'change', width: 80, render: (row) => {
    const change = Number(row.change || 0)
    return change > 0 ? change : ''
  }},
  { title: '减少', key: 'change', width: 80, render: (row) => {
    const change = Number(row.change || 0)
    return change < 0 ? Math.abs(change) : ''
  }},
  { title: '金额', key: 'change', width: 100, render: (row) => {
    const change = Number(row.change || 0)
    return h('span', {
      style: { color: change >= 0 ? '#16a34a' : '#ef4444', fontWeight: '600' }
    }, change >= 0 ? `+${change}` : `${change}`)
  }},
  { title: '变更原因', key: 'change_reason', minWidth: 200 },
  { title: '订单类型', key: 'bill_type_name', width: 120 },
  { title: '订单id', key: 'item_detail_id', width: 80 },
  { title: '操作员', key: 'admin_nickname', width: 100 },
  { title: '时间', key: 'create_time', width: 160, render: (row) => {
    return formatDateTime(row.create_time) || row.create_time
  }},
  { title: '操作', key: 'actions', width: 140, render: (row) => {
    const buttons = [
      h('span', {
        class: 'action-btn action-btn--print',
        onClick: () => handlePrint(row)
      }, '打印')
    ]

    // 只有会员充值类型才显示撤销按钮
    if (canRevoke(row)) {
      buttons.push(
        h('span', {
          class: 'action-btn action-btn--revoke',
          onClick: () => handleRevokeRecharge(row)
        }, '撤销')
      )
    }

    return h('div', { class: 'action-buttons' }, buttons)
  }}
]

// 单店卡累计实付明细表格列（与跨店卡累计金额明细完全一致）
const storePaidDetailColumns = [
  { title: 'id', key: 'id', width: 60 },
  { title: '增加', key: 'change', width: 80, render: (row) => {
    const change = Number(row.change || 0)
    return change > 0 ? change : ''
  }},
  { title: '减少', key: 'change', width: 80, render: (row) => {
    const change = Number(row.change || 0)
    return change < 0 ? Math.abs(change) : ''
  }},
  { title: '金额', key: 'change', width: 100, render: (row) => {
    const change = Number(row.change || 0)
    return h('span', {
      style: { color: change >= 0 ? '#16a34a' : '#ef4444', fontWeight: '600' }
    }, change >= 0 ? `+${change}` : `${change}`)
  }},
  { title: '变更原因', key: 'change_reason', minWidth: 200 },
  { title: '订单类型', key: 'bill_type_name', width: 120 },
  { title: '订单id', key: 'item_detail_id', width: 80 },
  { title: '操作员', key: 'admin_nickname', width: 100 },
  { title: '时间', key: 'create_time', width: 160, render: (row) => {
    return formatDateTime(row.create_time) || row.create_time
  }},
  { title: '操作', key: 'actions', width: 140, render: (row) => {
    const buttons = [
      h('span', {
        class: 'action-btn action-btn--print',
        onClick: () => handlePrint(row)
      }, '打印')
    ]

    // 只有会员充值类型才显示撤销按钮
    if (canRevoke(row)) {
      buttons.push(
        h('span', {
          class: 'action-btn action-btn--revoke',
          onClick: () => handleRevokeRecharge(row)
        }, '撤销')
      )
    }

    return h('div', { class: 'action-buttons' }, buttons)
  }}
]

// 编辑基本信息相关数据
const editBasicInfoModal = ref({
  show: false
})

const editBasicInfoForm = ref({
  name: '',
  phone: '',
  gender: null,
  identification_type: 1,
  identification_number: '',
  develop_admin_id: null,
  user_source: null,
  nation: null,
  birthday: null,
  hobby: '',
  car_number: '',
  remark: ''
})

const editBasicInfoLoading = ref(false)
const editBasicInfoFormRef = ref(null)

// 基础数据列表
const adminList = ref([])
const userSourceList = ref([])
const nationList = ref([])
const memberGradeList = ref([])

// 编辑会员等级相关数据
const editMemberGradeModal = ref({
  show: false
})

const editMemberGradeForm = ref({
  grade_id: null
})

const editMemberGradeLoading = ref(false)
const editMemberGradeFormRef = ref(null)

// 黑名单相关数据
const blockLoading = ref(false)

// 操作日志相关数据
const operationLogModal = ref({
  show: false
})

const operationLogData = ref([])
const operationLogLoading = ref(false)
const logTimeFilter = ref('unlimited')
const logFilterForm = ref({
  start_time: null,
  end_time: null,
  type: ''
})

// 操作日志分页
const operationLogPagination = ref({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  prefix: ({ itemCount }) => `共 ${itemCount} 条`,
  onChange: (page) => {
    operationLogPagination.value.page = page
    fetchOperationLogs()
  },
  onUpdatePageSize: (pageSize) => {
    operationLogPagination.value.pageSize = pageSize
    operationLogPagination.value.page = 1
    fetchOperationLogs()
  }
})



// 编辑基本信息表单验证规则
const editBasicInfoRules = {
  name: {
    required: true,
    message: '请输入姓名',
    trigger: 'blur'
  },
  phone: {
    required: true,
    message: '请输入手机号',
    trigger: 'blur'
  }
}

// 编辑会员等级表单验证规则
const editMemberGradeRules = {
  grade_id: {
    required: true,
    type: 'number',
    message: '请选择会员等级',
    trigger: 'change'
  }
}

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  commonCode: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:show'])

// 状态
const visible = ref(false)
const loading = ref(false)
const memberInfo = ref({})
const memberHistory = ref([])

// 计算属性 - 确保数据安全显示
const safeDisplayValue = (value, defaultValue = '-') => {
  if (value === null || value === undefined || value === '') {
    return defaultValue
  }

  // 如果是对象，尝试提取有意义的值
  if (typeof value === 'object' && value !== null) {
    // 如果是数组，返回数组长度或第一个元素
    if (Array.isArray(value)) {
      return value.length > 0 ? value[0] : defaultValue
    }

    // 如果是对象，尝试提取常见字段
    if (value.value !== undefined) return value.value
    if (value.text !== undefined) return value.text
    if (value.name !== undefined) return value.name
    if (value.title !== undefined) return value.title

    // 如果对象只有一个属性，返回该属性的值
    const keys = Object.keys(value)
    if (keys.length === 1) {
      return value[keys[0]]
    }

    // 否则返回默认值
    return defaultValue
  }

  return value
}

// 余额显示（兼容多种字段命名，包含嵌套路径）
function getNested(obj, path) {
  if (!obj) return undefined
  const parts = typeof path === 'string' ? path.split('.') : path
  let cur = obj
  for (const k of parts) {
    if (cur && Object.prototype.hasOwnProperty.call(cur, k)) {
      cur = cur[k]
    } else {
      return undefined
    }
  }
  return cur
}
function pickFirstNumber(obj, paths) {
  for (const p of paths) {
    const v = getNested(obj, p)
    if (v !== undefined && v !== null && v !== '') {
      const n = Number(v)
      if (Number.isFinite(n)) return n
    }
  }
  return 0
}

// 跨店卡：本金和赠送余额
const chainPrincipal = computed(() => {
  const m = memberInfo.value || {}
  return pickFirstNumber(m, [ 'real_balance', 'balance.chain_principal', 'chain_principal' ])
})
const chainGift = computed(() => {
  const m = memberInfo.value || {}
  return pickFirstNumber(m, [ 'gift_balance', 'balance.chain_gift', 'chain_gift' ])
})
// 单店卡余额
const storeBalance = computed(() => {
  const m = memberInfo.value || {}
  return pickFirstNumber(m, [ 'user_shop_balance.balance', 'user_shop_balance', 'store_balance', 'balance.store', 'local_balance' ])
})

// 跨店卡总余额 = 本金 + 赠送
const chainTotal = computed(() => {
  return Number(chainPrincipal.value || 0) + Number(chainGift.value || 0)
})

// 单店卡累计实付
const storePaidAmount = computed(() => {
  const m = memberInfo.value || {}
  return pickFirstNumber(m, [ 'consume.independent_consumer_amount', 'independent_consumer_amount' ])
})

// 获取备注信息
const getRemarkText = (memberInfo) => {
  if (!memberInfo) return ''

  // 如果remark是字符串，直接返回
  if (typeof memberInfo.remark === 'string') {
    return memberInfo.remark
  }

  // 如果remark是对象，提取remark字段
  if (typeof memberInfo.remark === 'object' && memberInfo.remark && memberInfo.remark.remark) {
    return memberInfo.remark.remark
  }

  return ''
}

// 判断是否在黑名单中
const isBlocked = computed(() => {
  return memberInfo.value?.is_block === 1 || memberInfo.value?.block === 1
})



// 获取优惠券数量
const getCouponsCount = (memberInfo) => {
  if (!memberInfo) return 0

  const coupons = memberInfo.coupons

  // 如果是数字，直接返回
  if (typeof coupons === 'number') {
    return coupons
  }

  // 如果是字符串数字，转换为数字
  if (typeof coupons === 'string' && !isNaN(coupons)) {
    return parseInt(coupons) || 0
  }

  // 如果是对象，尝试提取数量相关字段
  if (typeof coupons === 'object' && coupons) {
    // 尝试常见的字段名


    if (typeof coupons.count === 'number') return coupons.count
    if (typeof coupons.total === 'number') return coupons.total
    if (typeof coupons.num === 'number') return coupons.num
    if (typeof coupons.amount === 'number') return coupons.amount

    // 如果是数组，返回数组长度
    if (Array.isArray(coupons)) return coupons.length
  }

  return 0
}

// 监听显示状态
watch(() => props.show, (newVal) => {
  visible.value = newVal
  if (newVal && props.commonCode) {
    fetchMemberHistory();
    fetchMemberInfo(); // 仅用于 grade_info 与 coupons
  }
})

// 关闭弹窗
function handleClose(show) {
  emit('update:show', show)
  if (!show) {
    memberInfo.value = {}
    memberHistory.value = []
  }
}

// 顶部卡片操作与充值弹窗
const rechargeModal = ref({ show: false, scope: 'chain' })
const rechargeLoading = ref(false)
const rechargeList = ref([])

const getScopeLabel = (scope) => (scope === 'chain' ? '跨店卡' : '单店卡')

const operatorName = computed(() => userStore.nickName || userStore.username || '—')



async function fetchRechargeSettings(scope = rechargeModal.value.scope) {
  rechargeLoading.value = true
  try {
    const endpoint = scope === 'store'
      ? '/admin/IndependentRecharge/getMemberRechargeSettingList'
      : '/admin/MemberRecharge/getMemberRechargeSettingList'

    const params = {
      page: 1,
      limit: 1000,
      status: '1'
    }

    // 单店卡充值列表需要添加type=3参数
    if (scope === 'store') {
      params.type = 3
    }

    const res = await request.post(endpoint, params, { needToken: true, needTip: false })
    if (res.code === 0) {
      rechargeList.value = res.data?.list || []
    }
  } catch (e) {
    message.error(e?.message || '获取充值套餐失败')
  } finally {
    rechargeLoading.value = false
  }
}

function handleRecharge(scope) {
  rechargeModal.value = { show: true, scope }
  fetchRechargeSettings(scope)
}
function handleViewCardDetails(scope, type = 'all') {
  const scopeLabel = getScopeLabel(scope)
  let detailLabel = ''

  switch (type) {
    case 'balance':
      detailLabel = scope === 'chain' ? '总余额' : '储值金额'
      break
    case 'total_amount':
      detailLabel = '累计金额'
      break
    case 'points':
      detailLabel = '累计积分'
      break
    case 'paid_amount':
      detailLabel = '累计实付'
      break
    default:
      detailLabel = '全部'
  }

  // 如果是余额明细，打开明细弹窗
  if (type === 'balance') {
    openBalanceDetailModal(scope, type, detailLabel)
  } else {
    message.info(`查看${scopeLabel}${detailLabel}明细，功能开发中`)
  }
}

// 打开余额明细弹窗
async function openBalanceDetailModal(scope, type, detailLabel) {
  const scopeLabel = getScopeLabel(scope)
  balanceDetailModal.value = {
    show: true,
    title: `${scopeLabel}${detailLabel}明细 - ${memberInfo.value?.name || ''}`,
    scope,
    type
  }

  // 重置筛选条件
  timeFilter.value = 'all'
  filterForm.value = {
    shop_id: '',
    bill_type: '',
    start_time: '',
    end_time: ''
  }

  // 获取基础数据
  await Promise.all([
    fetchShopList(),
    fetchBillTypeList()
  ])

  // 获取明细记录
  await fetchBalanceRecords()
}

// 时间筛选
function setTimeFilter(filter) {
  timeFilter.value = filter
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

  switch (filter) {
    case 'today':
      filterForm.value.start_time = formatDateForApi(today)
      filterForm.value.end_time = formatDateForApi(now)
      break
    case 'yesterday':
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
      filterForm.value.start_time = formatDateForApi(yesterday)
      filterForm.value.end_time = formatDateForApi(new Date(yesterday.getTime() + 24 * 60 * 60 * 1000 - 1))
      break
    case 'week':
      const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
      filterForm.value.start_time = formatDateForApi(weekAgo)
      filterForm.value.end_time = formatDateForApi(now)
      break
    case 'month':
      const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
      filterForm.value.start_time = formatDateForApi(monthAgo)
      filterForm.value.end_time = formatDateForApi(now)
      break
    case 'all':
    case 'custom':
      filterForm.value.start_time = ''
      filterForm.value.end_time = ''
      break
  }

  // 自动查询
  onFilterChange()
}

// 筛选条件变化时自动查询
function onFilterChange() {
  balanceDetailPagination.value.page = 1
  fetchBalanceRecords()
}

// 格式化日期为API需要的格式
function formatDateForApi(date) {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 格式化显示时间
function formatDateTime(timestamp) {
  if (!timestamp) return ''

  // 如果是时间戳格式（数字）
  if (typeof timestamp === 'number') {
    const date = new Date(timestamp * 1000)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }

  // 如果是字符串格式，直接返回
  return timestamp
}



// 判断是否可以撤销
function canRevoke(row) {
  // 只有会员充值类型才能撤销
  // 根据bill_type_sign或bill_type_name判断
  return row.bill_type_sign === 'CASH_RECHARGE' ||
         row.bill_type_name === '会员充值' ||
         row.bill_type_name === '单店充值' ||
         row.bill_type_name === '独立充值' ||
         (row.bill_type && row.bill_type === 6) || // 如果bill_type为6表示会员充值
         (row.change && row.change > 0 && row.bill_id) // 有正向变动且有订单ID的记录
}

// 操作按钮方法
function handlePrint(row) {
  printSelectModal.value.currentRow = row
  printSelectModal.value.show = true
}

// 撤销充值
function handleRevokeRecharge(row) {
  if (!row.bill_id) {
    message.error('订单ID不能为空')
    return
  }

  // 验证是否为会员充值类型
  if (!canRevoke(row)) {
    message.error('只有会员充值记录才能撤销')
    return
  }

  // 二次确认
  let chargeType = '会员充值'
  if (storeBalanceDetailModal.value.show) {
    chargeType = '单店卡充值'
  } else if (storePaidDetailModal.value.show) {
    chargeType = '单店卡充值'
  }
  const amount = Math.abs(row.change_amount || row.change || 0)

  dialog.warning({
    title: `确认撤销${chargeType}`,
    content: `确定要撤销订单号为 ${row.bill_id} 的${chargeType}记录吗？\n\n撤销后将退回充值金额：${amount}元\n${row.gift_amount ? `赠送金额：${row.gift_amount}元也将被扣除` : ''}`,
    positiveText: '确认撤销',
    negativeText: '取消',
    onPositiveClick: async () => {
      await doRevokeRecharge(row.bill_id)
    }
  })
}

// 执行撤销充值
async function doRevokeRecharge(billId) {
  try {
    // 根据当前弹窗类型选择不同的撤销接口
    let apiUrl = '/admin/MemberRecharge/refundMemberRecharge'
    let refreshFunctions = [fetchMemberInfo(), fetchBalanceRecords(), fetchMemberHistory()]

    // 如果是单店卡余额明细弹窗，使用单店卡撤销接口
    if (storeBalanceDetailModal.value.show) {
      apiUrl = '/admin/IndependentRecharge/adminIndependentRefund'
      refreshFunctions = [fetchMemberInfo(), fetchStoreBalanceRecords(), fetchMemberHistory()]
    }
    // 如果是单店卡累计实付明细弹窗，使用单店卡撤销接口
    else if (storePaidDetailModal.value.show) {
      apiUrl = '/admin/IndependentRecharge/adminIndependentRefund'
      refreshFunctions = [fetchMemberInfo(), fetchStorePaidRecords(), fetchMemberHistory()]
    }

    const response = await request.post(apiUrl, {
      bill_id: billId
    }, { needToken: true, needTip: false })

    if (response.code === 0) {
      message.success('撤销充值成功')

      // 根据当前弹窗刷新对应的数据
      await Promise.all(refreshFunctions)
    } else if (response.code === 886) {
      message.error('登录已过期，请重新登录')
    } else {
      message.error(response.message || '撤销充值失败')
    }
  } catch (error) {
    console.error('撤销充值失败:', error)
    message.error('撤销充值失败')
  }
}

// A4打印
function handleA4Print() {
  const row = printSelectModal.value.currentRow
  if (!row) return

  // 准备打印数据
  printData.value = {
    memberName: memberInfo.value?.name || memberInfo.value?.nickname || '',
    phone: memberInfo.value?.phone || '',
    cardNumber: memberInfo.value?.card_number || '',
    rechargeAmount: row.change_amount || '',
    giftAmount: row.gift_amount || '',
    giftPoints: '0',
    orderNumber: row.bill_id || '',
    orderTime: formatDateTime(row.create_time) || '',
    printTime: new Date().toLocaleString('zh-CN'),
    operator: row.admin_nickname || ''
  }

  printSelectModal.value.show = false
  a4PrintModal.value.show = true
}

// 小票打印
async function handleTicketPrint() {
  const row = printSelectModal.value.currentRow
  if (!row) return

  try {
    const response = await request.post('/admin/Printer/printMemberRecharge', {
      id: row.id
    }, { needToken: true, needTip: false })

    if (response.code === 0) {
      message.success('小票打印成功')
    } else if (response.code === 886) {
      message.error('登录已过期，请重新登录')
    } else {
      message.error(response.message || '小票打印失败')
    }
  } catch (error) {
    console.error('小票打印失败:', error)
    message.error('小票打印失败')
  } finally {
    printSelectModal.value.show = false
  }
}

// 执行A4打印
function doPrint() {
  const printContent = document.getElementById('printContent')
  if (!printContent) return

  // 创建新窗口进行打印
  const printWindow = window.open('', '_blank')
  printWindow.document.write(`
    <html>
      <head>
        <title>打印</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .print-header { text-align: center; margin-bottom: 20px; }
          .print-header h2 { margin: 0; font-size: 24px; }
          .print-header h3 { margin: 5px 0; font-size: 18px; }
          .hotel-info { text-align: center; margin-bottom: 20px; font-size: 14px; }
          .member-info-section { margin: 20px 0; }
          .info-row { margin: 10px 0; display: flex; justify-content: space-between; }
          .info-row span { flex: 1; }
          .print-footer { margin-top: 30px; }
          .footer-row { display: flex; justify-content: space-between; }
          @media print {
            body { margin: 0; }
          }
        </style>
      </head>
      <body>
        ${printContent.innerHTML}
      </body>
    </html>
  `)
  printWindow.document.close()
  printWindow.print()
  printWindow.close()

  a4PrintModal.value.show = false
}

// 查看累计金额明细
async function handleViewPaymentDetails() {
  paymentDetailModal.value.show = true
  paymentDetailModal.value.title = `累计金额明细 - ${memberInfo.value?.name || memberInfo.value?.nickname || ''}`

  // 重置筛选条件
  paymentTimeFilter.value = 'all'
  paymentFilterForm.value = {
    shop_id: '',
    bill_type: '',
    start_time: '',
    end_time: ''
  }

  // 重置分页
  paymentDetailPagination.value.page = 1

  // 并行获取基础数据和明细数据
  try {
    await Promise.all([
      fetchShopList().catch(err => console.warn('获取酒店列表失败:', err)),
      fetchBillTypeList().catch(err => console.warn('获取单据类型失败:', err)),
      fetchPaymentRecords()
    ])
  } catch (error) {
    console.error('获取累计金额明细数据失败:', error)
    // 即使基础数据获取失败，也要确保明细数据能正常加载
    if (paymentDetailList.value.length === 0) {
      await fetchPaymentRecords()
    }
  }
}

// 获取实付款记录
async function fetchPaymentRecords() {
  if (!props.commonCode) return

  paymentDetailLoading.value = true
  try {
    const params = {
      page: paymentDetailPagination.value.page,
      limit: paymentDetailPagination.value.pageSize,
      common_code: props.commonCode,
      bill_type: paymentFilterForm.value.bill_type || '',
      shop_id: paymentFilterForm.value.shop_id || '',
      start_time: paymentFilterForm.value.start_time || '',
      end_time: paymentFilterForm.value.end_time || ''
    }

    const response = await request.get('/admin/Member/gerUserBalancePayRecord', {
      params
    }, { needToken: true, needTip: false })

    if (response.code === 0) {
      const data = response.data || {}
      paymentDetailList.value = Array.isArray(data.list) ? data.list : []

      // 调试信息：打印返回的数据结构
      if (data.list && data.list.length > 0) {
        console.log('累计金额明细API返回的第一条数据结构:', data.list[0])
        console.log('所有字段名:', Object.keys(data.list[0]))
      }

      if (data.count !== undefined) {
        paymentDetailPagination.value.itemCount = data.count
      }
    } else if (response.code === 886) {
      message.error('登录已过期，请重新登录')
    } else {
      message.error(response.message || '获取实付款记录失败')
    }
  } catch (error) {
    console.error('获取实付款记录失败:', error)
    message.error('获取实付款记录失败')
  } finally {
    paymentDetailLoading.value = false
  }
}

// 实付款时间筛选
function setPaymentTimeFilter(filter) {
  paymentTimeFilter.value = filter
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

  switch (filter) {
    case 'today':
      paymentFilterForm.value.start_time = formatDateForApi(today)
      paymentFilterForm.value.end_time = formatDateForApi(now)
      break
    case 'yesterday':
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
      paymentFilterForm.value.start_time = formatDateForApi(yesterday)
      paymentFilterForm.value.end_time = formatDateForApi(new Date(yesterday.getTime() + 24 * 60 * 60 * 1000 - 1))
      break
    case 'week':
      const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
      paymentFilterForm.value.start_time = formatDateForApi(weekAgo)
      paymentFilterForm.value.end_time = formatDateForApi(now)
      break
    case 'month':
      const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
      paymentFilterForm.value.start_time = formatDateForApi(monthAgo)
      paymentFilterForm.value.end_time = formatDateForApi(now)
      break
    case 'all':
    case 'custom':
      paymentFilterForm.value.start_time = ''
      paymentFilterForm.value.end_time = ''
      break
  }

  // 自动查询
  onPaymentFilterChange()
}

// 实付款筛选条件变化时自动查询
function onPaymentFilterChange() {
  paymentDetailPagination.value.page = 1
  fetchPaymentRecords()
}

// 查看积分明细
async function handleViewPointDetails() {
  pointDetailModal.value.show = true
  pointDetailModal.value.title = `积分明细 - ${memberInfo.value?.name || memberInfo.value?.nickname || ''}`

  // 重置筛选条件
  pointTimeFilter.value = 'all'
  pointFilterForm.value = {
    shop_id: '',
    bill_type: '',
    start_time: '',
    end_time: ''
  }

  // 重置分页
  pointDetailPagination.value.page = 1

  // 并行获取基础数据和明细数据
  try {
    await Promise.all([
      fetchShopList().catch(err => console.warn('获取酒店列表失败:', err)),
      fetchBillTypeList().catch(err => console.warn('获取单据类型失败:', err)),
      fetchPointRecords()
    ])
  } catch (error) {
    console.error('获取积分明细数据失败:', error)
    // 即使基础数据获取失败，也要确保明细数据能正常加载
    if (pointDetailList.value.length === 0) {
      await fetchPointRecords()
    }
  }
}

// 获取积分记录
async function fetchPointRecords() {
  if (!props.commonCode) return

  pointDetailLoading.value = true
  try {
    const params = {
      page: pointDetailPagination.value.page,
      limit: pointDetailPagination.value.pageSize,
      common_code: props.commonCode,
      bill_type: pointFilterForm.value.bill_type || '',
      shop_id: pointFilterForm.value.shop_id || '',
      start_time: pointFilterForm.value.start_time || null,
      end_time: pointFilterForm.value.end_time || null
    }

    const response = await request.get('/admin/Member/gerUserPointRecord', {
      params
    }, { needToken: true, needTip: false })

    if (response.code === 0) {
      const data = response.data || {}
      pointDetailList.value = Array.isArray(data.list) ? data.list : []

      // 调试信息：打印返回的数据结构
      if (data.list && data.list.length > 0) {
        console.log('积分明细API返回的第一条数据结构:', data.list[0])
        console.log('所有字段名:', Object.keys(data.list[0]))
      }

      if (data.count !== undefined) {
        pointDetailPagination.value.itemCount = data.count
      }
    } else if (response.code === 886) {
      message.error('登录已过期，请重新登录')
    } else {
      message.error(response.message || '获取积分记录失败')
    }
  } catch (error) {
    console.error('获取积分记录失败:', error)
    message.error('获取积分记录失败')
  } finally {
    pointDetailLoading.value = false
  }
}

// 积分时间筛选
function setPointTimeFilter(filter) {
  pointTimeFilter.value = filter
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

  switch (filter) {
    case 'today':
      pointFilterForm.value.start_time = formatDateForApi(today)
      pointFilterForm.value.end_time = formatDateForApi(now)
      break
    case 'yesterday':
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
      pointFilterForm.value.start_time = formatDateForApi(yesterday)
      pointFilterForm.value.end_time = formatDateForApi(new Date(yesterday.getTime() + 24 * 60 * 60 * 1000 - 1))
      break
    case 'week':
      const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
      pointFilterForm.value.start_time = formatDateForApi(weekAgo)
      pointFilterForm.value.end_time = formatDateForApi(now)
      break
    case 'month':
      const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
      pointFilterForm.value.start_time = formatDateForApi(monthAgo)
      pointFilterForm.value.end_time = formatDateForApi(now)
      break
    case 'all':
    case 'custom':
      pointFilterForm.value.start_time = ''
      pointFilterForm.value.end_time = ''
      break
  }

  // 自动查询
  onPointFilterChange()
}

// 积分筛选条件变化时自动查询
function onPointFilterChange() {
  pointDetailPagination.value.page = 1
  fetchPointRecords()
}

// 打开调整积分弹窗
function openAdjustPointModal() {
  adjustPointModal.value.show = true
  // 重置表单
  adjustPointForm.value = {
    point: null,
    remark: ''
  }
}

// 调整积分
async function handleAdjustPoint() {
  if (!adjustPointFormRef.value) return

  try {
    await adjustPointFormRef.value.validate()
  } catch (error) {
    return
  }

  if (!props.commonCode) {
    message.error('用户编码不能为空')
    return
  }

  adjustPointLoading.value = true
  try {
    const response = await request.post('/admin/Member/updateUserPoint', {
      common_code: props.commonCode,
      point: String(adjustPointForm.value.point),
      remark: adjustPointForm.value.remark
    }, { needToken: true, needTip: false })

    if (response.code === 0) {
      message.success('积分调整成功')
      adjustPointModal.value.show = false

      // 刷新会员信息、积分明细记录和客史记录
      await Promise.all([
        fetchMemberInfo(),
        fetchPointRecords(),
        fetchMemberHistory()
      ])
    } else if (response.code === 886) {
      message.error('登录已过期，请重新登录')
    } else {
      message.error(response.message || '积分调整失败')
    }
  } catch (error) {
    console.error('积分调整失败:', error)
    message.error('积分调整失败')
  } finally {
    adjustPointLoading.value = false
  }
}

// 查看单店卡余额明细
async function handleViewStoreBalanceDetails() {
  storeBalanceDetailModal.value.show = true
  storeBalanceDetailModal.value.title = `单店卡余额明细 - ${memberInfo.value?.name || memberInfo.value?.nickname || ''}`

  // 重置筛选条件
  storeBalanceTimeFilter.value = 'all'
  storeBalanceFilterForm.value = {
    shop_id: '',
    bill_type: '',
    start_time: '',
    end_time: ''
  }

  // 重置分页
  storeBalanceDetailPagination.value.page = 1

  // 并行获取基础数据和明细数据
  try {
    await Promise.all([
      fetchShopList().catch(err => console.warn('获取酒店列表失败:', err)),
      fetchBillTypeList().catch(err => console.warn('获取单据类型失败:', err)),
      fetchStoreBalanceRecords()
    ])
  } catch (error) {
    console.error('获取单店卡余额明细数据失败:', error)
    // 即使基础数据获取失败，也要确保明细数据能正常加载
    if (storeBalanceDetailList.value.length === 0) {
      await fetchStoreBalanceRecords()
    }
  }
}

// 获取单店卡余额记录
async function fetchStoreBalanceRecords() {
  if (!props.commonCode) return

  storeBalanceDetailLoading.value = true
  try {
    const params = {
      page: storeBalanceDetailPagination.value.page,
      limit: storeBalanceDetailPagination.value.pageSize,
      common_code: props.commonCode,
      bill_type: storeBalanceFilterForm.value.bill_type || '',
      shop_id: storeBalanceFilterForm.value.shop_id || '',
      start_time: storeBalanceFilterForm.value.start_time || '',
      end_time: storeBalanceFilterForm.value.end_time || ''
    }

    const response = await request.get('/admin/IndependentRecharge/getIndependentRecord', {
      params
    }, { needToken: true, needTip: false })

    if (response.code === 0) {
      const data = response.data || {}
      storeBalanceDetailList.value = Array.isArray(data.list) ? data.list : []

      // 调试信息：打印返回的数据结构
      if (data.list && data.list.length > 0) {
        console.log('单店卡余额明细API返回的第一条数据结构:', data.list[0])
        console.log('所有字段名:', Object.keys(data.list[0]))

        // 检查撤销条件
        data.list.forEach((item, index) => {
          const canRevokeResult = canRevoke(item)
          console.log(`记录${index + 1} 撤销条件检查:`, {
            bill_type_name: item.bill_type_name,
            bill_type_sign: item.bill_type_sign,
            bill_type: item.bill_type,
            change: item.change,
            bill_id: item.bill_id,
            canRevoke: canRevokeResult
          })
        })
      }

      if (data.count !== undefined) {
        storeBalanceDetailPagination.value.itemCount = data.count
      }
    } else if (response.code === 886) {
      message.error('登录已过期，请重新登录')
    } else {
      message.error(response.message || '获取单店卡余额记录失败')
    }
  } catch (error) {
    console.error('获取单店卡余额记录失败:', error)
    message.error('获取单店卡余额记录失败')
  } finally {
    storeBalanceDetailLoading.value = false
  }
}

// 单店卡余额时间筛选
function setStoreBalanceTimeFilter(filter) {
  storeBalanceTimeFilter.value = filter
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

  switch (filter) {
    case 'today':
      storeBalanceFilterForm.value.start_time = formatDateForApi(today)
      storeBalanceFilterForm.value.end_time = formatDateForApi(now)
      break
    case 'yesterday':
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
      storeBalanceFilterForm.value.start_time = formatDateForApi(yesterday)
      storeBalanceFilterForm.value.end_time = formatDateForApi(new Date(yesterday.getTime() + 24 * 60 * 60 * 1000 - 1))
      break
    case 'week':
      const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
      storeBalanceFilterForm.value.start_time = formatDateForApi(weekAgo)
      storeBalanceFilterForm.value.end_time = formatDateForApi(now)
      break
    case 'month':
      const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
      storeBalanceFilterForm.value.start_time = formatDateForApi(monthAgo)
      storeBalanceFilterForm.value.end_time = formatDateForApi(now)
      break
    case 'all':
    case 'custom':
      storeBalanceFilterForm.value.start_time = ''
      storeBalanceFilterForm.value.end_time = ''
      break
  }

  // 自动查询
  onStoreBalanceFilterChange()
}

// 单店卡余额筛选条件变化时自动查询
function onStoreBalanceFilterChange() {
  storeBalanceDetailPagination.value.page = 1
  fetchStoreBalanceRecords()
}

// 查看单店卡累计实付明细
async function handleViewStorePaidDetails() {
  storePaidDetailModal.value.show = true
  storePaidDetailModal.value.title = `单店卡累计实付明细 - ${memberInfo.value?.name || memberInfo.value?.nickname || ''}`

  // 重置筛选条件
  storePaidTimeFilter.value = 'all'
  storePaidFilterForm.value = {
    shop_id: '',
    bill_type: '',
    start_time: '',
    end_time: ''
  }

  // 重置分页
  storePaidDetailPagination.value.page = 1

  // 并行获取基础数据和明细数据
  try {
    await Promise.all([
      fetchShopList().catch(err => console.warn('获取酒店列表失败:', err)),
      fetchBillTypeList().catch(err => console.warn('获取单据类型失败:', err)),
      fetchStorePaidRecords()
    ])
  } catch (error) {
    console.error('获取单店卡累计实付明细数据失败:', error)
    // 即使基础数据获取失败，也要确保明细数据能正常加载
    if (storePaidDetailList.value.length === 0) {
      await fetchStorePaidRecords()
    }
  }
}

// 获取单店卡累计实付记录
async function fetchStorePaidRecords() {
  if (!props.commonCode) return

  storePaidDetailLoading.value = true
  try {
    const params = {
      page: storePaidDetailPagination.value.page,
      limit: storePaidDetailPagination.value.pageSize,
      common_code: props.commonCode,
      bill_type: storePaidFilterForm.value.bill_type || '',
      shop_id: storePaidFilterForm.value.shop_id || '',
      start_time: storePaidFilterForm.value.start_time || '',
      end_time: storePaidFilterForm.value.end_time || ''
    }

    const response = await request.get('/admin/IndependentRecharge/getIndependentPayRecord', {
      params
    }, { needToken: true, needTip: false })

    if (response.code === 0) {
      const data = response.data || {}
      storePaidDetailList.value = Array.isArray(data.list) ? data.list : []

      // 调试信息：打印返回的数据结构
      if (data.list && data.list.length > 0) {
        console.log('单店卡累计实付明细API返回的第一条数据结构:', data.list[0])
        console.log('所有字段名:', Object.keys(data.list[0]))

        // 检查撤销条件
        data.list.forEach((item, index) => {
          const canRevokeResult = canRevoke(item)
          console.log(`记录${index + 1} 撤销条件检查:`, {
            bill_type_name: item.bill_type_name,
            bill_type_sign: item.bill_type_sign,
            bill_type: item.bill_type,
            change: item.change,
            bill_id: item.bill_id,
            canRevoke: canRevokeResult
          })
        })
      }

      if (data.count !== undefined) {
        storePaidDetailPagination.value.itemCount = data.count
      }
    } else if (response.code === 886) {
      message.error('登录已过期，请重新登录')
    } else {
      message.error(response.message || '获取单店卡累计实付记录失败')
    }
  } catch (error) {
    console.error('获取单店卡累计实付记录失败:', error)
    message.error('获取单店卡累计实付记录失败')
  } finally {
    storePaidDetailLoading.value = false
  }
}

// 单店卡累计实付时间筛选
function setStorePaidTimeFilter(filter) {
  storePaidTimeFilter.value = filter
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

  switch (filter) {
    case 'today':
      storePaidFilterForm.value.start_time = formatDateForApi(today)
      storePaidFilterForm.value.end_time = formatDateForApi(now)
      break
    case 'yesterday':
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
      storePaidFilterForm.value.start_time = formatDateForApi(yesterday)
      storePaidFilterForm.value.end_time = formatDateForApi(new Date(yesterday.getTime() + 24 * 60 * 60 * 1000 - 1))
      break
    case 'week':
      const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
      storePaidFilterForm.value.start_time = formatDateForApi(weekAgo)
      storePaidFilterForm.value.end_time = formatDateForApi(now)
      break
    case 'month':
      const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
      storePaidFilterForm.value.start_time = formatDateForApi(monthAgo)
      storePaidFilterForm.value.end_time = formatDateForApi(now)
      break
    case 'all':
    case 'custom':
      storePaidFilterForm.value.start_time = ''
      storePaidFilterForm.value.end_time = ''
      break
  }

  // 自动查询
  onStorePaidFilterChange()
}

// 单店卡累计实付筛选条件变化时自动查询
function onStorePaidFilterChange() {
  storePaidDetailPagination.value.page = 1
  fetchStorePaidRecords()
}

// 打开编辑基本信息弹窗
async function openEditBasicInfoModal() {
  editBasicInfoModal.value.show = true

  // 填充当前会员信息
  if (memberInfo.value) {
    editBasicInfoForm.value = {
      name: memberInfo.value.name || '',
      phone: memberInfo.value.phone || '',
      gender: memberInfo.value.gender || null,
      identification_type: memberInfo.value.identification_type || 1,
      identification_number: memberInfo.value.identification_number || '',
      develop_admin_id: memberInfo.value.develop_admin_id || null,
      user_source: memberInfo.value.user_source || null,
      nation: memberInfo.value.nation || null,
      birthday: memberInfo.value.birthday ? new Date(memberInfo.value.birthday).getTime() : null,
      hobby: memberInfo.value.hobby || '',
      car_number: Array.isArray(memberInfo.value.car_number) ? memberInfo.value.car_number.join(',') : (memberInfo.value.car_number || ''),
      remark: getRemarkText(memberInfo.value) || ''
    }
  }

  // 并行获取基础数据
  try {
    await Promise.all([
      fetchAdminList().catch(err => console.warn('获取销售员列表失败:', err)),
      fetchUserSourceList().catch(err => console.warn('获取发展途径失败:', err)),
      fetchNationList().catch(err => console.warn('获取民族列表失败:', err))
    ])
  } catch (error) {
    console.error('获取基础数据失败:', error)
  }
}

// 获取销售员列表
async function fetchAdminList() {
  try {
    const response = await request.post('/admin/Admin/getAdmins', {}, { needToken: true, needTip: false })

    if (response.code === 0) {
      // 使用返回数据中的list字段
      adminList.value = Array.isArray(response.data?.list) ? response.data.list : []

      // 调试信息：打印返回的数据结构
      if (response.data?.list && response.data.list.length > 0) {
        console.log('销售员列表API返回的第一条数据结构:', response.data.list[0])
        console.log('所有字段名:', Object.keys(response.data.list[0]))
      }

      console.log('销售员列表加载完成，共', adminList.value.length, '条数据')
    } else {
      console.error('获取销售员列表失败，响应:', response)
      message.error(response.message || '获取销售员列表失败')
    }
  } catch (error) {
    console.error('获取销售员列表失败:', error)
    message.error('获取销售员列表失败')
  }
}

// 获取发展途径列表
async function fetchUserSourceList() {
  try {
    const response = await request.post('/admin/UserSource/getUserSource', {
      page: 1,
      limit: 10000,
      status: ''
    }, { needToken: true, needTip: false })

    if (response.code === 0) {
      userSourceList.value = Array.isArray(response.data?.list) ? response.data.list : []
    } else {
      message.error(response.message || '获取发展途径失败')
    }
  } catch (error) {
    console.error('获取发展途径失败:', error)
    message.error('获取发展途径失败')
  }
}

// 获取民族列表
async function fetchNationList() {
  try {
    const response = await request.post('/admin/CommonData/getNation', {}, { needToken: true, needTip: false })

    if (response.code === 0) {
      nationList.value = Array.isArray(response.data) ? response.data : []
    } else {
      message.error(response.message || '获取民族列表失败')
    }
  } catch (error) {
    console.error('获取民族列表失败:', error)
    message.error('获取民族列表失败')
  }
}

// 更新基本信息
async function handleUpdateBasicInfo() {
  if (!editBasicInfoFormRef.value) return

  try {
    await editBasicInfoFormRef.value.validate()
  } catch (error) {
    return
  }

  if (!props.commonCode) {
    message.error('用户编码不能为空')
    return
  }

  editBasicInfoLoading.value = true
  try {
    // 处理生日格式
    let birthday = ''
    if (editBasicInfoForm.value.birthday) {
      const date = new Date(editBasicInfoForm.value.birthday)
      birthday = `${date.getFullYear()}/${String(date.getMonth() + 1).padStart(2, '0')}/${String(date.getDate()).padStart(2, '0')}`
    }

    // 处理车牌号
    const carNumbers = editBasicInfoForm.value.car_number ?
      editBasicInfoForm.value.car_number.split(',').map(item => item.trim()).filter(item => item) :
      [null]

    const response = await request.post('/admin/Member/updateUserInfo', {
      common_code: props.commonCode,
      name: editBasicInfoForm.value.name,
      phone: editBasicInfoForm.value.phone,
      gender: editBasicInfoForm.value.gender,
      identification_type: editBasicInfoForm.value.identification_type,
      identification_number: editBasicInfoForm.value.identification_number,
      develop_admin_id: editBasicInfoForm.value.develop_admin_id || 0,
      user_source: editBasicInfoForm.value.user_source,
      nation: editBasicInfoForm.value.nation,
      birthday: birthday,
      hobby: editBasicInfoForm.value.hobby,
      car_number: carNumbers,
      remark: editBasicInfoForm.value.remark
    }, { needToken: true, needTip: false })

    if (response.code === 0) {
      message.success('基本信息更新成功')
      editBasicInfoModal.value.show = false

      // 刷新会员信息和客史记录
      await Promise.all([
        fetchMemberInfo(),
        fetchMemberHistory()
      ])
    } else if (response.code === 886) {
      message.error('登录已过期，请重新登录')
    } else {
      message.error(response.message || '基本信息更新失败')
    }
  } catch (error) {
    console.error('基本信息更新失败:', error)
    message.error('基本信息更新失败')
  } finally {
    editBasicInfoLoading.value = false
  }
}

// 打开编辑会员等级弹窗
async function openEditMemberGradeModal() {
  editMemberGradeModal.value.show = true

  // 填充当前会员等级
  if (memberInfo.value) {
    editMemberGradeForm.value = {
      grade_id: memberInfo.value.grade_id || null
    }
  }

  // 获取会员等级列表
  try {
    await fetchMemberGradeList()
  } catch (error) {
    console.error('获取会员等级列表失败:', error)
  }
}

// 获取会员等级列表
async function fetchMemberGradeList() {
  try {
    const response = await request.get('/admin/MemberGrade/getMemberGrade', {
      params: {
        status: 1,
        member: 1
      }
    }, { needToken: true, needTip: false })

    if (response.code === 0) {
      memberGradeList.value = Array.isArray(response.data) ? response.data : []

      // 调试信息：打印返回的数据结构
      if (response.data && response.data.length > 0) {
        console.log('会员等级列表API返回的第一条数据结构:', response.data[0])
        console.log('所有字段名:', Object.keys(response.data[0]))
      }

      console.log('会员等级列表加载完成，共', memberGradeList.value.length, '条数据')
    } else {
      console.error('获取会员等级列表失败，响应:', response)
      message.error(response.message || '获取会员等级列表失败')
    }
  } catch (error) {
    console.error('获取会员等级列表失败:', error)
    message.error('获取会员等级列表失败')
  }
}

// 更新会员等级
async function handleUpdateMemberGrade() {
  if (!editMemberGradeFormRef.value) return

  try {
    await editMemberGradeFormRef.value.validate()
  } catch (error) {
    return
  }

  if (!props.commonCode) {
    message.error('用户编码不能为空')
    return
  }

  editMemberGradeLoading.value = true
  try {
    const response = await request.post('/admin/Member/updateUserGrade', {
      common_code: props.commonCode,
      grade: editMemberGradeForm.value.grade_id
    }, { needToken: true, needTip: false })

    if (response.code === 0) {
      message.success('会员等级更新成功')
      editMemberGradeModal.value.show = false

      // 刷新会员信息和客史记录
      await Promise.all([
        fetchMemberInfo(),
        fetchMemberHistory()
      ])
    } else if (response.code === 886) {
      message.error('登录已过期，请重新登录')
    } else {
      message.error(response.message || '会员等级更新失败')
    }
  } catch (error) {
    console.error('会员等级更新失败:', error)
    message.error('会员等级更新失败')
  } finally {
    editMemberGradeLoading.value = false
  }
}

// 操作日志相关函数
function openOperationLogModal() {
  operationLogModal.value.show = true
  // 重置筛选条件
  logTimeFilter.value = 'unlimited'
  logFilterForm.value = {
    start_time: null,
    end_time: null,
    type: ''
  }
  operationLogPagination.value.page = 1
  fetchOperationLogs()
}

// 设置时间筛选
function setLogTimeFilter(filter) {
  logTimeFilter.value = filter

  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

  switch (filter) {
    case 'today':
      logFilterForm.value.start_time = today.getTime()
      logFilterForm.value.end_time = now.getTime()
      break
    case 'yesterday':
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
      logFilterForm.value.start_time = yesterday.getTime()
      logFilterForm.value.end_time = today.getTime()
      break
    case 'nearWeek':
      const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
      logFilterForm.value.start_time = weekAgo.getTime()
      logFilterForm.value.end_time = now.getTime()
      break
    case 'nearMonth':
      const threeDaysAgo = new Date(today.getTime() - 3 * 24 * 60 * 60 * 1000)
      logFilterForm.value.start_time = threeDaysAgo.getTime()
      logFilterForm.value.end_time = now.getTime()
      break
    case 'thisMonth':
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1)
      logFilterForm.value.start_time = monthStart.getTime()
      logFilterForm.value.end_time = now.getTime()
      break
    case 'lastMonth':
      const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1)
      const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0, 23, 59, 59)
      logFilterForm.value.start_time = lastMonthStart.getTime()
      logFilterForm.value.end_time = lastMonthEnd.getTime()
      break
    case 'unlimited':
    default:
      logFilterForm.value.start_time = null
      logFilterForm.value.end_time = null
      break
  }

  if (filter !== 'custom') {
    onLogFilterChange()
  }
}

// 筛选条件变化
function onLogFilterChange() {
  operationLogPagination.value.page = 1
  fetchOperationLogs()
}

// 获取操作日志
async function fetchOperationLogs() {
  if (!props.commonCode) return

  operationLogLoading.value = true
  try {
    const params = {
      page: operationLogPagination.value.page,
      limit: operationLogPagination.value.pageSize,
      common_code: props.commonCode,
      start_time: logFilterForm.value.start_time ? Math.floor(logFilterForm.value.start_time / 1000) : null,
      end_time: logFilterForm.value.end_time ? Math.floor(logFilterForm.value.end_time / 1000) : null,
      type: logFilterForm.value.type || ''
    }

    const response = await request.post('/admin/Member/getUerLog', params, { needToken: true, needTip: false })

    if (response.code === 0 && response.data) {
      operationLogData.value = response.data.list || []
      operationLogPagination.value.itemCount = response.data.count || 0
    } else if (response.code === 886) {
      message.error('登录已过期，请重新登录')
    } else {
      console.error('获取操作日志失败:', response.message)
      operationLogData.value = []
      operationLogPagination.value.itemCount = 0
    }
  } catch (error) {
    console.error('获取操作日志失败:', error)
    operationLogData.value = []
    operationLogPagination.value.itemCount = 0
  } finally {
    operationLogLoading.value = false
  }
}

// 处理黑名单操作
async function handleSetBlock(block) {
  if (!props.commonCode) {
    message.error('用户编码不能为空')
    return
  }

  const actionText = block ? '置黑名单' : '解除黑名单'
  const confirmText = block ? '确定要将该会员置入黑名单吗？' : '确定要解除该会员的黑名单状态吗？'

  // 二次确认
  dialog.warning({
    title: `确认${actionText}`,
    content: confirmText,
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: async () => {
      await doSetBlock(block)
    }
  })
}

// 执行黑名单操作
async function doSetBlock(block) {
  blockLoading.value = true
  try {
    const response = await request.post('/admin/Member/setBlock', {
      common_code: props.commonCode,
      block: block ? 1 : 0
    }, { needToken: true, needTip: false })

    if (response.code === 0) {
      const actionText = block ? '置黑名单' : '解除黑名单'
      message.success(`${actionText}成功`)

      // 刷新会员信息和客史记录
      await Promise.all([
        fetchMemberInfo(),
        fetchMemberHistory()
      ])
    } else if (response.code === 886) {
      message.error('登录已过期，请重新登录')
    } else {
      message.error(response.message || `${block ? '置黑名单' : '解除黑名单'}失败`)
    }
  } catch (error) {
    console.error('黑名单操作失败:', error)
    message.error(`${block ? '置黑名单' : '解除黑名单'}失败`)
  } finally {
    blockLoading.value = false
  }
}

// 打开手动调整余额弹窗
function openAdjustBalanceModal() {
  adjustBalanceModal.value.show = true
  // 重置表单
  adjustBalanceForm.value = {
    type: '1',
    amount: null,
    remark: ''
  }
}

// 手动调整余额
async function handleAdjustBalance() {
  if (!adjustFormRef.value) return

  try {
    await adjustFormRef.value.validate()
  } catch (error) {
    return
  }

  if (!props.commonCode) {
    message.error('用户编码不能为空')
    return
  }

  adjustBalanceLoading.value = true
  try {
    const response = await request.post('/admin/Member/updateMemberBalance', {
      common_code: props.commonCode,
      remark: adjustBalanceForm.value.remark,
      amount: String(adjustBalanceForm.value.amount),
      type: adjustBalanceForm.value.type
    }, { needToken: true, needTip: false })

    if (response.code === 0) {
      message.success('余额调整成功')
      adjustBalanceModal.value.show = false

      // 刷新会员信息、明细记录和客史记录
      await Promise.all([
        fetchMemberInfo(),
        fetchBalanceRecords(),
        fetchMemberHistory()
      ])
    } else if (response.code === 886) {
      message.error('登录已过期，请重新登录')
    } else {
      message.error(response.message || '余额调整失败')
    }
  } catch (error) {
    console.error('余额调整失败:', error)
    message.error('余额调整失败')
  } finally {
    adjustBalanceLoading.value = false
  }
}

// 充值套餐选择
const selectedRechargeId = ref(null)
const selectedPlan = computed(() => {
  return rechargeList.value.find(item => item.id === selectedRechargeId.value) || null
})

const formatNumber = (num) => {
  return Number(num || 0).toLocaleString()
}

// 判断是否为推荐套餐（赠送总价值最高）
const isRecommended = (item) => {
  if (!rechargeList.value.length) return false
  const totalValue = Number(item.give_money || 0) + Number(item.give_point || 0) * 0.01 + Number(item.give_growth || 0) * 0.01
  const maxValue = Math.max(...rechargeList.value.map(r =>
    Number(r.give_money || 0) + Number(r.give_point || 0) * 0.01 + Number(r.give_growth || 0) * 0.01
  ))
  return totalValue === maxValue && totalValue > 0
}

// 检查是否有赠送
const hasGives = (item) => {
  return Number(item.give_money || 0) > 0 || Number(item.give_point || 0) > 0 || Number(item.give_growth || 0) > 0
}

// 获取赠送摘要
const getGivesSummary = (item) => {
  const gives = []
  if (Number(item.give_money || 0) > 0) gives.push(`${formatNumber(item.give_money)}元`)
  if (Number(item.give_point || 0) > 0) gives.push(`${item.give_point}积分`)
  if (Number(item.give_growth || 0) > 0) gives.push(`${item.give_growth}成长值`)
  return `赠送 ${gives.join(' + ')}`
}

// 收银台
const cashierModal = ref({ show: false })
const accountLoading = ref(false)
const accountList = ref([])
const cashierSubmitting = ref(false)

function getAccountLabel(a) {
  return a?.name || a?.account_name || a?.title || `#${a?.id}`
}

function getAccountIcon(a) {
  const label = (getAccountLabel(a) + '').toLowerCase()
  if (label.includes('微信')) return 'i-mdi:wechat'
  if (label.includes('支付宝')) return 'i-mdi:alipay'
  if (label.includes('银行')) return 'i-mdi:bank'
  if (label.includes('现金')) return 'i-mdi:cash'
  if (label.includes('会员')) return 'i-mdi:card-account-details-outline'
  if (label.includes('单位') || label.includes('协议')) return 'i-mdi:office-building'
  if (label.includes('专属')) return 'i-mdi:star-circle'
  return 'i-mdi:credit-card-outline'
}

const cashierForm = ref({
  account_id: null,
  auth_code: null,
  memo: '',
  print: 1 // 1=打印, 0=不打印
})

function onBackToPlans() {
  cashierModal.value.show = false
}

async function fetchAccounts() {
  accountLoading.value = true
  try {
    // 根据充值类型决定账户类型：单店卡充值使用type=3，跨店卡充值使用type=8
    const accountType = rechargeModal.value.scope === 'store' ? 3 : 8

    const res = await request.post('/admin/CommonData/getAccountList', {
      type: accountType,
      search_word: ''
    }, { needToken: true, needTip: false })
    if (res.code === 0) {
      accountList.value = res.data?.list || res.data || []
    }
  } catch (e) {
    message.error(e?.message || '获取账户失败')
  } finally {
    accountLoading.value = false
  }
}

function handleConfirmRecharge() {
  if (!selectedPlan.value) {
    message.warning('请先选择一个充值套餐')
    return
  }
  cashierModal.value.show = true
  fetchAccounts()
}

async function submitRecharge() {
  if (!selectedPlan.value) return
  if (!cashierForm.value.account_id) {
    message.warning('请选择账户')
    return
  }
  cashierSubmitting.value = true
  try {
    const scope = rechargeModal.value.scope
    let payload, endpoint

    if (scope === 'store') {
      // 单店卡充值使用新接口和参数格式
      endpoint = '/admin/IndependentRecharge/adminIndependentRecharge'
      payload = {
        common_code: props.commonCode,
        setting_id: selectedPlan.value.id,
        recharge_money: selectedPlan.value.recharge_money,
        give_money: selectedPlan.value.give_money,
        pay_info: {
          account_id: cashierForm.value.account_id,
          auth_code: cashierForm.value.auth_code || null
        },
        print: cashierForm.value.print ? 1 : 0
      }
    } else {
      // 跨店卡充值使用原接口
      endpoint = '/admin/MemberRecharge/adminMemberRecharge'
      payload = {
        setting_id: String(selectedPlan.value.id),
        common_code: props.commonCode,
        pay_info: {
          account_id: String(cashierForm.value.account_id),
          auth_code: cashierForm.value.auth_code ?? null
        },
        recharge_money: String(selectedPlan.value.recharge_money ?? 0),
        give_money: String(selectedPlan.value.give_money ?? 0),
        give_growth: String(selectedPlan.value.give_growth ?? 0),
        give_point: String(selectedPlan.value.give_point ?? 0),
        memo: cashierForm.value.memo || '',
        print: cashierForm.value.print ? 1 : 0
      }
    }

    const res = await request.post(endpoint, payload, { needToken: true })
    if (res.code === 0) {
      message.success('充值成功')
      cashierModal.value.show = false
      rechargeModal.value.show = false

      // 刷新会员信息和客史记录
      await Promise.all([
        fetchMemberInfo(),
        fetchMemberHistory()
      ])
    }
  } catch (e) {
    message.error(e?.message || '充值失败')
  } finally {
    cashierSubmitting.value = false
  }
}

// 获取用户优惠券列表
async function fetchUserCoupons(page = 1, limit = 10) {
  if (!props.commonCode) return

  try {
    const response = await request.post('/admin/Member/getUserCoupon', {
      page,
      limit,
      common_code: props.commonCode
    }, { needToken: true, needTip: false })

    if (response.code === 0) {
      const data = response.data || {}
      couponsList.value = Array.isArray(data.list) ? data.list : []
      // 更新分页信息
      if (data.count !== undefined) {
        couponPaginationState.value.itemCount = data.count
        console.log('设置优惠券总数:', data.count, '当前页:', page, '每页数量:', limit)
      }
    } else if (response.code === 886) {
      message.error('登录已过期，请重新登录')
    } else {
      message.error(response.message || '获取优惠券列表失败')
    }
  } catch (error) {
    console.error('获取优惠券列表失败:', error)
    message.error('获取优惠券列表失败')
  }
}

// 获取酒店列表
async function fetchShopList() {
  try {
    const response = await request.post('/admin/Shop/getAllShopList', {}, { needToken: true, needTip: false })
    if (response.code === 0) {
      shopList.value = Array.isArray(response.data) ? response.data : []
    } else {
      message.error(response.message || '获取酒店列表失败')
    }
  } catch (error) {
    console.error('获取酒店列表失败:', error)
    message.error('获取酒店列表失败')
  }
}

// 获取单据类型列表
async function fetchBillTypeList() {
  try {
    const response = await request.post('/admin/SystemSetting/getBillType', {}, { needToken: true, needTip: false })
    if (response.code === 0) {
      billTypeList.value = Array.isArray(response.data) ? response.data : []
    } else {
      message.error(response.message || '获取单据类型失败')
    }
  } catch (error) {
    console.error('获取单据类型失败:', error)
    message.error('获取单据类型失败')
  }
}

// 获取余额明细记录
async function fetchBalanceRecords() {
  if (!props.commonCode) return

  balanceDetailLoading.value = true
  try {
    const params = {
      page: balanceDetailPagination.value.page,
      limit: balanceDetailPagination.value.pageSize,
      common_code: props.commonCode,
      bill_type: filterForm.value.bill_type || '',
      shop_id: filterForm.value.shop_id || '',
      start_time: filterForm.value.start_time || '',
      end_time: filterForm.value.end_time || ''
    }

    const response = await request.post('/admin/Member/gerUserBalanceRecord', params, { needToken: true, needTip: false })

    if (response.code === 0) {
      const data = response.data || {}
      balanceDetailList.value = Array.isArray(data.list) ? data.list : []

      if (data.count !== undefined) {
        balanceDetailPagination.value.itemCount = data.count
      }
    } else if (response.code === 886) {
      message.error('登录已过期，请重新登录')
    } else {
      message.error(response.message || '获取余额明细失败')
    }
  } catch (error) {
    console.error('获取余额明细失败:', error)
    message.error('获取余额明细失败')
  } finally {
    balanceDetailLoading.value = false
  }
}

// 获取会员信息
async function fetchMemberInfo() {
  if (!props.commonCode) return

  loading.value = true
  try {
    const response = await request.post('/admin/Member/getUserInfo', {
      common_code: props.commonCode,
      simple: false
    })

    if (response.code === 0) {
      // 仅用于 grade_info 与 coupons
      const gi = response.data?.grade_info
      gradeInfo.value = Array.isArray(gi?.right_itererest) ? gi.right_itererest : []
      couponsList.value = Array.isArray(response.data?.coupons) ? response.data.coupons : []
    } else if (response.code === 886) {
      message.error('登录已过期，请重新登录')
      memberInfo.value = {}
    } else {
      message.error(response.message || '获取会员信息失败')
      memberInfo.value = {}
    }
  } catch (error) {
    console.error('获取会员信息失败:', error)
    message.error('获取会员信息失败')
    memberInfo.value = {}
  } finally {
    loading.value = false
  }
}

// 获取客史记录
async function fetchMemberHistory() {
  if (!props.commonCode) return

  try {
    const response = await request.post('/admin/Member/getUserHistory', {
      common_code: props.commonCode
    })

    if (response.code === 0) {
      // 根据需求：所有展示数据来源于 getUserHistory
      memberInfo.value = response.data || {}
      memberHistory.value = response.data?.history || response.data || []
    } else if (response.code === 886) {
      message.error('登录已过期，请重新登录')
    } else {
      console.error('获取客史记录失败:', response.message)
    }
  } catch (error) {
    console.error('获取客史记录失败:', error)
  }
}



// 格式化日期
function formatDate(timestamp) {
  if (!timestamp) return ''
  const date = new Date(timestamp * 1000)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }).replace(/\//g, '/')
}

// 获取性别文字
function getGenderText(gender) {
  if (gender === 1) return '男'
  if (gender === 2) return '女'
  return '未知'
}

// 格式化身份证号
function formatIdCard(idCard) {
  if (!idCard) return ''
  if (idCard.length <= 4) return idCard
  return idCard.substring(0, 4) + '****' + idCard.substring(idCard.length - 4)
}
</script>

<style scoped>
/* 会员信息弹窗样式 */
.member-info-modal .n-card {
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.member-info-modal .n-card__content {
  padding: 6px;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

@media (max-width: 1200px) {
  .member-info-modal .n-card { max-height: 85vh; }
}
@media (max-width: 768px) {
  .member-info-modal .n-card { max-height: 90vh; }
}


.member-info-content {
  position: relative;
  padding-top: 48px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.member-info-content {
  display: flex;
  flex-direction: column;
  gap: 6px;
  overflow-x: hidden;
}

/* 顶部卡片区域 */
.top-cards-section {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.card-section {
  flex: 1;
  border-radius: 6px;
  padding: 8px;
  background: #ffffff;
  color: #1f2937;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
  min-height: 90px;
}

.chain-hotels {
  flex: 2;
  border-left: 4px solid #3b82f6;
}

.single-store {
  flex: 1;
  border-left: 4px solid #10b981;
}

.card-header-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}
.card-actions { display: flex; gap: 6px; }
.section-title {
  margin: 0 0 8px 0;
  font-size: 13px;
  font-weight: 600;
  color: #1f2937;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 6px;
}
.cards-grid--store {
  grid-template-columns: repeat(2, 1fr);
}
.cards-grid--chain .info-card:nth-child(1) .card-main-value { color: #16a34a; }
.cards-grid--chain .info-card:nth-child(2) .card-main-value { color: #2563eb; }
.cards-grid--chain .info-card:nth-child(3) .card-main-value { color: #9333ea; }

.info-card {
  background: #f8fafc;
  border-radius: 6px;
  padding: 6px;
  text-align: left;
  min-height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  border: 1px solid #e2e8f0;
  position: relative;
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 48px;
}

.card-detail-btn {
  position: absolute;
  top: 4px;
  right: 4px;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.card-detail-btn .n-button {
  height: 22px;
  padding: 0 6px;
  border-radius: 11px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  font-size: 10px;
  line-height: 1;
}

.card-detail-btn .n-button:hover {
  background: rgba(255, 255, 255, 0.95);
  transform: scale(1.05);
}

.info-card:hover .card-detail-btn {
  opacity: 1;
}
.info-card .row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.info-card .muted { color: #6b7280; font-size: 12px; }

.card-main-value {
  font-size: 16px;
  font-weight: bold;
  color: #4ade80;
  margin-bottom: 3px;
}

.card-label {
  font-size: 11px;
  color: #6b7280;
  margin-bottom: 3px;
}

.card-sub-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.sub-label {
  font-size: 10px;
  color: #94a3b8;
}

.sub-value {
  font-size: 10px;
  color: #4ade80;
}



.bottom-label {
  font-size: 10px;
  color: #94a3b8;
}

.bottom-value {
  font-size: 10px;
  color: #4ade80;
}

/* 本金赠送显示 */
.subpairs {
  display: flex;
  gap: 8px;
  font-size: 10px;
}

.subpairs .pair {
  color: #6b7280;
}

.subpairs .pair b {
  color: #059669;
  font-weight: 600;
}

/* 会员基本信息 */
.member-basic-info {
  background: #f8fafc;
  border-radius: 6px;
  padding: 8px;
  margin-bottom: 8px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
  box-sizing: border-box;
}

.basic-info-row {
  display: flex;
  gap: 20px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.basic-info-row:last-child {
  margin-bottom: 0;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6px;
  min-width: 180px;
  flex: 1;
}

.info-label {
  font-weight: 500;
  color: #374151;
  min-width: 50px;
  font-size: 11px;
}

.info-value {
  color: #1f2937;
  font-size: 11px;
}

/* 操作按钮区域 */
.action-buttons-section {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  gap: 6px;
  z-index: 10;
  background: rgba(255, 255, 255, 0.95);
  padding: 8px;
  border-radius: 0 0 0 8px;
  backdrop-filter: blur(4px);
}

/* 客史统计 */
.history-stats-section {
  background: white;
  border-radius: 6px;
  padding: 8px;
  margin-bottom: 8px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
  box-sizing: border-box;
}

.history-stats-section .section-title {
  color: #1f2937;
  margin-bottom: 8px;
  font-size: 13px;
  font-weight: 600;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 6px 0;
}

.stat-label {
  color: #6b7280;
  min-width: 100px;
  font-size: 12px;
}

.stat-value {
  color: #1f2937;
  font-weight: 500;
  font-size: 12px;
}

/* 基本信息 */
.basic-info-section {
  background: white;
  border-radius: 6px;
  padding: 8px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
  margin: 0;
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
}

.basic-info-section .section-title {
  color: #1f2937;
  margin-bottom: 8px;
  font-size: 13px;
  font-weight: 600;
}

.basic-info-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.basic-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 8px;
}

.basic-item {
  display: flex;
  align-items: center;
  min-width: 0;
  overflow: hidden;
}

.basic-item.full-width {
  grid-column: 1 / -1;
}
.basic-item.full-width .basic-value {
  white-space: normal;
  overflow: visible;
  word-break: break-all;
}

.basic-label {
  color: #6b7280;
  min-width: 48px;
  font-size: 11px;
  flex-shrink: 0;
}

.basic-value {
  color: #1f2937;
  font-size: 11px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.info-label {
  font-weight: 500;
  color: #6b7280;
  font-size: 12px;
}

.info-value {
  color: #1f2937;
  font-size: 12px;
  font-weight: 500;
}

.info-remark {
  margin-top: 16px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.info-remark .info-label {
  display: block;
  margin-bottom: 8px;
}

.info-remark .info-value {
  display: block;
  line-height: 1.6;
}



/* 响应式设计 */
/* 可点击的链接样式（权益/优惠券数量） */
.link { color: #2563eb; cursor: pointer; text-decoration: underline; text-underline-offset: 2px; }
.link:hover { color: #1d4ed8; }

/* 优惠券行样式 */
.coupon-row { display:flex; align-items:center; gap:12px; }
.coupon-row .c-title { font-weight:600; }
.coupon-row .c-sub { color:#6b7280; font-size:12px; }

@media (max-width: 1200px) {
  .basic-row {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .info-item {
    min-width: 140px;
  }
}

@media (max-width: 900px) {
  .top-cards-section {
    flex-direction: column;
    gap: 8px;
  }

  .chain-hotels,
  .single-store {
    flex: 1;
  }

  .basic-info-row {
    gap: 16px;
  }

  .basic-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 6px;
  }

  .info-item {
    min-width: 120px;
  }
}

@media (max-width: 768px) {
  .top-cards-section {
    gap: 8px;
  }

  .card-section {
    padding: 12px;
  }

  .info-card {
    min-height: 80px;
    padding: 8px;
  }

  .card-main-value {
    font-size: 16px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .basic-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .action-buttons-section {
    flex-wrap: wrap;
    gap: 6px;
  }
}


.recharge-modal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
}
.recharge-modal-footer .muted { color: #6b7280; }
/* 充值列表网格样式 */
.recharge-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 12px;
}
/* 让 n-radio-group 不影响网格，将其子元素直接作为网格项 */
.recharge-grid :deep(.n-radio-group) { display: contents; }

.recharge-card-wrapper { position: relative; }
.recharge-radio { display: block; width: 100%; margin: 0; }
.recharge-card {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #fff;
  transition: all 0.2s ease;
  cursor: pointer;
  min-height: 90px;
  justify-content: center;
}
.recharge-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59,130,246,0.1);
  transform: translateY(-1px);
}
.recharge-card.is-recommended {
  border-color: #f59e0b;
  background: #fffbeb;
}
.recommend-badge {
  position: absolute;
  top: -6px;
  right: 8px;
  background: #f59e0b;
  color: white;
  padding: 2px 8px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 600;
}
.recharge-amount {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
  line-height: 1;
}
.recharge-gives-summary {
  font-size: 11px;
  color: #059669;
  margin-bottom: 6px;
  font-weight: 500;
  line-height: 1.2;
}
.recharge-operator {
  font-size: 10px;
  color: #9ca3af;
}
:deep(.n-radio--checked) .recharge-card {
  border-color: #3b82f6;
  background: #eff6ff;
  box-shadow: 0 0 0 2px rgba(59,130,246,0.15);
  transform: translateY(-1px);
}
:deep(.n-radio--checked) .recharge-card.is-recommended {
  border-color: #3b82f6;
  background: #eff6ff;
}
.cashier-modal-body { display: flex; flex-direction: column; gap: 12px; }
.cashier-summary { background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 10px; }
.cashier-summary .row { display: flex; justify-content: space-between; margin-bottom: 6px; }
.cashier-summary .row:last-child { margin-bottom: 0; }
.cashier-summary .label { color: #6b7280; }
.cashier-summary .value { font-weight: 600; }
.cashier-summary .gives { display: flex; gap: 12px; color: #374151; }
.cashier-footer { display: flex; justify-content: flex-end; gap: 8px; }

/* 收银台账户列表样式（更紧凑、更稳定） */
.account-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 10px;
  width: 100%;
  max-height: 320px;
  overflow-y: auto;
  padding-right: 4px;
}
.account-list::-webkit-scrollbar { width: 6px; }
.account-list::-webkit-scrollbar-thumb { background: #cbd5e1; border-radius: 4px; }
.account-list::-webkit-scrollbar-track { background: #f1f5f9; border-radius: 4px; }

.account-option { margin: 0; }
.account-card {
  position: relative;
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #fff;
  box-sizing: border-box;
  transition: all .2s ease;
  cursor: pointer;
  min-height: 60px;
}
.account-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 10px rgba(59,130,246,.12);
}
.selected-check {
  position: absolute;
  top: 8px;
  right: 8px;
  font-size: 18px;
  color: #3b82f6;
}
.account-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background: #f1f5f9;
  flex-shrink: 0;
}
.account-icon {
  font-size: 18px;
  color: #4b5563;
}
.account-info {
  flex: 1;
  min-width: 0;
}
.account-name {
  font-size: 13px;
  color: #1f2937;
  font-weight: 600;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.account-type {
  font-size: 12px;
  color: #6b7280;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.account-id {
  font-size: 11px;
  color: #9ca3af;
}

/* 选中态增强 */
:deep(.n-radio--checked) .account-card {
  border-color: #3b82f6;
  background: #eff6ff;
  box-shadow: 0 0 0 2px rgba(59,130,246,0.15);
}
:deep(.n-radio--checked) .account-icon-wrapper { background: #dbeafe; }
:deep(.n-radio--checked) .account-icon { color: #3b82f6; }
:deep(.n-radio--checked) .account-name { color: #1e40af; }
:deep(.n-radio--checked) .account-type { color: #3b82f6; }
/* 横向滚动账户列表（不影响弹窗整体布局） */
.cashier-modal-body { --account-item-w: 196px; --account-item-h: 64px; }
.account-hscroll {
  display:flex; gap:8px; width:100%; max-width:100%;
  overflow-x:auto; overflow-y:hidden; padding:4px 0; margin:0;
  box-sizing:border-box; overscroll-behavior: contain; isolation:isolate;
  scroll-snap-type: x proximity;
}
.account-hscroll::-webkit-scrollbar { height:6px; }
.account-hscroll::-webkit-scrollbar-thumb { background:#cbd5e1; border-radius:4px; }
.account-hscroll::-webkit-scrollbar-track { background:#f1f5f9; border-radius:4px; }
.account-hscroll .account-option { flex: 0 0 var(--account-item-w, 196px); min-width: var(--account-item-w, 196px); }
.account-hscroll .account-card { width: 100%; height: var(--account-item-h, 64px); min-height: 0; scroll-snap-align: start; overflow:hidden; }
/* 账户简洁横排（参考示例样式） */
.account-inline { display:flex; flex-wrap: wrap; gap: 10px; padding: 8px 12px; background: #f8fafc; border: 1px solid #e5e7eb; border-radius: 8px; }
:deep(.account-chip.n-radio) { border: 1px solid #e2e8f0; border-radius: 18px; padding: 6px 10px; background: #fff; }
:deep(.account-chip .n-radio__label) { display: inline-flex; align-items: center; gap: 8px; }
.chip-icon { font-size: 16px; color: #64748b; }
:deep(.account-chip.n-radio.n-radio--checked) { border-color: #3b82f6; background: #eff6ff; }
:deep(.account-chip.n-radio.n-radio--checked) .chip-icon { color: #3b82f6; }

/* 余额明细弹窗样式 */
.balance-detail-content,
.payment-detail-content,
.point-detail-content,
.store-balance-detail-content,
.store-paid-detail-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 16px;
  box-sizing: border-box;
  gap: 12px;
}

.filter-section {
  background: #f8fafc;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  flex-shrink: 0;
}

.filter-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;
}

.filter-actions {
  flex-shrink: 0;
}

.detail-table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
  gap: 0;
}

.detail-table-wrapper {
  flex: 1;
  overflow: hidden;
  border: 1px solid #e2e8f0;
  border-bottom: none;
  border-radius: 6px 6px 0 0;
  min-height: 0;
}

.detail-table-wrapper :deep(.n-data-table) {
  height: 100%;
}

.detail-table-wrapper :deep(.n-data-table .n-data-table-wrapper) {
  height: 100%;
  overflow: auto;
}

.detail-table-wrapper :deep(.n-data-table-base-table) {
  height: 100%;
}

.detail-table-wrapper :deep(.n-data-table-base-table-body) {
  overflow: auto;
}

.detail-pagination-section {
  flex-shrink: 0;
  padding: 12px;
  display: flex;
  justify-content: center;
  border: 1px solid #e2e8f0;
  border-top: none;
  background: #fafafa;
  border-radius: 0 0 6px 6px;
  margin: 0;
}

:deep(.action-buttons) {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  flex-wrap: wrap;
}

:deep(.action-btn) {
  display: inline-flex !important;
  align-items: center;
  justify-content: center;
  padding: 4px 8px !important;
  font-size: 12px !important;
  font-weight: 500;
  border-radius: 4px !important;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent !important;
  min-width: 40px;
  height: 24px;
  text-align: center;
  box-sizing: border-box;
}

:deep(.action-btn--print) {
  background-color: #e6f7ff !important;
  color: #1890ff !important;
  border-color: #91d5ff !important;
}

:deep(.action-btn--print:hover) {
  background-color: #bae7ff !important;
  border-color: #69c0ff !important;
  color: #096dd9 !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

:deep(.action-btn--revoke) {
  background-color: #fff7e6 !important;
  color: #fa8c16 !important;
  border-color: #ffd591 !important;
}

:deep(.action-btn--revoke:hover) {
  background-color: #ffe7ba !important;
  border-color: #ffb366 !important;
  color: #d46b08 !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(250, 140, 22, 0.2);
}

/* 打印相关样式 */
.print-select-content {
  padding: 20px 0;
}

.a4-print-content {
  height: 100%;
  overflow-y: auto;
}

.print-preview {
  background: white;
  padding: 20px;
  font-family: Arial, sans-serif;
  line-height: 1.5;
}

.print-header {
  text-align: center;
  margin-bottom: 20px;
  border-bottom: 2px solid #000;
  padding-bottom: 10px;
}

.print-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
}

.print-header h3 {
  margin: 5px 0 0 0;
  font-size: 18px;
  font-weight: normal;
}

.hotel-info {
  text-align: center;
  margin-bottom: 20px;
  font-size: 14px;
  border-bottom: 1px solid #000;
  padding-bottom: 10px;
}

.member-info-section {
  margin: 20px 0;
}

.info-row {
  margin: 10px 0;
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.info-row span {
  flex: 1;
  text-align: left;
}

.print-footer {
  margin-top: 30px;
  border-top: 1px solid #000;
  padding-top: 10px;
}

.footer-row {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

/* 实付款明细样式 */
.action-text {
  color: #999;
  font-size: 12px;
}

/* 编辑基本信息弹窗样式 */
.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.form-item-half {
  flex: 1;
  min-width: 200px;
}

.form-item-full {
  width: 100%;
}

/* 响应式优化 */
@media (max-width: 1400px) {
  .balance-detail-content {
    padding: 12px;
  }

  .filter-section {
    padding: 8px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .action-btn {
    font-size: 11px;
    padding: 3px 6px;
    min-width: 36px;
    height: 22px;
  }
}

@media (max-width: 768px) {
  .balance-detail-content {
    padding: 8px;
  }

  .filter-row {
    flex-direction: column;
    gap: 12px;
  }

  .filter-section .n-form {
    flex-direction: column;
    gap: 8px;
  }

  .detail-pagination-section {
    padding: 8px 0;
  }
}

/* 操作日志弹窗样式 */
.operation-log-content {
  height: 70vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.filter-section {
  flex-shrink: 0;
  padding-bottom: 16px;
  border-bottom: 1px solid #e2e8f0;
  margin-bottom: 16px;
}

.log-table-container {
  flex: 1;
  overflow: hidden;
  min-height: 0;
}

.custom-time-row {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e2e8f0;
}

.other-info-cell {
  font-size: 12px;
  line-height: 1.4;
}

.other-info-cell > div {
  margin-bottom: 2px;
}

.other-info-cell > div:last-child {
  margin-bottom: 0;
}

</style>
