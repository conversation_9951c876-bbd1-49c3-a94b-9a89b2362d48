<template>
  <div class="room-status-page">
    <!-- 布局容器 -->
    <RoomStatusLayout
      :layout-mode="layoutMode"
      :initial-sidebar-width="280"
      @sidebar-width-change="handleSidebarWidthChange"
    >
      <!-- 筛选器插槽 -->
      <template #filters>
        <RoomStatusFilters
          :layout-mode="layoutMode"
          :filters="filters"
          :building-options="buildingOptions"
          :floor-options="floorOptions"
          :room-type-options="roomTypeOptions"
          :status-options="statusOptions"
          :clean-status-options="cleanStatusOptions"
          @update:filters="handleFiltersUpdate"
          @building-change="handleBuildingChange"
          @floor-change="handleFloorChange"
          @room-type-change="handleRoomTypeChange"
          @status-change="handleStatusChange"
          @clean-status-change="handleCleanStatusChange"
          @search="handleSearchKeyword"
        />
      </template>

      <!-- 状态图例插槽 -->
      <template #legend>
        <StatusLegend
          :layout-mode="layoutMode"
          :clean-status-config="cleanStatusConfigWithCount"
          :room-status-config="roomStatusConfigWithCount"
          :selected-clean-status="filters.cleanStatus"
          :selected-status="filters.status"
          @clean-status-click="handleCleanStatusClick"
          @business-status-click="handleBusinessStatusClick"
        />
      </template>

      <!-- 统计信息插槽（已删除，因为状态图例已包含相同信息） -->
      <template #stats>
        <!-- 不再显示房态统计区域 -->
      </template>

      <!-- 控制按钮插槽 -->
      <template #controls>
        <RefreshControl
          :refresh-interval="refreshInterval"
          :is-refreshing="loading"
          :last-update-time="lastUpdateTime"
          :card-size="cardSize"
          :layout-mode="layoutMode"
          @refresh="handleRefresh"
          @interval-change="handleIntervalChange"
          @card-size-change="handleCardSizeChange"
          @layout-change="handleLayoutChange"
        />
      </template>

      <!-- 主内容区域插槽 -->
      <template #content>
        <RoomGrid
          :loading="loading"
          :error="error"
          :grouped-rooms="groupedRoomData"
          :card-size="cardSize"
          :hovered-connect-code="hoveredConnectCode"
          :force-rerender-key="forceRerenderKey"
          :status-text-map="statusTextMap"
          @retry="handleRetry"
          @refresh="handleRefresh"
          @room-click="handleRoomClickWithDetail"
          @room-contextmenu="handleRoomContextMenuWithCheckin"
          @room-mouseenter="handleRoomMouseEnter"
          @room-mouseleave="handleRoomMouseLeave"
          @connect-room-hover="handleConnectRoomHover"
        />
      </template>
    </RoomStatusLayout>

    <!-- 右键菜单 -->
    <RoomContextMenu
      v-if="contextMenuRoom"
      :show="contextMenuVisible"
      :x="contextMenuPosition.x"
      :y="contextMenuPosition.y"
      :room-data="contextMenuRoom"
      @update:show="contextMenuVisible = $event"
      @close="closeContextMenu"
      @action="handleContextMenuAction"
    />

    <!-- 房间详情弹窗 -->
    <RoomDetailModal
      v-if="selectedRoom"
      v-model:show="roomDetailVisible"
      :room-data="selectedRoom"
      @close="closeRoomDetail"
      @reservation="handleReservationFromDetail"
      @checkin="handleCheckInFromDetail"
      @checkout="handleCheckOutFromDetail"
      @room-service="handleRoomServiceFromDetail"
      @confirm-checkin="handleConfirmCheckInFromDetail"
      @cancel-reservation="handleCancelReservationFromDetail"
      @bill-detail="handleBillDetailFromDetail"
      @room-maintenance="handleRoomMaintenanceFromDetail"
      @room-cleaning="handleRoomCleaningFromDetail"
    />

    <!-- 办理入住弹窗 -->
    <CheckInModal
      v-model:show="showCheckInModal"
      :room-data="selectedRoomData"
      @success="handleModalSuccess"
      @refresh-room-status="handleRefreshRoomStatus"
    />

    <!-- 办理预订弹窗 -->
    <ReservationModal
      v-model:show="showReservationModal"
      :room-data="selectedRoomData"
      @success="handleModalSuccess"
    />

    <!-- 订单详情弹窗 -->
    <OrderDetailModal
      v-model:show="showOrderDetail"
      :order-data="selectedOrderData"
      :bill-id="selectedBillId"
      @print="handleOrderPrint"
      @export="handleOrderExport"
    />

    <!-- 客房服务弹窗 -->
    <RoomServiceModal
      v-model:show="showRoomServiceModal"
      :room-data="selectedRoomData"
      @submit="handleRoomServiceSubmit"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useMessage, useDialog } from 'naive-ui'

// 组件导入
import RoomStatusLayout from './components/RoomStatusLayout.vue'
import RoomStatusFilters from './components/RoomStatusFilters.vue'
import StatusLegend from './components/StatusLegend.vue'
import RefreshControl from './components/RefreshControl.vue'
import RoomGrid from './components/RoomGrid/RoomGrid.vue'
import RoomDetailModal from '@/components/hotel/RoomDetailModal.vue'
import RoomContextMenu from '@/components/hotel/RoomContextMenu.vue'
import CheckInModal from '@/components/hotel/CheckInModal.vue'
import ReservationModal from '@/components/hotel/ReservationModal.vue'
import OrderDetailModal from '@/components/hotel/OrderDetailModal.vue'
import RoomServiceModal from '@/components/hotel/RoomServiceModal.vue'

// Composables导入
import { useRoomData } from './composables/useRoomData'
import { useRoomFilters } from './composables/useRoomFilters'
import { useAutoRefresh } from './composables/useAutoRefresh'
import { useRoomInteraction } from './composables/useRoomInteraction'

// 服务导入
import roomDataTransform from './services/roomDataTransform'

const message = useMessage()
const $dialog = useDialog()

// 使用组合函数
const {
  loading,
  error,
  transformedRoomData,
  buildingList,
  floorList,
  roomTypeList,
  roomStatusConfig,
  cleanStatusConfig,
  lastUpdateTime,
  forceRerenderKey,
  fetchRoomData,
  retryFetch
} = useRoomData()

const {
  filters,
  buildingOptions,
  floorOptions,
  roomTypeOptions,
  statusOptions,
  cleanStatusOptions,
  filteredRoomData,
  updateFilter,
  updateFilters
} = useRoomFilters(
  transformedRoomData,
  buildingList,
  floorList,
  roomTypeList,
  roomStatusConfig,
  cleanStatusConfig
)

const {
  refreshInterval,
  setRefreshInterval,
  manualRefresh
} = useAutoRefresh(fetchRoomData)

// 对筛选后的数据进行分组
const groupedRoomData = computed(() => {
  if (!filteredRoomData.value.length) return []

  try {
    // 使用已导入的分组函数
    return roomDataTransform.groupRoomsByBuildingAndFloor(filteredRoomData.value)
  } catch (err) {
    console.error('房间数据分组失败:', err)
    return []
  }
})

const {
  selectedRoom,
  hoveredConnectCode,
  contextMenuVisible,
  contextMenuPosition,
  contextMenuRoom,
  handleRoomClick,
  handleRoomContextMenu,
  handleRoomMouseEnter,
  handleRoomMouseLeave,
  handleConnectRoomHover,
  closeContextMenu
} = useRoomInteraction()

// 弹窗状态
const roomDetailVisible = ref(false)
const showCheckInModal = ref(false)
const showReservationModal = ref(false)
const showOrderDetail = ref(false)
const showRoomServiceModal = ref(false)
const selectedRoomData = ref(null)
const selectedOrderData = ref(null)
const selectedBillId = ref(null)

// 重写房间点击处理，添加详情弹窗逻辑
const handleRoomClickWithDetail = async (room, event) => {
  // 调用原始的点击处理
  handleRoomClick(room, event)

  // 设置选中房间并显示详情弹窗
  if (room) {
    selectedRoom.value = room
    roomDetailVisible.value = true
  }
}

// 重写房间右键处理，支持空房间直接入住
const handleRoomContextMenuWithCheckin = async (room, event) => {
  // 调用原始的右键处理，获取返回的事件类型
  const result = handleRoomContextMenu(room, event)

  if (result && result.type === 'room-checkin') {
    // 空房间直接弹出入住弹窗
    console.log('空房间右键，弹出入住弹窗')
    selectedRoomData.value = room
    showCheckInModal.value = true
  }
  // 其他情况（room-contextmenu）会自动显示右键菜单，因为useRoomInteraction已经处理了
}

// 关闭房间详情弹窗
const closeRoomDetail = () => {
  roomDetailVisible.value = false
  selectedRoom.value = null
}

// 弹窗事件处理函数
const handleReservationFromDetail = (roomData) => {
  roomDetailVisible.value = false
  selectedRoomData.value = roomData
  showReservationModal.value = true
}

const handleCheckInFromDetail = (roomData) => {
  roomDetailVisible.value = false
  selectedRoomData.value = roomData
  showCheckInModal.value = true
}

const handleCheckOutFromDetail = (roomData) => {
  roomDetailVisible.value = false
  handleQuickCheckout(roomData)
}

const handleRoomServiceFromDetail = (roomData) => {
  roomDetailVisible.value = false
  selectedRoomData.value = roomData
  showRoomServiceModal.value = true
}

const handleConfirmCheckInFromDetail = (roomData) => {
  roomDetailVisible.value = false
  message.info(`确认房间 ${roomData.room_number || roomData.roomNumber} 入住`)
}

const handleCancelReservationFromDetail = (roomData) => {
  roomDetailVisible.value = false
  message.info(`取消房间 ${roomData.roomNumber || roomData.room_number} 预订`)
}

const handleBillDetailFromDetail = (roomData) => {
  roomDetailVisible.value = false

  const billId = roomData.bill_id || roomData.billId
  if (billId) {
    selectedBillId.value = billId
    selectedOrderData.value = roomData
    showOrderDetail.value = true
  } else {
    message.warning('该房间暂无账单信息')
  }
}

const handleRoomMaintenanceFromDetail = (roomData) => {
  roomDetailVisible.value = false
  message.info(`房间 ${roomData.roomNumber || roomData.room_number} 维修管理`)
}

const handleRoomCleaningFromDetail = (roomData) => {
  roomDetailVisible.value = false
  message.info(`开始清洁房间 ${roomData.roomNumber || roomData.room_number}`)
}

// 本地状态
const layoutMode = ref('left')
const cardSize = ref('medium')

// 计算属性
const roomStatusConfigWithCount = computed(() => {
  // 确保 roomStatusConfig.value 是数组
  if (!Array.isArray(roomStatusConfig.value)) {
    return []
  }
  return roomStatusConfig.value.map(status => ({
    ...status,
    count: calculateStatusCount(status.sign || status.status_sign)
  }))
})

const cleanStatusConfigWithCount = computed(() => {
  // 确保 cleanStatusConfig.value 是数组
  if (!Array.isArray(cleanStatusConfig.value)) {
    return []
  }
  return cleanStatusConfig.value.map(status => ({
    ...status,
    count: calculateCleanStatusCount(status.id || status.status_id)
  }))
})

const statusTextMap = computed(() => {
  const map = {}
  // 确保 roomStatusConfig.value 是数组
  if (Array.isArray(roomStatusConfig.value)) {
    roomStatusConfig.value.forEach(status => {
      map[status.sign || status.status_sign] = status.name || status.status_name
    })
  }
  return map
})

// 移除了contextMenuOptions，现在使用RoomContextMenu组件内部处理

// 事件处理函数
const handleFiltersUpdate = (newFilters) => {
  updateFilters(newFilters)
}

const handleBuildingChange = (value) => {
  updateFilter('building', value)
}

const handleFloorChange = (value) => {
  updateFilter('floor', value)
}

const handleRoomTypeChange = (value) => {
  updateFilter('roomType', value)
}

const handleStatusChange = (value) => {
  updateFilter('status', value)
}

const handleCleanStatusChange = (value) => {
  updateFilter('cleanStatus', value)
}

const handleSearchKeyword = (keyword) => {
  updateFilter('keyword', keyword)
}

const handleCleanStatusClick = (statusId) => {
  updateFilter('cleanStatus', filters.value.cleanStatus === statusId ? null : statusId)
}

const handleBusinessStatusClick = (statusSign) => {
  updateFilter('status', filters.value.status === statusSign ? null : statusSign)
}



const handleRefresh = async () => {
  try {
    await manualRefresh()
    message.success('刷新成功')
  } catch (error) {
    message.error('刷新失败')
  }
}



const handleIntervalChange = (interval) => {
  setRefreshInterval(interval)
}

const handleCardSizeChange = (size) => {
  cardSize.value = size
}

const handleLayoutChange = (layout) => {
  layoutMode.value = layout
}

const handleSidebarWidthChange = (width) => {
  // 可以保存到本地存储
  localStorage.setItem('roomStatus.sidebarWidth', width)
}

const handleRetry = () => {
  retryFetch()
}

// const handleRoomRefresh = async (roomId) => {
//   try {
//     await refreshRoomData(roomId)
//     message.success('房间刷新成功')
//   } catch (error) {
//     message.error('房间刷新失败')
//   }
// }

// 移除了handleContextMenuSelect，现在使用RoomContextMenu组件内部处理

// 弹窗成功处理
const handleModalSuccess = (data) => {
  message.success('操作成功！')
  showCheckInModal.value = false
  showReservationModal.value = false
  selectedRoomData.value = null

  // 刷新房间数据
  fetchRoomData({ force: true })
}

// 处理刷新房态事件
const handleRefreshRoomStatus = () => {
  console.log('收到刷新房态请求，正在刷新...')
  // 强制刷新房间数据
  fetchRoomData({ force: true })
}

// 快速退房
const handleQuickCheckout = (room) => {
  message.info(`正在为房间 ${room.roomNumber || room.room_number} 办理退房...`)
  // TODO: 实现快速退房逻辑
}

// 订单详情弹窗事件
const handleOrderPrint = (orderData) => {
  message.info('正在打印订单...')
  // TODO: 实现订单打印逻辑
}

const handleOrderExport = (orderData) => {
  message.info('正在导出订单...')
  // TODO: 实现订单导出逻辑
}

// 客房服务提交处理
const handleRoomServiceSubmit = async (serviceRequest) => {
  try {
    console.log('提交客房服务请求:', serviceRequest)

    // TODO: 调用API提交客房服务请求
    // await roomServiceApi.submitRequest(serviceRequest)

    message.success(`房间 ${serviceRequest.roomNumber} 的客房服务请求已提交`)
    showRoomServiceModal.value = false
    selectedRoomData.value = null

    // 可以选择刷新房间数据
    // fetchRoomData({ force: true })
  } catch (error) {
    console.error('提交客房服务请求失败:', error)
    message.error('提交客房服务请求失败，请重试')
  }
}

// 右键菜单事件处理
const handleContextMenuAction = (data) => {
  console.log('主页面: 接收到右键菜单操作', data)
  const { action, roomData } = data

  switch (action) {
    case 'detail':
      selectedRoom.value = roomData
      roomDetailVisible.value = true
      break
    case 'checkin':
      selectedRoomData.value = roomData
      showCheckInModal.value = true
      break
    case 'reservation':
      selectedRoomData.value = roomData
      showReservationModal.value = true
      break
    case 'checkout':
      handleQuickCheckout(roomData)
      break
    case 'extend':
      handleExtendCheckout(roomData)
      break
    case 'change-room':
      handleChangeRoom(roomData)
      break
    case 'room-service':
      selectedRoomData.value = roomData
      showRoomServiceModal.value = true
      break
    case 'cancel-reservation':
      handleCancelReservation(roomData)
      break
    case 'confirm-checkout':
      handleConfirmCheckout(roomData)
      break
    case 'start-cleaning':
      handleStartCleaning(roomData)
      break
    case 'complete-cleaning':
      handleCompleteCleaning(roomData)
      break
    case 'maintenance':
      handleStartMaintenance(roomData)
      break
    case 'complete-maintenance':
      handleCompleteMaintenance(roomData)
      break
    case 'toggle-block':
      handleToggleBlock(roomData)
      break
    case 'history':
      selectedRoom.value = roomData
      roomDetailVisible.value = true
      break
    case 'refresh':
      fetchRoomData({ force: true })
      break
    default:
      console.log('未处理的右键菜单操作:', action)
  }
}

// 右键菜单具体操作处理函数

// 延期退房
const handleExtendCheckout = (roomData) => {
  const roomNumber = roomData.roomNumber || roomData.room_number
  $dialog.warning({
    title: '延期退房',
    content: `确定要为房间 ${roomNumber} 办理延期退房吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        // TODO: 调用延期退房API
        message.success(`房间 ${roomNumber} 延期退房成功`)
        await fetchRoomData({ force: true })
      } catch (error) {
        console.error('延期退房失败:', error)
        message.error('延期退房失败，请重试')
      }
    }
  })
}

// 换房
const handleChangeRoom = (roomData) => {
  const roomNumber = roomData.roomNumber || roomData.room_number
  // TODO: 打开换房弹窗
  message.info(`正在为房间 ${roomNumber} 办理换房...`)
}

// 取消预订
const handleCancelReservation = (roomData) => {
  const roomNumber = roomData.roomNumber || roomData.room_number
  $dialog.warning({
    title: '取消预订',
    content: `确定要取消房间 ${roomNumber} 的预订吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        // TODO: 调用取消预订API
        message.success(`房间 ${roomNumber} 预订已取消`)
        await fetchRoomData({ force: true })
      } catch (error) {
        console.error('取消预订失败:', error)
        message.error('取消预订失败，请重试')
      }
    }
  })
}

// 确认退房
const handleConfirmCheckout = (roomData) => {
  const roomNumber = roomData.roomNumber || roomData.room_number
  $dialog.info({
    title: '确认退房',
    content: `确定要确认房间 ${roomNumber} 的退房吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        // TODO: 调用确认退房API
        message.success(`房间 ${roomNumber} 退房已确认`)
        await fetchRoomData({ force: true })
      } catch (error) {
        console.error('确认退房失败:', error)
        message.error('确认退房失败，请重试')
      }
    }
  })
}

// 开始清洁
const handleStartCleaning = (roomData) => {
  const roomNumber = roomData.roomNumber || roomData.room_number
  $dialog.info({
    title: '开始清洁',
    content: `确定要开始清洁房间 ${roomNumber} 吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        // TODO: 调用开始清洁API
        message.success(`房间 ${roomNumber} 开始清洁`)
        await fetchRoomData({ force: true })
      } catch (error) {
        console.error('开始清洁失败:', error)
        message.error('开始清洁失败，请重试')
      }
    }
  })
}

// 完成清洁
const handleCompleteCleaning = (roomData) => {
  const roomNumber = roomData.roomNumber || roomData.room_number
  $dialog.success({
    title: '完成清洁',
    content: `确定要完成房间 ${roomNumber} 的清洁吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        // TODO: 调用完成清洁API
        message.success(`房间 ${roomNumber} 清洁完成`)
        await fetchRoomData({ force: true })
      } catch (error) {
        console.error('完成清洁失败:', error)
        message.error('完成清洁失败，请重试')
      }
    }
  })
}

// 开始维修
const handleStartMaintenance = (roomData) => {
  const roomNumber = roomData.roomNumber || roomData.room_number
  $dialog.warning({
    title: '报修',
    content: `确定要为房间 ${roomNumber} 报修吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        // TODO: 调用报修API
        message.success(`房间 ${roomNumber} 报修成功`)
        await fetchRoomData({ force: true })
      } catch (error) {
        console.error('报修失败:', error)
        message.error('报修失败，请重试')
      }
    }
  })
}

// 完成维修
const handleCompleteMaintenance = (roomData) => {
  const roomNumber = roomData.roomNumber || roomData.room_number
  $dialog.success({
    title: '维修完成',
    content: `确定要完成房间 ${roomNumber} 的维修吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        // TODO: 调用完成维修API
        message.success(`房间 ${roomNumber} 维修完成`)
        await fetchRoomData({ force: true })
      } catch (error) {
        console.error('完成维修失败:', error)
        message.error('完成维修失败，请重试')
      }
    }
  })
}

// 切换封锁状态
const handleToggleBlock = (roomData) => {
  const roomNumber = roomData.roomNumber || roomData.room_number
  const isBlocked = roomData.is_blocked || roomData.isBlocked
  const action = isBlocked ? '解除封锁' : '封锁房间'

  $dialog.warning({
    title: action,
    content: `确定要${action}房间 ${roomNumber} 吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        // TODO: 调用切换封锁状态API
        message.success(`房间 ${roomNumber} ${action}成功`)
        await fetchRoomData({ force: true })
      } catch (error) {
        console.error('切换封锁状态失败:', error)
        message.error('操作失败，请重试')
      }
    }
  })
}

// 辅助函数
const calculateStatusCount = (statusSign) => {
  // 确保 transformedRoomData.value 是数组
  if (!Array.isArray(transformedRoomData.value)) {
    return 0
  }

  // 状态配置sign到转换后status的映射关系
  const signToStatusMap = {
    'stay': 'occupied',        // 在住
    'empty': 'available',      // 空房
    'locked': 'blocked',       // 锁房
    'closed': 'maintenance',   // 关房/维修
    'book': 'reserved',        // 预定
    'to_check_out': 'checkout' // 申请退房
  }



  // 获取对应的转换后状态值
  const targetStatus = signToStatusMap[statusSign] || statusSign

  const matchingRooms = transformedRoomData.value.filter(room => {
    // 使用转换后的status字段进行匹配
    const roomStatus = room.status
    return roomStatus === targetStatus
  })

  return matchingRooms.length
}

const calculateCleanStatusCount = (statusId) => {
  // 确保 transformedRoomData.value 是数组
  if (!Array.isArray(transformedRoomData.value)) {
    return 0
  }

  // 首先尝试通过ID匹配
  let matchingRooms = transformedRoomData.value.filter(room => {
    const cleanStatus = room.cleanStatus || room.clear_status_id
    return cleanStatus === statusId
  })

  // 如果ID匹配没有结果，尝试通过名称匹配
  if (matchingRooms.length === 0) {
    // 建立ID到名称的映射关系
    const statusIdToNameMap = {
      1: '净',
      2: '脏',
      3: '清洁中',
      4: '维修',
      5: '封锁',
      6: '查房'
    }

    // 根据配置动态建立映射关系
    if (Array.isArray(cleanStatusConfig.value)) {
      cleanStatusConfig.value.forEach(config => {
        if (config.id && config.name) {
          statusIdToNameMap[config.id] = config.name
        }
      })
    }

    const targetStatusName = statusIdToNameMap[statusId]

    if (targetStatusName) {
      matchingRooms = transformedRoomData.value.filter(room => {
        const cleanStatusName = room.cleanStatusName || room.clear_status_name || ''
        return cleanStatusName === targetStatusName
      })
    }
  }

  return matchingRooms.length
}

// 生命周期
onMounted(async () => {
  // 禁用外层布局的滚动，防止双滚动条
  const mainElement = document.querySelector('main.flex-1.overflow-auto')
  if (mainElement) {
    mainElement.style.overflow = 'hidden'
  }

  // 从本地存储恢复设置
  const savedLayoutMode = localStorage.getItem('roomStatus.layoutMode')
  const savedCardSize = localStorage.getItem('roomStatus.cardSize')

  if (savedLayoutMode) layoutMode.value = savedLayoutMode
  if (savedCardSize) cardSize.value = savedCardSize

  // 初始化数据
  try {
    await fetchRoomData()
  } catch (error) {
    message.error('初始化失败')
  }
})

onUnmounted(() => {
  // 恢复外层布局的滚动设置
  const mainElement = document.querySelector('main.flex-1')
  if (mainElement) {
    mainElement.style.overflow = 'auto'
  }

  // 保存设置到本地存储
  localStorage.setItem('roomStatus.layoutMode', layoutMode.value)
  localStorage.setItem('roomStatus.cardSize', cardSize.value)
})
</script>

<style scoped>
.room-status-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8fafc;
  overflow: hidden;
}
</style>
