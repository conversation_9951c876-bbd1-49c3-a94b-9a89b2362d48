{"name": "vue-naive-admin", "type": "module", "version": "2.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint:fix": "eslint --fix", "postinstall": "npx simple-git-hooks", "up": "taze major -I"}, "dependencies": {"@arco-design/color": "^0.4.0", "@vueuse/core": "^13.1.0", "axios": "^1.8.4", "dayjs": "^1.11.13", "echarts": "^5.6.0", "js-md5": "^0.8.3", "less": "^4.3.0", "lodash-es": "^4.17.21", "naive-ui": "^2.41.0", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.2.0", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-i18n": "^11.1.11", "vue-router": "^4.5.0", "xlsx": "^0.18.5"}, "devDependencies": {"@antfu/eslint-config": "^4.12.0", "@iconify/json": "^2.2.331", "@unocss/eslint-config": "^66.0.0", "@unocss/eslint-plugin": "^66.0.0", "@unocss/preset-rem-to-px": "^66.0.0", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "eslint": "^9.25.1", "eslint-plugin-format": "^1.0.1", "esno": "^4.8.0", "fs-extra": "^11.3.0", "glob": "^11.0.3", "lint-staged": "^15.5.1", "rollup-plugin-visualizer": "^5.14.0", "simple-git-hooks": "^2.13.0", "taze": "^19.0.4", "unocss": "^66.0.0", "unplugin-auto-import": "^19.1.2", "unplugin-vue-components": "^28.5.0", "vite": "^6.3.3", "vite-plugin-router-warn": "^1.0.0", "vite-plugin-vue-devtools": "^7.7.5", "vue3-intro-step": "^1.0.5"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}}