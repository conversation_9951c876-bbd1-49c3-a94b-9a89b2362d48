# 接口数据结构文档

本文档记录了系统中各个接口的真实返回数据结构，用于开发新功能时参考。

## 1. getUsableRoom - 获取可用房间列表

**接口路径：** `/api/room/getUsableRoom`
**请求方法：** GET/POST
**用途：** 获取酒店房态页面的房间列表数据

### 完整数据结构

```json
{
  "code": 0,
  "msg": "get_success",
  "data": {
    "list": [
      {
        "building": "1栋-超长文本测试超长文本测试超长文本测试超长文本测试",
        "building_number": 1,
        "room_count": 68,
        "floor_list": [
          {
            "floor": "1层",
            "floor_number": 1,
            "room_count": 57,
            "room_list": [
              {
                "id": 123,
                "shop_id": 36,
                "room_type_id": 3,
                "floor_id": 1,
                "room_number": "1001",
                "building_id": 1,
                "room_status": "occupied",
                "sort_order": 1,
                "room_type": "总统套房",
                "building_name": "1栋-超长文本测试超长文本测试超长文本测试超长文本测试",
                "floor_number": 1,
                "clean_status": "dirty",
                "business_status": "occupied",
                "bill_id": 2101,
                "bill_info": {
                  "link_man": "张迁2",
                  "leave_time_plan": 1755849600,
                  "enter_time_plan": 1755231964,
                  "enter_time": 1724838364,
                  "memo": "",
                  "bill_status": 1,
                  "leave_time": null,
                  "phone": "***********",
                  "member_level": "超级黑钻会员",
                  "member_level_color": "#000000",
                  "order_source": "步入",
                  "order_source_color": "#1890ff",
                  "sale_type": "全天房",
                  "sale_type_color": "#52c41a",
                  "total_amount": 0,
                  "paid_amount": 0,
                  "deposit_amount": 0,
                  "balance_amount": 0,
                  "stay_days": 0,
                  "check_in_date": "未设置",
                  "check_out_date": "未设置",
                  "bill_code": null,
                  "order_id": null,
                  "guest_count": 1,
                  "children_count": 0,
                  "room_price": 0,
                  "discount_amount": 0
                },
                "guest_info": {
                  "name": "张迁2",
                  "phone": "***********",
                  "member_level": "超级黑钻会员",
                  "member_level_short": "超钻"
                },
                "pricing_info": {
                  "total_amount": 0,
                  "paid_amount": 0,
                  "deposit_amount": 0,
                  "balance_amount": 0
                }
              }
            ]
          },
          {
            "floor": "3层",
            "floor_number": 3,
            "room_count": 11,
            "room_list": [
              {
                "id": 456,
                "room_number": "A100",
                "room_type": "大床房",
                "room_status": "available",
                "clean_status": "clean",
                "business_status": "available",
                "bill_id": null,
                "bill_info": null,
                "guest_info": null
              }
            ]
          }
        ]
      }
    ],
    "stay_rate": 16.3,
    "staying_count": 28,
    "total_count": 172,
    "room_type_count": {
      "大床房": 120,
      "总统套房": 30,
      "海景房": 15,
      "山景房": 7
    },
    "clean_status_count": {
      "clean": 144,
      "dirty": 25,
      "cleaning": 2,
      "maintenance": 1
    },
    "business_status_count": {
      "available": 144,
      "occupied": 28
    }
  }
}
```

### 关键字段说明

#### 房间数据字段 (room_list 中的每个房间对象)
- `id`: 房间ID (数字)
- `room_number`: 房间号 (字符串)
- `room_type`: 房型名称 (字符串)
- `room_status`: 房间状态 ("available", "occupied", "maintenance" 等)
- `clean_status`: 清洁状态 ("clean", "dirty", "cleaning", "maintenance")
- `business_status`: 业务状态 ("available", "occupied")
- **`bill_id`**: 账单ID (数字，关键字段！用于调用其他接口)
- `bill_info`: 账单详细信息 (对象，当房间有客人时存在)

#### bill_info 字段详情 (当房间有客人时)
- `link_man`: 联系人姓名
- `phone`: 联系电话
- `member_level`: 会员等级
- `order_source`: 订单来源
- `sale_type`: 售卖类型
- `total_amount`: 总金额
- `paid_amount`: 已付金额
- `deposit_amount`: 押金金额
- `balance_amount`: 余额
- `stay_days`: 住宿天数
- `check_in_date`: 入住日期
- `check_out_date`: 退房日期

### 使用示例

```javascript
// 获取房间列表
const response = await roomStatusApi.getUsableRoom(filters)

// 遍历楼栋和楼层
response.data.list.forEach(building => {
  building.floor_list.forEach(floor => {
    floor.room_list.forEach(room => {
      // 检查房间是否有客人
      if (room.bill_id && room.bill_info) {
        console.log(`房间 ${room.room_number} 有客人: ${room.bill_info.link_man}`)
        console.log(`账单ID: ${room.bill_id}`) // 用于调用其他接口
      } else {
        console.log(`房间 ${room.room_number} 空房`)
      }
    })
  })
})
```

### 注意事项

1. **bill_id 字段是关键**：用于调用其他相关接口（如 getTtLockConfig）
2. **字段名准确性**：使用 `bill_id` 而不是 `billId` 或 `bill_code`
3. **空房处理**：空房的 `bill_id` 为 `null`，`bill_info` 为 `null`
4. **数据层次**：数据结构为 楼栋 -> 楼层 -> 房间 的三层嵌套结构

---

## 2. getTtLockConfig - 获取门锁配置

**接口路径：** `/api/lock/getTtLockConfig`
**请求方法：** POST
**用途：** 根据账单ID获取门锁配置信息

### 请求参数

```json
{
  "bill_id": 2101  // 从 getUsableRoom 接口获取的 bill_id
}
```

### 返回数据结构

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "lock_config": {
      // 门锁配置信息
      // TODO: 补充真实返回数据结构
    }
  }
}
```

**注意：** 此接口的详细返回数据结构待补充，需要在有有效 bill_id 的房间上测试获取。

---

## 3. 办理入住接口 - createCheckin

**接口路径：** `/api/checkin/create`
**请求方法：** POST
**用途：** 办理客人入住手续

### 请求参数结构

```json
{
  "room_sale_type": 385,
  "user_info": {
    "link_man": "张迁",
    "link_phone": "***********",
    "common_code": "APCWRccWoL0tcT9bWD3pNBlB7wMidMEI"
  },
  "price_project": 2,
  "enter_time_plan": 1756382445,
  "times": 1,
  "bill_source": 9,
  "room_list": [
    {
      "room_type_id": 1,
      "room_id": 253,
      "room_service_selected": 204,
      "user_info": [
        {
          "name": "张迁",
          "gender": 1,
          "phone": "***********",
          "identification_type": 1,
          "identification_number": "xxxx"
        }
      ],
      "custom_price": {
        "custom_cash_pledge": 10,
        "custom_room_price": [
          {
            "data": "2025-08-28",
            "price": 100
          }
        ]
      }
    }
  ],
  "stay_type": 1,
  "secrecy": 0
}
```

### 入住接口字段说明

#### 主要参数

- `room_sale_type`: 房间销售类型ID
- `user_info`: 主要联系人信息
  - `link_man`: 联系人姓名
  - `link_phone`: 联系人电话
  - **`common_code`**: 客人唯一标识码（关键字段！）
- `price_project`: 价格方案ID
- `enter_time_plan`: 计划入住时间（时间戳）
- `times`: 入住天数
- `bill_source`: 账单来源
- `stay_type`: 住宿类型
- `secrecy`: 是否保密

#### 房间列表 (room_list)

- `room_type_id`: 房型ID
- `room_id`: 房间ID
- `room_service_selected`: 选择的房间服务
- `user_info`: 入住人信息数组
  - `name`: 姓名
  - `gender`: 性别（1=男，2=女）
  - `phone`: 电话
  - `identification_type`: 证件类型
  - `identification_number`: 证件号码
- `custom_price`: 自定义价格
  - `custom_cash_pledge`: 自定义押金
  - `custom_room_price`: 自定义房价数组

### 重要注意事项

#### common_code 获取方法

`common_code` 是客人的唯一标识码，需要从以下途径获取：

1. **从会员系统获取**：

   ```javascript
   // 通过手机号查询会员信息
   const memberInfo = await getMemberByPhone(phone)
   const commonCode = memberInfo.common_code
   ```

2. **从预订系统获取**：

   ```javascript
   // 从预订记录中获取
   const reservationInfo = await getReservationDetail(reservationId)
   const commonCode = reservationInfo.user_info.common_code
   ```

3. **新客人注册**：

   ```javascript
   // 为新客人创建 common_code
   const newMember = await createMember({
     name: "张迁",
     phone: "***********"
   })
   const commonCode = newMember.common_code
   ```

#### 数据验证要点

- `common_code` 必须有效且唯一
- `room_id` 必须是可用房间
- `enter_time_plan` 不能早于当前时间
- `user_info` 数组至少包含一个入住人
- 证件信息必须完整且格式正确

### 入住接口使用示例

```javascript
// 办理入住
async function handleCheckin(guestInfo, roomInfo) {
  // 1. 获取或创建客人的 common_code
  let commonCode = await getGuestCommonCode(guestInfo.phone)
  if (!commonCode) {
    const newGuest = await createGuest(guestInfo)
    commonCode = newGuest.common_code
  }

  // 2. 构建入住参数
  const checkinParams = {
    room_sale_type: roomInfo.saleType,
    user_info: {
      link_man: guestInfo.name,
      link_phone: guestInfo.phone,
      common_code: commonCode  // 关键字段
    },
    price_project: roomInfo.priceProject,
    enter_time_plan: Math.floor(Date.now() / 1000),
    times: roomInfo.stayDays,
    bill_source: 9, // 前台入住
    room_list: [{
      room_type_id: roomInfo.roomTypeId,
      room_id: roomInfo.roomId,
      room_service_selected: roomInfo.serviceId,
      user_info: [guestInfo],
      custom_price: roomInfo.customPrice
    }],
    stay_type: 1,
    secrecy: 0
  }

  // 3. 调用入住接口
  const response = await request.post('/api/checkin/create', checkinParams)
  return response
}
```

---

## 4. 办理入住相关接口

### 4.1 getRoomSellType - 获取销售类型

**接口路径：** `/admin/RoomSellType/getRoomSellType`
**请求方法：** POST
**用途：** 获取房间销售类型列表（全日房、时租房等）

#### 请求参数

```json
{
  "status": 1
}
```

#### 参数说明

- `status`: 状态筛选（1=启用，0=禁用）

#### 响应数据结构

```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "id": 1,
      "name": "全日房",
      "sign": "standard",
      "status": 1,
      "create_time": 1234567890,
      "update_time": 1234567890
    },
    {
      "id": 2,
      "name": "时租房",
      "sign": "hour",
      "status": 1,
      "create_time": 1234567890,
      "update_time": 1234567890
    }
  ]
}
```

#### 销售类型字段说明

- `id`: 销售类型ID
- `name`: 销售类型名称
- `sign`: 销售类型标识（standard=全日房，hour=时租房，long_standard=月租房）
- `status`: 状态（1=启用，0=禁用）

### 4.2 getRoomSaleType - 获取销售规则

**接口路径：** `/admin/RoomSaleType/getRoomSaleType`
**请求方法：** POST
**用途：** 根据销售类型获取具体的销售规则

#### 销售规则请求参数

```json
{
  "sell_type": 1,
  "status": 1
}
```

#### 销售规则参数说明

- `sell_type`: 销售类型ID（来自 getRoomSellType 接口）
- `status`: 状态筛选（1=启用，0=禁用）

#### 销售规则响应数据

```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "id": 385,
      "name": "标准全日房",
      "sign": "standard",
      "sell_type": 1,
      "unit": "天",
      "status": 1,
      "create_time": 1234567890,
      "update_time": 1234567890
    }
  ]
}
```

#### 销售规则字段说明

- `id`: 销售规则ID（用于 getRoomPriceByDate 接口）
- `name`: 销售规则名称
- `sign`: 规则标识
- `sell_type`: 所属销售类型ID
- `unit`: 计费单位（天、小时、月等）

### 4.3 getRoomPriceByDate - 获取房价和套餐

**接口路径：** `/admin/RoomPrice/getRoomPriceByDate`
**请求方法：** POST
**用途：** 根据销售规则、房型、日期等获取房价和可用套餐

#### 房价查询请求参数

```json
{
  "date": "2025-08-28",
  "days": 1,
  "room_sale_type_id": 385,
  "room_type_id": 1,
  "member_grade_id": 2
}
```

#### 房价查询参数说明

- `date`: 入住日期（YYYY-MM-DD 格式）
- `days`: 入住天数
- `room_sale_type_id`: 销售规则ID（来自 getRoomSaleType 接口）
- `room_type_id`: 房型ID
- `member_grade_id`: 会员等级ID（可选，用于获取会员价格）

#### 房价查询响应数据

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "room_price": [
      {
        "room_price": 100,
        "date": "2025-08-28",
        "room_type_id": 1,
        "member_grade_id": 2
      }
    ],
    "room_service": [
      {
        "id": 204,
        "service_name": "标准服务",
        "service_content": "包含早餐、WiFi",
        "price": 0,
        "status": 1,
        "is_show": 1,
        "cancelable": 1,
        "create_time": 1234567890,
        "update_time": 1234567890
      },
      {
        "id": 205,
        "service_name": "豪华套餐",
        "service_content": "包含早餐、WiFi、下午茶",
        "price": 50,
        "status": 1,
        "is_show": 1,
        "cancelable": 1,
        "create_time": 1234567890,
        "update_time": 1234567890
      }
    ]
  }
}
```

#### 房价响应字段说明

**room_price 数组：**

- `room_price`: 房间价格
- `date`: 价格对应的日期
- `room_type_id`: 房型ID
- `member_grade_id`: 会员等级ID（如果请求中包含）

**room_service 数组：**

- `id`: 服务ID（用于入住时的 room_service_selected 字段）
- `service_name`: 服务名称
- `service_content`: 服务内容描述
- `price`: 服务价格（0表示免费）
- `status`: 服务状态（1=启用，0=禁用）
- `is_show`: 是否显示（1=显示，0=隐藏）
- `cancelable`: 是否可取消（1=可取消，0=不可取消）

### 4.4 接口调用流程

办理入住弹窗打开时的接口调用顺序：

```text
1. getRoomSellType (获取销售类型)
   ↓
2. getRoomSaleType (根据选中的销售类型获取销售规则)
   ↓
3. getRoomPriceByDate (根据销售规则获取房价和套餐)
   ↓
4. 显示默认房价和套餐选项
```

### 4.5 使用示例

```javascript
// 1. 获取销售类型
const sellTypes = await api.getRoomSellType({ status: 1 })
const defaultSellType = sellTypes.data[0].id

// 2. 获取销售规则
const saleRules = await api.getRoomSaleType({
  sell_type: defaultSellType,
  status: 1
})
const defaultSaleRule = saleRules.data[0].id

// 3. 获取房价和套餐
const priceData = await api.getRoomPriceByDate({
  date: '2025-08-28',
  days: 1,
  room_sale_type_id: defaultSaleRule,
  room_type_id: roomTypeId
})

// 4. 应用数据
const roomPrice = priceData.data.room_price[0].room_price
const serviceOptions = priceData.data.room_service.filter(s =>
  s.status === 1 && s.is_show === 1
)
```

---

## 5. 其他相关接口

### 3.1 房间状态更新接口

**接口路径：** `/api/room/updateStatus`
**用途：** 更新房间清洁状态、维修状态等

### 3.2 客人入住接口

**接口路径：** `/api/checkin/create`
**用途：** 办理客人入住手续

### 3.3 客人退房接口

**接口路径：** `/api/checkout/process`
**用途：** 办理客人退房手续

---

## 4. 数据字段映射表

### 4.1 房间状态映射

```javascript
const ROOM_STATUS = {
  'available': '空房',
  'occupied': '在住',
  'maintenance': '维修',
  'out_of_order': '停用'
}

const CLEAN_STATUS = {
  'clean': '干净',
  'dirty': '脏房',
  'cleaning': '清洁中',
  'maintenance': '维修'
}
```

### 4.2 会员等级映射

```javascript
const MEMBER_LEVEL_SHORT = {
  '散客': '散客',
  '铜牌会员': '铜牌',
  '银牌会员': '银牌',
  '黄金会员': '黄金',
  '铂金会员': '铂金',
  '钻石会员': '钻石',
  '超级黑钻会员': '超钻'
}
```

### 4.3 订单来源映射

```javascript
const ORDER_SOURCE = {
  '步入': { name: '步入', color: '#1890ff' },
  '携程': { name: '携程', color: '#ff7a45' },
  '微信小程序': { name: '微信', color: '#52c41a' },
  '美团': { name: '美团', color: '#faad14' }
}
```

---

---

## 5. 数据流分析与问题记录

### 5.1 房态数据流：API → 转换 → 组件 → 显示

#### 数据流路径

```text
1. API接口 (getUsableRoom)
   ↓ 返回原始数据
2. 数据转换层 (roomDataTransform.js)
   ↓ transformSingleRoom 方法
3. 组件接收 (RoomDetailModal.vue)
   ↓ props.roomData
4. 界面显示 (订单详情按钮)
```

#### 关键数据字段传递

```javascript
// 1. API原始数据
{
  "bill_id": 2101,           // ← 关键字段
  "bill_info": { ... },
  "room_number": "1001",
  // ... 其他35个字段
}

// 2. 数据转换后 (transformSingleRoom)
{
  "bill_id": 2101,           // ← 必须保留！
  "bill_info": { ... },
  "roomNumber": "1001",      // 转换为驼峰命名
  // ... 其他转换后的字段
}

// 3. 组件接收
props.roomData.bill_id      // ← 用于调用 getTtLockConfig
```

### 5.2 常见问题记录

#### 问题1：bill_id 字段丢失 (2025-08-28)

**问题描述：**

- 房间详情弹窗中"查看订单详情"按钮无法正确获取 `bill_id`
- `getTtLockConfig` 接口无法被调用

**问题原因：**

- `roomDataTransform.js` 中的 `transformSingleRoom` 方法没有保留 `bill_id` 字段
- 数据在转换过程中丢失了关键字段

**解决方案：**
在 `src/views/hotel/roomStatus/services/roomDataTransform.js` 第185-188行添加：

```javascript
// 保留账单相关字段
bill_id: room.bill_id || null, // 关键字段！用于调用其他接口
bill_info: room.bill_info || null,
billInfo: room.bill_info || null, // 保持向后兼容
```

**验证方法：**

1. 点击有客人的房间卡片
2. 检查房间详情弹窗是否显示正确的订单号
3. 点击"查看订单详情"按钮
4. 查看控制台是否成功调用 `getTtLockConfig` 接口

**预防措施：**

- 在数据转换时，确保保留所有业务关键字段
- 添加字段映射注释，说明哪些字段用于调用其他接口
- 定期检查数据转换逻辑的完整性

#### 问题2：入住接口参数格式不统一 (2025-08-28)

**问题描述：**

- 现有入住功能使用的参数格式与标准格式不一致
- `common_code` 获取逻辑缺失或不完整
- 参数字段名和数据类型不符合接口要求

**解决方案：**

1. **修改 CheckInModal.vue 的 buildCheckInParams 函数**：

```javascript
// 新的参数格式
const baseParams = {
  room_sale_type: form.value.saleRule || 385,
  user_info: {
    link_man: form.value.contactName,
    link_phone: form.value.contactPhone,
    common_code: form.value.commonCode || ''
  },
  price_project: form.value.sellType === 1 ? 2 : 1,
  enter_time_plan: Math.floor(new Date(form.value.checkInTime).getTime() / 1000),
  times: form.value.stayDuration || 1,
  bill_source: form.value.orderSource || 9,
  room_list: [...],
  stay_type: 1,
  secrecy: form.value.isSecret ? 1 : 0
}
```

2. **添加 common_code 获取函数**：

```javascript
// 获取客人的 common_code
async function getGuestCommonCode(phone) {
  try {
    const memberResponse = await api.getUserInfo({ phone })
    if (memberResponse && memberResponse.data && memberResponse.data.common_code) {
      return memberResponse.data.common_code
    }
    return ''
  } catch (error) {
    console.error('获取客人 common_code 失败:', error)
    return ''
  }
}
```

3. **修改提交逻辑，确保 common_code 正确获取**：

```javascript
// 在构建参数前获取 common_code
if (!form.value.commonCode && form.value.contactPhone) {
  let commonCode = await getGuestCommonCode(form.value.contactPhone)
  if (!commonCode) {
    commonCode = await createGuestCommonCode({
      name: form.value.contactName,
      phone: form.value.contactPhone
    })
  }
  form.value.commonCode = commonCode
}
```

**修改的文件：**

- `src/components/hotel/CheckInModal.vue` - 房态页面入住弹窗
- `src/views/front-desk/checkin/index.vue` - 前台入住登记页面

**验证方法：**

1. 打开房态页面，点击空房进行入住操作
2. 检查控制台输出的入住参数格式
3. 确认 `common_code` 字段正确获取和传递
4. 验证参数格式符合接口文档要求

### 5.3 调试技巧

#### 数据流调试步骤

1. **API层调试**：在 `roomApi.js` 中打印原始接口返回数据
2. **转换层调试**：在 `roomDataTransform.js` 中打印转换前后的数据
3. **组件层调试**：在组件的 `watch` 中打印接收到的 props 数据
4. **界面层调试**：检查计算属性和方法中的数据使用

#### 常用调试代码

```javascript
// API层
console.log('API原始返回:', response)

// 转换层
console.log('转换前:', room)
console.log('转换后:', transformedRoom)

// 组件层
watch(() => props.roomData, (newData) => {
  console.log('组件接收数据:', newData)
  console.log('关键字段检查:', {
    bill_id: newData.bill_id,
    bill_info: newData.bill_info
  })
}, { immediate: true })
```

---

## 更新日志

- **2025-08-28**: 创建文档，添加 getUsableRoom 接口完整数据结构
- **2025-08-28**: 添加 getTtLockConfig 接口基础结构（待完善）
- **2025-08-28**: 添加数据流分析与问题记录章节，记录 bill_id 字段丢失问题的解决过程
- **2025-08-28**: 添加办理入住接口 (createCheckin) 完整参数结构和 common_code 获取方法
- **2025-08-28**: 修改入住功能代码，按照标准参数格式重构 CheckInModal.vue 和前台入住页面
- **2025-08-28**: 添加办理入住相关接口文档：getRoomSellType、getRoomSaleType、getRoomPriceByDate，包含完整的请求参数、响应数据结构和调用流程

---

## 使用说明

1. 开发新功能时，先查看此文档了解接口数据结构
2. 如果发现接口数据结构有变化，及时更新此文档
3. 新增接口时，请按照相同格式添加到此文档中
4. 遇到数据流问题时，参考第5章的调试技巧和问题记录
5. 建议在代码中添加注释引用此文档的相关章节
