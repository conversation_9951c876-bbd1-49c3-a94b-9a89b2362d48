<template>
  <div class="stats-cards-container">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-placeholder">
      <n-skeleton height="120px" />
    </div>
    
    <!-- 统计卡片 -->
    <div v-else class="stats-cards">
      <n-card 
        v-for="stat in statsData" 
        :key="stat.key"
        class="stat-card"
        :class="`stat-card-${stat.type}`"
        hoverable
      >
        <div class="stat-content">
          <div class="stat-icon">
            <i :class="stat.icon" :style="{ color: stat.color }"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
            <div v-if="stat.trend" class="stat-trend" :class="stat.trend.type">
              <i :class="stat.trend.icon"></i>
              <span>{{ stat.trend.text }}</span>
            </div>
          </div>
        </div>
      </n-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'

const props = defineProps({
  roomStats: {
    type: Object,
    default: () => ({})
  },
  totalStats: {
    type: Object,
    default: () => ({})
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const loading = ref(true)

// 模拟异步加载
onMounted(() => {
  setTimeout(() => {
    loading.value = false
  }, 500)
})

const statsData = computed(() => {
  const stats = props.roomStats
  const total = props.totalStats
  
  return [
    {
      key: 'available',
      label: '空房',
      value: stats.available || 0,
      icon: 'i-material-symbols:hotel',
      color: '#52c41a',
      type: 'success',
      trend: {
        type: 'up',
        icon: 'i-material-symbols:trending-up',
        text: '较昨日+5'
      }
    },
    {
      key: 'occupied',
      label: '有客',
      value: stats.occupied || 0,
      icon: 'i-material-symbols:person',
      color: '#1890ff',
      type: 'info',
      trend: {
        type: 'down',
        icon: 'i-material-symbols:trending-down',
        text: '较昨日-3'
      }
    },
    {
      key: 'reserved',
      label: '预订',
      value: stats.reserved || 0,
      icon: 'i-material-symbols:event-available',
      color: '#faad14',
      type: 'warning',
      trend: {
        type: 'up',
        icon: 'i-material-symbols:trending-up',
        text: '较昨日+8'
      }
    },
    {
      key: 'maintenance',
      label: '维修',
      value: stats.maintenance || 0,
      icon: 'i-material-symbols:build',
      color: '#ff4d4f',
      type: 'error',
      trend: {
        type: 'stable',
        icon: 'i-material-symbols:trending-flat',
        text: '较昨日持平'
      }
    },
    {
      key: 'occupancy',
      label: '入住率',
      value: `${total.averageOccupancy || 0}%`,
      icon: 'i-material-symbols:analytics',
      color: '#722ed1',
      type: 'primary',
      trend: {
        type: 'up',
        icon: 'i-material-symbols:trending-up',
        text: '较昨日+2.5%'
      }
    },
    {
      key: 'revenue',
      label: '预计收入',
      value: '¥' + ((stats.occupied || 0) * 300 + (stats.reserved || 0) * 280).toLocaleString(),
      icon: 'i-material-symbols:attach-money',
      color: '#13c2c2',
      type: 'success',
      trend: {
        type: 'up',
        icon: 'i-material-symbols:trending-up',
        text: '较昨日+12%'
      }
    }
  ]
})

// 监听数据变化，重新计算
watch(() => [props.roomStats, props.totalStats], () => {
  // 数据变化时可以添加动画效果
}, { deep: true })
</script>

<style scoped>
.stats-cards-container {
  margin-bottom: 16px;
}

.loading-placeholder {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat-card {
  transition: all 0.3s ease;
  border-radius: 8px;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px;
}

.stat-icon {
  font-size: 32px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.stat-trend {
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-trend.up {
  color: #52c41a;
}

.stat-trend.down {
  color: #ff4d4f;
}

.stat-trend.stable {
  color: #666;
}

/* 不同类型的卡片样式 */
.stat-card-success {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  color: white;
}

.stat-card-info {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  color: white;
}

.stat-card-warning {
  background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);
  color: white;
}

.stat-card-error {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  color: white;
}

.stat-card-primary {
  background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-cards {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
  }
  
  .stat-content {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }
  
  .stat-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
  }
  
  .stat-value {
    font-size: 20px;
  }
}
</style>
