<template>
  <div
    :class="cellClasses"
    :style="cellStyles"
    @click="handleCellClick"
  >
    <template v-if="isClosedStatus">
      <div class="status-text">关房</div>
    </template>
    <template v-else-if="isSoldOutStatus">
      <div class="status-text">售完</div>
    </template>
    <template v-else>
      <!-- 预订信息 -->
      <div v-if="cellData.reserved > 0" class="info-line reserved">
        总:{{ cellData.reserved }}单预订:{{ Math.round(cellData.reserved * 1.2) }}房
      </div>
      
      <!-- 在住信息 -->
      <div v-if="cellData.occupied > 0" class="info-line occupied">
        总:{{ cellData.occupied }}单在住
      </div>
      
      <!-- 入住率和剩余 -->
      <div class="info-line rate-info">
        <span :style="{ color: occupancyColor }">
          入住率:{{ cellData.occupancyRate }}%
        </span>
        <span class="remaining">剩:{{ cellData.available }}</span>
      </div>
      
      <!-- 渠道分布 -->
      <div v-if="hasChannelData" class="info-line channel-info">
        OTA:{{ cellData.ota }} 直订:{{ cellData.direct }} 协议:{{ cellData.corporate }}
      </div>
    </template>
  </div>
</template>

<script setup>
import { computed, defineProps, defineEmits } from 'vue'

const props = defineProps({
  cellData: {
    type: Object,
    required: true
  },
  roomType: {
    type: Object,
    required: true
  },
  date: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['cell-click'])

// 使用 computed 缓存计算结果
const isClosedStatus = computed(() => props.cellData.closed)
const isSoldOutStatus = computed(() => props.cellData.soldOut && !props.cellData.closed)

const hasChannelData = computed(() => {
  const { ota, direct, corporate, walkIn } = props.cellData
  return (ota + direct + corporate + walkIn) > 0
})

const occupancyColor = computed(() => {
  const rate = props.cellData.occupancyRate
  if (rate >= 80) return '#ff4d4f'
  if (rate >= 60) return '#faad14'
  return '#52c41a'
})

const cellClasses = computed(() => {
  const classes = ['cell-content']
  
  if (isClosedStatus.value) {
    classes.push('status-closed')
  } else if (isSoldOutStatus.value) {
    classes.push('status-sold-out')
  } else {
    classes.push('status-normal')
  }
  
  return classes
})

const cellStyles = computed(() => {
  if (isClosedStatus.value) {
    return {
      backgroundColor: '#8c8c8c',
      color: 'white',
      padding: '8px',
      borderRadius: '4px'
    }
  }
  
  if (isSoldOutStatus.value) {
    return {
      backgroundColor: '#ff4d4f',
      color: 'white',
      padding: '8px',
      borderRadius: '4px'
    }
  }
  
  const rate = props.cellData.occupancyRate
  let backgroundColor, borderColor
  
  if (rate >= 90) {
    backgroundColor = '#fff2f0'
    borderColor = '#ffa39e'
  } else if (rate >= 80) {
    backgroundColor = '#fffbe6'
    borderColor = '#ffe58f'
  } else if (rate >= 60) {
    backgroundColor = '#f6ffed'
    borderColor = '#b7eb8f'
  } else {
    backgroundColor = '#f0f5ff'
    borderColor = '#91d5ff'
  }
  
  return {
    backgroundColor,
    border: `1px solid ${borderColor}`,
    padding: '6px',
    borderRadius: '4px',
    minHeight: '80px',
    lineHeight: '1.2'
  }
})

const handleCellClick = () => {
  emit('cell-click', {
    roomType: props.roomType,
    date: props.date,
    data: props.cellData
  })
}
</script>

<style scoped>
.cell-content {
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
}

.cell-content:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.info-line {
  margin-bottom: 2px;
}

.info-line.reserved {
  color: #1890ff;
  font-size: 12px;
}

.info-line.occupied {
  color: #52c41a;
  font-size: 12px;
}

.info-line.rate-info {
  font-size: 12px;
  font-weight: bold;
}

.info-line.channel-info {
  font-size: 10px;
  color: #999;
  margin-top: 4px;
}

.remaining {
  margin-left: 8px;
  color: #666;
}

.status-text {
  text-align: center;
  font-weight: bold;
  font-size: 14px;
}

.status-closed {
  background-color: #8c8c8c !important;
  color: white !important;
}

.status-sold-out {
  background-color: #ff4d4f !important;
  color: white !important;
}
</style>
