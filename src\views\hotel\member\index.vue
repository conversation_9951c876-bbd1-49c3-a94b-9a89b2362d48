 <template>
 <n-card title="会员管理">
    <div class="mb-4">
      <n-button type="primary" @click="handleAdd">
        <template #icon>
          <i class="i-fe:user-plus"></i>
        </template>
        新增会员
      </n-button>
    </div>

    <n-data-table
      :columns="columns"
      :data="tableData"
      :loading="loading"
      :pagination="pagination"
      @update:page="handlePageChange"
    />

    <n-modal v-model:show="showModal" :title="modalTitle" preset="dialog" :show-icon="false">
      <n-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
        :style="{
          maxWidth: '640px'
        }"
      >
        <n-form-item label="会员名称" path="name">
          <n-input v-model:value="formData.name" placeholder="请输入会员名称" />
        </n-form-item>
        <n-form-item label="手机号码" path="phone">
          <n-input v-model:value="formData.phone" placeholder="请输入手机号码" />
        </n-form-item>
        <n-form-item label="会员等级" path="level">
          <n-select v-model:value="formData.level" :options="levelOptions" placeholder="请选择会员等级" />
        </n-form-item>
        <n-form-item label="积分" path="points">
          <n-input-number v-model:value="formData.points" placeholder="请输入积分" />
        </n-form-item>
        <n-form-item label="备注" path="remark">
          <n-input v-model:value="formData.remark" type="textarea" placeholder="请输入备注" />
        </n-form-item>
      </n-form>

      <template #action>
        <n-space>
          <n-button @click="closeModal">取消</n-button>
          <n-button type="primary" :loading="submitLoading" @click="handleSubmit">确定</n-button>
        </n-space>
      </template>
    </n-modal>
  </n-card>
</template>

<script setup>
import { h, ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { createDiscreteApi } from 'naive-ui'
import { getMemberList, createMember, updateMember, deleteMember } from '@/api/hotel/member'

const { message } = createDiscreteApi(['message'])
const router = useRouter()
defineOptions({ name: 'MemberList' })
// 查看会员详情
const handleDetail = (row) => {
  router.push(`/hotel/member/${row.id}`)
}

// 表格列配置
const columns = [
  {
    title: '会员名称',
    key: 'name',
  },
  {
    title: '手机号码',
    key: 'phone',
  },
  {
    title: '会员等级',
    key: 'level',
    render(row) {
      const config = levelConfig[row.level] || levelConfig[1]

      return h('div', {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '6px',
          padding: '4px 8px',
          borderRadius: '4px',
          backgroundColor: config.bgColor,
          color: config.color,
          fontSize: '12px',
          fontWeight: '500'
        }
      }, [
        h('i', {
          class: `${config.icon} text-14`,
          style: { color: config.color }
        }),
        config.label
      ])
    }
  },
  {
    title: '积分',
    key: 'points',
    render(row) {
      return h('div', {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '4px',
          color: '#f39c12',
          fontWeight: '500'
        }
      }, [
        h('i', { class: 'i-fe:star text-12', style: { color: '#f39c12' } }),
        row.points || 0
      ])
    }
  },
  {
    title: '备注',
    key: 'remark',
  },
  {
    title: '操作',
    key: 'actions',
    render(row) {
      return h('div', {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }
      }, [
        h('a', {
          style: {
            display: 'flex',
            alignItems: 'center',
            gap: '4px',
            color: '#2080f0',
            textDecoration: 'none',
            padding: '4px 8px',
            borderRadius: '4px',
            fontSize: '12px',
            cursor: 'pointer'
          },
          onClick: () => handleDetail(row),
          onMouseenter: (e) => {
            e.target.style.backgroundColor = '#f0f8ff'
          },
          onMouseleave: (e) => {
            e.target.style.backgroundColor = 'transparent'
          }
        }, [
          h('i', { class: 'i-fe:eye text-12' }),
          '详情'
        ]),
        h('a', {
          style: {
            display: 'flex',
            alignItems: 'center',
            gap: '4px',
            color: '#f39c12',
            textDecoration: 'none',
            padding: '4px 8px',
            borderRadius: '4px',
            fontSize: '12px',
            cursor: 'pointer'
          },
          onClick: () => handleEdit(row),
          onMouseenter: (e) => {
            e.target.style.backgroundColor = '#fef9e7'
          },
          onMouseleave: (e) => {
            e.target.style.backgroundColor = 'transparent'
          }
        }, [
          h('i', { class: 'i-fe:edit text-12' }),
          '编辑'
        ]),
        h('a', {
          style: {
            display: 'flex',
            alignItems: 'center',
            gap: '4px',
            color: '#e74c3c',
            textDecoration: 'none',
            padding: '4px 8px',
            borderRadius: '4px',
            fontSize: '12px',
            cursor: 'pointer'
          },
          onClick: () => handleDelete(row),
          onMouseenter: (e) => {
            e.target.style.backgroundColor = '#fef2f2'
          },
          onMouseleave: (e) => {
            e.target.style.backgroundColor = 'transparent'
          }
        }, [
          h('i', { class: 'i-fe:trash-2 text-12' }),
          '删除'
        ])
      ])
    },
  },
]

// 会员等级配置
const levelConfig = {
  1: { icon: 'i-fe:user', color: '#909399', bgColor: '#f4f4f5', label: '普通会员' },
  2: { icon: 'i-fe:award', color: '#c0c4cc', bgColor: '#f0f0f0', label: '银卡会员' },
  3: { icon: 'i-fe:star', color: '#f39c12', bgColor: '#fef9e7', label: '金卡会员' },
  4: { icon: 'i-fe:crown', color: '#8e44ad', bgColor: '#f4f0ff', label: '钻石会员' }
}

// 会员等级选项
const levelOptions = [
  {
    label: '普通会员',
    value: 1,
    render: () => h('div', {
      style: {
        display: 'flex',
        alignItems: 'center',
        gap: '6px'
      }
    }, [
      h('i', { class: 'i-fe:user text-14', style: { color: '#909399' } }),
      '普通会员'
    ])
  },
  {
    label: '银卡会员',
    value: 2,
    render: () => h('div', {
      style: {
        display: 'flex',
        alignItems: 'center',
        gap: '6px'
      }
    }, [
      h('i', { class: 'i-fe:award text-14', style: { color: '#c0c4cc' } }),
      '银卡会员'
    ])
  },
  {
    label: '金卡会员',
    value: 3,
    render: () => h('div', {
      style: {
        display: 'flex',
        alignItems: 'center',
        gap: '6px'
      }
    }, [
      h('i', { class: 'i-fe:star text-14', style: { color: '#f39c12' } }),
      '金卡会员'
    ])
  },
  {
    label: '钻石会员',
    value: 4,
    render: () => h('div', {
      style: {
        display: 'flex',
        alignItems: 'center',
        gap: '6px'
      }
    }, [
      h('i', { class: 'i-fe:crown text-14', style: { color: '#8e44ad' } }),
      '钻石会员'
    ])
  },
]

// 表格数据
const tableData = ref([])
const loading = ref(false)
const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 30, 40],
})

// 获取会员列表
const loadData = async () => {
  loading.value = true
  try {
    const { data } = await getMemberList({
      page: pagination.page,
      pageSize: pagination.pageSize,
    })
    tableData.value = data.list
    pagination.itemCount = data.total
  } catch (error) {
    message.error('获取会员列表失败')
  }
  loading.value = false
}

// 页码变化
const handlePageChange = (page) => {
  pagination.page = page
  loadData()
}

// 表单相关
const showModal = ref(false)
const modalTitle = ref('')
const submitLoading = ref(false)
const formRef = ref(null)
const formData = reactive({
  id: null,
  name: '',
  phone: '',
  level: null,
  points: 0,
  remark: '',
})

// 表单校验规则
const rules = {
  name: {
    required: true,
    message: '请输入会员名称',
    trigger: 'blur',
  },
  phone: {
    required: true,
    message: '请输入手机号码',
    trigger: 'blur',
    pattern: /^1[3-9]\d{9}$/,
  },
  level: {
    required: true,
    message: '请选择会员等级',
    trigger: 'change',
  },
}

// 新增会员
const handleAdd = () => {
  modalTitle.value = '新增会员'
  Object.assign(formData, {
    id: null,
    name: '',
    phone: '',
    level: null,
    points: 0,
    remark: '',
  })
  showModal.value = true
}

// 编辑会员
const handleEdit = (row) => {
  modalTitle.value = '编辑会员'
  Object.assign(formData, row)
  showModal.value = true
}

// 删除会员
const handleDelete = async (row) => {
  try {
    await deleteMember(row.id)
    message.success('删除成功')
    loadData()
  } catch (error) {
    message.error('删除失败')
  }
}

// 提交表单
const handleSubmit = async () => {
  await formRef.value?.validate()
  submitLoading.value = true
  try {
    if (formData.id) {
      await updateMember(formData)
      message.success('更新成功')
    } else {
      await createMember(formData)
      message.success('新增成功')
    }
    closeModal()
    loadData()
  } catch (error) {
    message.error(formData.id ? '更新失败' : '新增失败')
  }
  submitLoading.value = false
}

// 关闭弹窗
const closeModal = () => {
  showModal.value = false
}

// 初始化加载数据
loadData()
</script>

<style scoped>
.mb-4 {
  margin-bottom: 16px;
}
</style>
