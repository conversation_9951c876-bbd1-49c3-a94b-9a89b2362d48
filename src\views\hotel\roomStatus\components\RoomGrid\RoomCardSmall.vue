<template>
  <div class="room-card-small">
    <!-- 顶部：房间号 + 联房标识 -->
    <div class="room-header-small">
      <span class="room-number-small">{{ room.roomNumber || room.room_number }}</span>
      <!-- 联房标识 -->
      <div
        v-if="room.isConnectedRoom && room.connectCode"
        class="connect-room-badge-small"
        :title="`联房组: ${room.connectCode}`"
      >
        <i class="i-mdi:link-variant"></i>
      </div>
    </div>

    <!-- 房型信息 -->
    <div class="room-type-small">{{ room.roomType || room.room_type_name }}</div>

    <!-- 中间：主要信息 -->
    <div class="room-content-small">
      <!-- 客人信息 -->
      <div v-if="hasGuestInfo(room)" class="guest-info-small">
        <div class="guest-name-small">{{ room.guestName || getGuestName(room) }}</div>
        <div v-if="room.checkInTime || room.checkOutTime" class="time-info-small">
          <span v-if="room.checkInTime">入{{ formatTimeShort(room.checkInTime) }}</span>
          <span v-if="room.checkOutTime">退{{ formatTimeShort(room.checkOutTime) }}</span>
        </div>
      </div>

      <!-- 预订信息 -->
      <div v-else-if="hasReservationInfo(room)" class="reservation-info-small">
        <div class="guest-name-small">{{ room.guestName || '预订' }}</div>
        <div v-if="getArrivalTime(room)" class="arrival-info-small">
          {{ formatTimeShort(getArrivalTime(room)) }}
        </div>
      </div>

      <!-- 维修信息 -->
      <div v-else-if="room.maintenance" class="maintenance-info-small">
        <div class="maintenance-text-small">维修中</div>
        <div v-if="room.maintenance.assignedTo" class="assignee-small">{{ room.maintenance.assignedTo }}</div>
      </div>

      <!-- 清洁信息 -->
      <div v-else-if="room.housekeeping && (room.status === 'cleaning' || room.status === 'inspecting')" class="housekeeping-info-small">
        <div class="housekeeping-text-small">{{ room.status === 'cleaning' ? '清洁中' : '查房中' }}</div>
        <div v-if="room.housekeeping.assignedTo" class="assignee-small">{{ room.housekeeping.assignedTo }}</div>
      </div>

      <!-- 空房状态 -->
      <div v-else class="empty-room-small">
        <div class="empty-status-small">空房</div>
      </div>
    </div>

    <!-- 底部：状态指示器 -->
    <div class="room-bottom-small">
      <!-- 左侧：状态点 -->
      <div class="status-indicators-small">
        <!-- 业务状态点 -->
        <div
          class="status-dot-small business-status"
          :style="getBusinessStatusStyle(room)"
          :title="room.roomStatusName"
        ></div>

        <!-- 清洁状态点 -->
        <div
          v-if="room.cleanStatusName && room.cleanStatusName !== '净'"
          class="status-dot-small clean-status"
          :style="getCleanStatusStyle(room)"
          :title="room.cleanStatusName"
        ></div>

        <!-- 退房提醒点 -->
        <div v-if="isCheckoutOverdue(room)" class="status-dot-small alert-status overdue" title="超时未退房"></div>
        <div v-else-if="isCheckoutSoon(room)" class="status-dot-small alert-status soon" title="即将退房"></div>
      </div>

      <!-- 右侧：重要提醒和指示器 -->
      <div class="right-indicators-small">
        <!-- 欠费提醒（最高优先级） -->
        <div v-if="room.debtInfo" class="debt-reminder-small" :title="room.debtInfo.fullText">
          ¥
        </div>
        <!-- 离店提醒 -->
        <div v-else-if="room.checkoutReminderInfo" class="checkout-reminder-small" :title="room.checkoutReminderInfo.fullText">
          {{ room.checkoutReminderInfo.remainingHours }}
        </div>
        <!-- 会员等级标签 -->
        <div v-else-if="room.memberGrade" class="member-badge-small" :title="room.memberGrade">
          {{ room.memberGrade.charAt(0) }}
        </div>

        <!-- 订单指示器 -->
        <div v-if="getTodayOrders(room.futureOrders).length > 0 || getFutureOrders(room.futureOrders).length > 0" class="orders-indicator-small">
          <span v-if="getTodayOrders(room.futureOrders).length > 0" class="order-dot today" :title="`今日${getTodayOrders(room.futureOrders).length}单`">
            {{ getTodayOrders(room.futureOrders).length }}
          </span>
          <span v-if="getFutureOrders(room.futureOrders).length > 0" class="order-dot future" :title="`未来${getFutureOrders(room.futureOrders).length}单`">
            {{ getFutureOrders(room.futureOrders).length }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Props
const props = defineProps({
  room: {
    type: Object,
    required: true
  }
})

// 判断是否有客人信息
const hasGuestInfo = (room) => {
  if (!room) return false

  // 优先检查bill_info中的客人信息
  if (room.bill_info && room.bill_info.link_man) {
    return true
  }

  // 检查guest对象
  if (room.guest && room.guest.name) {
    return true
  }

  // 检查转换后的数据
  if (room.guestName) {
    return true
  }

  return false
}

// 获取客人姓名
const getGuestName = (room) => {
  if (!room) return ''

  if (room.bill_info && room.bill_info.link_man) {
    return room.bill_info.link_man
  }

  if (room.guest && room.guest.name) {
    return room.guest.name
  }

  return room.guest_name || ''
}

// 判断是否有预订信息
const hasReservationInfo = (room) => {
  if (!room) return false

  // 房间状态为预订相关
  const statusName = room.roomStatusName || room.room_status_name || ''
  if (statusName.includes('预订') || statusName.includes('预定')) {
    return true
  }

  // 或者有预订相关的状态标识
  const status = room.status || ''
  if (status === 'reserved' || status === 'noshow') {
    return true
  }

  return false
}

// 获取到店时间
const getArrivalTime = (room) => {
  if (!room) return null

  // 从bill_info获取
  if (room.bill_info && room.bill_info.arrival_time) {
    return room.bill_info.arrival_time
  }

  // 从guest对象获取
  if (room.guest && room.guest.arrivalTime) {
    return room.guest.arrivalTime
  }

  return null
}

// 获取今日订单
const getTodayOrders = (orders) => {
  if (!orders || !Array.isArray(orders)) return []

  const today = new Date()
  today.setHours(0, 0, 0, 0)
  const tomorrow = new Date(today)
  tomorrow.setDate(tomorrow.getDate() + 1)

  return orders.filter(order => {
    const timestamp = order.enter_time || order.enter_time_plan || order.start_time_plan
    if (!timestamp) return false

    const orderDate = new Date(timestamp * 1000)
    return orderDate >= today && orderDate < tomorrow
  })
}

// 获取未来订单（明日及以后）
const getFutureOrders = (orders) => {
  if (!orders || !Array.isArray(orders)) return []

  const tomorrow = new Date()
  tomorrow.setHours(0, 0, 0, 0)
  tomorrow.setDate(tomorrow.getDate() + 1)

  return orders.filter(order => {
    const timestamp = order.enter_time || order.enter_time_plan || order.start_time_plan
    if (!timestamp) return false

    const orderDate = new Date(timestamp * 1000)
    return orderDate >= tomorrow
  })
}

// 格式化时间（简短版本）
const formatTimeShort = (timeStr) => {
  if (!timeStr) return ''
  try {
    const date = new Date(timeStr)
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } catch {
    return timeStr
  }
}

// 获取业务状态样式
const getBusinessStatusStyle = (room) => {
  const color = room.room_status_color || '#e5e7eb'
  return {
    'background': color,
    'border': `1px solid ${colorWithOpacity(color, 0.8)}`
  }
}

// 获取清洁状态样式
const getCleanStatusStyle = (room) => {
  const color = room.clear_color || '#f3f4f6'
  return {
    'background': color,
    'border': `1px solid ${colorWithOpacity(color, 0.8)}`
  }
}

const isCheckoutSoon = (room) => {
  if (!room.checkoutTime && !room.checkout_time) return false

  const checkoutTime = new Date(room.checkoutTime || room.checkout_time)
  const now = new Date()
  const timeDiff = checkoutTime.getTime() - now.getTime()
  const hoursDiff = timeDiff / (1000 * 60 * 60)

  return hoursDiff > 0 && hoursDiff <= 2
}

const isCheckoutOverdue = (room) => {
  if (!room.checkoutTime && !room.checkout_time) return false

  const checkoutTime = new Date(room.checkoutTime || room.checkout_time)
  const now = new Date()

  return now > checkoutTime
}

const colorWithOpacity = (color, opacity) => {
  if (color.startsWith('#')) {
    const hex = color.replace('#', '')
    const r = parseInt(hex.substr(0, 2), 16)
    const g = parseInt(hex.substr(2, 2), 16)
    const b = parseInt(hex.substr(4, 2), 16)
    return `rgba(${r}, ${g}, ${b}, ${opacity})`
  }
  return color
}
</script>

<style scoped>
.room-card-small {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.1rem;
  padding: 0.25rem;
  overflow: hidden;
}

/* 顶部信息行 */
.room-header-small {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.1rem;
}

.room-number-small {
  font-size: 1rem;
  font-weight: 800;
  color: #1f2937;
  line-height: 1;
}

/* 空房的房号更大 */
.room-card-small:not(:has(.guest-main-info)):not(:has(.reservation-main-info)) .room-number-small {
  font-size: 1.2rem;
  font-weight: 900;
}

.connect-room-badge-small {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 12px;
  height: 12px;
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  border-radius: 50%;
  font-size: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.connect-room-badge-small:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 6px rgba(139, 92, 246, 0.5);
}

/* 中间内容区域 */
.room-content-small {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 0;
  overflow: hidden;
}

.guest-info-small,
.reservation-info-small,
.maintenance-info-small,
.housekeeping-info-small,
.empty-room-small {
  display: flex;
  flex-direction: column;
  gap: 0.1rem;
  text-align: center;
}

.guest-name-small {
  font-weight: 600;
  color: #374151;
  font-size: 0.65rem;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.time-info-small,
.arrival-info-small {
  font-size: 0.55rem;
  color: #6b7280;
  display: flex;
  flex-direction: column;
  gap: 0.05rem;
}

.maintenance-text-small,
.housekeeping-text-small {
  font-size: 0.6rem;
  font-weight: 600;
  color: #dc2626;
}

.assignee-small {
  font-size: 0.5rem;
  color: #6b7280;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.room-type-small {
  font-size: 0.7rem;
  color: #4b5563;
  font-weight: 500;
  margin-bottom: 0.1rem;
  line-height: 1;
}

.empty-status-small {
  font-size: 0.6rem;
  color: #6b7280;
  font-weight: 500;
}

/* 底部状态区域 */
.room-bottom-small {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  gap: 0.2rem;
}

.status-indicators-small {
  display: flex;
  gap: 0.15rem;
}

.right-indicators-small {
  display: flex;
  gap: 0.1rem;
  align-items: center;
  flex-shrink: 0;
}

.status-dot-small {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-dot-small.business-status {
  border: 1px solid rgba(0, 0, 0, 0.2);
}

.status-dot-small.clean-status {
  border: 1px solid rgba(0, 0, 0, 0.2);
}

.status-dot-small.alert-status.overdue {
  background: #ef4444;
  border: 1px solid #dc2626;
}

.status-dot-small.alert-status.soon {
  background: #f59e0b;
  border: 1px solid #d97706;
}

/* 欠费提醒标签（小卡片，最高优先级） */
.debt-reminder-small {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 14px;
  height: 14px;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border-radius: 50%;
  font-size: 0.6rem;
  font-weight: 600;
  box-shadow: 0 1px 2px rgba(239, 68, 68, 0.3);
  animation: debtPulse 2s ease-in-out infinite;
}

/* 离店提醒标签（小卡片，第二优先级） */
.checkout-reminder-small {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 14px;
  height: 14px;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  border-radius: 50%;
  font-size: 0.55rem;
  font-weight: 600;
  box-shadow: 0 1px 2px rgba(245, 158, 11, 0.3);
}

/* 会员等级标签 */
.member-badge-small {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 14px;
  height: 14px;
  background: linear-gradient(135deg, #d97706, #b45309);
  color: white;
  border-radius: 50%;
  font-size: 0.55rem;
  font-weight: 600;
  box-shadow: 0 1px 2px rgba(217, 119, 6, 0.3);
}

/* 欠费脉冲动画 */
@keyframes debtPulse {
  0% {
    box-shadow: 0 1px 2px rgba(239, 68, 68, 0.3);
  }
  50% {
    box-shadow: 0 2px 6px rgba(239, 68, 68, 0.6), 0 0 0 2px rgba(239, 68, 68, 0.2);
  }
  100% {
    box-shadow: 0 1px 2px rgba(239, 68, 68, 0.3);
  }
}

/* 订单指示器 */
.orders-indicator-small {
  display: flex;
  gap: 0.1rem;
  flex-shrink: 0;
}

.order-dot {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  font-size: 0.5rem;
  font-weight: 600;
  color: white;
  cursor: pointer;
}

.order-dot.today {
  background: linear-gradient(135deg, #10b981, #059669);
}

.order-dot.future {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}
</style>
