<template>
  <div class="reservation-page">
    <div class="page-header">
      <h1 class="page-title">
        <i class="i-material-symbols:event-available-outline"></i>
        预订管理
      </h1>
      <p class="page-description">管理客人预订信息，处理预订确认和取消</p>
    </div>

    <div class="reservation-content">
      <!-- 操作工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <n-button type="primary" @click="showCreateModal = true">
            <i class="i-material-symbols:add"></i>
            新增预订
          </n-button>
          <n-select
            v-model:value="statusFilter"
            :options="statusOptions"
            placeholder="筛选状态"
            style="width: 150px"
            clearable
          />
        </div>
        <div class="toolbar-right">
          <n-input
            v-model:value="searchKeyword"
            placeholder="搜索客人姓名或房间号"
            clearable
            style="width: 250px"
          >
            <template #prefix>
              <i class="i-material-symbols:search"></i>
            </template>
          </n-input>
        </div>
      </div>

      <!-- 预订列表 -->
      <n-card class="reservation-list-card">
        <n-data-table
          :columns="columns"
          :data="filteredReservations"
          :pagination="pagination"
          :loading="loading"
        />
      </n-card>
    </div>

    <!-- 新增预订弹窗 -->
    <n-modal v-model:show="showCreateModal" preset="card" title="新增预订" style="width: 600px">
      <n-form ref="formRef" :model="formData" :rules="rules" label-placement="left" label-width="100px">
        <n-form-item label="客人姓名" path="guestName">
          <n-input v-model:value="formData.guestName" placeholder="请输入客人姓名" />
        </n-form-item>
        <n-form-item label="手机号" path="phone">
          <n-input v-model:value="formData.phone" placeholder="请输入手机号" />
        </n-form-item>
        <n-form-item label="房间类型" path="roomType">
          <n-select v-model:value="formData.roomType" :options="roomTypeOptions" placeholder="选择房间类型" />
        </n-form-item>
        <n-form-item label="入住日期" path="checkinDate">
          <n-date-picker v-model:value="formData.checkinDate" type="date" />
        </n-form-item>
        <n-form-item label="退房日期" path="checkoutDate">
          <n-date-picker v-model:value="formData.checkoutDate" type="date" />
        </n-form-item>
        <n-form-item label="预订备注" path="remarks">
          <n-input v-model:value="formData.remarks" type="textarea" placeholder="预订备注信息" />
        </n-form-item>
      </n-form>
      <template #footer>
        <div class="modal-actions">
          <n-button @click="showCreateModal = false">取消</n-button>
          <n-button type="primary" @click="handleCreateReservation">确认预订</n-button>
        </div>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, h } from 'vue'
import { useMessage } from 'naive-ui'
import { NButton, NTag } from 'naive-ui'

const $message = useMessage()

const loading = ref(false)
const showCreateModal = ref(false)
const searchKeyword = ref('')
const statusFilter = ref(null)

const formRef = ref(null)
const formData = reactive({
  guestName: '',
  phone: '',
  roomType: null,
  checkinDate: null,
  checkoutDate: null,
  remarks: ''
})

const rules = {
  guestName: { required: true, message: '请输入客人姓名', trigger: 'blur' },
  phone: { required: true, message: '请输入手机号', trigger: 'blur' },
  roomType: { required: true, message: '请选择房间类型', trigger: 'change' },
  checkinDate: { required: true, message: '请选择入住日期', trigger: 'change' },
  checkoutDate: { required: true, message: '请选择退房日期', trigger: 'change' }
}

const statusOptions = [
  { label: '待确认', value: 'pending' },
  { label: '已确认', value: 'confirmed' },
  { label: '已入住', value: 'checkedin' },
  { label: '已取消', value: 'cancelled' }
]

const roomTypeOptions = [
  { label: '标准间', value: 'standard' },
  { label: '豪华间', value: 'deluxe' },
  { label: '套房', value: 'suite' }
]

// 模拟预订数据
const reservations = ref([
  {
    id: 1,
    guestName: '张三',
    phone: '13800138001',
    roomType: '标准间',
    checkinDate: '2024-01-20',
    checkoutDate: '2024-01-22',
    status: 'confirmed',
    amount: 600,
    remarks: '需要婴儿床'
  },
  {
    id: 2,
    guestName: '李四',
    phone: '13800138002',
    roomType: '豪华间',
    checkinDate: '2024-01-21',
    checkoutDate: '2024-01-23',
    status: 'pending',
    amount: 800,
    remarks: ''
  },
  {
    id: 3,
    guestName: '王五',
    phone: '13800138003',
    roomType: '套房',
    checkinDate: '2024-01-19',
    checkoutDate: '2024-01-21',
    status: 'checkedin',
    amount: 1200,
    remarks: '商务客人'
  }
])

const filteredReservations = computed(() => {
  let result = reservations.value

  if (searchKeyword.value) {
    result = result.filter(item =>
      item.guestName.includes(searchKeyword.value) ||
      item.phone.includes(searchKeyword.value)
    )
  }

  if (statusFilter.value) {
    result = result.filter(item => item.status === statusFilter.value)
  }

  return result
})

const columns = [
  { title: '预订编号', key: 'id', width: 100 },
  { title: '客人姓名', key: 'guestName', width: 120 },
  { title: '手机号', key: 'phone', width: 130 },
  { title: '房间类型', key: 'roomType', width: 100 },
  { title: '入住日期', key: 'checkinDate', width: 120 },
  { title: '退房日期', key: 'checkoutDate', width: 120 },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row) => {
      const statusMap = {
        pending: { type: 'warning', text: '待确认' },
        confirmed: { type: 'info', text: '已确认' },
        checkedin: { type: 'success', text: '已入住' },
        cancelled: { type: 'error', text: '已取消' }
      }
      const status = statusMap[row.status]
      return h(NTag, { type: status.type }, { default: () => status.text })
    }
  },
  { title: '金额', key: 'amount', width: 100, render: (row) => `¥${row.amount}` },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    render: (row) => {
      return h('div', { class: 'action-buttons' }, [
        h(NButton, {
          size: 'small',
          type: 'primary',
          onClick: () => handleConfirm(row)
        }, { default: () => '确认' }),
        h(NButton, {
          size: 'small',
          type: 'error',
          style: 'margin-left: 8px',
          onClick: () => handleCancel(row)
        }, { default: () => '取消' })
      ])
    }
  }
]

const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  showQuickJumper: true
})

function handleCreateReservation() {
  formRef.value?.validate((errors) => {
    if (!errors) {
      const newReservation = {
        id: reservations.value.length + 1,
        ...formData,
        status: 'pending',
        amount: 300, // 这里应该根据房型和日期计算
        checkinDate: new Date(formData.checkinDate).toISOString().split('T')[0],
        checkoutDate: new Date(formData.checkoutDate).toISOString().split('T')[0],
        roomType: roomTypeOptions.find(opt => opt.value === formData.roomType)?.label
      }

      reservations.value.unshift(newReservation)
      showCreateModal.value = false
      $message.success('预订创建成功！')

      // 重置表单
      Object.assign(formData, {
        guestName: '',
        phone: '',
        roomType: null,
        checkinDate: null,
        checkoutDate: null,
        remarks: ''
      })
    }
  })
}

function handleConfirm(row) {
  row.status = 'confirmed'
  $message.success(`预订 ${row.id} 已确认`)
}

function handleCancel(row) {
  row.status = 'cancelled'
  $message.success(`预订 ${row.id} 已取消`)
}
</script>

<style scoped>
.reservation-page {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: var(--text-color-1);
}

.page-description {
  color: var(--text-color-3);
  margin: 0;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
  align-items: center;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}
</style>