import * as roomStatusApi from '../api/index'

/**
 * 房间相关API服务
 * 负责所有房间数据的API调用
 */
export const roomApi = {
  /**
   * 获取房间列表
   * @param {Object} filters - 筛选条件
   * @returns {Promise<Array>} 房间列表
   */
  async getRoomList(filters = {}) {
    try {
      // 使用现有的API接口
      const response = await roomStatusApi.getUsableRoom(filters)
      return response
    } catch (error) {
      console.error('获取房间列表失败:', error)
      throw error
    }
  },

  /**
   * 获取房间详情
   * @param {string|number} roomId - 房间ID
   * @returns {Promise<Object>} 房间详情
   */
  async getRoomDetail(roomId) {
    try {
      // 使用现有的API接口
      return await roomStatusApi.getRoomBillDetail({ room_id: roomId })
    } catch (error) {
      console.error('获取房间详情失败:', error)
      throw error
    }
  },

  /**
   * 获取楼栋列表
   * @returns {Promise<Array>} 楼栋列表
   */
  async getBuildingList() {
    try {
      // 使用现有的API接口
      return await roomStatusApi.getUsableRoomBuildingList()
    } catch (error) {
      console.error('获取楼栋列表失败:', error)
      throw error
    }
  },

  /**
   * 获取楼层列表
   * @param {string|number} buildingId - 楼栋ID
   * @returns {Promise<Array>} 楼层列表
   */
  async getFloorList(buildingId = null) {
    try {
      // 使用现有的API接口
      const params = buildingId ? { building_id: buildingId } : {}
      return await roomStatusApi.getRoomFloor(params)
    } catch (error) {
      console.error('获取楼层列表失败:', error)
      throw error
    }
  },

  /**
   * 获取房型列表
   * @returns {Promise<Array>} 房型列表
   */
  async getRoomTypeList() {
    try {
      // 使用现有的API接口
      return await roomStatusApi.getRoomTypeList()
    } catch (error) {
      console.error('获取房型列表失败:', error)
      throw error
    }
  },

  /**
   * 获取房间状态配置
   * @returns {Promise<Array>} 房间状态配置
   */
  async getRoomStatusConfig() {
    try {
      // 使用现有的API接口
      return await roomStatusApi.getRoomRecordStatus()
    } catch (error) {
      console.error('获取房间状态配置失败:', error)
      throw error
    }
  },

  /**
   * 获取清洁状态配置
   * @returns {Promise<Array>} 清洁状态配置
   */
  async getCleanStatusConfig() {
    try {
      // 使用现有的API接口
      return await roomStatusApi.getRoomClearStatus()
    } catch (error) {
      console.error('获取清洁状态配置失败:', error)
      throw error
    }
  },

  /**
   * 更新房间状态
   * @param {string|number} roomId - 房间ID
   * @param {Object} statusData - 状态数据
   * @returns {Promise<Object>} 更新结果
   */
  async updateRoomStatus(roomId, statusData) {
    try {
      // 使用现有的API接口
      return await roomStatusApi.updateRoomStatus(roomId, statusData)
    } catch (error) {
      console.error('更新房间状态失败:', error)
      throw error
    }
  },

  /**
   * 批量更新房间状态
   * @param {Array} roomIds - 房间ID列表
   * @param {Object} statusData - 状态数据
   * @returns {Promise<Object>} 更新结果
   */
  async batchUpdateRoomStatus(roomIds, statusData) {
    try {
      // 使用现有的API接口或逐个更新
      const results = await Promise.all(
        roomIds.map(roomId => this.updateRoomStatus(roomId, statusData))
      )
      return { success: true, results }
    } catch (error) {
      console.error('批量更新房间状态失败:', error)
      throw error
    }
  },

  /**
   * 获取房间历史记录
   * @param {string|number} roomId - 房间ID
   * @param {Object} options - 查询选项
   * @returns {Promise<Array>} 历史记录列表
   */
  async getRoomHistory(roomId, options = {}) {
    try {
      // 简化实现，返回空数组，可以后续根据需要实现
      console.log('获取房间历史记录:', roomId, options)
      return []
    } catch (error) {
      console.error('获取房间历史记录失败:', error)
      throw error
    }
  },

  /**
   * 获取房间统计数据
   * @param {Object} filters - 筛选条件
   * @returns {Promise<Object>} 统计数据
   */
  async getRoomStats(filters = {}) {
    try {
      // 简化实现，从房间列表中计算统计数据
      const rooms = await this.getRoomList(filters)
      const stats = {
        total: rooms.length,
        occupied: 0,
        available: 0,
        reserved: 0,
        checkout: 0,
        dirty: 0,
        cleaning: 0,
        maintenance: 0,
        blocked: 0
      }

      rooms.forEach(room => {
        const status = room.room_status_sign || room.status
        switch (status) {
          case 'stay':
          case 'occupied':
            stats.occupied++
            break
          case 'vacant':
          case 'available':
            stats.available++
            break
          case 'reserved':
            stats.reserved++
            break
          case 'checkout':
            stats.checkout++
            break
          case 'blocked':
            stats.blocked++
            break
        }
      })

      return stats
    } catch (error) {
      console.error('获取房间统计数据失败:', error)
      throw error
    }
  },

  /**
   * 更新房间清洁状态
   * @param {string|number} roomId - 房间ID
   * @param {string} cleanStatus - 清洁状态
   * @returns {Promise<Object>} 更新结果
   */
  async updateRoomCleanStatus(roomId, cleanStatus) {
    try {
      return await roomStatusApi.updateRoomCleanStatus({
        room_id: roomId,
        clean_status: cleanStatus
      })
    } catch (error) {
      console.error('更新房间清洁状态失败:', error)
      throw error
    }
  },

  /**
   * 房间封锁/解封
   * @param {string|number} roomId - 房间ID
   * @param {boolean} isBlocked - 是否封锁
   * @returns {Promise<Object>} 操作结果
   */
  async toggleRoomBlock(roomId, isBlocked) {
    try {
      return await roomStatusApi.toggleRoomBlock({
        room_id: roomId,
        is_blocked: isBlocked
      })
    } catch (error) {
      console.error('房间封锁操作失败:', error)
      throw error
    }
  },

  /**
   * 房间维修
   * @param {string|number} roomId - 房间ID
   * @param {Object} maintenanceData - 维修数据
   * @returns {Promise<Object>} 操作结果
   */
  async roomMaintenance(roomId, maintenanceData = {}) {
    try {
      return await roomStatusApi.roomMaintenance({
        room_id: roomId,
        ...maintenanceData
      })
    } catch (error) {
      console.error('房间维修操作失败:', error)
      throw error
    }
  },

  /**
   * 房间清洁
   * @param {string|number} roomId - 房间ID
   * @param {Object} cleaningData - 清洁数据
   * @returns {Promise<Object>} 操作结果
   */
  async roomCleaning(roomId, cleaningData = {}) {
    try {
      return await roomStatusApi.roomCleaning({
        room_id: roomId,
        ...cleaningData
      })
    } catch (error) {
      console.error('房间清洁操作失败:', error)
      throw error
    }
  },

  /**
   * 快速退房
   * @param {string|number} roomId - 房间ID
   * @param {Object} checkoutData - 退房数据
   * @returns {Promise<Object>} 操作结果
   */
  async quickCheckout(roomId, checkoutData = {}) {
    try {
      return await roomStatusApi.quickCheckout({
        room_id: roomId,
        ...checkoutData
      })
    } catch (error) {
      console.error('快速退房操作失败:', error)
      throw error
    }
  },

  /**
   * 获取联房信息
   * @param {string|number} roomId - 房间ID
   * @returns {Promise<Object>} 联房信息
   */
  async getConnectBill(roomId) {
    try {
      return await roomStatusApi.getConnectBill({ room_id: roomId })
    } catch (error) {
      console.error('获取联房信息失败:', error)
      throw error
    }
  }
}
