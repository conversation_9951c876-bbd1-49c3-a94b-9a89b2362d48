/**
 * 房态页面工具函数
 * 提供通用的工具方法和常量定义
 */

// 房间状态映射
export const ROOM_STATUS_MAP = {
  'stay': 'occupied',
  'vacant': 'available', 
  'reserved': 'reserved',
  'checkout': 'checkout',
  'dirty': 'dirty',
  'cleaning': 'cleaning',
  'maintenance': 'maintenance',
  'blocked': 'blocked',
  'inspecting': 'inspecting',
  'noshow': 'noshow'
}

// 清洁状态映射
export const CLEAN_STATUS_MAP = {
  1: 'clean',
  2: 'dirty',
  3: 'cleaning',
  4: 'maintenance',
  5: 'blocked',
  6: 'inspecting'
}

// 布局模式常量
export const LAYOUT_MODES = {
  TOP: 'top',
  LEFT: 'left',
  BOTTOM: 'bottom'
}

// 卡片大小常量
export const CARD_SIZES = {
  LARGE: 'large',
  MEDIUM: 'medium',
  SMALL: 'small'
}

// 刷新间隔选项
export const REFRESH_INTERVALS = [
  { label: '10秒', value: 10 },
  { label: '30秒', value: 30 },
  { label: '1分钟', value: 60 },
  { label: '2分钟', value: 120 },
  { label: '5分钟', value: 300 }
]

/**
 * 格式化时间
 * @param {string|number|Date} time - 时间
 * @param {string} format - 格式类型
 * @returns {string} 格式化后的时间
 */
export function formatTime(time, format = 'HH:mm') {
  if (!time) return ''
  
  try {
    const date = new Date(typeof time === 'number' ? time * 1000 : time)
    
    if (isNaN(date.getTime())) {
      return String(time)
    }
    
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    const seconds = date.getSeconds().toString().padStart(2, '0')
    
    switch (format) {
      case 'HH:mm':
        return `${hours}:${minutes}`
      case 'HH:mm:ss':
        return `${hours}:${minutes}:${seconds}`
      case 'MM-DD HH:mm':
        const month = (date.getMonth() + 1).toString().padStart(2, '0')
        const day = date.getDate().toString().padStart(2, '0')
        return `${month}-${day} ${hours}:${minutes}`
      default:
        return date.toLocaleString('zh-CN')
    }
  } catch (error) {
    console.error('时间格式化失败:', error)
    return String(time)
  }
}

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, delay = 300) {
  let timeoutId
  return function (...args) {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func.apply(this, args), delay)
  }
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Function} 节流后的函数
 */
export function throttle(func, delay = 300) {
  let lastCall = 0
  return function (...args) {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      return func.apply(this, args)
    }
  }
}

/**
 * 深拷贝对象
 * @param {any} obj - 要拷贝的对象
 * @returns {any} 拷贝后的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime())
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item))
  }
  
  if (typeof obj === 'object') {
    const cloned = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key])
      }
    }
    return cloned
  }
  
  return obj
}

/**
 * 颜色转换为带透明度的rgba格式
 * @param {string} color - 颜色值
 * @param {number} opacity - 透明度 (0-1)
 * @returns {string} rgba颜色值
 */
export function colorWithOpacity(color, opacity = 1) {
  if (!color) return 'rgba(0, 0, 0, 0)'
  
  // 如果是十六进制颜色
  if (color.startsWith('#')) {
    const hex = color.replace('#', '')
    const r = parseInt(hex.substr(0, 2), 16)
    const g = parseInt(hex.substr(2, 2), 16)
    const b = parseInt(hex.substr(4, 2), 16)
    return `rgba(${r}, ${g}, ${b}, ${opacity})`
  }
  
  // 如果是rgb格式
  if (color.startsWith('rgb(')) {
    const values = color.match(/\d+/g)
    if (values && values.length >= 3) {
      return `rgba(${values[0]}, ${values[1]}, ${values[2]}, ${opacity})`
    }
  }
  
  // 如果是rgba格式，替换透明度
  if (color.startsWith('rgba(')) {
    return color.replace(/,\s*[\d.]+\)$/, `, ${opacity})`)
  }
  
  // 其他情况，返回原色
  return color
}

/**
 * 生成唯一ID
 * @param {string} prefix - 前缀
 * @returns {string} 唯一ID
 */
export function generateId(prefix = 'id') {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 检查是否为移动设备
 * @returns {boolean} 是否为移动设备
 */
export function isMobile() {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
}

/**
 * 获取设备类型
 * @returns {string} 设备类型
 */
export function getDeviceType() {
  const width = window.innerWidth
  if (width < 768) return 'mobile'
  if (width < 1024) return 'tablet'
  return 'desktop'
}

/**
 * 本地存储工具
 */
export const storage = {
  /**
   * 设置本地存储
   * @param {string} key - 键
   * @param {any} value - 值
   */
  set(key, value) {
    try {
      localStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.error('设置本地存储失败:', error)
    }
  },
  
  /**
   * 获取本地存储
   * @param {string} key - 键
   * @param {any} defaultValue - 默认值
   * @returns {any} 存储的值
   */
  get(key, defaultValue = null) {
    try {
      const value = localStorage.getItem(key)
      return value ? JSON.parse(value) : defaultValue
    } catch (error) {
      console.error('获取本地存储失败:', error)
      return defaultValue
    }
  },
  
  /**
   * 删除本地存储
   * @param {string} key - 键
   */
  remove(key) {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.error('删除本地存储失败:', error)
    }
  },
  
  /**
   * 清空本地存储
   */
  clear() {
    try {
      localStorage.clear()
    } catch (error) {
      console.error('清空本地存储失败:', error)
    }
  }
}

/**
 * 错误处理工具
 */
export const errorHandler = {
  /**
   * 处理API错误
   * @param {Error} error - 错误对象
   * @returns {string} 错误消息
   */
  handleApiError(error) {
    if (error.response) {
      // 服务器响应错误
      const status = error.response.status
      const message = error.response.data?.message || error.message
      
      switch (status) {
        case 401:
          return '登录已过期，请重新登录'
        case 403:
          return '没有权限访问此资源'
        case 404:
          return '请求的资源不存在'
        case 500:
          return '服务器内部错误'
        default:
          return message || '请求失败'
      }
    } else if (error.request) {
      // 网络错误
      return '网络连接失败，请检查网络设置'
    } else {
      // 其他错误
      return error.message || '未知错误'
    }
  },
  
  /**
   * 记录错误日志
   * @param {Error} error - 错误对象
   * @param {string} context - 错误上下文
   */
  logError(error, context = '') {
    console.error(`[${context}] 错误:`, {
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    })
    
    // 这里可以添加错误上报逻辑
    // 例如发送到错误监控服务
  }
}

/**
 * 验证工具
 */
export const validator = {
  /**
   * 验证房间号格式
   * @param {string} roomNumber - 房间号
   * @returns {boolean} 是否有效
   */
  isValidRoomNumber(roomNumber) {
    return /^\d{3,4}$/.test(roomNumber)
  },
  
  /**
   * 验证手机号格式
   * @param {string} phone - 手机号
   * @returns {boolean} 是否有效
   */
  isValidPhone(phone) {
    return /^1[3-9]\d{9}$/.test(phone)
  },
  
  /**
   * 验证邮箱格式
   * @param {string} email - 邮箱
   * @returns {boolean} 是否有效
   */
  isValidEmail(email) {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
  }
}
