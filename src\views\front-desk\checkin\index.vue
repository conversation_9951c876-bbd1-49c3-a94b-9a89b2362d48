<template>
  <div class="checkin-page">
    <div class="page-header">
      <h1 class="page-title">
        <i class="i-material-symbols:person-add-outline"></i>
        入住登记
      </h1>
      <p class="page-description">办理客人入住手续，分配房间并完成登记</p>
    </div>

    <div class="checkin-content">
      <n-card title="入住登记" class="checkin-form-card">
        <div class="form-section">
          <h3>客人信息</h3>
          <n-form ref="formRef" :model="formData" :rules="rules" label-placement="left" label-width="100px">
            <n-grid :cols="2" :x-gap="24">
              <n-form-item-gi label="姓名" path="guestName">
                <n-input v-model:value="formData.guestName" placeholder="请输入客人姓名" />
              </n-form-item-gi>
              <n-form-item-gi label="手机号" path="phone">
                <n-input v-model:value="formData.phone" placeholder="请输入手机号" />
              </n-form-item-gi>
              <n-form-item-gi label="身份证号" path="idCard">
                <n-input v-model:value="formData.idCard" placeholder="请输入身份证号" />
              </n-form-item-gi>
              <n-form-item-gi label="房间号" path="roomNumber">
                <n-button @click="handleOpenRoomSelector" block>
                  {{ selectedRoomText || '选择房间' }}
                </n-button>
              </n-form-item-gi>
            </n-grid>
          </n-form>
        </div>

        <div class="form-actions">
          <n-button type="primary" size="large" @click="handleCheckin">
            <i class="i-material-symbols:check"></i>
            确认入住
          </n-button>
          <n-button size="large" @click="handleReset">
            重置
          </n-button>
        </div>
      </n-card>
    </div>

    <!-- 房间选择器 -->
    <RoomSelector
      v-model:show="showRoomSelector"
      :check-in-info="checkInInfo"
      @confirm="handleRoomSelectorConfirm"
      @cancel="handleRoomSelectorCancel"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { useMessage } from 'naive-ui'
import RoomSelector from '@/components/hotel/RoomSelector.vue'

const $message = useMessage()

const formRef = ref(null)
const formData = reactive({
  guestName: '',
  phone: '',
  idCard: '',
  roomNumber: null
})

const rules = {
  guestName: { required: true, message: '请输入客人姓名', trigger: 'blur' },
  phone: { required: true, message: '请输入手机号', trigger: 'blur' },
  idCard: { required: true, message: '请输入身份证号', trigger: 'blur' },
  roomNumber: { required: true, message: '请选择房间', trigger: 'change' }
}

// 房间选择器相关
const showRoomSelector = ref(false)
const selectedRooms = ref([])

// 入住信息（传递给RoomSelector组件）
const checkInInfo = computed(() => ({
  actualDate: '全天房',
  priceScheme: '散客'
}))

// 选中房间的显示文本
const selectedRoomText = computed(() => {
  if (selectedRooms.value.length === 0) return ''
  if (selectedRooms.value.length === 1) {
    const room = selectedRooms.value[0]
    return `${room.room_number} - ${room.room_type_name}`
  }
  return `已选择 ${selectedRooms.value.length} 间房`
})

// 打开房间选择器
function handleOpenRoomSelector() {
  showRoomSelector.value = true
}

// 处理房间选择器确认事件
function handleRoomSelectorConfirm(rooms) {
  selectedRooms.value = rooms
  if (rooms.length > 0) {
    // 如果只选择了一个房间，设置为表单的房间号
    if (rooms.length === 1) {
      formData.roomNumber = rooms[0].room_number
    } else {
      // 多个房间的情况，可以根据业务需求处理
      formData.roomNumber = rooms.map(r => r.room_number).join(',')
    }
  }
  showRoomSelector.value = false
}

// 处理房间选择器取消事件
function handleRoomSelectorCancel() {
  showRoomSelector.value = false
}

async function handleCheckin() {
  formRef.value?.validate(async (errors) => {
    if (!errors) {
      try {
        // 1. 获取客人的 common_code
        let commonCode = await getGuestCommonCode(formData.phone)
        if (!commonCode) {
          commonCode = await createGuestCommonCode({
            name: formData.guestName,
            phone: formData.phone
          })
        }

        // 2. 构建入住参数
        const checkinParams = {
          room_sale_type: 385, // 默认销售类型
          user_info: {
            link_man: formData.guestName,
            link_phone: formData.phone,
            common_code: commonCode
          },
          price_project: 2,
          enter_time_plan: Math.floor(Date.now() / 1000),
          times: 1,
          bill_source: 9,
          room_list: selectedRooms.value.map(room => ({
            room_type_id: room.room_type_id,
            room_id: room.id,
            room_service_selected: 204,
            user_info: [{
              name: formData.guestName,
              gender: 1, // 默认男性
              phone: formData.phone,
              identification_type: 1,
              identification_number: formData.idCard
            }],
            custom_price: {
              custom_cash_pledge: 10,
              custom_room_price: [{
                data: new Date().toISOString().split('T')[0],
                price: 100
              }]
            }
          })),
          stay_type: 1,
          secrecy: 0
        }

        console.log('前台入住参数:', JSON.stringify(checkinParams, null, 2))

        // 3. 调用入住接口（这里需要导入相应的API）
        // const response = await checkinApi.create(checkinParams)

        $message.success('入住登记成功！')
        handleReset()
      } catch (error) {
        console.error('入住登记失败:', error)
        $message.error('入住登记失败，请重试')
      }
    }
  })
}

// 获取客人的 common_code
async function getGuestCommonCode(phone) {
  try {
    // 这里应该调用获取会员信息的接口
    // const memberResponse = await memberApi.getUserInfo({ phone })
    // if (memberResponse && memberResponse.data && memberResponse.data.common_code) {
    //   return memberResponse.data.common_code
    // }
    return ''
  } catch (error) {
    console.error('获取客人 common_code 失败:', error)
    return ''
  }
}

// 创建新客人并获取 common_code
async function createGuestCommonCode(guestInfo) {
  try {
    // 这里应该调用创建会员的接口
    const tempCommonCode = `TEMP_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
    console.log('创建临时 common_code:', tempCommonCode)
    return tempCommonCode
  } catch (error) {
    console.error('创建客人 common_code 失败:', error)
    return ''
  }
}

function handleReset() {
  Object.assign(formData, {
    guestName: '',
    phone: '',
    idCard: '',
    roomNumber: null
  })
  selectedRooms.value = []
}
</script>

<style scoped>
.checkin-page {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: var(--text-color-1);
}

.page-description {
  color: var(--text-color-3);
  margin: 0;
}

.checkin-form-card {
  max-width: 800px;
}

.form-section h3 {
  margin: 0 0 16px 0;
  color: var(--text-color-2);
  font-weight: 600;
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid var(--border-color);
}
</style>