/**********************************
 * @Author: <PERSON>
 * @LastEditor: <PERSON>
 * @LastEditTime: 2023/12/05 21:25:31
 * @Email: <EMAIL>
 * Copyright © 2023 <PERSON>(大脸怪) | https://isme.top
 **********************************/

import { defaultLayout, defaultPrimaryColor, naiveThemeOverrides } from '@/settings'
import { generate, getRgbStr } from '@arco-design/color'
import { useDark } from '@vueuse/core'
import { defineStore } from 'pinia'

export const useAppStore = defineStore('app', {
  state: () => ({
    collapsed: false,
    isDark: false, // 默认为浅色模式，会在初始化时同步
    layout: defaultLayout,
    primaryColor: defaultPrimaryColor,
    currentTheme: 'default', // 当前主题
    naiveThemeOverrides,
  }),
  actions: {
    switchCollapsed() {
      this.collapsed = !this.collapsed
    },
    setCollapsed(b) {
      this.collapsed = b
    },
    toggleDark() {
      this.isDark = !this.isDark
    },
    setLayout(v) {
      this.layout = v
    },
    setPrimaryColor(color) {
      this.primaryColor = color
    },
    setCurrentTheme(theme) {
      this.currentTheme = theme
    },
    updateThemeOverrides(overrides) {
      this.naiveThemeOverrides = {
        ...this.naiveThemeOverrides,
        ...overrides
      }
    },
    setThemeColor(color = this.primaryColor, isDark = this.isDark) {
      try {
        const colors = generate(color, {
          list: true,
          dark: isDark,
        })
        document.body.style.setProperty('--primary-color', getRgbStr(colors[5]))

        // 避免直接修改响应式对象，创建新对象
        const newCommon = Object.assign({}, this.naiveThemeOverrides.common || {}, {
          primaryColor: colors[5],
          primaryColorHover: colors[4],
          primaryColorSuppl: colors[4],
          primaryColorPressed: colors[6],
        })

        this.naiveThemeOverrides = {
          ...this.naiveThemeOverrides,
          common: newCommon
        }
      } catch (error) {
      }
    },
  },
  persist: {
    pick: ['collapsed', 'layout', 'primaryColor', 'currentTheme', 'naiveThemeOverrides', 'isDark'],
    storage: sessionStorage,
  },
})
