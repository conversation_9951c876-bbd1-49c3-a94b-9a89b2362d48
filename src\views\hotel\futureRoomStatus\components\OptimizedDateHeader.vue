<template>
  <div :class="headerClasses">
    <div class="date-header-date">{{ formattedDate }}</div>
    <div class="date-header-weekday">周{{ weekday }}</div>
  </div>
</template>

<script setup>
import { computed, defineProps } from 'vue'

const props = defineProps({
  date: {
    type: String,
    required: true
  }
})

// 缓存日期格式化结果
const dateObj = computed(() => new Date(props.date))

const formattedDate = computed(() => {
  const date = dateObj.value
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  return `${month}-${day}`
})

const weekday = computed(() => {
  const weekdays = ['日', '一', '二', '三', '四', '五', '六']
  return weekdays[dateObj.value.getDay()]
})

const isWeekend = computed(() => {
  const day = dateObj.value.getDay()
  return day === 0 || day === 6
})

const isToday = computed(() => {
  const today = new Date().toISOString().split('T')[0]
  return props.date === today
})

const headerClasses = computed(() => {
  const classes = ['date-header']
  
  if (isWeekend.value) {
    classes.push('weekend')
  }
  
  if (isToday.value) {
    classes.push('today')
  }
  
  return classes
})
</script>

<style scoped>
.date-header {
  line-height: 1.2;
  text-align: center;
}

.date-header-date {
  font-size: 13px;
  font-weight: 600;
}

.date-header-weekday {
  font-size: 11px;
  font-weight: 400;
  margin-top: 2px;
}

.date-header.weekend {
  color: #fa541c;
}

.date-header.today {
  color: #1890ff;
  font-weight: 700;
}
</style>
