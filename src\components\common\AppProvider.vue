<!--------------------------------
 - @Author: <PERSON>
 - @LastEditor: <PERSON>
 - @LastEditTime: 2023/12/05 21:24:09
 - @Email: <EMAIL>
 - Copyright © 2023 <PERSON>(大脸怪) | https://isme.top
 --------------------------------->

<template>
  <slot />
</template>

<script setup>
import { useAppStore } from '@/store'
import { useOsTheme } from 'naive-ui'

const appStore = useAppStore()
const osTheme = useOsTheme()

// 同步系统主题
watchEffect(() => {
  if (appStore.isDark !== (osTheme.value === 'dark')) {
    appStore.toggleDark()
  }
})
</script>
