import api from '@/api'
import { useAuthStore, usePermissionStore, useUserStore } from '@/store'
import { getPermissions } from '@/store/helper'
import { lStorage } from '@/utils'
import { ensureTokenSync } from '@/utils/token-manager'
import { checkLoginStatus } from '@/utils/auth-check'

const WHITE_LIST = [
  '/login', '/404', '/403', '/'
]

let checkedLoginStatus = false

// 重置登录检查状态的函数
function resetLoginCheckStatus() {
  checkedLoginStatus = false
}

export function createPermissionGuard(router) {
  router.beforeEach(async (to) => {
    const authStore = useAuthStore()

    // 确保token同步（页面刷新时从localStorage恢复最新token）
    ensureTokenSync()

    const token = authStore.accessToken

    // 如果要去登录页，直接允许
    if (to.path === '/login') {
      resetLoginCheckStatus()
      return true
    }

    // 页面刷新时检查登录状态（只检查一次）
    if (!checkedLoginStatus && token && to.path !== '/login') {
      checkedLoginStatus = true

      try {
        // 统一使用工具方法检查登录状态，并且不自动重定向
        const ok = await checkLoginStatus({ needTip: false, autoRedirect: false })
        if (!ok) {
          // 如果失效则在守卫里做跳转，这样刷新不会闪回登录
          resetLoginCheckStatus()
          const currentRoute = to.path
          return {
            path: '/login',
            query: { redirect: currentRoute },
            replace: true
          }
        } else {
          // 登录状态正常，继续正常流程
        }
      } catch (error) {
        // 如果是886错误，直接在这里处理跳转
        if (error.code === 886) {
          // 清理登录状态
          try {
            authStore.resetLoginState()
          } catch (resetError) {
          }

          // 重置检查状态
          resetLoginCheckStatus()

          // 直接返回登录页路由，让Vue Router处理跳转
          const currentRoute = to.path
          const loginRoute = {
            path: '/login',
            query: { redirect: currentRoute },
            replace: true
          }
          return loginRoute
        }
      }
    }

    // 没有token的情况
    if (!token || token === 'null' || token === 'undefined' || Object.keys(token).length === 0) {
      // 重置登录检查状态，因为没有token了
      resetLoginCheckStatus()

      if (WHITE_LIST.includes(to.path)) {
        return true
      }
      return { path: '/login', query: { ...to.query, redirect: to.path } }
    }

    // 有token的情况
    if (to.path === '/login') {
      return { path: '/' }
    }

    if (WHITE_LIST.includes(to.path)) {
      return true
    }

    const userStore = useUserStore()
    const permissionStore = usePermissionStore()

    // 如果没有用户信息，初始化用户信息和权限
    if (!userStore.userInfo) {
      const permissions = await getPermissions()
      // 如果没有权限信息，说明登录状态异常，跳转到登录页
      // 但是要确保登录状态检查已经通过，避免在正常刷新时误判
      if (!permissions || permissions.length === 0) {
        // 尝试从登录响应数据中恢复权限
        const loginData = lStorage.get('loginResponseData')
        if (loginData && loginData.auth_list) {
          lStorage.set('auth_list', loginData.auth_list)
          // 重新获取权限
          const recoveredPermissions = await getPermissions()
          if (recoveredPermissions && recoveredPermissions.length > 0) {
          } else {
            return { path: '/login', query: { ...to.query, redirect: to.path } }
          }
        } else {
          return { path: '/login', query: { ...to.query, redirect: to.path } }
        }
      }

      // 从本地存储中获取用户信息
      const storedUserInfo = lStorage.get('userInfo')
      if (storedUserInfo) {
        userStore.setUser(storedUserInfo)
      } else {
        // 兜底方案：使用基本信息
        const authList = lStorage.get('auth_list') || []
        const loginInfo = lStorage.get('loginInfo') || {}
        const userInfo = {
          id: null,
          username: loginInfo.name || 'Unknown',
          avatar: '',
          nickName: loginInfo.name || 'Unknown',
          roles: authList,
          currentRole: {}
        }
        userStore.setUser(userInfo)
      }

      // 设置权限
      permissionStore.setPermissions(permissions)

      // 初始化权限系统
      if (!permissionStore.menus || permissionStore.menus.length === 0) {
        await permissionStore.initPermissions()
      }

      // 动态添加路由
      const routeComponents = import.meta.glob('@/views/**/*.vue')
      permissionStore.accessRoutes.forEach((route) => {
        route.component = routeComponents[route.component] || undefined
        !router.hasRoute(route.name) && router.addRoute(route)
      })

      return { ...to, replace: true }
    }

    // 默认允许访问（简化权限检查）
    return true

  })
}
