<!--------------------------------
 - @Author: <PERSON>
 - @LastEditor: <PERSON>
 - @LastEditTime: 2023/12/16 18:46:06
 - @Email: <EMAIL>
 - Copyright © 2023 <PERSON>(大脸怪) | https://isme.top
 --------------------------------->

<template>
  <SideLogo border-b="1px solid light_border dark:dark_border" />
  <SideMenu class="cus-scroll-y h-0 flex-1 overflow-hidden hover:overflow-auto" />
</template>

<script setup>
import { SideLogo, SideMenu } from '@/layouts/components'
</script>

<style scoped>
/* Improve scrollbar behavior */
.cus-scroll-y {
  scrollbar-width: thin;
  scrollbar-color: rgba(var(--primary-color), 0.2) transparent;
}

.cus-scroll-y::-webkit-scrollbar {
  width: 4px;
}

.cus-scroll-y::-webkit-scrollbar-track {
  background: transparent;
}

.cus-scroll-y::-webkit-scrollbar-thumb {
  background-color: rgba(var(--primary-color), 0.2);
  border-radius: 4px;
}

.cus-scroll-y::-webkit-scrollbar-thumb:hover {
  background-color: rgba(var(--primary-color), 0.4);
}
</style>
