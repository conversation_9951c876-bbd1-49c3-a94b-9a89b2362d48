/**
 * 事件监听器优化工具
 * 用于优化滚轮事件和触摸事件的性能
 */

/**
 * 检测浏览器是否支持被动事件监听器
 */
let supportsPassive = false
try {
  const opts = Object.defineProperty({}, 'passive', {
    get() {
      supportsPassive = true
      return true
    }
  })
  window.addEventListener('testPassive', null, opts)
  window.removeEventListener('testPassive', null, opts)
} catch (e) {
  // 浏览器不支持被动事件监听器
}

/**
 * 获取优化的事件选项
 * @param {boolean} passive - 是否使用被动监听器
 * @param {boolean} capture - 是否在捕获阶段处理
 * @returns {boolean|Object} 事件选项
 */
export function getEventOptions(passive = true, capture = false) {
  if (supportsPassive) {
    return {
      passive,
      capture
    }
  }
  return capture
}

/**
 * 优化的事件监听器添加函数
 * @param {Element} element - 目标元素
 * @param {string} event - 事件名称
 * @param {Function} handler - 事件处理函数
 * @param {Object} options - 选项
 */
export function addOptimizedEventListener(element, event, handler, options = {}) {
  const { passive = true, capture = false } = options
  
  // 对于滚轮和触摸事件，默认使用被动监听器
  const shouldUsePassive = passive && (
    event === 'wheel' ||
    event === 'touchstart' ||
    event === 'touchmove' ||
    event === 'touchend' ||
    event === 'scroll'
  )
  
  const eventOptions = getEventOptions(shouldUsePassive, capture)
  element.addEventListener(event, handler, eventOptions)
  
  return () => {
    element.removeEventListener(event, handler, eventOptions)
  }
}

/**
 * 节流函数 - 用于优化高频事件
 * @param {Function} func - 要节流的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Function} 节流后的函数
 */
export function throttle(func, delay = 16) {
  let timeoutId
  let lastExecTime = 0
  
  return function (...args) {
    const currentTime = Date.now()
    
    if (currentTime - lastExecTime > delay) {
      func.apply(this, args)
      lastExecTime = currentTime
    } else {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => {
        func.apply(this, args)
        lastExecTime = Date.now()
      }, delay - (currentTime - lastExecTime))
    }
  }
}

/**
 * 防抖函数 - 用于优化频繁触发的事件
 * @param {Function} func - 要防抖的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, delay = 300) {
  let timeoutId
  
  return function (...args) {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func.apply(this, args), delay)
  }
}

/**
 * 初始化全局事件优化
 * 在应用启动时调用，优化常见的性能问题
 */
export function initEventOptimization() {
  // 优化滚轮事件
  if (supportsPassive) {
    // 为 document 添加被动的滚轮事件监听器，防止第三方库的非被动监听器
    const passiveWheelHandler = () => {
      // 空函数，仅用于确保有被动监听器
    }
    
    document.addEventListener('wheel', passiveWheelHandler, { passive: true })
    document.addEventListener('touchstart', passiveWheelHandler, { passive: true })
    document.addEventListener('touchmove', passiveWheelHandler, { passive: true })
  }
  
  // 优化 resize 事件
  let resizeTimer
  const optimizedResizeHandler = () => {
    clearTimeout(resizeTimer)
    resizeTimer = setTimeout(() => {
      // 触发自定义的优化 resize 事件
      window.dispatchEvent(new CustomEvent('optimizedResize'))
    }, 100)
  }
  
  window.addEventListener('resize', optimizedResizeHandler, getEventOptions(true))
  
  // 优化 scroll 事件
  let scrollTimer
  const optimizedScrollHandler = throttle(() => {
    // 触发自定义的优化 scroll 事件
    window.dispatchEvent(new CustomEvent('optimizedScroll'))
  }, 16)
  
  window.addEventListener('scroll', optimizedScrollHandler, getEventOptions(true))
}

/**
 * 创建优化的滚动监听器
 * @param {Function} callback - 滚动回调函数
 * @param {Object} options - 选项
 * @returns {Function} 清理函数
 */
export function createOptimizedScrollListener(callback, options = {}) {
  const { throttleDelay = 16, element = window } = options
  
  const throttledCallback = throttle(callback, throttleDelay)
  const eventOptions = getEventOptions(true)
  
  element.addEventListener('scroll', throttledCallback, eventOptions)
  
  return () => {
    element.removeEventListener('scroll', throttledCallback, eventOptions)
  }
}

/**
 * 创建优化的滚轮监听器
 * @param {Function} callback - 滚轮回调函数
 * @param {Object} options - 选项
 * @returns {Function} 清理函数
 */
export function createOptimizedWheelListener(callback, options = {}) {
  const { throttleDelay = 16, element = window, passive = true } = options
  
  const throttledCallback = throttle(callback, throttleDelay)
  const eventOptions = getEventOptions(passive)
  
  element.addEventListener('wheel', throttledCallback, eventOptions)
  
  return () => {
    element.removeEventListener('wheel', throttledCallback, eventOptions)
  }
}

// 默认导出所有工具函数
export default {
  getEventOptions,
  addOptimizedEventListener,
  throttle,
  debounce,
  initEventOptimization,
  createOptimizedScrollListener,
  createOptimizedWheelListener,
  supportsPassive
}
