import { request } from '@/utils'

// 会员卡管理相关接口
export const getCardList = (params) => {
  return request.get('/hotel/card/list', { params })
}

export const createCard = (data) => {
  return request.post('/hotel/card', data)
}

export const updateCard = (data) => {
  return request.put(`/hotel/card/${data.id}`, data)
}

export const deleteCard = (id) => {
  return request.delete(`/hotel/card/${id}`)
}

export const getCardDetail = (id) => {
  return request.get(`/hotel/card/${id}`)
}

// 会员卡激活
export const activateCard = (id) => {
  return request.put(`/hotel/card/${id}/activate`)
}

// 会员卡挂失
export const reportLossCard = (id) => {
  return request.put(`/hotel/card/${id}/report-loss`)
}

// 会员卡解挂
export const unlockCard = (id) => {
  return request.put(`/hotel/card/${id}/unlock`)
}
