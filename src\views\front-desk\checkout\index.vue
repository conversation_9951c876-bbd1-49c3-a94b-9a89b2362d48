<template>
  <div class="checkout-page">
    <div class="page-header">
      <h1 class="page-title">
        <i class="i-material-symbols:person-remove-outline"></i>
        退房结账
      </h1>
      <p class="page-description">办理客人退房手续，结算费用并释放房间</p>
    </div>

    <div class="checkout-content">
      <n-grid :cols="2" :x-gap="24">
        <!-- 房间搜索 -->
        <n-card title="选择房间" class="room-search-card">
          <n-input
            v-model:value="searchRoom"
            placeholder="输入房间号搜索"
            size="large"
            @input="handleRoomSearch"
          >
            <template #prefix>
              <i class="i-material-symbols:search"></i>
            </template>
          </n-input>

          <div class="room-list" v-if="occupiedRooms.length > 0">
            <div
              v-for="room in filteredRooms"
              :key="room.number"
              :class="['room-item', { active: selectedRoom?.number === room.number }]"
              @click="selectRoom(room)"
            >
              <div class="room-info">
                <div class="room-number">{{ room.number }}</div>
                <div class="guest-name">{{ room.guestName }}</div>
                <div class="checkin-date">入住：{{ room.checkinDate }}</div>
              </div>
              <div class="room-amount">
                ¥{{ room.totalAmount }}
              </div>
            </div>
          </div>
        </n-card>

        <!-- 账单详情 -->
        <n-card title="账单详情" class="bill-card">
          <div v-if="selectedRoom" class="bill-content">
            <div class="guest-info">
              <h3>{{ selectedRoom.guestName }}</h3>
              <p>房间：{{ selectedRoom.number }} | 入住：{{ selectedRoom.checkinDate }}</p>
            </div>

            <div class="bill-items">
              <div class="bill-item">
                <span>房费</span>
                <span>¥{{ selectedRoom.roomCharge }}</span>
              </div>
              <div class="bill-item">
                <span>服务费</span>
                <span>¥{{ selectedRoom.serviceCharge }}</span>
              </div>
              <div class="bill-item">
                <span>其他费用</span>
                <span>¥{{ selectedRoom.otherCharge }}</span>
              </div>
              <div class="bill-total">
                <span>总计</span>
                <span>¥{{ selectedRoom.totalAmount }}</span>
              </div>
            </div>

            <div class="checkout-actions">
              <n-button type="primary" size="large" block @click="handleCheckout">
                <i class="i-material-symbols:check"></i>
                确认退房
              </n-button>
            </div>
          </div>
          <div v-else class="no-selection">
            <n-empty description="请选择要退房的房间" />
          </div>
        </n-card>
      </n-grid>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useMessage } from 'naive-ui'

const $message = useMessage()

const searchRoom = ref('')
const selectedRoom = ref(null)

// 已入住房间数据
const occupiedRooms = ref([])

const filteredRooms = computed(() => {
  if (!searchRoom.value) return occupiedRooms.value
  return occupiedRooms.value.filter(room =>
    room.number.includes(searchRoom.value) ||
    room.guestName.includes(searchRoom.value)
  )
})

function handleRoomSearch() {
  // 搜索逻辑已在computed中处理
}

function selectRoom(room) {
  selectedRoom.value = room
}

function handleCheckout() {
  if (!selectedRoom.value) {
    $message.warning('请先选择要退房的房间')
    return
  }

  $message.success(`房间 ${selectedRoom.value.number} 退房成功！`)
  // 这里添加实际的退房逻辑

  // 从列表中移除已退房的房间
  const index = occupiedRooms.value.findIndex(room => room.number === selectedRoom.value.number)
  if (index > -1) {
    occupiedRooms.value.splice(index, 1)
  }
  selectedRoom.value = null
}
</script>

<style scoped>
.checkout-page {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: var(--text-color-1);
}

.page-description {
  color: var(--text-color-3);
  margin: 0;
}

.room-list {
  margin-top: 16px;
}

.room-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.room-item:hover {
  border-color: var(--primary-color);
  background: var(--primary-color-hover);
}

.room-item.active {
  border-color: var(--primary-color);
  background: var(--primary-color-pressed);
}

.room-info {
  flex: 1;
}

.room-number {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color-1);
}

.guest-name {
  color: var(--text-color-2);
  margin: 4px 0;
}

.checkin-date {
  font-size: 12px;
  color: var(--text-color-3);
}

.room-amount {
  font-size: 18px;
  font-weight: 600;
  color: var(--primary-color);
}

.bill-content {
  padding: 16px 0;
}

.guest-info h3 {
  margin: 0 0 8px 0;
  color: var(--text-color-1);
}

.guest-info p {
  margin: 0;
  color: var(--text-color-3);
}

.bill-items {
  margin: 24px 0;
  border-top: 1px solid var(--border-color);
  padding-top: 16px;
}

.bill-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  color: var(--text-color-2);
}

.bill-total {
  display: flex;
  justify-content: space-between;
  padding: 12px 0;
  border-top: 1px solid var(--border-color);
  margin-top: 8px;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color-1);
}

.checkout-actions {
  margin-top: 24px;
}

.no-selection {
  padding: 40px 0;
  text-align: center;
}
</style>