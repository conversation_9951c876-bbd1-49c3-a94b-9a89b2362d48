/**********************************
 * @Author: <PERSON>
 * @LastEditor: <PERSON>
 * @LastEditTime: 2023/12/05 21:25:47
 * @Email: <EMAIL>
 * Copyright © 2023 <PERSON>(大脸怪) | https://isme.top
 **********************************/

import { h } from 'vue'
import { isExternal } from '@/utils'
import { hyphenate } from '@vueuse/core'
import { defineStore } from 'pinia'

export const usePermissionStore = defineStore('permission', {
  state: () => ({
    accessRoutes: [],
    permissions: [],
    menus: [],
  }),
  actions: {
    setPermissions(permissions) {
      this.permissions = permissions

      // 根据新的权限数据结构处理菜单
      const authList = permissions.filter(item => item.auth_type === 'page' && item.router_url)
      // 构建菜单树结构
      const menuTree = this.buildMenuTree(authList)
      this.menus = menuTree

      // 生成路由
      this.accessRoutes = this.generateRoutes(authList)
    },

    // 构建优化后的菜单树结构
    buildMenuTree(authList) {
      if (!authList || authList.length === 0) {
        return []
      }

      // 过滤有效权限
      const validAuths = authList.filter(auth =>
        auth.auth_type === 'page' &&
        auth.router_url &&
        auth.router_url.trim() !== ''
      )

      if (validAuths.length === 0) {
        return this.generateDefaultMenus()
      }

      // 转换权限数据为菜单格式
      const menuItems = validAuths.map(auth => ({
        key: auth.auth_mark,
        label: auth.auth_name,
        path: auth.router_url,
        icon: this.getOptimizedIcon(auth.auth_name, auth.icon),
        order: this.getMenuOrder(auth),
        auth_id: auth.auth_id
      }))

      // 使用智能分组优化（同步版本）
      return this.optimizeMenuStructure(menuItems)
    },

    // 专业PMS菜单结构优化 - 更细致的业务分类
    optimizeMenuStructure(items) {
      const MENU_GROUPS = {
        'dashboard': {
          name: '运营驾驶舱',
          icon: 'i-fe:monitor',
          priority: 1,
          keywords: ['dashboard', 'overview', 'today', 'alerts', '概览', '驾驶舱', '总览', '预警']
        },
        'front-desk': {
          name: '前台接待',
          icon: 'i-fe:clipboard',
          priority: 2,
          keywords: ['front-desk', 'checkin', 'checkout', 'reservation', '前台', '接待', '入住', '退房', '预订']
        },
        'room-management': {
          name: '房态管理',
          icon: 'i-fe:layout',
          priority: 3,
          keywords: ['room', 'status', 'grid', '房态', '房间', '客房', '房型']
        },
        'housekeeping': {
          name: '房务服务',
          icon: 'i-fe:home',
          priority: 4,
          keywords: ['housekeeping', 'cleaning', 'maintenance', '房务', '清洁', '维修', '保养']
        },
        'guest-management': {
          name: '客户关系',
          icon: 'i-fe:users',
          priority: 5,
          keywords: ['guest', 'customer', 'crm', '客户', '客人', '档案']
        },
        'membership': {
          name: '会员体系',
          icon: 'i-fe:award',
          priority: 6,
          keywords: ['member', 'loyalty', 'points', '会员', '积分', '等级', '卡券']
        },
        'finance': {
          name: '财务管理',
          icon: 'i-fe:dollar-sign',
          priority: 7,
          keywords: ['finance', 'billing', 'payment', 'invoice', '财务', '账单', '收款', '对账', '费用']
        },
        'inventory': {
          name: '库存管理',
          icon: 'i-fe:package',
          priority: 8,
          keywords: ['inventory', 'stock', 'supply', '库存', '物料', '采购', '供应']
        },
        'marketing': {
          name: '营销推广',
          icon: 'i-fe:trending-up',
          priority: 9,
          keywords: ['marketing', 'promotion', 'campaign', '营销', '推广', '活动', '广告']
        },
        'channel': {
          name: '渠道管理',
          icon: 'i-fe:globe',
          priority: 10,
          keywords: ['channel', 'ota', 'distribution', '渠道', '分销', 'OTA', '第三方']
        },
        'wechat': {
          name: '微信生态',
          icon: 'i-fe:message-circle',
          priority: 11,
          keywords: ['wechat', 'wx', 'weixin', '微信', '小程序', '公众号']
        },
        'reports': {
          name: '数据报表',
          icon: 'i-fe:bar-chart',
          priority: 12,
          keywords: ['report', 'statistics', 'analysis', 'data', '报表', '统计', '分析', '数据']
        },
        'system': {
          name: '系统管理',
          icon: 'i-fe:settings',
          priority: 13,
          keywords: ['system', 'setting', 'config', 'admin', '系统', '设置', '配置', '管理员']
        },
        'security': {
          name: '权限安全',
          icon: 'i-fe:shield',
          priority: 14,
          keywords: ['security', 'permission', 'role', 'auth', '权限', '角色', '安全', '认证']
        },
        'integration': {
          name: '系统集成',
          icon: 'i-fe:link',
          priority: 15,
          keywords: ['integration', 'api', 'interface', '集成', '接口', '对接', 'API']
        },

      }

      const groups = new Map()

      // 初始化分组
      Object.keys(MENU_GROUPS).forEach(key => {
        groups.set(key, {
          key: key,
          label: MENU_GROUPS[key].name,
          icon: () => h('i', { class: `${MENU_GROUPS[key].icon} text-16` }),
          order: MENU_GROUPS[key].priority,
          children: []
        })
      })

      // 智能分组匹配
      items.forEach(item => {
        let matched = false
        const path = (item.path || '').toLowerCase()
        const label = (item.label || '').toLowerCase()

        for (const [groupKey, group] of Object.entries(MENU_GROUPS)) {
          // 检查路径匹配
          if (path.includes(groupKey)) {
            groups.get(groupKey).children.push(item)
            matched = true
            break
          }

          // 检查标签关键词匹配
          const keywords = group.keywords
          for (const keyword of keywords) {
            if (label.includes(keyword) || path.includes(keyword)) {
              groups.get(groupKey).children.push(item)
              matched = true
              break
            }
          }

          if (matched) break
        }

        // 基于内容智能分组
        if (!matched) {
          // 房态相关
          if (label.includes('房态') || label.includes('房间') || label.includes('客房') || label.includes('房型')) {
            groups.get('room-management').children.push(item)
          }
          // 前台业务相关
          else if (label.includes('前台') || label.includes('接待') || label.includes('入住') || label.includes('退房') || label.includes('预订')) {
            groups.get('front-desk').children.push(item)
          }
          // 房务服务相关
          else if (label.includes('房务') || label.includes('清洁') || label.includes('维修') || label.includes('保养')) {
            groups.get('housekeeping').children.push(item)
          }
          // 客户相关
          else if (label.includes('客户') || label.includes('客人') || label.includes('档案')) {
            groups.get('guest-management').children.push(item)
          }
          // 会员相关
          else if (label.includes('会员') || label.includes('积分') || label.includes('等级') || label.includes('卡券')) {
            groups.get('membership').children.push(item)
          }
          // 财务相关
          else if (label.includes('财务') || label.includes('账单') || label.includes('费用') || label.includes('支付') || label.includes('收款') || label.includes('对账')) {
            groups.get('finance').children.push(item)
          }
          // 库存相关
          else if (label.includes('库存') || label.includes('物料') || label.includes('采购') || label.includes('供应')) {
            groups.get('inventory').children.push(item)
          }
          // 营销相关
          else if (label.includes('营销') || label.includes('推广') || label.includes('活动') || label.includes('广告')) {
            groups.get('marketing').children.push(item)
          }
          // 渠道相关
          else if (label.includes('渠道') || label.includes('分销') || label.includes('ota') || label.includes('第三方')) {
            groups.get('channel').children.push(item)
          }
          // 微信相关
          else if (label.includes('微信') || label.includes('小程序') || label.includes('公众号')) {
            groups.get('wechat').children.push(item)
          }
          // 报表相关
          else if (label.includes('报表') || label.includes('统计') || label.includes('分析') || label.includes('数据')) {
            groups.get('reports').children.push(item)
          }
          // 权限安全相关
          else if (label.includes('权限') || label.includes('角色') || label.includes('安全') || label.includes('认证')) {
            groups.get('security').children.push(item)
          }
          // 系统集成相关
          else if (label.includes('集成') || label.includes('接口') || label.includes('对接') || label.includes('api')) {
            groups.get('integration').children.push(item)
          }
          // 开发工具相关
          else if (label.includes('工具') || label.includes('开发') || label.includes('测试') || label.includes('调试')) {
            groups.get('tools').children.push(item)
          }
          // 其他放入系统管理
          else {
            groups.get('system').children.push(item)
          }
        }
      })

      // 清理空分组并排序
      let result = Array.from(groups.values())
        .filter(group => group.children.length > 0)
        .sort((a, b) => a.order - b.order)
        .map(group => {
          group.children = group.children.sort((a, b) => a.order - b.order)
          return group
        })

      // 直接返回菜单结构，不再进行店铺类型过滤
      return result
    },

    // 获取优化后的图标 - 只为一级菜单提供图标，二级菜单不显示图标
    getOptimizedIcon(name, icon) {
      // 一级菜单图标映射（主分类）
      const primaryIconMapping = {
        // 运营驾驶舱模块
        '运营驾驶舱': 'i-fe:monitor',
        '运营总览': 'i-fe:monitor',

        // 前台业务模块
        '前台业务': 'i-fe:clipboard',
        '前台接待': 'i-fe:clipboard',

        // 房态管理模块
        '房态管理': 'i-fe:layout',
        '房态总览': 'i-fe:layout',
        '实时房态': 'i-fe:layout',

        // 房务服务模块
        '房务服务': 'i-fe:home',
        '房务管理': 'i-fe:home',

        // 客户关系模块
        '客户关系': 'i-fe:users',
        '客户管理': 'i-fe:users',

        // 会员体系模块
        '会员体系': 'i-fe:award',
        '会员管理': 'i-fe:award',

        // 财务管理模块
        '财务管理': 'i-fe:dollar-sign',

        // 库存管理模块
        '库存管理': 'i-fe:package',

        // 营销推广模块
        '营销推广': 'i-fe:trending-up',
        '营销中心': 'i-fe:trending-up',

        // 渠道管理模块
        '渠道管理': 'i-fe:globe',

        // 微信生态模块
        '微信生态': 'i-fe:message-circle',
        '微信管理': 'i-fe:message-circle',

        // 数据报表模块
        '数据报表': 'i-fe:bar-chart',
        '报表中心': 'i-fe:bar-chart',

        // 系统管理模块
        '系统管理': 'i-fe:settings',
        '系统设置': 'i-fe:settings',

        // 权限安全模块
        '权限安全': 'i-fe:shield',

        // 系统集成模块
        '系统集成': 'i-fe:link',

        // 系统维护模块
        '系统维护': 'i-fe:wrench',

        // 开发工具模块
        '开发工具': 'i-fe:code'
      }

      // 只为一级菜单（主分类）提供图标
      const iconClass = primaryIconMapping[name]
      return iconClass ? () => h('i', { class: `${iconClass} text-16` }) : null
    },

    // 获取菜单排序权重
    getMenuOrder(auth) {
      const orderMapping = {
        'dashboard': 1,
        'front-desk': 2,
        'crm': 3,
        'hotel': 4,
        'wx': 5,
        'system': 8
      }

      const path = auth.router_url || ''
      for (const [key, order] of Object.entries(orderMapping)) {
        if (path.includes(key)) {
          return order
        }
      }
      return auth.auth_id || 999
    },

    // 根据子菜单生成父级名称
    generateParentName(children) {
      if (!children || children.length === 0) return '未知分组'

      // 尝试从子菜单名称中提取公共前缀
      const names = children.map(child => child.auth_name)

      // 查找公共关键词
      const commonKeywords = ['财务', '管理', '系统', '设置', '订单', '用户', '权限']
      for (const keyword of commonKeywords) {
        if (names.some(name => name.includes(keyword))) {
          return `${keyword}管理`
        }
      }

      // 如果没有找到公共关键词，使用第一个子菜单的前缀
      const firstName = names[0]
      if (firstName.length > 2) {
        return firstName.substring(0, 2) + '管理'
      }

      return '功能分组'
    },

    // 获取默认图标（简化版本）
    getDefaultIcon(authName, authMark) {
      // 关键词到图标的映射
      const keywordMap = [
        ['dashboard', 'i-fe:pie-chart'],
        ['home', 'i-fe:home'],
        ['user', 'i-fe:user'],
        ['people', 'i-fe:users'],
        ['role', 'i-fe:shield'],
        ['security', 'i-fe:lock'],
        ['settings', 'i-fe:settings'],
        ['hotel', 'i-fe:home'],
        ['room', 'i-fe:home'],
        ['grid', 'i-fe:grid'],
        ['calendar', 'i-fe:calendar'],
        ['money', 'i-fe:dollar-sign'],
        ['chart', 'i-fe:bar-chart'],
        ['file', 'i-fe:file-text'],
        ['log', 'i-fe:file-text']
      ]

      const searchText = (authName + ' ' + authMark).toLowerCase()

      for (const [keyword, icon] of keywordMap) {
        if (searchText.includes(keyword)) {
          return icon
        }
      }

      return 'i-fe:folder'
    },

    // 递归打印菜单树结构
    logMenuTree(menus, level = 0) {
      menus.forEach(menu => {

        if (menu.children && menu.children.length > 0) {
          this.logMenuTree(menu.children, level + 1)
        }
      })
    },

    // 生成路由配置（优化版）
    generateRoutes(authList) {
      const validRoutes = []
      const basicRoutePaths = new Set([
        '/dashboard/overview',
        '/dashboard/today',
        '/dashboard/alerts',
        '/front-desk/room-status',
        '/front-desk/checkin',
        '/front-desk/checkout',
        '/front-desk/reservation',
        '/crm/guests',
        '/crm/members',
        '/crm/loyalty',
        '/system/users',
        '/system/roles',
        '/system/audit-log',
        '/system/backup',
        '/system/status',
        '/wx/manager',
        '/wx/codemodel',
        '/wx/codecgx'
      ])

      authList.forEach(auth => {
        if (auth.auth_type !== 'page' || !auth.router_url) {
          return
        }

        const routePath = auth.router_url.trim()
        if (!routePath || routePath === '/') {
          return
        }

        // 清理路径格式
        const cleanPath = routePath.replace(/\/+/g, '/')

        // 检查是否为有效路由
        const isValidRoute = this.isValidRoute(cleanPath)
        if (!isValidRoute) {
          return
        }

        const route = {
          name: auth.auth_mark,
          path: cleanPath,
          component: this.getComponentPath(cleanPath),
          meta: {
            title: auth.auth_name,
            icon: auth.icon,
            auth_mark: auth.auth_mark,
            auth_id: auth.auth_id,
            keepAlive: true,
            hidden: false
          }
        }

        // 检查是否为已知的基础路由
        if (basicRoutePaths.has(cleanPath)) {
          route.meta.order = this.getRouteOrder(cleanPath)
        }

        validRoutes.push(route)
      })

      return validRoutes.filter(route => route.component)
    },

    // 检查路由有效性
    isValidRoute(path) {
      if (!path || path.includes('//')) return false

      // 检查是否为外部链接
      if (path.startsWith('http')) return true

      // 检查路径格式
      if (!path.startsWith('/')) return false

      // 检查特殊字符
      if (/[^\w\-\/]/.test(path)) return false

      return true
    },

    // 获取路由排序
    getRouteOrder(path) {
      const orderMap = {
        '/dashboard/overview': 1,
        '/dashboard/today': 2,
        '/dashboard/alerts': 3,
        '/front-desk/room-status': 10,
        '/front-desk/checkin': 11,
        '/front-desk/checkout': 12,
        '/front-desk/reservation': 13,
        '/crm/guests': 20,
        '/crm/members': 21,
        '/crm/loyalty': 22,
        '/system/users': 80,
        '/system/roles': 81,
        '/system/audit-log': 82,
        '/system/backup': 83,
        '/system/status': 84,
        '/wx/manager': 50,
        '/wx/codemodel': 51,
        '/wx/codecgx': 52
      }

      return orderMap[path] || 999
    },

    // 根据路由路径获取组件路径
    getComponentPath(routerUrl) {
      if (!routerUrl) return null

      // 清理路径格式
      const cleanPath = routerUrl.replace(/^\//, '').replace(/\/+/g, '/')

      // 检查特殊路由
      const specialRoutes = {
        'system/audit-log': '/src/views/system/status.vue',
        'system/backup': '/src/views/system/status.vue',
        'system/status': '/src/views/system/status.vue'
      }

      if (specialRoutes[cleanPath]) {
        return specialRoutes[cleanPath]
      }

      // 标准路由格式
      return `/src/views/${cleanPath}/index.vue`
    },
    getMenuItem(item, parent) {
      const route = this.generateRoute(item, item.show ? null : parent?.key)
      if (item.enable && route.path && !route.path.startsWith('http'))
        this.accessRoutes.push(route)
      if (!item.show)
        return null
      const menuItem = {
        label: route.meta.title,
        key: route.name,
        path: route.path,
        originPath: route.meta.originPath,
        icon: () => h('i', { class: `${route.meta.icon} text-16` }),
        order: item.order ?? 0,
      }
      const children = item.children?.filter(item => item.type === 'MENU') || []
      if (children.length) {
        menuItem.children = children
          .map(child => this.getMenuItem(child, menuItem))
          .filter(item => !!item)
          .sort((a, b) => a.order - b.order)
        if (!menuItem.children.length)
          delete menuItem.children
      }
      return menuItem
    },
    generateRoute(item, parentKey) {
      let originPath
      if (isExternal(item.path)) {
        originPath = item.path
        item.component = '/src/views/iframe/index.vue'
        item.path = `/iframe/${hyphenate(item.code)}`
      }
      return {
        name: item.code,
        path: item.path,
        redirect: item.redirect,
        component: item.component,
        meta: {
          originPath,
          icon: `${item.icon}?mask`,
          title: item.name,
          layout: item.layout,
          keepAlive: !!item.keepAlive,
          parentKey,
          btns: item.children
            ?.filter(item => item.type === 'BUTTON')
            .map(item => ({ code: item.code, name: item.name })),
        },
      }
    },
    // 检查是否有指定权限
    hasPermission(authMark) {
      if (!authMark) return true
      return this.permissions.some(permission => permission.auth_mark === authMark)
    },

    // 生成专业PMS菜单结构（按照酒店管理系统标准分类）
    generateDefaultMenus() {
      return [
        // 1. 运营驾驶舱 - 核心数据总览
        {
          key: 'dashboard',
          label: '运营驾驶舱',
          path: '/dashboard',
          icon: () => h('i', { class: 'i-fe:monitor text-16' }),
          order: 1,
          children: [
            {
              key: 'dashboard-overview',
              label: '今日概览',
              path: '/dashboard/overview',
              order: 1
            },
            {
              key: 'dashboard-today',
              label: '经营日报',
              path: '/dashboard/today',
              order: 2
            },
            {
              key: 'dashboard-alerts',
              label: '预警中心',
              path: '/dashboard/alerts',
              order: 3
            }
          ]
        },

        // 2. 前台业务 - 核心接待流程
        {
          key: 'front-desk',
          label: '前台业务',
          path: '/front-desk',
          icon: () => h('i', { class: 'i-fe:clipboard text-16' }),
          order: 2,
          children: [
            {
              key: 'front-desk-room-status',
              label: '房态总览',
              path: '/front-desk/room-status',
              order: 1
            },
            {
              key: 'front-desk-checkin',
              label: '入住登记',
              path: '/front-desk/checkin',
              order: 2
            },
            {
              key: 'front-desk-checkout',
              label: '退房结账',
              path: '/front-desk/checkout',
              order: 3
            },
            {
              key: 'front-desk-reservation',
              label: '预订中心',
              path: '/front-desk/reservation',
              order: 4
            }
          ]
        },

        // 3. 客户关系 - CRM核心功能
        {
          key: 'crm',
          label: '客户关系',
          path: '/crm',
          icon: () => h('i', { class: 'i-fe:users text-16' }),
          order: 3,
          children: [
            {
              key: 'crm-guests',
              label: '客户档案',
              path: '/crm/guests',
              order: 1
            },
            {
              key: 'crm-members',
              label: '会员管理',
              path: '/crm/members',
              order: 2
            },
            {
              key: 'crm-loyalty',
              label: '积分体系',
              path: '/crm/loyalty',
              order: 3
            }
          ]
        },

        // 4. 房务管理 - 客房运营
        {
          key: 'housekeeping',
          label: '房务管理',
          path: '/housekeeping',
          icon: () => h('i', { class: 'i-fe:home text-16' }),
          order: 4,
          children: [
            {
              key: 'housekeeping-rooms',
              label: '房间状态',
              path: '/housekeeping/rooms',
              order: 1
            },
            {
              key: 'housekeeping-cleaning',
              label: '清洁任务',
              path: '/housekeeping/cleaning',
              order: 2
            },
            {
              key: 'housekeeping-maintenance',
              label: '维修工单',
              path: '/housekeeping/maintenance',
              order: 3
            }
          ]
        },

        // 5. 财务管理 - 账务核心
        {
          key: 'finance',
          label: '财务管理',
          path: '/finance',
          icon: () => h('i', { class: 'i-fe:dollar-sign text-16' }),
          order: 5,
          children: [
            {
              key: 'finance-billing',
              label: '账单管理',
              path: '/finance/billing',
              order: 1
            },
            {
              key: 'finance-payments',
              label: '收款管理',
              path: '/finance/payments',
              order: 2
            },
            {
              key: 'finance-reconciliation',
              label: '对账管理',
              path: '/finance/reconciliation',
              order: 3
            }
          ]
        },

        // 6. 数据分析 - 商业智能
        {
          key: 'analytics',
          label: '数据分析',
          path: '/analytics',
          icon: () => h('i', { class: 'i-fe:trending-up text-16' }),
          order: 6,
          children: [
            {
              key: 'analytics-revenue',
              label: '收益分析',
              path: '/analytics/revenue',
              order: 1
            },
            {
              key: 'analytics-occupancy',
              label: '入住分析',
              path: '/analytics/occupancy',
              order: 2
            },
            {
              key: 'analytics-customer',
              label: '客户分析',
              path: '/analytics/customer',
              order: 3
            }
          ]
        },

        // 7. 营销中心 - 数字化营销
        {
          key: 'marketing',
          label: '营销中心',
          path: '/marketing',
          icon: () => h('i', { class: 'i-fe:trending-up text-16' }),
          order: 7,
          children: [
            {
              key: 'marketing-campaigns',
              label: '营销活动',
              path: '/marketing/campaigns',
              order: 1
            },
            {
              key: 'marketing-wechat',
              label: '微信营销',
              path: '/marketing/wechat',
              order: 2
            },
            {
              key: 'marketing-tools',
              label: '营销工具',
              path: '/marketing/tools',
              order: 3
            }
          ]
        },

        // 8. 系统管理 - 基础配置
        {
          key: 'system',
          label: '系统管理',
          path: '/system',
          icon: () => h('i', { class: 'i-fe:settings text-16' }),
          order: 8,
          children: [
            {
              key: 'system-users',
              label: '用户管理',
              path: '/system/users',
              order: 1
            },
            {
              key: 'system-roles',
              label: '角色权限',
              path: '/system/roles',
              order: 2
            },
            {
              key: 'system-config',
              label: '系统配置',
              path: '/system/config',
              order: 3
            },
            {
              key: 'system-logs',
              label: '操作日志',
              path: '/system/logs',
              order: 4
            },
            {
              key: 'system-status',
              label: '系统监控',
              path: '/system/status',
              order: 5
            }
          ]
        },

      ]
    },

    // 初始化权限系统 - 简化版本，不涉及店铺切换
    async initPermissions() {
      try {
        // 获取登录数据中的权限列表
        const loginData = JSON.parse(localStorage.getItem('loginResponseData') || '{}')
        let authList = []

        if (loginData.auth_list && Array.isArray(loginData.auth_list)) {
          authList = loginData.auth_list
        }

        if (authList.length === 0) {
          this.permissions = []
          this.menus = this.generateDefaultMenus()
          return
        }

        // 设置权限列表，根据接口返回的权限信息构建菜单
        this.setPermissions(authList)
        // 如果没有生成任何菜单，则使用默认菜单作为备选
        if (this.menus.length === 0) {
          this.menus = this.generateDefaultMenus()
        }
      } catch (error) {
        this.permissions = []
        this.menus = this.generateDefaultMenus()
      }
    },

    // 检查是否有页面权限
    hasPagePermission(routerUrl) {
      if (!routerUrl) return true
      return this.permissions.some(permission =>
        permission.auth_type === 'page' && permission.router_url === routerUrl
      )
    },

    // 检查是否有元素权限
    hasElementPermission(authMark) {
      if (!authMark) return true
      return this.permissions.some(permission =>
        permission.auth_type === 'element' && permission.auth_mark === authMark
      )
    },

    // 获取指定页面的所有元素权限
    getPageElementPermissions(routerUrl) {
      // 先找到页面权限
      const pagePermission = this.permissions.find(permission =>
        permission.auth_type === 'page' && permission.router_url === routerUrl
      )

      if (!pagePermission) return []

      // 找到该页面下的所有元素权限
      return this.permissions.filter(permission =>
        permission.auth_type === 'element' && permission.pid === pagePermission.auth_id
      )
    },

    resetPermission() {
      this.$reset()
    },
  },
})
