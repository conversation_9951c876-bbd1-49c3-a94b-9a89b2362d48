<!--------------------------------
 - @Author: <PERSON>
 - @LastEditor: <PERSON>
 - @LastEditTime: 2023/12/13 20:54:55
 - @Email: <EMAIL>
 - Copyright © 2023 <PERSON>(大脸怪) | https://isme.top
 --------------------------------->

<template>
  <div class="h-screen w-screen flex overflow-hidden bg-white dark:bg-black">
    <aside
      class="flex-col flex-shrink-0 transition-width-300"
      :style="{ width: appStore.collapsed ? '64px' : '200px' }"
      border-r="1px solid light_border dark:dark_border"
    >
      <SideBar />
    </aside>

    <article class="flex flex-col flex-1 h-full">
      <AppHeader class="h-60 flex-shrink-0" />
      <main class="flex-1 overflow-auto">
        <router-view />
      </main>
    </article>
  </div>
</template>

<script setup>
import { useAppStore } from '@/store'
import AppHeader from './header/index.vue'
import SideBar from './sidebar/index.vue'

const appStore = useAppStore()
</script>

<style>
/* Fix collapsed width for proper sidebar rendering */
.collapsed {
  width: 64px !important;
}

.transition-width-300 {
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 小屏幕优化 - 减少头部高度 */
@media (max-width: 1440px) and (max-height: 900px) {
  .h-60 {
    height: 48px !important; /* 从60px减少到48px */
  }
}

/* 移动端进一步优化 */
@media (max-width: 768px) {
  .h-60 {
    height: 44px !important; /* 移动端进一步减少到44px */
  }
}
</style>
