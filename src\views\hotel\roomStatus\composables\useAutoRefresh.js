import { ref, computed, watch, onMounted, onUnmounted } from 'vue'

/**
 * 自动刷新功能组合函数
 * 负责自动刷新逻辑、倒计时管理和刷新控制
 */
export function useAutoRefresh(refreshCallback) {
  // 响应式状态
  const autoRefresh = ref(true) // 默认启用自动刷新
  const refreshInterval = ref(30) // 默认30秒
  const countdown = ref(0)
  const isRefreshing = ref(false)
  const lastRefreshTime = ref(null)

  // 内部状态
  const countdownTimer = ref(null)
  const refreshTimer = ref(null)
  const isPageVisible = ref(true)
  const isOnline = ref(navigator.onLine)

  // 计算属性
  const countdownPercentage = computed(() => {
    if (countdown.value === 0 || refreshInterval.value === 0) return 0
    return ((refreshInterval.value - countdown.value) / refreshInterval.value) * 100
  })

  const nextRefreshTime = computed(() => {
    if (!autoRefresh.value || countdown.value === 0) return null
    return new Date(Date.now() + countdown.value * 1000)
  })

  const canAutoRefresh = computed(() => {
    return autoRefresh.value &&
           isPageVisible.value &&
           isOnline.value &&
           !isRefreshing.value
  })

  // 开始自动刷新
  const startAutoRefresh = () => {
    if (!refreshCallback || typeof refreshCallback !== 'function') {
      console.warn('自动刷新需要提供有效的刷新回调函数')
      return
    }

    autoRefresh.value = true
    startCountdown()

    console.log(`自动刷新已启动，间隔: ${refreshInterval.value}秒`)
  }

  // 停止自动刷新
  const stopAutoRefresh = () => {
    autoRefresh.value = false
    stopCountdown()

    console.log('自动刷新已停止')
  }

  // 切换自动刷新状态
  const toggleAutoRefresh = () => {
    if (autoRefresh.value) {
      stopAutoRefresh()
    } else {
      startAutoRefresh()
    }
    return autoRefresh.value
  }

  // 设置刷新间隔
  const setRefreshInterval = (interval) => {
    if (typeof interval !== 'number' || interval < 5) {
      console.warn('刷新间隔必须是大于等于5秒的数字')
      return
    }

    refreshInterval.value = interval

    // 如果正在自动刷新，重启倒计时
    if (autoRefresh.value) {
      restartCountdown()
    }

    console.log(`刷新间隔已设置为: ${interval}秒`)
  }

  // 手动刷新
  const manualRefresh = async () => {
    if (isRefreshing.value) {
      console.log('正在刷新中，跳过手动刷新请求')
      return
    }

    try {
      isRefreshing.value = true
      await refreshCallback()
      lastRefreshTime.value = new Date()

      // 如果正在自动刷新，重置倒计时
      if (autoRefresh.value) {
        restartCountdown()
      }

      console.log('手动刷新完成')
    } catch (error) {
      console.error('手动刷新失败:', error)
      throw error
    } finally {
      isRefreshing.value = false
    }
  }

  // 开始倒计时
  const startCountdown = () => {
    if (countdownTimer.value) {
      clearInterval(countdownTimer.value)
    }

    countdown.value = refreshInterval.value

    countdownTimer.value = setInterval(() => {
      if (!canAutoRefresh.value) {
        // 如果不能自动刷新，暂停倒计时但不清零
        return
      }

      countdown.value--

      if (countdown.value <= 0) {
        executeAutoRefresh()
      }
    }, 1000)
  }

  // 停止倒计时
  const stopCountdown = () => {
    if (countdownTimer.value) {
      clearInterval(countdownTimer.value)
      countdownTimer.value = null
    }
    countdown.value = 0
  }

  // 重启倒计时
  const restartCountdown = () => {
    stopCountdown()
    if (autoRefresh.value) {
      startCountdown()
    }
  }

  // 执行自动刷新
  const executeAutoRefresh = async () => {
    if (!canAutoRefresh.value) {
      console.log('当前状态不允许自动刷新')
      return
    }

    try {
      isRefreshing.value = true
      await refreshCallback()
      lastRefreshTime.value = new Date()

      console.log('自动刷新完成')
    } catch (error) {
      console.error('自动刷新失败:', error)

      // 刷新失败时，可以选择停止自动刷新或继续尝试
      // 这里选择继续尝试，但可以根据错误类型做不同处理
      if (error.name === 'NetworkError' || error.code === 'NETWORK_ERROR') {
        console.log('网络错误，继续自动刷新')
      }
    } finally {
      isRefreshing.value = false

      // 重置倒计时
      if (autoRefresh.value) {
        countdown.value = refreshInterval.value
      }
    }
  }

  // 页面可见性变化处理
  const handleVisibilityChange = () => {
    isPageVisible.value = !document.hidden

    if (isPageVisible.value && autoRefresh.value) {
      // 页面重新可见时，检查是否需要立即刷新
      const timeSinceLastRefresh = lastRefreshTime.value
        ? Date.now() - lastRefreshTime.value.getTime()
        : Infinity

      // 如果距离上次刷新超过了刷新间隔，立即刷新
      if (timeSinceLastRefresh > refreshInterval.value * 1000) {
        console.log('页面重新可见，立即刷新数据')
        executeAutoRefresh()
      } else {
        // 否则重启倒计时
        restartCountdown()
      }
    }
  }

  // 网络状态变化处理
  const handleOnlineStatusChange = () => {
    isOnline.value = navigator.onLine

    if (isOnline.value && autoRefresh.value) {
      console.log('网络重新连接，重启自动刷新')
      restartCountdown()
    } else if (!isOnline.value) {
      console.log('网络断开，暂停自动刷新')
    }
  }

  // 监听自动刷新状态变化
  watch(autoRefresh, (newValue) => {
    if (newValue) {
      startCountdown()
    } else {
      stopCountdown()
    }
  })

  // 监听刷新间隔变化
  watch(refreshInterval, () => {
    if (autoRefresh.value) {
      restartCountdown()
    }
  })

  // 生命周期管理
  onMounted(() => {
    // 监听页面可见性变化
    document.addEventListener('visibilitychange', handleVisibilityChange)

    // 监听网络状态变化
    window.addEventListener('online', handleOnlineStatusChange)
    window.addEventListener('offline', handleOnlineStatusChange)

    // 监听页面焦点变化（作为可见性的补充）
    window.addEventListener('focus', handleVisibilityChange)
    window.addEventListener('blur', handleVisibilityChange)

    // 默认启动自动刷新
    if (autoRefresh.value && refreshCallback) {
      startAutoRefresh()
    }
  })

  onUnmounted(() => {
    // 清理定时器
    stopCountdown()

    // 移除事件监听器
    document.removeEventListener('visibilitychange', handleVisibilityChange)
    window.removeEventListener('online', handleOnlineStatusChange)
    window.removeEventListener('offline', handleOnlineStatusChange)
    window.removeEventListener('focus', handleVisibilityChange)
    window.removeEventListener('blur', handleVisibilityChange)
  })

  // 格式化剩余时间
  const formatCountdown = (seconds) => {
    if (seconds <= 0) return '0s'

    if (seconds < 60) {
      return `${seconds}s`
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = seconds % 60
      return remainingSeconds > 0 ? `${minutes}m${remainingSeconds}s` : `${minutes}m`
    } else {
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      return minutes > 0 ? `${hours}h${minutes}m` : `${hours}h`
    }
  }

  return {
    // 状态
    autoRefresh,
    refreshInterval,
    countdown,
    isRefreshing,
    lastRefreshTime,
    isPageVisible,
    isOnline,

    // 计算属性
    countdownPercentage,
    nextRefreshTime,
    canAutoRefresh,

    // 方法
    startAutoRefresh,
    stopAutoRefresh,
    toggleAutoRefresh,
    setRefreshInterval,
    manualRefresh,
    restartCountdown,
    formatCountdown
  }
}
