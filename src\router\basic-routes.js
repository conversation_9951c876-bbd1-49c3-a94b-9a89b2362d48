export const basicRoutes = [
  {
    name: 'Login',
    path: '/login',
    component: () => import('@/views/login/index.vue'),
    meta: {
      title: '登录页',
      layout: 'empty',
    },
  },

  {
    name: 'Home',
    path: '/',
    redirect: '/dashboard/overview',
    meta: {
      title: '首页',
      closable: false,
    },
  },

  // 1. 运营驾驶舱 - 核心数据总览
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/layouts/index.vue'),
    redirect: '/dashboard/overview',
    meta: {
      title: '运营驾驶舱',
      icon: 'i-fe:monitor',
      order: 1
    },
    children: [
      {
        path: 'overview',
        name: 'DashboardOverview',
        component: () => import('@/views/dashboard/overview.vue'),
        meta: {
          title: '今日概览',
          icon: 'i-fe:eye',
          keepAlive: true,
          order: 1
        }
      },
      {
        path: 'today',
        name: 'DashboardToday',
        component: () => import('@/views/dashboard/today.vue'),
        meta: {
          title: '经营日报',
          icon: 'i-fe:calendar',
          keepAlive: true,
          order: 2
        }
      },
      {
        path: 'alerts',
        name: 'DashboardAlerts',
        component: () => import('@/views/dashboard/alerts.vue'),
        meta: {
          title: '预警中心',
          icon: 'i-fe:alert-circle',
          keepAlive: true,
          order: 3
        }
      }
    ]
  },

  // 2. 前台业务 - 核心接待流程
  {
    path: '/front-desk',
    name: 'FrontDesk',
    component: () => import('@/layouts/index.vue'),
    redirect: '/front-desk/room-status',
    meta: {
      title: '前台业务',
      icon: 'i-fe:clipboard',
      order: 2
    },
    children: [
      {
        path: 'room-status',
        name: 'RoomStatus',
        component: () => import('@/views/hotel/roomStatus/index.vue'),
        meta: {
          title: '房态总览',
          icon: 'i-fe:layout',
          keepAlive: true,
          order: 1
        }
      },

      {
        path: 'future-room-status',
        name: 'FutureRoomStatus',
        component: () => import('@/views/hotel/futureRoomStatus/index.vue'),
        meta: {
          title: '远期房态',
          icon: 'i-material-symbols:calendar-view-month',
          keepAlive: true,
          order: 2
        }
      },
      {
        path: 'checkin',
        name: 'CheckIn',
        component: () => import('@/views/front-desk/checkin/index.vue'),
        meta: {
          title: '入住登记',
          icon: 'i-fe:log-in',
          keepAlive: true,
          order: 3
        }
      },
      {
        path: 'checkout',
        name: 'CheckOut',
        component: () => import('@/views/front-desk/checkout/index.vue'),
        meta: {
          title: '退房结账',
          icon: 'i-fe:log-out',
          keepAlive: true,
          order: 4
        }
      },
      {
        path: 'reservation',
        name: 'Reservation',
        component: () => import('@/views/front-desk/reservation/index.vue'),
        meta: {
          title: '预订中心',
          icon: 'i-fe:bookmark',
          keepAlive: true,
          order: 5
        }
      }
    ]
  },

  // 3. 客户关系 - CRM核心功能
  {
    path: '/crm',
    name: 'CRM',
    component: () => import('@/layouts/index.vue'),
    redirect: '/crm/guests',
    meta: {
      title: '客户关系',
      icon: 'i-fe:users',
      order: 3
    },
    children: [
      {
        path: 'guests',
        name: 'Guests',
        component: () => import('@/views/crm/guests/index.vue'),
        meta: {
          title: '客户档案',
          icon: 'i-fe:user-check',
          keepAlive: true,
          order: 1
        }
      },
      {
        path: 'members',
        name: 'Members',
        component: () => import('@/views/hotel/member/index.vue'),
        meta: {
          title: '会员管理',
          icon: 'i-fe:award',
          keepAlive: true,
          order: 2
        }
      },
      {
        path: 'member/:id',
        name: 'MemberDetail',
        component: () => import('@/views/hotel/member/detail.vue'),
        meta: {
          title: '会员详情',
          icon: 'i-fe:user',
          hidden: true,
          keepAlive: false
        }
      },
      {
        path: 'loyalty',
        name: 'Loyalty',
        component: () => import('@/views/crm/loyalty/index.vue'),
        meta: {
          title: '积分体系',
          icon: 'i-fe:gift',
          keepAlive: true,
          order: 3
        }
      }
    ]
  },

  // 4. 房务管理 - 客房运营
  {
    path: '/housekeeping',
    name: 'Housekeeping',
    component: () => import('@/layouts/index.vue'),
    redirect: '/housekeeping/rooms',
    meta: {
      title: '房务管理',
      icon: 'i-fe:home',
      order: 4
    },
    children: [
      {
        path: 'rooms',
        name: 'HousekeepingRooms',
        component: () => import('@/views/common/placeholder.vue'),
        meta: {
          title: '房间状态',
          icon: 'i-fe:square',
          keepAlive: true,
          order: 1
        }
      },
      {
        path: 'cleaning',
        name: 'HousekeepingCleaning',
        component: () => import('@/views/common/placeholder.vue'),
        meta: {
          title: '清洁任务',
          icon: 'i-fe:refresh-cw',
          keepAlive: true,
          order: 2
        }
      },
      {
        path: 'maintenance',
        name: 'HousekeepingMaintenance',
        component: () => import('@/views/common/placeholder.vue'),
        meta: {
          title: '维修工单',
          icon: 'i-fe:wrench',
          keepAlive: true,
          order: 3
        }
      }
    ]
  },

  // 5. 财务管理 - 账务核心
  {
    path: '/finance',
    name: 'Finance',
    component: () => import('@/layouts/index.vue'),
    redirect: '/finance/billing',
    meta: {
      title: '财务管理',
      icon: 'i-fe:dollar-sign',
      order: 5
    },
    children: [
      {
        path: 'billing',
        name: 'FinanceBilling',
        component: () => import('@/views/common/placeholder.vue'),
        meta: {
          title: '账单管理',
          icon: 'i-fe:file-text',
          keepAlive: true,
          order: 1
        }
      },
      {
        path: 'payments',
        name: 'FinancePayments',
        component: () => import('@/views/common/placeholder.vue'),
        meta: {
          title: '收款管理',
          icon: 'i-fe:credit-card',
          keepAlive: true,
          order: 2
        }
      },
      {
        path: 'reconciliation',
        name: 'FinanceReconciliation',
        component: () => import('@/views/common/placeholder.vue'),
        meta: {
          title: '对账管理',
          icon: 'i-fe:check-circle',
          keepAlive: true,
          order: 3
        }
      }
    ]
  },

  // 6. 数据分析 - 商业智能
  {
    path: '/analytics',
    name: 'Analytics',
    component: () => import('@/layouts/index.vue'),
    redirect: '/analytics/revenue',
    meta: {
      title: '数据分析',
      icon: 'i-fe:trending-up',
      order: 6
    },
    children: [
      {
        path: 'revenue',
        name: 'AnalyticsRevenue',
        component: () => import('@/views/common/placeholder.vue'),
        meta: {
          title: '收益分析',
          icon: 'i-fe:bar-chart-2',
          keepAlive: true,
          order: 1
        }
      },
      {
        path: 'occupancy',
        name: 'AnalyticsOccupancy',
        component: () => import('@/views/common/placeholder.vue'),
        meta: {
          title: '入住分析',
          icon: 'i-fe:pie-chart',
          keepAlive: true,
          order: 2
        }
      },
      {
        path: 'customer',
        name: 'AnalyticsCustomer',
        component: () => import('@/views/common/placeholder.vue'),
        meta: {
          title: '客户分析',
          icon: 'i-fe:users',
          keepAlive: true,
          order: 3
        }
      }
    ]
  },

  // 7. 营销中心 - 数字化营销
  {
    path: '/marketing',
    name: 'Marketing',
    component: () => import('@/layouts/index.vue'),
    redirect: '/marketing/campaigns',
    meta: {
      title: '营销中心',
      icon: 'i-fe:megaphone',
      order: 7
    },
    children: [
      {
        path: 'campaigns',
        name: 'MarketingCampaigns',
        component: () => import('@/views/common/placeholder.vue'),
        meta: {
          title: '营销活动',
          icon: 'i-fe:zap',
          keepAlive: true,
          order: 1
        }
      },
      {
        path: 'wechat',
        name: 'MarketingWechat',
        component: () => import('@/views/wx/manager/index.vue'),
        meta: {
          title: '微信营销',
          icon: 'i-fe:message-circle',
          keepAlive: true,
          order: 2
        }
      },
      {
        path: 'tools',
        name: 'MarketingTools',
        component: () => import('@/views/wx/codeModel/index.vue'),
        meta: {
          title: '营销工具',
          icon: 'i-fe:package',
          keepAlive: true,
          order: 3
        }
      }
    ]
  },

  // 8. 系统管理 - 基础配置
  {
    path: '/system',
    name: 'System',
    component: () => import('@/layouts/index.vue'),
    redirect: '/system/users',
    meta: {
      title: '系统管理',
      icon: 'i-fe:settings',
      order: 8
    },
    children: [
      {
        path: 'users',
        name: 'SystemUsers',
        component: () => import('@/views/pms/user/index.vue'),
        meta: {
          title: '用户管理',
          icon: 'i-fe:user',
          keepAlive: true,
          order: 1
        }
      },
      {
        path: 'roles',
        name: 'SystemRoles',
        component: () => import('@/views/pms/role/index.vue'),
        meta: {
          title: '角色权限',
          icon: 'i-fe:shield',
          keepAlive: true,
          order: 2
        }
      },
      {
        path: 'config',
        name: 'SystemConfig',
        component: () => import('@/views/common/placeholder.vue'),
        meta: {
          title: '系统配置',
          icon: 'i-fe:sliders',
          keepAlive: true,
          order: 3
        }
      },
      {
        path: 'logs',
        name: 'SystemLogs',
        component: () => import('@/views/common/placeholder.vue'),
        meta: {
          title: '操作日志',
          icon: 'i-fe:list',
          keepAlive: true,
          order: 4
        }
      },
      {
        path: 'status',
        name: 'SystemStatus',
        component: () => import('@/views/system/status.vue'),
        meta: {
          title: '系统监控',
          icon: 'i-fe:server',
          keepAlive: true,
          order: 5
        }
      }
    ]
  },

  // 兼容旧路由 - 微信管理重定向到营销中心
  {
    path: '/wx',
    name: 'WxRedirect',
    redirect: '/marketing'
  },
  {
    path: '/wx/manager',
    name: 'WxManagerRedirect',
    redirect: '/marketing/wechat'
  },
  {
    path: '/wx/codemodel',
    name: 'WxCodeModelRedirect',
    redirect: '/marketing/tools'
  },
  {
    path: '/wx/codecgx',
    name: 'WxCodeCgxRedirect',
    redirect: '/marketing/tools'
  },

  // 兼容旧路由 - 设置重定向到系统管理
  {
    path: '/settings',
    name: 'SettingsRedirect',
    redirect: '/system'
  },
  {
    path: '/settings/users',
    name: 'SettingsUsersRedirect',
    redirect: '/system/users'
  },
  {
    path: '/settings/roles',
    name: 'SettingsRolesRedirect',
    redirect: '/system/roles'
  },
  {
    path: '/settings/status',
    name: 'SettingsStatusRedirect',
    redirect: '/system/status'
  },

  // 错误页面
  {
    name: '404',
    path: '/404',
    component: () => import('@/views/error-page/404.vue'),
    meta: {
      title: '页面飞走了',
      layout: 'empty',
    },
  },

  {
    name: '403',
    path: '/403',
    component: () => import('@/views/error-page/403.vue'),
    meta: {
      title: '没有权限',
      layout: 'empty',
    },
  },

  // 回调页面
  {
    path: '/callback',
    name: 'WxCallback',
    component: () => import('@/views/wx/callback/index.vue'),
    meta: { title: '授权详情' }
  },

  // 个人中心
  {
    name: 'Profile',
    path: '/profile',
    component: () => import('@/views/profile/index.vue'),
    meta: {
      title: '个人中心',
    },
  },

  // 兼容旧路由 - 重定向
  {
    path: '/hotel/roomstatus',
    redirect: '/front-desk/room-status'
  },
  {
    path: '/hotel/member',
    redirect: '/crm/members'
  },
  {
    path: '/hotel/card',
    redirect: '/crm/loyalty'
  },
  {
    path: '/pms',
    redirect: '/system'
  },
  // 兼容旧的房态页面路由
  {
    path: '/receptionist/RoomStatusNow',
    redirect: '/front-desk/room-status'
  },

  // 通用404路由（必须放在最后）
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    redirect: '/404',
  }
]
