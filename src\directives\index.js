
import { withDirectives } from 'vue'
import permissionDirective from './permission'

export function setupDirectives(app) {
  app.directive('permission', permissionDirective)
}

/**
 * 用于h函数使用自定义权限指令
 *
 * @param {*} vnode 虚拟节点
 * @param {*} authMark 权限标识
 * @returns 返回一个包含权限指令的vnode
 *
 * 使用示例：withPermission(h('button', {class: 'text-red-500'}, '删除'), 'roomBill_download')
 *
 */
export function withPermission(vnode, authMark) {
  return withDirectives(vnode, [[permissionDirective, authMark]])
}
