/**
 * 最近访问记录管理工具
 * 提供跨页面的最近访问功能记录和管理
 */

import { ref, computed } from 'vue'

// 最近访问记录的响应式数据
const recentAccess = ref([])

// 存储键名
const STORAGE_KEY = 'hotel_recent_access'

// 最大记录数量
const MAX_RECORDS = 10

// 最大显示数量
const MAX_DISPLAY = 5

/**
 * 初始化最近访问记录
 * 从本地存储加载数据
 */
export function initRecentAccess() {
  try {
    const saved = localStorage.getItem(STORAGE_KEY)
    if (saved) {
      const data = JSON.parse(saved)
      // 验证数据格式
      if (Array.isArray(data)) {
        recentAccess.value = data.filter(item =>
          item &&
          typeof item.label === 'string' &&
          typeof item.path === 'string' &&
          typeof item.timestamp === 'number'
        )
      }
    }
  } catch (error) {

    recentAccess.value = []
  }
}

/**
 * 保存最近访问记录到本地存储
 */
function saveToStorage() {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(recentAccess.value))
  } catch (error) {

  }
}

/**
 * 添加到最近访问记录
 * @param {string} label - 功能名称
 * @param {string} path - 路径
 * @param {string} iconClass - 图标类名
 * @param {Object} options - 额外选项
 */
export function addToRecentAccess(label, path, iconClass, options = {}) {
  if (!label || !path) {

    return
  }

  const key = path.replace(/\//g, '-').replace(/^-/, '')

  // 移除已存在的相同项
  recentAccess.value = recentAccess.value.filter(item => item.key !== key)

  // 创建新记录
  const newRecord = {
    label,
    path,
    iconClass: iconClass || 'i-mdi:application',
    key,
    timestamp: Date.now(),
    ...options
  }

  // 添加到开头
  recentAccess.value.unshift(newRecord)

  // 限制记录数量
  if (recentAccess.value.length > MAX_RECORDS) {
    recentAccess.value = recentAccess.value.slice(0, MAX_RECORDS)
  }

  // 保存到本地存储
  saveToStorage()

}

/**
 * 获取最近访问记录
 * @param {number} limit - 限制数量，默认为最大显示数量
 * @returns {Array} 最近访问记录数组
 */
export function getRecentAccess(limit = MAX_DISPLAY) {
  return recentAccess.value.slice(0, limit)
}

/**
 * 获取最近访问记录的响应式引用
 * @returns {Ref} 响应式的最近访问记录
 */
export function useRecentAccess() {
  return recentAccess
}

/**
 * 清除所有最近访问记录
 */
export function clearRecentAccess() {
  recentAccess.value = []
  saveToStorage()

}

/**
 * 移除指定的最近访问记录
 * @param {string} key - 记录的key
 */
export function removeRecentAccess(key) {
  const index = recentAccess.value.findIndex(item => item.key === key)
  if (index > -1) {
    const removed = recentAccess.value.splice(index, 1)[0]
    saveToStorage()

    return removed
  }
  return null
}

/**
 * 获取最近访问统计信息
 * @returns {Object} 统计信息
 */
export function getRecentAccessStats() {
  const total = recentAccess.value.length
  const today = new Date().toDateString()
  const todayCount = recentAccess.value.filter(item =>
    new Date(item.timestamp).toDateString() === today
  ).length

  // 统计最常访问的功能
  const usage = {}
  recentAccess.value.forEach(item => {
    usage[item.label] = (usage[item.label] || 0) + 1
  })

  const mostUsed = Object.entries(usage)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 3)
    .map(([label, count]) => ({ label, count }))

  return {
    total,
    todayCount,
    mostUsed,
    hasRecords: total > 0
  }
}

/**
 * 格式化时间显示
 * @param {number} timestamp - 时间戳
 * @returns {string} 格式化的时间字符串
 */
export function formatAccessTime(timestamp) {
  const now = Date.now()
  const diff = now - timestamp

  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < ********) return `${Math.floor(diff / 3600000)}小时前`
  if (diff < *********) return `${Math.floor(diff / ********)}天前`

  return new Date(timestamp).toLocaleDateString()
}

/**
 * 获取用户角色相关的快捷功能选项
 * @param {string} role - 用户角色
 * @returns {Array} 角色相关的功能选项
 */
export function getRoleBasedOptions(role) {
  const roleOptions = {
    admin: [
      { label: '系统设置', key: 'system', path: '/system', iconClass: 'i-mdi:cog' },
      { label: '用户管理', key: 'users', path: '/system/users', iconClass: 'i-mdi:account-group' },
      { label: '权限管理', key: 'permissions', path: '/system/permissions', iconClass: 'i-mdi:shield-account' },
      { label: '数据报表', key: 'reports', path: '/reports', iconClass: 'i-mdi:chart-line' },
      { label: '系统监控', key: 'monitor', path: '/system/monitor', iconClass: 'i-mdi:monitor-dashboard' }
    ],
    manager: [
      { label: '预订管理', key: 'reservation', path: '/reservation', iconClass: 'i-mdi:calendar-check' },
      { label: '客房管理', key: 'rooms', path: '/rooms', iconClass: 'i-mdi:bed' },
      { label: '财务管理', key: 'finance', path: '/finance', iconClass: 'i-mdi:currency-usd' },
      { label: '数据报表', key: 'reports', path: '/reports', iconClass: 'i-mdi:chart-line' },
      { label: '员工管理', key: 'staff', path: '/staff', iconClass: 'i-mdi:account-tie' }
    ],
    staff: [
      { label: '预订管理', key: 'reservation', path: '/reservation', iconClass: 'i-mdi:calendar-check' },
      { label: '入住管理', key: 'checkin', path: '/checkin', iconClass: 'i-mdi:account-plus' },
      { label: '会员管理', key: 'members', path: '/members', iconClass: 'i-mdi:account-star' },
      { label: '房态查看', key: 'roomstatus', path: '/hotel/roomstatus', iconClass: 'i-mdi:bed' }
    ],
    reception: [
      { label: '前台接待', key: 'reception', path: '/reception', iconClass: 'i-mdi:desk' },
      { label: '入住登记', key: 'checkin', path: '/checkin', iconClass: 'i-mdi:account-plus' },
      { label: '退房结算', key: 'checkout', path: '/checkout', iconClass: 'i-mdi:account-minus' },
      { label: '房态管理', key: 'roomstatus', path: '/hotel/roomstatus', iconClass: 'i-mdi:bed' },
      { label: '会员查询', key: 'members', path: '/members', iconClass: 'i-mdi:account-search' }
    ]
  }

  return roleOptions[role] || roleOptions.staff
}

/**
 * 获取功能中心下拉菜单选项
 * @param {string} userRole - 用户角色
 * @param {Function} h - Vue的h函数
 * @returns {Array} 下拉菜单选项
 */
export function getFunctionCenterOptions(userRole, h) {
  const baseOptions = [
    {
      label: '功能中心',
      key: 'overview',
      icon: () => h('i', { class: 'i-mdi:view-dashboard' })
    },
    {
      type: 'divider'
    }
  ]

  // 角色相关选项
  const roleOptions = getRoleBasedOptions(userRole).map(option => ({
    label: option.label,
    key: option.key,
    icon: () => h('i', { class: option.iconClass }),
    path: option.path,
    iconClass: option.iconClass
  }))

  // 最近访问选项
  const recentOptions = getRecentAccess().map(item => ({
    label: item.label,
    key: `recent-${item.key}`,
    icon: () => h('i', { class: item.iconClass }),
    path: item.path,
    iconClass: item.iconClass
  }))

  return [
    ...baseOptions,
    ...roleOptions,
    ...(recentOptions.length > 0 ? [
      { type: 'divider' },
      {
        label: '最近访问',
        key: 'recent-header',
        disabled: true,
        icon: () => h('i', { class: 'i-mdi:history' })
      },
      ...recentOptions
    ] : [])
  ]
}

/**
 * 根据key查找功能选项
 * @param {string} key - 选项key
 * @param {string} userRole - 用户角色
 * @returns {Object|null} 找到的选项或null
 */
export function findOptionByKey(key, userRole) {
  const allOptions = [
    { label: '功能中心', key: 'overview', path: '/dashboard/overview', iconClass: 'i-mdi:view-dashboard' },
    ...getRoleBasedOptions(userRole),
    ...getRecentAccess()
  ]

  // 处理最近访问的key
  const searchKey = key.startsWith('recent-') ? key.replace('recent-', '') : key

  return allOptions.find(option => option.key === searchKey)
}

// 自动初始化
if (typeof window !== 'undefined') {
  initRecentAccess()
}

export default {
  initRecentAccess,
  addToRecentAccess,
  getRecentAccess,
  useRecentAccess,
  clearRecentAccess,
  removeRecentAccess,
  getRecentAccessStats,
  formatAccessTime,
  getRoleBasedOptions,
  getFunctionCenterOptions,
  findOptionByKey
}
