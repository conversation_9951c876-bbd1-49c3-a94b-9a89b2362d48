<template>
  <div class="loyalty-page">
    <n-card title="会员忠诚度管理" size="small">
      <template #header-extra>
        <n-space>
          <n-button type="primary" @click="handleAddGrade">
            <template #icon>
              <n-icon><i-material-symbols:add /></n-icon>
            </template>
            新增等级
          </n-button>
          <n-button @click="handleRefresh">
            <template #icon>
              <n-icon><i-material-symbols:refresh /></n-icon>
            </template>
            刷新
          </n-button>
        </n-space>
      </template>

      <!-- 统计卡片 -->
      <n-grid :cols="4" :x-gap="16" class="mb-4">
        <n-gi>
          <n-statistic label="总会员数" :value="statistics.totalMembers">
            <template #prefix>
              <n-icon color="#18a058"><i-material-symbols:person /></n-icon>
            </template>
          </n-statistic>
        </n-gi>
        <n-gi>
          <n-statistic label="活跃会员" :value="statistics.activeMembers">
            <template #prefix>
              <n-icon color="#2080f0"><i-material-symbols:trending-up /></n-icon>
            </template>
          </n-statistic>
        </n-gi>
        <n-gi>
          <n-statistic label="本月新增" :value="statistics.newMembers">
            <template #prefix>
              <n-icon color="#f0a020"><i-material-symbols:person-add /></n-icon>
            </template>
          </n-statistic>
        </n-gi>
        <n-gi>
          <n-statistic label="会员等级数" :value="statistics.gradeCount">
            <template #prefix>
              <n-icon color="#d03050"><i-material-symbols:star /></n-icon>
            </template>
          </n-statistic>
        </n-gi>
      </n-grid>

      <!-- 会员等级管理 -->
      <n-tabs default-value="grades" type="line">
        <n-tab-pane name="grades" tab="会员等级">
          <n-data-table :columns="gradeColumns" :data="gradeData" :loading="gradeLoading" :row-key="row => row.id" />
        </n-tab-pane>

        <n-tab-pane name="points" tab="积分规则">
          <n-space vertical>
            <n-alert type="info" title="积分规则说明">
              会员通过消费、签到、推荐等方式获得积分，积分可用于兑换优惠券或升级会员等级。
            </n-alert>

            <n-data-table :columns="pointRuleColumns" :data="pointRuleData" :loading="pointRuleLoading"
              :row-key="row => row.id" />
          </n-space>
        </n-tab-pane>

        <n-tab-pane name="benefits" tab="会员权益">
          <n-data-table :columns="benefitColumns" :data="benefitData" :loading="benefitLoading"
            :row-key="row => row.id" />
        </n-tab-pane>
      </n-tabs>
    </n-card>

    <!-- 新增/编辑会员等级弹窗 -->
    <n-modal v-model:show="showGradeModal" preset="dialog" title="会员等级设置">
      <n-form ref="gradeFormRef" :model="gradeFormData" :rules="gradeFormRules" label-placement="left"
        label-width="120px">
        <n-form-item label="等级名称" path="name">
          <n-input v-model:value="gradeFormData.name" placeholder="请输入等级名称" />
        </n-form-item>
        <n-form-item label="等级图标" path="icon">
          <n-input v-model:value="gradeFormData.icon" placeholder="请输入图标类名" />
        </n-form-item>
        <n-form-item label="等级颜色" path="color">
          <n-color-picker v-model:value="gradeFormData.color" />
        </n-form-item>
        <n-form-item label="升级所需积分" path="requiredPoints">
          <n-input-number v-model:value="gradeFormData.requiredPoints" :min="0" placeholder="请输入所需积分"
            style="width: 100%;" />
        </n-form-item>
        <n-form-item label="折扣比例" path="discountRate">
          <n-input-number v-model:value="gradeFormData.discountRate" :min="0" :max="100" :step="0.1"
            placeholder="请输入折扣比例" style="width: 100%;">
            <template #suffix>%</template>
          </n-input-number>
        </n-form-item>
        <n-form-item label="等级描述" path="description">
          <n-input v-model:value="gradeFormData.description" type="textarea" placeholder="请输入等级描述" :rows="3" />
        </n-form-item>
      </n-form>
      <template #action>
        <n-space>
          <n-button @click="showGradeModal = false">取消</n-button>
          <n-button type="primary" @click="handleSubmitGrade">确定</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, h } from 'vue'
import { useMessage } from 'naive-ui'

const message = useMessage()

// 统计数据
const statistics = reactive({
  totalMembers: 1256,
  activeMembers: 892,
  newMembers: 45,
  gradeCount: 5
})

// 会员等级数据
const gradeData = ref([])
const gradeLoading = ref(false)

// 积分规则数据
const pointRuleData = ref([])
const pointRuleLoading = ref(false)

// 会员权益数据
const benefitData = ref([])
const benefitLoading = ref(false)

// 会员等级表格列
const gradeColumns = [
  {
    title: '等级名称',
    key: 'name',
    width: 120,
    render: (row) => {
      return h('div', {
        style: `color: ${row.color}; font-weight: bold;`
      }, row.name)
    }
  },
  {
    title: '等级图标',
    key: 'icon',
    width: 100,
    render: (row) => {
      return h('n-icon', {
        size: 20,
        color: row.color
      }, h('i', { class: row.icon }))
    }
  },
  {
    title: '所需积分',
    key: 'requiredPoints',
    width: 120
  },
  {
    title: '折扣比例',
    key: 'discountRate',
    width: 120,
    render: (row) => `${row.discountRate}%`
  },
  {
    title: '会员数量',
    key: 'memberCount',
    width: 100
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row) => {
      return h('n-tag', {
        type: row.status === 'active' ? 'success' : 'default'
      }, row.status === 'active' ? '启用' : '禁用')
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    render: (row) => {
      return [
        h('n-button', {
          size: 'small',
          type: 'primary',
          style: 'margin-right: 8px;',
          onClick: () => handleEditGrade(row)
        }, '编辑'),
        h('n-button', {
          size: 'small',
          type: 'error',
          onClick: () => handleDeleteGrade(row)
        }, '删除')
      ]
    }
  }
]

// 积分规则表格列
const pointRuleColumns = [
  { title: '规则名称', key: 'name', width: 150 },
  { title: '获得积分', key: 'points', width: 100 },
  { title: '规则描述', key: 'description' },
  { title: '状态', key: 'status', width: 100 }
]

// 会员权益表格列
const benefitColumns = [
  { title: '权益名称', key: 'name', width: 150 },
  { title: '适用等级', key: 'applicableGrades', width: 200 },
  { title: '权益描述', key: 'description' },
  { title: '状态', key: 'status', width: 100 }
]

// 弹窗相关
const showGradeModal = ref(false)
const gradeFormRef = ref(null)
const gradeFormData = reactive({
  id: null,
  name: '',
  icon: '',
  color: '#2080f0',
  requiredPoints: 0,
  discountRate: 0,
  description: ''
})

// 表单验证规则
const gradeFormRules = {
  name: { required: true, message: '请输入等级名称', trigger: 'blur' },
  requiredPoints: { required: true, type: 'number', message: '请输入所需积分', trigger: 'blur' },
  discountRate: { required: true, type: 'number', message: '请输入折扣比例', trigger: 'blur' }
}

// 加载会员等级数据
const loadGradeData = async () => {
  gradeLoading.value = true
  try {
    // 模拟数据
    await new Promise(resolve => setTimeout(resolve, 500))
    gradeData.value = [
      {
        id: 1,
        name: '普通会员',
        icon: 'i-material-symbols:person',
        color: '#909399',
        requiredPoints: 0,
        discountRate: 0,
        memberCount: 856,
        status: 'active'
      },
      {
        id: 2,
        name: '银卡会员',
        icon: 'i-material-symbols:star',
        color: '#C0C0C0',
        requiredPoints: 1000,
        discountRate: 5,
        memberCount: 245,
        status: 'active'
      },
      {
        id: 3,
        name: '金卡会员',
        icon: 'i-material-symbols:star',
        color: '#FFD700',
        requiredPoints: 5000,
        discountRate: 10,
        memberCount: 125,
        status: 'active'
      },
      {
        id: 4,
        name: '钻石会员',
        icon: 'i-material-symbols:diamond',
        color: '#E6E6FA',
        requiredPoints: 20000,
        discountRate: 20,
        memberCount: 30,
        status: 'active'
      }
    ]
  } catch (error) {

    message.error('加载数据失败')
  } finally {
    gradeLoading.value = false
  }
}

// 加载积分规则数据
const loadPointRuleData = async () => {
  pointRuleLoading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 300))
    pointRuleData.value = [
      { id: 1, name: '消费积分', points: 1, description: '每消费1元获得1积分', status: '启用' },
      { id: 2, name: '签到积分', points: 10, description: '每日签到获得10积分', status: '启用' },
      { id: 3, name: '推荐积分', points: 100, description: '成功推荐新会员获得100积分', status: '启用' }
    ]
  } finally {
    pointRuleLoading.value = false
  }
}

// 加载会员权益数据
const loadBenefitData = async () => {
  benefitLoading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 300))
    benefitData.value = [
      { id: 1, name: '生日优惠', applicableGrades: '所有等级', description: '生日当月享受特别优惠', status: '启用' },
      { id: 2, name: '免费升房', applicableGrades: '金卡、钻石', description: '入住时免费升级房型', status: '启用' },
      { id: 3, name: '延迟退房', applicableGrades: '钻石', description: '可延迟至下午2点退房', status: '启用' }
    ]
  } finally {
    benefitLoading.value = false
  }
}

// 新增等级
const handleAddGrade = () => {
  Object.assign(gradeFormData, {
    id: null,
    name: '',
    icon: 'i-material-symbols:star',
    color: '#2080f0',
    requiredPoints: 0,
    discountRate: 0,
    description: ''
  })
  showGradeModal.value = true
}

// 编辑等级
const handleEditGrade = (row) => {
  Object.assign(gradeFormData, { ...row })
  showGradeModal.value = true
}

// 删除等级
const handleDeleteGrade = (row) => {
  message.success(`删除等级 ${row.name} 成功`)
  loadGradeData()
}

// 提交等级表单
const handleSubmitGrade = async () => {
  try {
    await gradeFormRef.value?.validate()
    message.success(gradeFormData.id ? '更新成功' : '新增成功')
    showGradeModal.value = false
    loadGradeData()
  } catch (error) {

  }
}

// 刷新数据
const handleRefresh = () => {
  loadGradeData()
  loadPointRuleData()
  loadBenefitData()
}

// 组件挂载时加载数据
onMounted(() => {
  loadGradeData()
  loadPointRuleData()
  loadBenefitData()
})
</script>

<style scoped>
.loyalty-page {
  padding: 16px;
}
</style>
