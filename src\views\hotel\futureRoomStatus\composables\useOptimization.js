import { ref, computed, shallowRef, watch, nextTick } from 'vue'
import { debounce, throttle } from 'lodash-es'

/**
 * 远期房态页面性能优化 Composable
 */
export function useFutureRoomStatusOptimization() {
  // 缓存管理
  const cache = new Map()
  const cacheExpiry = new Map()
  const CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

  /**
   * 智能缓存机制
   */
  const getCacheKey = (params) => {
    return JSON.stringify(params)
  }

  const setCache = (key, data) => {
    cache.set(key, data)
    cacheExpiry.set(key, Date.now() + CACHE_DURATION)
  }

  const getCache = (key) => {
    const expiry = cacheExpiry.get(key)
    if (expiry && Date.now() < expiry) {
      return cache.get(key)
    }
    // 清理过期缓存
    cache.delete(key)
    cacheExpiry.delete(key)
    return null
  }

  const clearExpiredCache = () => {
    const now = Date.now()
    for (const [key, expiry] of cacheExpiry.entries()) {
      if (now >= expiry) {
        cache.delete(key)
        cacheExpiry.delete(key)
      }
    }
  }

  /**
   * 优化的房型汇总计算
   */
  const createOptimizedRoomTypeSummary = (roomList, dateList, roomStatusData) => {
    const cacheKey = getCacheKey({
      roomCount: roomList.length,
      dateCount: dateList.length,
      dataHash: Object.keys(roomStatusData).length
    })

    const cached = getCache(cacheKey)
    if (cached) {
      return cached
    }

    const summary = {}

    // 使用 Map 提高查找性能
    const roomTypeMap = new Map()

    // 第一遍：分组房间
    roomList.forEach(room => {
      const typeName = room.roomTypeName
      if (!roomTypeMap.has(typeName)) {
        roomTypeMap.set(typeName, {
          id: room.roomTypeId || room.id,
          roomTypeName: typeName,
          roomTypeId: room.roomTypeId,
          totalRooms: 0,
          rooms: [],
          dailyStats: new Map(),
          longTerm: 0,
          allotment: 0,
          maintenance: 0,
          closed: 0,
          occupiedMaintenance: 0,
          occupiedLongStay: 0,
        })

        // 预初始化日期统计
        dateList.forEach(date => {
          roomTypeMap.get(typeName).dailyStats.set(date, {
            reserved: 0,
            occupied: 0,
            maintenance: 0,
            longStay: 0,
            soldOut: false,
            closed: false,
            total: 0,
            sellable: 0,
            available: 0,
            occupancyRate: 0,
            ota: 0,
            direct: 0,
            corporate: 0,
            walkIn: 0,
          })
        })
      }

      const roomType = roomTypeMap.get(typeName)
      roomType.totalRooms++
      roomType.rooms.push(room)
    })

    // 第二遍：计算统计数据
    for (const [typeName, roomType] of roomTypeMap.entries()) {
      // 计算固定占用数量
      roomType.occupiedMaintenance = Math.floor(roomType.totalRooms * 0.1)
      roomType.occupiedLongStay = Math.floor(roomType.totalRooms * 0.05)

      // 批量处理日期统计
      dateList.forEach(date => {
        const dailyStat = roomType.dailyStats.get(date)
        let reservedCount = 0
        let occupiedCount = 0
        let maintenanceCount = 0
        let longStayCount = 0
        let closedCount = 0

        // 使用 for 循环替代 forEach 提高性能
        for (let i = 0; i < roomType.rooms.length; i++) {
          const room = roomType.rooms[i]
          const statusData = roomStatusData[room.id]?.[date]
          const status = statusData?.status || 'available'

          switch (status) {
            case 'reserved':
              reservedCount++
              // 简化渠道分布计算
              const reservedRandom = Math.random()
              if (reservedRandom < 0.4) dailyStat.ota++
              else if (reservedRandom < 0.7) dailyStat.direct++
              else if (reservedRandom < 0.9) dailyStat.corporate++
              else dailyStat.walkIn++
              break
            case 'occupied':
              occupiedCount++
              const occupiedRandom = Math.random()
              if (occupiedRandom < 0.4) dailyStat.ota++
              else if (occupiedRandom < 0.7) dailyStat.direct++
              else if (occupiedRandom < 0.9) dailyStat.corporate++
              else dailyStat.walkIn++
              break
            case 'maintenance':
              maintenanceCount++
              break
            case 'longStay':
              longStayCount++
              break
            case 'closed':
              closedCount++
              break
          }
        }

        // 更新统计数据
        Object.assign(dailyStat, {
          reserved: reservedCount,
          occupied: occupiedCount,
          maintenance: maintenanceCount,
          longStay: longStayCount,
          closed: closedCount,
          total: roomType.totalRooms,
          sellable: roomType.totalRooms - maintenanceCount - closedCount,
          available: roomType.totalRooms - reservedCount - occupiedCount - maintenanceCount - closedCount,
          occupancyRate: roomType.totalRooms > 0 ?
            Math.round(((reservedCount + occupiedCount) / roomType.totalRooms) * 100) : 0
        })

        // 检查是否售完
        dailyStat.soldOut = dailyStat.available <= 0 && !dailyStat.closed
      })

      // 转换 Map 为普通对象以便模板使用
      summary[typeName] = {
        ...roomType,
        dailyStats: Object.fromEntries(roomType.dailyStats)
      }
    }

    const result = Object.values(summary)
    setCache(cacheKey, result)
    return result
  }

  /**
   * 防抖的数据获取函数
   */
  const createDebouncedFetch = (fetchFn, delay = 300) => {
    return debounce(fetchFn, delay)
  }

  /**
   * 节流的滚动处理函数
   */
  const createThrottledScroll = (scrollFn, delay = 16) => {
    return throttle(scrollFn, delay)
  }

  /**
   * 内存优化：清理不需要的数据
   */
  const optimizeMemoryUsage = () => {
    clearExpiredCache()

    // 强制垃圾回收（在开发环境中）
    if (process.env.NODE_ENV === 'development' && window.gc) {
      window.gc()
    }
  }

  /**
   * 批量更新状态以减少重渲染
   */
  const batchUpdate = (updateFn) => {
    return nextTick(() => {
      updateFn()
    })
  }

  return {
    // 缓存相关
    setCache,
    getCache,
    clearExpiredCache,

    // 优化计算
    createOptimizedRoomTypeSummary,

    // 防抖节流
    createDebouncedFetch,
    createThrottledScroll,

    // 内存优化
    optimizeMemoryUsage,

    // 批量更新
    batchUpdate
  }
}

/**
 * 虚拟滚动优化 Composable
 */
export function useVirtualScroll(itemHeight = 50, containerHeight = 600) {
  const scrollTop = ref(0)
  const containerRef = ref(null)

  const visibleRange = computed(() => {
    const start = Math.floor(scrollTop.value / itemHeight)
    const visibleCount = Math.ceil(containerHeight / itemHeight)
    const end = start + visibleCount + 2 // 添加缓冲区

    return { start, end }
  })

  const handleScroll = throttle((event) => {
    scrollTop.value = event.target.scrollTop
  }, 16)

  return {
    scrollTop,
    containerRef,
    visibleRange,
    handleScroll
  }
}

/**
 * 分页加载数据管理 Composable
 */
export function usePaginatedData(fetchFunction, pageSize = 50) {
  const data = shallowRef([])
  const loading = ref(false)
  const hasMore = ref(true)
  const currentPage = ref(1)
  const error = ref(null)

  const loadData = async (reset = false) => {
    if (loading.value || (!hasMore.value && !reset)) return

    try {
      loading.value = true
      error.value = null

      if (reset) {
        currentPage.value = 1
        data.value = []
        hasMore.value = true
      }

      const response = await fetchFunction({
        page: currentPage.value,
        pageSize,
        offset: (currentPage.value - 1) * pageSize
      })

      const newData = response.data || response

      if (reset) {
        data.value = newData
      } else {
        data.value = [...data.value, ...newData]
      }

      hasMore.value = newData.length === pageSize
      currentPage.value++

    } catch (err) {
      error.value = err
      console.error('加载数据失败:', err)
    } finally {
      loading.value = false
    }
  }

  const loadMore = () => {
    if (!loading.value && hasMore.value) {
      loadData(false)
    }
  }

  const refresh = () => {
    loadData(true)
  }

  return {
    data,
    loading,
    hasMore,
    error,
    loadData,
    loadMore,
    refresh
  }
}

/**
 * 数据筛选和搜索 Composable
 */
export function useDataFilter(sourceData) {
  const filters = ref({})
  const searchQuery = ref('')
  const sortConfig = ref({ key: '', direction: 'asc' })

  const filteredData = computed(() => {
    let result = sourceData.value || []

    // 应用搜索
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase()
      result = result.filter(item => {
        return Object.values(item).some(value =>
          String(value).toLowerCase().includes(query)
        )
      })
    }

    // 应用筛选
    Object.entries(filters.value).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        if (Array.isArray(value)) {
          result = result.filter(item => value.includes(item[key]))
        } else {
          result = result.filter(item => item[key] === value)
        }
      }
    })

    // 应用排序
    if (sortConfig.value.key) {
      result.sort((a, b) => {
        const aVal = a[sortConfig.value.key]
        const bVal = b[sortConfig.value.key]
        const direction = sortConfig.value.direction === 'asc' ? 1 : -1

        if (aVal < bVal) return -1 * direction
        if (aVal > bVal) return 1 * direction
        return 0
      })
    }

    return result
  })

  const setFilter = (key, value) => {
    filters.value[key] = value
  }

  const clearFilters = () => {
    filters.value = {}
    searchQuery.value = ''
  }

  const setSort = (key, direction = 'asc') => {
    sortConfig.value = { key, direction }
  }

  return {
    filters,
    searchQuery,
    sortConfig,
    filteredData,
    setFilter,
    clearFilters,
    setSort
  }
}
