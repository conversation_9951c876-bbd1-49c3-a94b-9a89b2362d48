import { request } from '@/utils'

// 房型列表单独定义，避免循环引用
export const roomTypes = [
  { id: 1, name: '标准双人房', price: 299 },
  { id: 2, name: '豪华大床房', price: 399 },
  { id: 3, name: '商务套房', price: 599 },
  { id: 4, name: '总统套房', price: 1999 }
]

// ==================== 酒店房间相关API ====================

// 1. 获取酒店楼栋列表
export function getUsableRoomBuildingList(params = {}) {
  return request.post('/admin/Room/getUsableRoomBuildingList', params, {
    needToken: true,
    needTip: false
  })
}

// 2. 获取楼栋的楼层列表
export function getUsableRoomFloorList(params = {}) {
  return request.post('/admin/Room/getUsableRoomFloorList', params, {
    needToken: true,
    needTip: false
  })
}

// 2.1 获取楼层列表（新接口）
export function getRoomFloor(params = {}) {
  return request.post('/admin/Room/getRoomFloor', params, {
    needToken: true,
    needTip: false
  })
}

// 3. 获取房间列表
export function getUsableRoom(params = {}) {
  return request.post('/admin/RoomStatusRecord/getUsableRoom', params, {
    needToken: true,
    needTip: false
  })
}

// 4. 获取房间清洁状态
export function getRoomClearStatus(params = {}) {
  return request.post('/admin/RoomStatusRecord/getRoomClearStatus', params, {
    needToken: true,
    needTip: false
  })
}

// 5. 获取房间业务状态
export function getRoomRecordStatus(params = {}) {
  return request.post('/admin/RoomStatusRecord/getRoomRecordStatus', params, {
    needToken: true,
    needTip: false
  })
}

// 6. 获取房型列表
export function getRoomType(params = {}) {
  return request.post('/admin/Room/getRoomType', params, {
    needToken: true,
    needTip: false
  })
}

// 7. 预定入住选房接口
export function selectRoom(params = {}) {
  return request.post('/admin/RoomStatusRecord/selectRoom', params, {
    needToken: true,
    needTip: false
  })
}

// ==================== 入住相关API ====================

// 获取销售类型
export function getRoomSellType(params = {}) {
  return request.post('/admin/Room/getRoomSellType', params, {
    needToken: true,
    needTip: false
  })
}

// 获取销售规则
export function getRoomSaleType(params = {}) {
  return request.post('/admin/Room/getRoomSaleType', params, {
    needToken: true,
    needTip: false
  })
}

// 搜索用户信息
export function searchUser(params = {}) {
  return request.post('/admin/Member/searchUser', params, {
    needToken: true,
    needTip: false
  })
}

// 获取单位/中介列表
export function getIntermediaryList(params = {}) {
  return request.post('/admin/Intermediary/getIntermediaryList', params, {
    needToken: true,
    needTip: false
  })
}

// 获取离店时间
export function getCheckOutTime(params = {}) {
  return request.post('/admin/SystemSetting/getCheckOutTime', params, {
    needToken: true,
    needTip: false
  })
}

// 获取订单来源
export function getBillSource(params = {}) {
  return request.post('/admin/SystemSetting/getBillSource', params, {
    needToken: true,
    needTip: false
  })
}

// 获取房价
export function getRoomPriceByDate(params = {}) {
  return request.post('/admin/RoomPrice/getRoomPriceByDate', params, {
    needToken: true,
    needTip: false
  })
}

// 获取会员等级
export function getMemberGrade(params = {}) {
  return request.post('/admin/MemberGrade/getMemberGrade', params, {
    needToken: true,
    needTip: false
  })
}

// 读取身份证信息
export function readIdCard() {
  return fetch('http://127.0.0.1:19196/OpenDevice', {
    method: 'GET',
    headers: {
      'Referrer-Policy': 'strict-origin-when-cross-origin'
    }
  }).then(response => response.json())
}

// 获取客史记录
export function getUserHistory(params = {}) {
  return request.post('/admin/Member/getUserHistory', params, {
    needToken: true,
    needTip: false
  })
}

// 获取用户优惠券
export function getUserCoupon(params = {}) {
  return request.post('/admin/Member/getUserCoupon', params, {
    needToken: true,
    needTip: false
  })
}

// 获取用户详细信息
export function getUserInfo(params = {}) {
  return request.post('/admin/Member/getUserInfo', params, {
    needToken: true,
    needTip: false
  })
}

// 获取套餐列表
export function getPackageList(params = {}) {
  return request.post('/admin/Package/getPackageList', params, {
    needToken: true,
    needTip: false
  })
}

// ==================== 入住接口 ====================

// 全日房入住
export function standardRoomCheckIn(params = {}) {
  return request.post('/admin/StandardRoomBill/checkIn', params, {
    needToken: true,
    needTip: false
  })
}

// 时租房入住
export function hourRoomCheckIn(params = {}) {
  return request.post('/admin/HourRoomBill/checkIn', params, {
    needToken: true,
    needTip: false
  })
}

// 长期标准房入住（会议室）
export function longStandardRoomCheckIn(params = {}) {
  return request.post('/admin/LongStandardRoomBill/checkIn', params, {
    needToken: true,
    needTip: false
  })
}

// 获取远期房态数据
export function getFutureRoomStatus(params = {}) {
  return request.get('/admin/Room/getFutureRoomStatus', { params }, {
    needToken: true,
    needTip: false
  })
}

// ==================== 备用API ====================

// 获取房态图数据 - 备用API
export function getRoomStatusList(params) {
  return request.get('/api/hotel/room-status', { params })
}

// 更新房间状态
export function updateRoomStatus(data) {
  return request.put(`/api/hotel/room-status/${data.id}`, data)
}

