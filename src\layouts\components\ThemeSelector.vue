<template>
  <div>
    <n-dropdown
      :options="themeOptions"
      @select="handleThemeSelect"
      trigger="click"
      placement="bottom-end"
    >
      <n-button
        quaternary
        circle
        size="medium"
        class="theme-selector-btn"
        :title="currentTheme.label"
      >
        <template #icon>
          <n-icon size="18" :color="currentTheme.color">
            <i :class="currentTheme.icon"></i>
          </n-icon>
        </template>
      </n-button>
    </n-dropdown>

    <!-- 主题预览模态框 -->
    <n-modal
      v-model:show="showPreview"
      preset="card"
      title="选择主题"
      :style="{ width: '720px', maxWidth: '90vw' }"
      :bordered="false"
      :segmented="true"
    >
      <ThemePreview @selected="onThemeSelected" />
    </n-modal>
  </div>
</template>

<script setup>
import { computed, h, onMounted, ref } from 'vue'
import { useMessage } from 'naive-ui'
import { useAppStore } from '@/store'
import ThemePreview from '@/components/common/ThemePreview.vue'

const appStore = useAppStore()
const message = useMessage()
const showPreview = ref(false)

// 预设主题配置
const themes = [
  // 明亮主题系列
  {
    key: 'default',
    label: '默认蓝色',
    icon: 'i-material-symbols:palette',
    color: '#8A9CFF',
    primaryColor: '#8A9CFF',
    primaryColorHover: '#6E8AF8',
    primaryColorPressed: '#4C73E6',
    primaryColorSuppl: '#A8B5FF',
    category: 'light'
  },
  {
    key: 'green',
    label: '清新绿色',
    icon: 'i-material-symbols:eco',
    color: '#10B981',
    primaryColor: '#10B981',
    primaryColorHover: '#059669',
    primaryColorPressed: '#047857',
    primaryColorSuppl: '#34D399',
    category: 'light'
  },
  {
    key: 'purple',
    label: '优雅紫色',
    icon: 'i-material-symbols:auto-awesome',
    color: '#8B5CF6',
    primaryColor: '#8B5CF6',
    primaryColorHover: '#7C3AED',
    primaryColorPressed: '#6D28D9',
    primaryColorSuppl: '#A78BFA',
    category: 'light'
  },
  {
    key: 'orange',
    label: '活力橙色',
    icon: 'i-material-symbols:wb-sunny',
    color: '#F59E0B',
    primaryColor: '#F59E0B',
    primaryColorHover: '#D97706',
    primaryColorPressed: '#B45309',
    primaryColorSuppl: '#FCD34D',
    category: 'light'
  },
  {
    key: 'red',
    label: '热情红色',
    icon: 'i-material-symbols:favorite',
    color: '#EF4444',
    primaryColor: '#EF4444',
    primaryColorHover: '#DC2626',
    primaryColorPressed: '#B91C1C',
    primaryColorSuppl: '#F87171',
    category: 'light'
  },

  // 暗色主题系列
  {
    key: 'dark-default',
    label: '暗夜黑',
    icon: 'i-material-symbols:dark-mode',
    color: '#1F2937',
    primaryColor: '#1F2937',
    primaryColorHover: '#374151',
    primaryColorPressed: '#4B5563',
    primaryColorSuppl: '#6B7280',
    category: 'dark'
  },
  {
    key: 'dark-purple',
    label: '神秘紫',
    icon: 'i-material-symbols:psychology',
    color: '#581C87',
    primaryColor: '#581C87',
    primaryColorHover: '#6B21A8',
    primaryColorPressed: '#7C2D12',
    primaryColorSuppl: '#8B5CF6',
    category: 'dark'
  },
  {
    key: 'dark-green',
    label: '森林绿',
    icon: 'i-material-symbols:forest',
    color: '#14532D',
    primaryColor: '#14532D',
    primaryColorHover: '#166534',
    primaryColorPressed: '#15803D',
    primaryColorSuppl: '#22C55E',
    category: 'dark'
  },
  {
    key: 'dark-red',
    label: '深红酒',
    icon: 'i-material-symbols:wine-bar',
    color: '#7F1D1D',
    primaryColor: '#7F1D1D',
    primaryColorHover: '#991B1B',
    primaryColorPressed: '#B91C1C',
    primaryColorSuppl: '#EF4444',
    category: 'dark'
  },
  {
    key: 'dark-orange',
    label: '暖炉橙',
    icon: 'i-material-symbols:fireplace',
    color: '#9A3412',
    primaryColor: '#9A3412',
    primaryColorHover: '#C2410C',
    primaryColorPressed: '#EA580C',
    primaryColorSuppl: '#F97316',
    category: 'dark'
  },

]

// 当前主题
const currentTheme = computed(() => {
  return themes.find(theme => theme.key === appStore.currentTheme) || themes[0]
})

// 按分类分组主题
const lightThemes = computed(() => themes.filter(theme => theme.category === 'light'))
const darkThemes = computed(() => themes.filter(theme => theme.category === 'dark'))

// 下拉菜单选项 - 按分类组织
const themeOptions = computed(() => {
  return [
    {
      type: 'group',
      label: '明亮主题',
      key: 'light-group',
      children: lightThemes.value.map(theme => ({
        key: theme.key,
        label: theme.label,
        icon: () => h('i', {
          class: `${theme.icon} text-16`,
          style: { color: theme.color }
        })
      }))
    },
    {
      type: 'divider',
      key: 'divider'
    },
    {
      type: 'group',
      label: '暗色主题',
      key: 'dark-group',
      children: darkThemes.value.map(theme => ({
        key: theme.key,
        label: theme.label,
        icon: () => h('i', {
          class: `${theme.icon} text-16`,
          style: { color: theme.color }
        })
      }))
    },
    {
      type: 'divider',
      key: 'divider2'
    },
    {
      key: 'more-themes',
      label: '更多主题...',
      icon: () => h('i', {
        class: 'i-material-symbols:palette text-16',
        style: { color: 'var(--primary-color)' }
      })
    }
  ]
})

// 处理主题选择
function handleThemeSelect(themeKey) {
  if (themeKey === 'more-themes') {
    showPreview.value = true
    return
  }

  const selectedTheme = themes.find(theme => theme.key === themeKey)
  if (selectedTheme) {
    // 更新应用store中的主题
    appStore.setCurrentTheme(themeKey)

    // 更新Naive UI主题覆盖
    appStore.updateThemeOverrides({
      common: {
        primaryColor: selectedTheme.primaryColor,
        primaryColorHover: selectedTheme.primaryColorHover,
        primaryColorPressed: selectedTheme.primaryColorPressed,
        primaryColorSuppl: selectedTheme.primaryColorSuppl
      }
    })

    // 更新CSS变量
    updateCSSVariables(selectedTheme)

    message.success(`已切换到${selectedTheme.label}主题`)
  }
}

function onThemeSelected(theme) {
  appStore.setCurrentTheme(theme.key)
  appStore.updateThemeOverrides({
    common: {
      primaryColor: theme.primaryColor,
      primaryColorHover: theme.primaryColorHover || theme.primaryColor,
      primaryColorPressed: theme.primaryColorPressed || theme.primaryColor,
      primaryColorSuppl: theme.primaryColorSuppl || theme.primaryColor
    }
  })
  updateCSSVariables(theme)
  // 不自动关闭，让用户能直观看到“已选”样式
  message.success(`已切换到${theme.label}主题`)
}

// 更新CSS变量
function updateCSSVariables(theme) {
  const root = document.documentElement
  root.style.setProperty('--primary-color', theme.primaryColor)
  root.style.setProperty('--primary-color-hover', theme.primaryColorHover)
  root.style.setProperty('--primary-color-pressed', theme.primaryColorPressed)
  root.style.setProperty('--primary-color-suppl', theme.primaryColorSuppl)

  // 提取RGB值
  const hex = theme.primaryColor.replace('#', '')
  const r = parseInt(hex.substring(0, 2), 16)
  const g = parseInt(hex.substring(2, 4), 16)
  const b = parseInt(hex.substring(4, 6), 16)
  root.style.setProperty('--primary-color-rgb', `${r}, ${g}, ${b}`)
}

// 初始化主题
onMounted(() => {
  updateCSSVariables(currentTheme.value)
})
</script>

<style scoped>
.theme-selector-btn {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.theme-selector-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(var(--primary-color-rgb), 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.theme-selector-btn:hover::before {
  opacity: 1;
}

.theme-selector-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.2);
}

/* 暗色主题适配 */
.dark .theme-selector-btn:hover {
  box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.3);
}
</style>

<style scoped>
.theme-selector-btn {
  transition: all 0.3s ease;
}

.theme-selector-btn:hover {
  transform: scale(1.1);
}
</style>
