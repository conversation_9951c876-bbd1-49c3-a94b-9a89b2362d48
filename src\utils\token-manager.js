/**
 * Token管理工具
 * 
 * 提供统一的token获取、存储和刷新机制
 * 确保始终使用最新的有效token
 */

import { useAuthStore } from '@/store'
import { lStorage } from '@/utils'

/**
 * 获取当前最新的token
 * 优先从store获取，如果store中没有则从localStorage获取
 * @returns {string|null} 当前有效的token
 */
export function getCurrentToken() {
  const authStore = useAuthStore()
  
  // 优先从store获取
  if (authStore.accessToken) {
    return authStore.accessToken
  }
  
  // 从localStorage获取登录响应数据中的token
  const loginData = lStorage.get('loginResponseData')
  if (loginData && (loginData.user_token || loginData.token)) {
    const token = loginData.user_token || loginData.token
    // 同步到store
    authStore.setToken({ user_token: token })
    return token
  }
  
  // 从持久化的auth store获取（pinia持久化）
  const persistedAuth = lStorage.get('hotelhotel')
  if (persistedAuth && persistedAuth.accessToken) {
    return persistedAuth.accessToken
  }
  
  return null
}

/**
 * 设置新的token
 * 同时更新store和localStorage
 * @param {string} token 新的token
 */
export function setCurrentToken(token) {
  const authStore = useAuthStore()
  
  // 更新store
  authStore.setToken({ user_token: token })
  
  // 更新localStorage中的登录数据
  const loginData = lStorage.get('loginResponseData') || {}
  loginData.user_token = token
  lStorage.set('loginResponseData', loginData)

}

/**
 * 检查token是否有效
 * @param {string} token 要检查的token
 * @returns {boolean} token是否有效
 */
export function isTokenValid(token) {
  if (!token || token === 'null' || token === 'undefined') {
    return false
  }
  
  if (typeof token === 'object' && Object.keys(token).length === 0) {
    return false
  }
  
  return true
}

/**
 * 清除所有token相关数据
 */
export function clearAllTokens() {
  const authStore = useAuthStore()
  
  // 清除store中的token
  authStore.resetToken()
  
  // 清除localStorage中的token相关数据
  const loginData = lStorage.get('loginResponseData') || {}
  delete loginData.user_token
  delete loginData.token
  lStorage.set('loginResponseData', loginData)

}

/**
 * 从登录响应中提取并设置token
 * @param {Object} loginResponse 登录接口的响应数据
 * @returns {string|null} 提取到的token
 */
export function extractAndSetTokenFromLogin(loginResponse) {
  if (!loginResponse || typeof loginResponse !== 'object') {

    return null
  }
  
  // 提取token（支持多种字段名）
  const token = loginResponse.user_token || 
                loginResponse.token || 
                loginResponse.access_token || 
                loginResponse.accessToken
  
  if (!token) {

    return null
  }
  
  // 设置token
  setCurrentToken(token)

  return token
}

/**
 * 刷新token（如果有刷新机制）
 * @returns {Promise<string|null>} 新的token
 */
export async function refreshToken() {
  try {
    // 这里可以调用刷新token的API
    // const response = await api.refreshToken()
    // const newToken = response.data.token
    // setCurrentToken(newToken)
    // return newToken

    return null
  } catch (error) {

    return null
  }
}

/**
 * 确保token同步
 * 在页面刷新或应用启动时调用，确保token状态一致
 */
export function ensureTokenSync() {
  const authStore = useAuthStore()
  const currentToken = getCurrentToken()
  
  if (currentToken && !authStore.accessToken) {
    // 如果localStorage有token但store中没有，同步到store
    authStore.setToken({ user_token: currentToken })

  } else if (!currentToken && authStore.accessToken) {
    // 如果store有token但localStorage中没有，同步到localStorage
    const loginData = lStorage.get('loginResponseData') || {}
    loginData.user_token = authStore.accessToken
    lStorage.set('loginResponseData', loginData)

  }
  
  return currentToken
}

/**
 * 获取token的详细信息（用于调试）
 * @returns {Object} token的详细信息
 */
export function getTokenInfo() {
  const authStore = useAuthStore()
  const loginData = lStorage.get('loginResponseData') || {}
  const persistedAuth = lStorage.get('hotelhotel') || {}
  
  return {
    storeToken: authStore.accessToken,
    loginDataToken: loginData.user_token || loginData.token,
    persistedToken: persistedAuth.accessToken,
    currentToken: getCurrentToken(),
    isValid: isTokenValid(getCurrentToken())
  }
}

/**
 * 监听token变化
 * @param {Function} callback 当token变化时的回调函数
 * @returns {Function} 取消监听的函数
 */
export function watchTokenChange(callback) {
  let lastToken = getCurrentToken()
  
  const checkToken = () => {
    const currentToken = getCurrentToken()
    if (currentToken !== lastToken) {
      lastToken = currentToken
      callback(currentToken, lastToken)
    }
  }
  
  // 定期检查token变化
  const interval = setInterval(checkToken, 1000)
  
  // 返回取消监听的函数
  return () => {
    clearInterval(interval)
  }
}

export default {
  getCurrentToken,
  setCurrentToken,
  isTokenValid,
  clearAllTokens,
  extractAndSetTokenFromLogin,
  refreshToken,
  ensureTokenSync,
  getTokenInfo,
  watchTokenChange
}
