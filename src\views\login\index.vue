<template>
  <div class="login-container">
    <!-- 左侧品牌展示区 -->
    <div class="brand-section" :style="brandSectionStyle">
      <!-- 暗黑风格动效背景 -->
      <div v-if="!webConfig.bg_img_login" class="dark-bg-effects">
        <div class="floating-particles">
          <div v-for="i in 20" :key="i" class="particle" :style="getParticleStyle(i)"></div>
        </div>
        <div class="gradient-orbs">
          <div class="orb orb-1"></div>
          <div class="orb orb-2"></div>
          <div class="orb orb-3"></div>
        </div>
        <div class="grid-pattern"></div>
      </div>

      <!-- 自定义背景图片遮罩 -->
      <div v-if="webConfig.bg_img_login" class="bg-overlay"></div>

      <div class="brand-content">
        <div class="brand-header">
          <div class="logo-wrapper">
            <img
              v-if="hotelInfo.logo"
              :src="hotelInfo.logo"
              :alt="hotelInfo.title"
              class="brand-logo"
            />
            <div v-else class="default-brand-logo">
              <i class="i-fluent:building-home-24-filled"></i>
            </div>
          </div>
          <h1 class="brand-title">{{ hotelInfo.title }}</h1>
          <p class="brand-subtitle">智能酒店管理系统</p>
        </div>
      </div>
    </div>

    <!-- 右侧登录区 -->
    <div class="login-section">
      <!-- 右侧装饰动效 -->
      <div class="login-decorations">
        <div class="decoration-circle decoration-circle-1"></div>
        <div class="decoration-circle decoration-circle-2"></div>
        <div class="decoration-circle decoration-circle-3"></div>
        <div class="decoration-lines">
          <div class="decoration-line decoration-line-1"></div>
          <div class="decoration-line decoration-line-2"></div>
          <div class="decoration-line decoration-line-3"></div>
        </div>
        <div class="decoration-dots">
          <div v-for="i in 8" :key="i" class="decoration-dot" :style="getDotStyle(i)"></div>
        </div>
      </div>

      <div class="login-wrapper">
        <div class="login-header">
          <h2 class="login-title">欢迎回来</h2>
          <p class="login-subtitle">请输入您的账户信息</p>
        </div>

        <n-form ref="formRef" :model="form" :rules="rules" class="login-form">
          <n-form-item path="name">
            <n-input
              v-model:value="form.name"
              placeholder="请输入用户名"
              size="large"
            >
              <template #prefix>
                <i class="i-fluent:person-24-filled"></i>
              </template>
            </n-input>
          </n-form-item>

          <n-form-item path="pwd">
            <n-input
              v-model:value="form.pwd"
              type="password"
              show-password-on="click"
              placeholder="请输入密码"
              size="large"
              @keyup.enter="handleLogin()"
            >
              <template #prefix>
                <i class="i-fluent:lock-closed-24-filled"></i>
              </template>
            </n-input>
          </n-form-item>

          <div class="form-options">
            <n-checkbox v-model:checked="isRemember">
              记住我
            </n-checkbox>
          </div>

          <n-button
            type="primary"
            size="large"
            block
            :loading="loading"
            @click="handleLogin()"
          >
            {{ loading ? '登录中...' : '登录' }}
          </n-button>
        </n-form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useStorage } from '@vueuse/core'
import { useAuthStore } from '@/store'
import { lStorage } from '@/utils'
import { extractAndSetTokenFromLogin } from '@/utils/token-manager'
import { getCachedWebConfig, updateLoginCache } from '@/utils/loginCache'
import { initAccountsCache } from '@/utils/accountsCache'
import loginApi from './api.js'
import { ref, onMounted, computed } from 'vue'
import { useMessage } from 'naive-ui'
import { useUserStore } from '@/store/modules/user'
import { usePermissionStore } from '@/store'
import { useRouter, useRoute } from 'vue-router'
import { updateFaviconFromLogin } from '@/utils/favicon'
import md5 from 'js-md5'
import { getPermissions } from '@/store/helper'

const authStore = useAuthStore()
const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const permissionStore = usePermissionStore()
const message = useMessage()
const isRemember = useStorage('isRemember', false)

const formRef = ref(null)
const form = ref({
  name: '',
  pwd: '',
})
const loading = ref(false)
const configUpdateTrigger = ref(0) // 用于强制更新配置

const rules = {
  name: { required: true, message: '请输入账户名', trigger: 'blur' },
  pwd: { required: true, message: '请输入密码', trigger: 'blur' },
}

// 网站配置信息
const webConfig = computed(() => {
  // 使用触发器强制重新计算
  configUpdateTrigger.value
  return getCachedWebConfig()
})

// 酒店信息计算属性
const hotelInfo = computed(() => {
  const config = webConfig.value
  let title = config.set_web_title || '管理系统'

  return {
    title,
    logo: config.set_web_logo || '',
    copyright: config.set_web_copyright || '',
    copyrightUrl: config.set_web_copyright_url || ''
  }
})

// 左侧品牌区背景样式
const brandSectionStyle = computed(() => {
  const config = webConfig.value

  if (config.bg_img_login) {
    return {
      backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)), url(${config.bg_img_login})`,
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat'
    }
  } else {
    return {
      background: 'linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e)',
      position: 'relative',
      overflow: 'hidden'
    }
  }
})

// 生成粒子样式
const getParticleStyle = (index) => {
  const size = Math.random() * 4 + 2
  const left = Math.random() * 100
  const animationDelay = Math.random() * 20
  const animationDuration = Math.random() * 10 + 15

  return {
    width: `${size}px`,
    height: `${size}px`,
    left: `${left}%`,
    animationDelay: `${animationDelay}s`,
    animationDuration: `${animationDuration}s`
  }
}

// 生成右侧装饰点样式
const getDotStyle = (index) => {
  const positions = [
    { top: '15%', right: '10%' },
    { top: '25%', right: '5%' },
    { top: '35%', right: '15%' },
    { top: '45%', right: '8%' },
    { top: '55%', right: '12%' },
    { top: '65%', right: '6%' },
    { top: '75%', right: '14%' },
    { top: '85%', right: '9%' }
  ]

  const position = positions[index - 1] || positions[0]
  const delay = index * 0.5

  return {
    ...position,
    animationDelay: `${delay}s`
  }
}

// 更新网站标题和图标
function updateWebsiteInfo() {
  try {
    updateFaviconFromLogin()
    const currentRoute = router.currentRoute.value
    router.replace(currentRoute.fullPath)

  } catch (error) {

  }
}

// 登录处理
async function handleLogin() {
  try {
    await formRef.value?.validate()
    loading.value = true

    const loginData = {
      name: form.value.name,
      pwd: md5(form.value.pwd)
    }

    const response = await loginApi.login(loginData)

    if (response && response.data && typeof response.data === 'object') {
      const data = response.data

      // 修正总店登录数据 - 如果有children_admin_list，说明这是总店登录
      if (data.children_admin_list && data.children_admin_list.length > 0) {
        const mainAccount = data.children_admin_list[0] // 总店账号信息

        // 将主要字段替换为总店账号信息，保持其他字段不变
        data.admin_id = mainAccount.id
        data.admin_name = mainAccount.name
        data.nickname = mainAccount.nickname
        data.shop_name = mainAccount.shop_name
        data.name = mainAccount.name
        data.account_name = mainAccount.name

        // 如果总店账号有权限列表，也使用总店的权限
        if (mainAccount.auth_list) {
          data.auth_list = mainAccount.auth_list
        }
      }

      // 设置用户信息
      const userInfo = {
        id: data.admin_id || data.id || data.user_id,
        username: data.admin_name || data.username || data.name || data.account_name,
        nickName: data.nickname || data.nickName || data.nick_name || data.name || data.account_name,
        avatar: data.avatar || data.user_avatar || '',
        roles: data.roles || [],
        currentRole: data.currentRole || data.current_role || {},
        roleName: data.role_name || data.roleName || '管理员',
        accountName: data.admin_name || data.account_name || data.accountName || data.name || data.username,
        shop_name: data.shop_name,
      }

      userStore.setUser(userInfo)

      // 更新登录缓存（包括网站配置）
      updateLoginCache(data, userInfo)

      // 初始化账号缓存
      initAccountsCache(data)

      // 强制更新配置以显示新的背景图片
      configUpdateTrigger.value++

      // 提取并设置token
      extractAndSetTokenFromLogin(data)

      // 获取权限
      const permissions = await getPermissions()
      permissionStore.setPermissions(permissions)

      // 记住登录状态
      if (isRemember.value) {
        lStorage.set('loginForm', { name: form.value.name })
      }

      message.success('登录成功')

      // 短暂延迟让用户看到成功提示，然后快速跳转
      await new Promise(resolve => setTimeout(resolve, 500))

      // 跳转到目标页面
      const targetPath = route.query.redirect || '/'
      await router.push(targetPath)

    } else {
      message.error('登录失败：响应数据格式错误')
    }
  } catch (error) {

    message.error(error.message || '登录失败，请重试')
  } finally {
    loading.value = false
  }
}

// 组件挂载时的初始化
onMounted(async () => {
  updateWebsiteInfo()

  // 预加载登录配置，如果有缓存的配置则立即显示
  const cachedConfig = getCachedWebConfig()
  if (cachedConfig && Object.keys(cachedConfig).length > 0) {
    configUpdateTrigger.value++
  }

  // 恢复记住的登录信息
  if (isRemember.value) {
    const savedForm = lStorage.get('loginForm')
    if (savedForm && savedForm.name) {
      form.value.name = savedForm.name
    }
  }
})
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  background: #f8fafc;
}

.brand-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

/* 暗黑风格动效背景 */
.dark-bg-effects {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

/* 浮动粒子 */
.floating-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.particle {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 20s infinite linear;
}

@keyframes float {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

/* 渐变光球 */
.gradient-orbs {
  position: absolute;
  width: 100%;
  height: 100%;
}

.orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(40px);
  animation: pulse 4s ease-in-out infinite;
}

.orb-1 {
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(99, 102, 241, 0.3) 0%, transparent 70%);
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.orb-2 {
  width: 150px;
  height: 150px;
  background: radial-gradient(circle, rgba(139, 92, 246, 0.3) 0%, transparent 70%);
  top: 60%;
  right: 20%;
  animation-delay: 2s;
}

.orb-3 {
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, transparent 70%);
  bottom: 30%;
  left: 30%;
  animation-delay: 1s;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

/* 网格图案 */
.grid-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(50px, 50px);
  }
}

/* 自定义背景图片遮罩 */
.bg-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 1;
}

.brand-content {
  text-align: center;
  max-width: 400px;
  position: relative;
  z-index: 2;
}

.brand-header {
  margin-bottom: 2rem;
}

.logo-wrapper {
  margin-bottom: 1.5rem;
}

.brand-logo {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.default-brand-logo {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  font-size: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.brand-title {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.brand-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
  text-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
}

.login-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%);
  position: relative;
  overflow: hidden;
}

/* 右侧装饰动效 */
.login-decorations {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
  animation: floatCircle 8s ease-in-out infinite;
}

.decoration-circle-1 {
  width: 120px;
  height: 120px;
  top: 10%;
  right: 5%;
  animation-delay: 0s;
}

.decoration-circle-2 {
  width: 80px;
  height: 80px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.decoration-circle-3 {
  width: 60px;
  height: 60px;
  top: 30%;
  right: 25%;
  animation-delay: 4s;
}

@keyframes floatCircle {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.6;
  }
}

.decoration-lines {
  position: absolute;
  width: 100%;
  height: 100%;
}

.decoration-line {
  position: absolute;
  background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.2), transparent);
  animation: moveLine 6s linear infinite;
}

.decoration-line-1 {
  width: 200px;
  height: 1px;
  top: 20%;
  right: -100px;
  animation-delay: 0s;
}

.decoration-line-2 {
  width: 150px;
  height: 1px;
  top: 50%;
  right: -75px;
  animation-delay: 2s;
}

.decoration-line-3 {
  width: 180px;
  height: 1px;
  top: 80%;
  right: -90px;
  animation-delay: 4s;
}

@keyframes moveLine {
  0% {
    transform: translateX(100px);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(-300px);
    opacity: 0;
  }
}

.decoration-dots {
  position: absolute;
  width: 100%;
  height: 100%;
}

.decoration-dot {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(99, 102, 241, 0.4);
  border-radius: 50%;
  animation: twinkle 3s ease-in-out infinite;
}

@keyframes twinkle {
  0%, 100% {
    opacity: 0.2;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.5);
  }
}

.login-wrapper {
  width: 100%;
  max-width: 400px;
  position: relative;
  z-index: 2;
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.login-subtitle {
  color: #6b7280;
  margin: 0;
}

.login-form {
  margin-top: 1.5rem;
}

/* 修复输入框背景色 */
.login-form :deep(.n-input) {
  background-color: #ffffff !important;
}

.login-form :deep(.n-input__input-el) {
  background-color: #ffffff !important;
  color: #333333 !important;
}

.login-form :deep(.n-input__input-el:-webkit-autofill) {
  -webkit-box-shadow: 0 0 0 1000px #ffffff inset !important;
  -webkit-text-fill-color: #333333 !important;
  background-color: #ffffff !important;
}

.login-form :deep(.n-input__input-el:-webkit-autofill:hover) {
  -webkit-box-shadow: 0 0 0 1000px #ffffff inset !important;
  -webkit-text-fill-color: #333333 !important;
  background-color: #ffffff !important;
}

.login-form :deep(.n-input__input-el:-webkit-autofill:focus) {
  -webkit-box-shadow: 0 0 0 1000px #ffffff inset !important;
  -webkit-text-fill-color: #333333 !important;
  background-color: #ffffff !important;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 1rem 0;
}

@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
  }

  .brand-section {
    flex: none;
    min-height: 200px;
  }

  .login-section {
    flex: 1;
    padding: 1rem;
  }

  .orb-1, .orb-2, .orb-3 {
    width: 100px;
    height: 100px;
  }

  .grid-pattern {
    background-size: 30px 30px;
  }

  /* 移动端右侧装饰适配 */
  .decoration-circle-1 {
    width: 80px;
    height: 80px;
    top: 5%;
    right: 2%;
  }

  .decoration-circle-2 {
    width: 60px;
    height: 60px;
    top: 70%;
    right: 8%;
  }

  .decoration-circle-3 {
    width: 40px;
    height: 40px;
    top: 40%;
    right: 15%;
  }

  .decoration-line {
    display: none; /* 移动端隐藏线条动效 */
  }

  .decoration-dot {
    width: 3px;
    height: 3px;
  }
}
</style>
