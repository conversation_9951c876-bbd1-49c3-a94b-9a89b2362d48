/**
 * 房态页面 API 接口
 */
import { request } from '@/utils'

import {
  getUsableRoomBuildingList as getUsableRoomBuildingList<PERSON><PERSON>,
  getUsableRoomF<PERSON>orList as getUsableRoomFloorList<PERSON><PERSON>,
  getRoomFloor as getRoomFloor<PERSON><PERSON>,
  getUsableRoom as getUsableRoom<PERSON><PERSON>,
  getRoomClearStatus as getRoomClearStatusApi,
  getRoomRecordStatus as getRoomRecordStatusApi,
  getRoomType as getRoomType<PERSON><PERSON>,
  selectRoom as selectRoom<PERSON>pi,
  getRoomStatusList as getRoomStatusListApi,
  updateRoomStatus as updateRoomStatus<PERSON>pi,
  getFutureRoomStatus as getFutureRoomStatusApi,
  searchUser as searchUser<PERSON>pi,
  getUserInfo as getUserInfoApi,
  getUserHistory as getUserHistoryApi,
  standardRoomCheckIn as standardRoomCheckInApi,
  hourRoomCheckIn as hourRoomCheckInApi,
  longStandardRoomCheckIn as longStandardRoomCheckInApi
} from '@/api/hotel/roomStatus'

// 获取可用楼栋列表（委托全局实现）
export function getUsableRoomBuildingList(params = {}) {
  return getUsableRoomBuildingListApi(params)
}
// 获取楼层列表（新接口，委托全局实现）
export function getRoomFloor(params = {}) {
  return getRoomFloorApi(params)
}


// 获取可用楼层列表（委托全局实现）
export function getUsableRoomFloorList(params = {}) {
  return getUsableRoomFloorListApi(params)
}

// 获取可用房间列表（委托全局实现）
export function getUsableRoom(params = {}) {
  return getUsableRoomApi(params)
}

// 获取房间清洁状态配置（委托全局实现）
export function getRoomClearStatus(params = {}) {
  return getRoomClearStatusApi(params)
}

// 获取房间业务状态配置（委托全局实现）
export function getRoomRecordStatus(params = {}) {
  return getRoomRecordStatusApi(params)
}

// 获取房间账单详情
export function getRoomBillDetail(params = {}) {
  return request.post('/admin/RoomBill/getRoomBillDetail', params, {
    needToken: true,
    needTip: false
  })
}

// 更新房间状态（使用正确的API）
export function updateRoomStatus(data) {
  return updateRoomStatusApi(data)
}

// 更新房间清洁状态
export function updateRoomCleanStatus(data) {
  return request({
    url: '/room/updateRoomCleanStatus',
    method: 'post',
    data
  })
}

// 房间封锁/解封
export function toggleRoomBlock(data) {
  return request({
    url: '/room/toggleRoomBlock',
    method: 'post',
    data
  })
}

// 房间维修
export function roomMaintenance(data) {
  return request({
    url: '/room/roomMaintenance',
    method: 'post',
    data
  })
}

// 房间清洁
export function roomCleaning(data) {
  return request({
    url: '/room/roomCleaning',
    method: 'post',
    data
  })
}

// 快速退房
export function quickCheckout(data) {
  return request({
    url: '/room/quickCheckout',
    method: 'post',
    data
  })
}


// 预订：创建预订（兼容 ReservationModal 调用）
export function createReservation(data = {}) {
  // 临时占位：与后端约定后调整真实接口路径
  return request({
    url: '/admin/Reservation/create',
    method: 'post',
    data
  })
}

// 预订：房型列表（兼容 ReservationModal 的 getRoomTypeList）
export function getRoomTypeList(params = {}) {
  return getRoomTypeApi(params)
}


// 联房：查询联房信息（兼容 RoomDetailModal 调用）
export function getConnectBill(params = {}) {
  return request({
    url: '/admin/RoomBill/getConnectBill',
    method: 'post',
    data: params
  })
}

// ==================== 使用正确API的新增方法 ====================

// 获取房态图数据（使用正确的API）
export function getRoomStatusList(params = {}) {
  return getRoomStatusListApi(params)
}

// 选房接口（使用正确的API）
export function selectRoom(params = {}) {
  return selectRoomApi(params)
}

// 获取远期房态数据（使用正确的API）
export function getFutureRoomStatus(params = {}) {
  return getFutureRoomStatusApi(params)
}

// 搜索用户信息（使用正确的API）
export function searchUser(params = {}) {
  return searchUserApi(params)
}

// 获取用户详细信息（使用正确的API）
export function getUserInfo(params = {}) {
  return getUserInfoApi(params)
}

// 获取用户历史记录（使用正确的API）
export function getUserHistory(params = {}) {
  return getUserHistoryApi(params)
}

// ==================== 入住相关API（使用正确的API） ====================

// 全日房入住（使用正确的API）
export function standardRoomCheckIn(params = {}) {
  return standardRoomCheckInApi(params)
}

// 时租房入住（使用正确的API）
export function hourRoomCheckIn(params = {}) {
  return hourRoomCheckInApi(params)
}

// 长期标准房入住（使用正确的API）
export function longStandardRoomCheckIn(params = {}) {
  return longStandardRoomCheckInApi(params)
}
