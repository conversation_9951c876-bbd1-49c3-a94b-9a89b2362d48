<template>
  <div class="alerts-dashboard">
    <div class="page-header">
      <h1 class="page-title">
        <i class="i-material-symbols:notification-important-outline"></i>
        预警中心
      </h1>
      <p class="page-description">实时监控酒店运营异常，及时处理重要事件</p>
    </div>

    <!-- 预警统计 -->
    <div class="alert-stats">
      <n-card class="stat-card critical">
        <div class="stat-content">
          <div class="stat-number">{{ alertStats.critical }}</div>
          <div class="stat-label">紧急预警</div>
        </div>
      </n-card>
      <n-card class="stat-card warning">
        <div class="stat-content">
          <div class="stat-number">{{ alertStats.warning }}</div>
          <div class="stat-label">重要预警</div>
        </div>
      </n-card>
      <n-card class="stat-card info">
        <div class="stat-content">
          <div class="stat-number">{{ alertStats.info }}</div>
          <div class="stat-label">一般提醒</div>
        </div>
      </n-card>
      <n-card class="stat-card resolved">
        <div class="stat-content">
          <div class="stat-number">{{ alertStats.resolved }}</div>
          <div class="stat-label">已处理</div>
        </div>
      </n-card>
    </div>

    <!-- 筛选工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <n-select
          v-model:value="selectedType"
          :options="alertTypeOptions"
          placeholder="预警类型"
          style="width: 150px"
          clearable
        />
        <n-select
          v-model:value="selectedLevel"
          :options="alertLevelOptions"
          placeholder="预警级别"
          style="width: 150px"
          clearable
        />
        <n-select
          v-model:value="selectedStatus"
          :options="alertStatusOptions"
          placeholder="处理状态"
          style="width: 150px"
          clearable
        />
      </div>
      <div class="toolbar-right">
        <n-button @click="refreshAlerts" :loading="loading">
          <i class="i-material-symbols:refresh"></i>
          刷新
        </n-button>
        <n-button type="primary" @click="markAllAsRead">
          <i class="i-material-symbols:done-all"></i>
          全部已读
        </n-button>
      </div>
    </div>

    <!-- 预警列表 -->
    <div class="alerts-list">
      <div
        v-for="alert in filteredAlerts"
        :key="alert.id"
        :class="['alert-item', alert.level, { unread: !alert.isRead }]"
      >
        <div class="alert-icon">
          <i :class="getAlertIcon(alert.type)"></i>
        </div>
        <div class="alert-content">
          <div class="alert-header">
            <div class="alert-title">{{ alert.title }}</div>
            <div class="alert-time">{{ formatTime(alert.createdAt) }}</div>
          </div>
          <div class="alert-description">{{ alert.description }}</div>
          <div class="alert-details" v-if="alert.details">
            <div class="detail-item" v-for="(value, key) in alert.details" :key="key">
              <span class="detail-key">{{ key }}:</span>
              <span class="detail-value">{{ value }}</span>
            </div>
          </div>
        </div>
        <div class="alert-actions">
          <n-button
            v-if="!alert.isRead"
            size="small"
            @click="markAsRead(alert)"
          >
            标记已读
          </n-button>
          <n-button
            v-if="alert.status === 'pending'"
            size="small"
            type="primary"
            @click="handleAlert(alert)"
          >
            处理
          </n-button>
          <n-button
            size="small"
            type="error"
            @click="dismissAlert(alert)"
          >
            忽略
          </n-button>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="filteredAlerts.length === 0" class="empty-state">
      <n-empty description="暂无预警信息">
        <template #extra>
          <n-button @click="refreshAlerts">刷新数据</n-button>
        </template>
      </n-empty>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useMessage } from 'naive-ui'

const $message = useMessage()

const loading = ref(false)
const selectedType = ref(null)
const selectedLevel = ref(null)
const selectedStatus = ref(null)

// 预警统计
const alertStats = ref({
  critical: 3,
  warning: 8,
  info: 12,
  resolved: 45
})

// 筛选选项
const alertTypeOptions = [
  { label: '房间异常', value: 'room' },
  { label: '设备故障', value: 'equipment' },
  { label: '客人投诉', value: 'complaint' },
  { label: '财务异常', value: 'finance' },
  { label: '系统异常', value: 'system' }
]

const alertLevelOptions = [
  { label: '紧急', value: 'critical' },
  { label: '重要', value: 'warning' },
  { label: '一般', value: 'info' }
]

const alertStatusOptions = [
  { label: '待处理', value: 'pending' },
  { label: '处理中', value: 'processing' },
  { label: '已处理', value: 'resolved' },
  { label: '已忽略', value: 'dismissed' }
]

// 模拟预警数据
const alerts = ref([
  {
    id: 1,
    type: 'room',
    level: 'critical',
    title: '房间101设备故障',
    description: '空调系统无法正常工作，客人已投诉',
    details: {
      '房间号': '101',
      '故障设备': '空调',
      '客人姓名': '张三',
      '投诉时间': '10:30'
    },
    status: 'pending',
    isRead: false,
    createdAt: new Date(Date.now() - 30 * 60 * 1000) // 30分钟前
  },
  {
    id: 2,
    type: 'complaint',
    level: 'warning',
    title: '客人投诉服务质量',
    description: '客人对前台服务态度不满，要求更换房间',
    details: {
      '客人姓名': '李四',
      '房间号': '205',
      '投诉内容': '前台服务态度差',
      '处理人员': '王经理'
    },
    status: 'processing',
    isRead: true,
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2小时前
  },
  {
    id: 3,
    type: 'finance',
    level: 'warning',
    title: '账单异常',
    description: '房间302客人账单金额异常，需要核实',
    details: {
      '房间号': '302',
      '客人姓名': '王五',
      '异常金额': '¥2,500',
      '正常金额': '¥800'
    },
    status: 'pending',
    isRead: false,
    createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000) // 4小时前
  },
  {
    id: 4,
    type: 'equipment',
    level: 'info',
    title: '电梯维护提醒',
    description: '1号电梯即将到达维护周期，请安排维护',
    details: {
      '设备': '1号电梯',
      '上次维护': '2024-01-01',
      '建议维护时间': '2024-01-20'
    },
    status: 'pending',
    isRead: true,
    createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000) // 6小时前
  },
  {
    id: 5,
    type: 'system',
    level: 'critical',
    title: '系统连接异常',
    description: 'PMS系统与支付网关连接中断，影响结账功能',
    details: {
      '系统': 'PMS',
      '异常模块': '支付网关',
      '影响功能': '在线支付、结账',
      '错误代码': 'ERR_CONNECTION_TIMEOUT'
    },
    status: 'pending',
    isRead: false,
    createdAt: new Date(Date.now() - 10 * 60 * 1000) // 10分钟前
  }
])

// 过滤后的预警列表
const filteredAlerts = computed(() => {
  let result = alerts.value

  if (selectedType.value) {
    result = result.filter(alert => alert.type === selectedType.value)
  }

  if (selectedLevel.value) {
    result = result.filter(alert => alert.level === selectedLevel.value)
  }

  if (selectedStatus.value) {
    result = result.filter(alert => alert.status === selectedStatus.value)
  }

  return result.sort((a, b) => {
    // 未读的排在前面
    if (a.isRead !== b.isRead) {
      return a.isRead ? 1 : -1
    }
    // 按级别排序：critical > warning > info
    const levelOrder = { critical: 3, warning: 2, info: 1 }
    if (levelOrder[a.level] !== levelOrder[b.level]) {
      return levelOrder[b.level] - levelOrder[a.level]
    }
    // 按时间排序，最新的在前
    return new Date(b.createdAt) - new Date(a.createdAt)
  })
})

function getAlertIcon(type) {
  const iconMap = {
    room: 'i-material-symbols:hotel-outline',
    equipment: 'i-material-symbols:build-outline',
    complaint: 'i-material-symbols:feedback-outline',
    finance: 'i-material-symbols:account-balance-outline',
    system: 'i-material-symbols:computer-outline'
  }
  return iconMap[type] || 'i-material-symbols:warning-outline'
}

function formatTime(date) {
  const now = new Date()
  const diff = now - date
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else {
    return `${days}天前`
  }
}

function markAsRead(alert) {
  alert.isRead = true
  $message.success('已标记为已读')
}

function markAllAsRead() {
  alerts.value.forEach(alert => {
    alert.isRead = true
  })
  $message.success('所有预警已标记为已读')
}

function handleAlert(alert) {
  alert.status = 'processing'
  $message.success(`开始处理预警：${alert.title}`)
}

function dismissAlert(alert) {
  alert.status = 'dismissed'
  $message.success('预警已忽略')
}

function refreshAlerts() {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    $message.success('预警数据已刷新')
  }, 1000)
}
</script>

<style scoped>
.alerts-dashboard {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: var(--text-color-1);
}

.page-description {
  color: var(--text-color-3);
  margin: 0;
}

.alert-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 8px;
  overflow: hidden;
}

.stat-content {
  text-align: center;
  padding: 8px;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--text-color-2);
}

.critical .stat-number {
  color: #d03050;
}

.warning .stat-number {
  color: #f0a020;
}

.info .stat-number {
  color: #2080f0;
}

.resolved .stat-number {
  color: #18a058;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  background: var(--card-color);
  transition: all 0.2s ease;
}

.alert-item.unread {
  border-left: 4px solid var(--primary-color);
  background: rgba(var(--primary-color), 0.02);
}

.alert-item.critical {
  border-left-color: #d03050;
}

.alert-item.warning {
  border-left-color: #f0a020;
}

.alert-item.info {
  border-left-color: #2080f0;
}

.alert-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  flex-shrink: 0;
}

.critical .alert-icon {
  background: rgba(208, 48, 80, 0.1);
  color: #d03050;
}

.warning .alert-icon {
  background: rgba(240, 160, 32, 0.1);
  color: #f0a020;
}

.info .alert-icon {
  background: rgba(32, 128, 240, 0.1);
  color: #2080f0;
}

.alert-content {
  flex: 1;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.alert-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color-1);
}

.alert-time {
  font-size: 12px;
  color: var(--text-color-3);
  white-space: nowrap;
}

.alert-description {
  color: var(--text-color-2);
  margin-bottom: 12px;
  line-height: 1.4;
}

.alert-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 8px;
  padding: 12px;
  background: var(--fill-color);
  border-radius: 6px;
  font-size: 12px;
}

.detail-item {
  display: flex;
  gap: 4px;
}

.detail-key {
  color: var(--text-color-3);
  font-weight: 500;
}

.detail-value {
  color: var(--text-color-1);
}

.alert-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex-shrink: 0;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
}
</style>