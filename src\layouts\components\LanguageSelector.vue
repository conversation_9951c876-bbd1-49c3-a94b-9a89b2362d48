<template>
  <NDropdown
    :options="languageOptions"
    @select="handleLanguageSelect"
    trigger="hover"
    placement="bottom-end"
  >
    <NButton
      quaternary
      circle
      size="medium"
      :title="t('language.title')"
    >
      <template #icon>
        <i class="i-material-symbols:translate text-18"></i>
      </template>
    </NButton>
  </NDropdown>
</template>

<script setup>
import { computed, h } from 'vue'
import { useI18n } from 'vue-i18n'
import { useMessage } from 'naive-ui'
import { SUPPORT_LOCALES, setLocale, getLocaleLabel } from '@/i18n'

const { locale, t } = useI18n()
const message = useMessage()

// 语言选项
const languageOptions = computed(() => {
  return SUPPORT_LOCALES.map(lang => ({
    key: lang.key,
    label: lang.label,
    icon: () => h('span', {
      style: { fontSize: '16px', marginRight: '8px' }
    }, lang.flag),
    disabled: locale.value === lang.key
  }))
})

// 处理语言选择
function handleLanguageSelect(languageKey) {
  const newLocale = setLocale(languageKey)
  const languageLabel = getLocaleLabel(newLocale)

  // 显示切换成功消息
  message.success(t('language.switchSuccess', { language: languageLabel }))

  // 可选：刷新页面以确保所有组件都使用新语言
  // setTimeout(() => {
  //   window.location.reload()
  // }, 500)
}
</script>

<style scoped>
.n-button {
  transition: all 0.3s ease;
}

.n-button:hover {
  background-color: var(--n-color-hover);
  transform: scale(1.05);
}
</style>
