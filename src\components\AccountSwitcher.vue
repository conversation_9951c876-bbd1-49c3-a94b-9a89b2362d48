<template>
  <div
    @click="openAccountSwitchModal"
    class="account-switcher-btn"
    title="切换账号"
  >
    <n-icon size="14">
      <i class="i-material-symbols:switch-account-outline" />
    </n-icon>
    <span class="btn-text">切换</span>
  </div>

  <!-- 账号切换弹窗 -->
  <AccountSwitchModal ref="accountSwitchModalRef" />
</template>

<script setup>
import { ref } from 'vue'
import AccountSwitchModal from './AccountSwitchModal.vue'

const accountSwitchModalRef = ref(null)

// 打开账号切换弹窗
const openAccountSwitchModal = () => {
  accountSwitchModalRef.value?.open()
}
</script>

<style scoped>
.account-switcher-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 6px;
  border-radius: 4px;
  transition: all 0.2s ease;
  color: var(--text-color-3);
  font-size: 11px;
  cursor: pointer;
  border: 1px solid transparent;
  background: rgba(255, 255, 255, 0.05);
}

.account-switcher-btn:hover {
  background-color: var(--primary-color-suppl);
  color: var(--primary-color);
  border-color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-text {
  font-weight: 500;
  white-space: nowrap;
}

/* 深色模式适配 */
.dark .account-switcher-btn {
  color: var(--text-color-3);
  background: rgba(255, 255, 255, 0.08);
}

.dark .account-switcher-btn:hover {
  background-color: var(--primary-color-suppl);
  color: var(--primary-color);
  border-color: var(--primary-color);
}
</style>
