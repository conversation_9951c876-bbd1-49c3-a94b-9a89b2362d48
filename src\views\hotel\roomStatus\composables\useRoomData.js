import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { roomApi } from '../services/roomApi'
import { roomDataTransform } from '../services/roomDataTransform'

/**
 * 房间数据管理组合函数
 * 负责房间数据的获取、缓存、更新和状态管理
 */
export function useRoomData() {
  // 响应式状态
  const loading = ref(false)
  const error = ref(null)
  const rawRoomData = ref([])
  const buildingList = ref([])
  const floorList = ref([])
  const roomTypeList = ref([])
  const roomStatusConfig = ref([])
  const cleanStatusConfig = ref([])
  const lastUpdateTime = ref(null)
  const forceRerenderKey = ref(0)

  // 计算属性 - 转换后的房间数据
  const transformedRoomData = computed(() => {
    if (!rawRoomData.value.length) return []

    try {
      return roomDataTransform.transformRoomData(rawRoomData.value, {
        roomStatusConfig: roomStatusConfig.value,
        cleanStatusConfig: cleanStatusConfig.value
      })
    } catch (err) {
      console.error('房间数据转换失败:', err)
      return []
    }
  })

  // 计算属性 - 按楼栋楼层分组的房间数据
  const groupedRoomData = computed(() => {
    if (!transformedRoomData.value.length) return []

    try {
      return roomDataTransform.groupRoomsByBuildingAndFloor(transformedRoomData.value)
    } catch (err) {
      console.error('房间数据分组失败:', err)
      return []
    }
  })

  // 计算属性 - 房间统计数据
  const roomStats = computed(() => {
    if (!transformedRoomData.value.length) return {
      total: 0,
      occupied: 0,
      available: 0,
      reserved: 0,
      checkout: 0,
      dirty: 0,
      cleaning: 0,
      maintenance: 0,
      blocked: 0
    }

    try {
      return roomDataTransform.calculateRoomStats(transformedRoomData.value)
    } catch (err) {
      console.error('房间统计计算失败:', err)
      return {
        total: 0,
        occupied: 0,
        available: 0,
        reserved: 0,
        checkout: 0,
        dirty: 0,
        cleaning: 0,
        maintenance: 0,
        blocked: 0
      }
    }
  })

  // 获取房间数据
  const fetchRoomData = async (options = {}) => {
    if (loading.value && !options.force) {
      console.log('正在加载中，跳过重复请求')
      return
    }

    loading.value = true
    error.value = null

    try {
      // 并行获取所有必要数据
      const [
        roomDataResult,
        buildingResult,
        roomStatusResult,
        cleanStatusResult
      ] = await Promise.allSettled([
        roomApi.getRoomList(options.filters),
        roomApi.getBuildingList(),
        roomApi.getRoomStatusConfig(),
        roomApi.getCleanStatusConfig()
      ])

      // 处理房间数据
      if (roomDataResult.status === 'fulfilled') {
        const response = roomDataResult.value
        if (response && response.data) {
          // 处理嵌套的楼栋-楼层-房间结构
          let allRooms = []
          let buildingData = []

          if (Array.isArray(response.data)) {
            // 数据是楼栋数组
            buildingData = response.data
          } else if (response.data.list && Array.isArray(response.data.list)) {
            buildingData = response.data.list
          }

          // 提取所有房间数据
          buildingData.forEach(building => {
            if (building.floor_list && Array.isArray(building.floor_list)) {
              building.floor_list.forEach(floor => {
                if (floor.room_list && Array.isArray(floor.room_list)) {
                  floor.room_list.forEach(room => {
                    allRooms.push({
                      ...room,
                      building_id: building.building_number || building.id,
                      building_name: building.building || building.building_name,
                      floor_number: floor.floor_number
                    })
                  })
                }
              })
            }
          })

          rawRoomData.value = allRooms
          // 更新楼栋列表
          buildingList.value = buildingData.map(building => ({
            id: building.building_number || building.id,
            name: building.building || building.building_name,
            building_name: building.building || building.building_name,
            building_number: building.building_number,
            floors: building.floor_list || []
          }))
        } else {
          rawRoomData.value = []
          buildingList.value = []
        }
      } else {
        throw new Error(`获取房间数据失败: ${roomDataResult.reason?.message || '未知错误'}`)
      }

      // 从房间数据中提取楼层和房型信息
      updateFloorAndRoomTypeList()

      // 处理状态配置
      if (roomStatusResult.status === 'fulfilled') {
        const roomStatusData = roomStatusResult.value
        let statusArray = []

        if (Array.isArray(roomStatusData)) {
          statusArray = roomStatusData
        } else if (roomStatusData && roomStatusData.data && Array.isArray(roomStatusData.data)) {
          statusArray = roomStatusData.data
        } else if (roomStatusData && roomStatusData.list && Array.isArray(roomStatusData.list)) {
          statusArray = roomStatusData.list
        }

        // 标准化状态配置数据格式
        roomStatusConfig.value = statusArray.map(status => ({
          id: status.id,
          name: status.status_name || status.name || '未知状态',
          sign: status.sign || status.status_sign || 'unknown',
          color: status.color || '#999999',
          count: 0
        }))
      }

      if (cleanStatusResult.status === 'fulfilled') {
        const cleanStatusData = cleanStatusResult.value
        let statusArray = []

        if (Array.isArray(cleanStatusData)) {
          statusArray = cleanStatusData
        } else if (cleanStatusData && cleanStatusData.data && Array.isArray(cleanStatusData.data)) {
          statusArray = cleanStatusData.data
        } else if (cleanStatusData && cleanStatusData.list && Array.isArray(cleanStatusData.list)) {
          statusArray = cleanStatusData.list
        }

        // 标准化状态配置数据格式
        cleanStatusConfig.value = statusArray.map(status => ({
          id: status.id,
          name: status.status_name || status.name || '未知状态',
          sign: status.sign || status.status_sign || 'unknown',
          color: status.color || '#999999',
          count: 0
        }))
      }

      lastUpdateTime.value = new Date()

      // 强制重新渲染（解决某些缓存问题）
      forceRerenderKey.value++

      console.log('房间数据获取成功:', {
        roomCount: rawRoomData.value.length,
        buildingCount: buildingList.value.length,
        updateTime: lastUpdateTime.value
      })

    } catch (err) {
      console.error('获取房间数据失败:', err)
      error.value = err.message || '获取房间数据失败'

      // 如果是网络错误，保留之前的数据
      if (err.name !== 'NetworkError' && err.code !== 'NETWORK_ERROR') {
        rawRoomData.value = []
      }
    } finally {
      loading.value = false
    }
  }

  // 更新楼层和房型列表
  const updateFloorAndRoomTypeList = () => {
    const floors = new Set()
    const roomTypes = new Set()

    // 确保 rawRoomData.value 是数组
    if (Array.isArray(rawRoomData.value)) {
      rawRoomData.value.forEach(room => {
        if (room.floor_number || room.floorNumber) {
          floors.add(room.floor_number || room.floorNumber)
        }
        if (room.room_type_name || room.roomType) {
          roomTypes.add(room.room_type_name || room.roomType)
        }
      })
    }

    floorList.value = Array.from(floors).sort((a, b) => {
      // 数字排序
      const numA = parseInt(a)
      const numB = parseInt(b)
      if (!isNaN(numA) && !isNaN(numB)) {
        return numA - numB
      }
      // 字符串排序
      return String(a).localeCompare(String(b))
    })

    roomTypeList.value = Array.from(roomTypes).sort()
  }

  // 根据房间ID获取房间详情
  const getRoomById = (roomId) => {
    return transformedRoomData.value.find(room =>
      room.id === roomId ||
      room.room_id === roomId ||
      room.roomId === roomId
    )
  }

  // 根据房间号获取房间详情
  const getRoomByNumber = (roomNumber) => {
    return transformedRoomData.value.find(room =>
      room.roomNumber === roomNumber ||
      room.room_number === roomNumber
    )
  }

  // 刷新单个房间数据
  const refreshRoomData = async (roomId) => {
    try {
      const roomData = await roomApi.getRoomDetail(roomId)
      if (roomData) {
        // 更新本地数据中的对应房间
        const index = rawRoomData.value.findIndex(room =>
          room.id === roomId || room.room_id === roomId
        )
        if (index !== -1) {
          rawRoomData.value[index] = roomData
          forceRerenderKey.value++
        }
      }
    } catch (err) {
      console.error('刷新房间数据失败:', err)
    }
  }

  // 清空数据
  const clearData = () => {
    rawRoomData.value = []
    buildingList.value = []
    floorList.value = []
    roomTypeList.value = []
    error.value = null
    lastUpdateTime.value = null
  }

  // 重试获取数据
  const retryFetch = () => {
    error.value = null
    return fetchRoomData({ force: true })
  }

  return {
    // 状态
    loading,
    error,
    rawRoomData,
    transformedRoomData,
    groupedRoomData,
    roomStats,
    buildingList,
    floorList,
    roomTypeList,
    roomStatusConfig,
    cleanStatusConfig,
    lastUpdateTime,
    forceRerenderKey,

    // 方法
    fetchRoomData,
    getRoomById,
    getRoomByNumber,
    refreshRoomData,
    clearData,
    retryFetch
  }
}
