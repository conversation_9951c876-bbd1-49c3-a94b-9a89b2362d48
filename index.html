<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" href="/favicon.png" />
    <title>%VITE_TITLE%</title>
    <style>
      /* 现代化加载页面样式 */
      .loading-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: 
          linear-gradient(135deg, 
            #fefefe 0%, 
            #f8fafc 25%, 
            #f1f5f9 50%, 
            #f8fafc 75%, 
            #fefefe 100%
          );
        overflow: hidden;
        z-index: 10000;
      }

      /* 背景装饰效果 */
      .loading-container::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: 
          radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.08) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.06) 0%, transparent 50%),
          radial-gradient(circle at 40% 40%, rgba(236, 72, 153, 0.04) 0%, transparent 50%);
        pointer-events: none;
        z-index: -1;
      }

      /* 旋转背景装饰 */
      .loading-container::after {
        content: '';
        position: fixed;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: conic-gradient(from 0deg, transparent, rgba(99, 102, 241, 0.02), transparent, rgba(139, 92, 246, 0.015), transparent);
        animation: rotate 20s linear infinite;
        z-index: -1;
      }

      /* 主加载容器 */
      .initial-loading {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 3rem;
        padding: 3rem;
        background: rgba(255, 255, 255, 0.85);
        border-radius: 32px;
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 12px 48px rgba(15, 23, 42, 0.12);
        animation: slideInUp 0.8s ease-out;
      }

      /* 品牌Logo区域 */
      .brand-loading {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1.5rem;
      }

      .loading-logo {
        position: relative;
        width: 80px;
        height: 80px;
        border-radius: 24px;
        background: linear-gradient(135deg, #6366f1, #8b5cf6);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2.5rem;
        box-shadow: 0 8px 32px rgba(15, 23, 42, 0.10);
        overflow: hidden;
        animation: logoFloat 3s ease-in-out infinite;
      }

      .loading-logo::before {
        content: '';
        position: absolute;
        inset: 0;
        background: linear-gradient(45deg, #ec4899, transparent, #8b9eff);
        opacity: 0.6;
        animation: rotate 6s linear infinite;
      }

      .loading-logo::after {
        content: '';
        position: absolute;
        inset: -4px;
        border-radius: 24px;
        background: linear-gradient(135deg, #6366f1, #8b5cf6);
        opacity: 0;
        animation: logoPulse 3s ease-in-out infinite;
        z-index: -1;
      }

      /* Logo图标 */
      .logo-icon {
        position: relative;
        z-index: 1;
        font-weight: bold;
      }

      /* 现代化进度条 */
      .progress-container {
        width: 280px;
        height: 6px;
        background: rgba(226, 232, 240, 0.5);
        border-radius: 3px;
        overflow: hidden;
        position: relative;
      }

      .progress-bar {
        height: 100%;
        background: linear-gradient(90deg, #6366f1, #8b5cf6, #ec4899);
        border-radius: 3px;
        width: 0%;
        animation: progressLoad 2.5s ease-in-out infinite;
        position: relative;
      }

      .progress-bar::after {
        content: '';
        position: absolute;
        inset: 0;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
        animation: progressShimmer 1.5s ease-in-out infinite;
      }

      /* 加载状态指示器 */
      .loading-status {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
      }

      .loading-text {
        color: #334155;
        font-size: 1.125rem;
        font-weight: 600;
        text-align: center;
        letter-spacing: 0.5px;
        animation: textFade 2s ease-in-out infinite;
      }

      .loading-subtitle {
        color: #64748b;
        font-size: 0.875rem;
        font-weight: 500;
        text-align: center;
        letter-spacing: 0.25px;
        opacity: 0.8;
      }

      /* 加载点动画 */
      .loading-dots {
        display: flex;
        gap: 8px;
        margin-top: 0.5rem;
      }

      .loading-dot {
        width: 8px;
        height: 8px;
        background: #6366f1;
        border-radius: 50%;
        animation: dotBounce 1.4s ease-in-out infinite;
        box-shadow: 0 2px 4px rgba(99, 102, 241, 0.3);
      }

      .loading-dot:nth-child(1) { animation-delay: 0s; }
      .loading-dot:nth-child(2) { animation-delay: 0.2s; }
      .loading-dot:nth-child(3) { animation-delay: 0.4s; }

      /* 装饰粒子效果 */
      .particles {
        position: absolute;
        inset: 0;
        pointer-events: none;
        overflow: hidden;
      }

      .particle {
        position: absolute;
        width: 4px;
        height: 4px;
        background: rgba(99, 102, 241, 0.4);
        border-radius: 50%;
        animation: particleFloat 8s linear infinite;
      }

      .particle:nth-child(1) { left: 10%; animation-delay: 0s; }
      .particle:nth-child(2) { left: 20%; animation-delay: 1s; }
      .particle:nth-child(3) { left: 30%; animation-delay: 2s; }
      .particle:nth-child(4) { left: 40%; animation-delay: 3s; }
      .particle:nth-child(5) { left: 50%; animation-delay: 4s; }
      .particle:nth-child(6) { left: 60%; animation-delay: 1.5s; }
      .particle:nth-child(7) { left: 70%; animation-delay: 2.5s; }
      .particle:nth-child(8) { left: 80%; animation-delay: 3.5s; }

      /* 版本信息 */
      .version-info {
        position: absolute;
        bottom: 2rem;
        color: #94a3b8;
        font-size: 0.75rem;
        font-weight: 500;
        letter-spacing: 0.5px;
        opacity: 0.6;
      }

      /* ========== 动画定义 ========== */
      @keyframes rotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
      }

      @keyframes slideInUp {
        from {
          opacity: 0;
          transform: translateY(40px) scale(0.95);
        }
        to {
          opacity: 1;
          transform: translateY(0) scale(1);
        }
      }

      @keyframes logoFloat {
        0%, 100% {
          transform: translateY(0px) scale(1);
        }
        50% {
          transform: translateY(-10px) scale(1.05);
        }
      }

      @keyframes logoPulse {
        0%, 100% {
          opacity: 0;
          transform: scale(1);
        }
        50% {
          opacity: 0.3;
          transform: scale(1.1);
        }
      }

      @keyframes progressLoad {
        0% {
          width: 0%;
        }
        50% {
          width: 70%;
        }
        100% {
          width: 95%;
        }
      }

      @keyframes progressShimmer {
        0% {
          transform: translateX(-100%);
        }
        100% {
          transform: translateX(200%);
        }
      }

      @keyframes textFade {
        0%, 100% {
          opacity: 1;
        }
        50% {
          opacity: 0.7;
        }
      }

      @keyframes dotBounce {
        0%, 60%, 100% {
          transform: translateY(0) scale(1);
          opacity: 0.7;
        }
        30% {
          transform: translateY(-12px) scale(1.2);
          opacity: 1;
        }
      }

      @keyframes particleFloat {
        0% {
          transform: translateY(100vh) scale(0);
          opacity: 0;
        }
        10% {
          opacity: 1;
          transform: scale(1);
        }
        90% {
          opacity: 1;
        }
        100% {
          transform: translateY(-100vh) scale(0);
          opacity: 0;
        }
      }

      /* ========== 响应式设计 ========== */
      @media (max-width: 768px) {
        .initial-loading {
          gap: 2rem;
          padding: 2rem;
          border-radius: 24px;
        }

        .loading-logo {
          width: 64px;
          height: 64px;
          font-size: 2rem;
        }

        .progress-container {
          width: 240px;
        }

        .loading-text {
          font-size: 1rem;
        }

        .loading-subtitle {
          font-size: 0.8rem;
        }
      }

      @media (max-width: 480px) {
        .initial-loading {
          gap: 1.5rem;
          padding: 1.5rem;
          border-radius: 20px;
          margin: 1rem;
        }

        .loading-logo {
          width: 56px;
          height: 56px;
          font-size: 1.75rem;
        }

        .progress-container {
          width: 200px;
          height: 4px;
        }

        .loading-text {
          font-size: 0.9rem;
        }

        .loading-subtitle {
          font-size: 0.75rem;
        }

        .loading-dot {
          width: 6px;
          height: 6px;
        }
      }

      /* 深色模式支持 */
      @media (prefers-color-scheme: dark) {
        .loading-container {
          background: 
            linear-gradient(135deg, 
              #0f172a 0%, 
              #1e293b 25%, 
              #334155 50%, 
              #1e293b 75%, 
              #0f172a 100%
            );
        }

        .initial-loading {
          background: rgba(30, 41, 59, 0.85);
          border-color: rgba(71, 85, 105, 0.3);
        }

        .loading-text {
          color: #f1f5f9;
        }

        .loading-subtitle {
          color: #94a3b8;
        }

        .progress-container {
          background: rgba(71, 85, 105, 0.3);
        }
      }

      /* 减少动画偏好设置 */
      @media (prefers-reduced-motion: reduce) {
        .loading-logo,
        .loading-container::after,
        .loading-logo::before,
        .loading-logo::after,
        .particle {
          animation: none;
        }

        .progress-bar {
          animation: none;
          width: 70%;
        }

        .loading-dot {
          animation: none;
        }
      }
    </style>
  </head>
  <body class="dark:text-#e9e9e9 auto-bg">
    <div id="app">
      <div class="loading-container">
        <!-- 粒子装饰效果 -->
        <div class="particles">
          <div class="particle"></div>
          <div class="particle"></div>
          <div class="particle"></div>
          <div class="particle"></div>
          <div class="particle"></div>
          <div class="particle"></div>
          <div class="particle"></div>
          <div class="particle"></div>
        </div>

        <div class="initial-loading">
          <!-- 品牌Logo区域 -->
          <div class="brand-loading">
            <div class="loading-logo">
              <span class="logo-icon">🏨</span>
            </div>
          </div>

          <!-- 进度条 -->
          <div class="progress-container">
            <div class="progress-bar"></div>
          </div>

          <!-- 加载状态 -->
          <div class="loading-status">
            <div class="loading-text">正在启动酒店管理系统</div>
            <div class="loading-subtitle">为您准备最佳管理体验</div>
            <div class="loading-dots">
              <div class="loading-dot"></div>
              <div class="loading-dot"></div>
              <div class="loading-dot"></div>
            </div>
          </div>
        </div>

        <!-- 版本信息 -->
        <div class="version-info">PMS Hotel Management System v3.0</div>
      </div>
    </div>

    <script>
      // 现代化加载动画管理
      window.hideInitialLoading = function() {
        const loadingContainer = document.querySelector('.loading-container');
        if (loadingContainer) {
          // 添加淡出动画
          loadingContainer.style.transition = 'opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1), transform 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
          loadingContainer.style.opacity = '0';
          loadingContainer.style.transform = 'scale(0.95)';

          setTimeout(() => {
            loadingContainer.remove();
          }, 800);
        }
      };

      // 监听Vue应用挂载完成
      window.addEventListener('app-mounted', () => {
        setTimeout(() => {
          window.hideInitialLoading();
        }, 500); // 适当延迟，确保用户能看到完整的加载动画
      });

      // 备用方案：如果Vue应用在6秒内没有触发事件，自动隐藏
      setTimeout(() => {
        if (document.querySelector('.loading-container')) {
          window.hideInitialLoading();
        }
      }, 6000);

      // 动态进度条效果
      document.addEventListener('DOMContentLoaded', () => {
        const progressBar = document.querySelector('.progress-bar');
        if (progressBar) {
          let progress = 0;
          const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress > 90) {
              progress = 90;
              clearInterval(interval);
            }
            progressBar.style.width = progress + '%';
          }, 200);
        }
      });
    </script>

    <script type="module" src="/src/main.js"></script>
  </body>
</html>