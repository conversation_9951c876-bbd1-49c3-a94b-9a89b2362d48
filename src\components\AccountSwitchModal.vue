<template>
  <n-modal
    v-model:show="showModal"
    preset="card"
    title="切换账号"
    style="width: 500px"
    :mask-closable="false"
  >
    <div class="account-switch-modal">
      <!-- 当前账号 -->
      <div class="current-account-section">
        <h4 class="section-title">当前账号</h4>
        <div class="account-item current">
          <div class="account-avatar">
            <n-avatar
              round
              :size="40"
              :style="{ background: currentAccountBg }"
            >
              {{ currentAccountText }}
            </n-avatar>
            <div class="current-badge">当前</div>
          </div>
          <div class="account-info">
            <div class="account-name">{{ currentAccount.nickname || currentAccount.name }}</div>
            <div class="account-detail">{{ currentAccount.shop_name }}</div>
          </div>
        </div>
      </div>

      <!-- 返回总店按钮 -->
      <div v-if="showBackToMain" class="back-to-main-section">
        <n-button
          @click="handleBackToMain"
          type="primary"
          ghost
          block
          class="back-to-main-btn"
        >
          <template #icon>
            <n-icon>
              <i class="i-material-symbols:arrow-back" />
            </n-icon>
          </template>
          返回总店
        </n-button>
      </div>

      <!-- 可切换账号 -->
      <div v-if="childAccounts.length > 0" class="child-accounts-section">
        <h4 class="section-title">可切换账号</h4>
        <div class="accounts-list">
          <div
            v-for="account in childAccounts"
            :key="account.id"
            :class="['account-item', { 'clickable': !account.isCurrent, 'current-account': account.isCurrent }]"
            @click="account.isCurrent ? null : handleSwitchAccount(account)"
          >
            <div class="account-avatar">
              <n-avatar
                round
                :size="40"
                :style="{ background: getAccountBg(account.name) }"
              >
                {{ getAccountText(account.name) }}
              </n-avatar>
            </div>
            <div class="account-info">
              <div class="account-name">
                {{ account.nickname || account.name }}
                <n-tag v-if="account.isCurrent" type="success" size="small" style="margin-left: 8px;">
                  当前
                </n-tag>
                <n-tag v-if="account.isMainAccount" type="info" size="small" style="margin-left: 8px;">
                  主账号
                </n-tag>
              </div>
              <div class="account-detail">{{ account.shop_name }}</div>
            </div>
            <div class="switch-icon">
              <n-icon v-if="!account.isCurrent" size="18">
                <i class="i-material-symbols:arrow-forward-ios" />
              </n-icon>
              <n-icon v-else size="18" style="color: #18a058;">
                <i class="i-material-symbols:check-circle" />
              </n-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- 无可切换账号 -->
      <div v-else class="no-accounts">
        <n-empty description="暂无可切换的账号" />
      </div>
    </div>

    <template #footer>
      <div class="modal-footer">
        <n-button @click="showModal = false">取消</n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useMessage } from 'naive-ui'
import { useUserStore, usePermissionStore } from '@/store'
import { toggleAdmin } from '@/api/admin'
import { updateLoginDataNow } from '@/composables/useLoginData'
import {
  getAvailableAccounts,
  getCurrentAccount,
  getMainAccount,
  updateCurrentAccountId,
  getAccountById,
  hasChildAccounts,
  isCurrentChildAccount
} from '@/utils/accountsCache'

const message = useMessage()
const userStore = useUserStore()
const permissionStore = usePermissionStore()

const showModal = ref(false)

// 获取当前账号信息 - 使用账号缓存
const currentAccount = computed(() => {
  const account = getCurrentAccount()
  return account || {
    id: 0,
    name: '未知账号',
    nickname: '未知账号',
    shop_name: '未知店铺',
  }
})

// 获取主账号信息 - 使用账号缓存
const mainAccount = computed(() => {
  const account = getMainAccount()
  return account || currentAccount.value
})

// 是否显示返回总店按钮 - 使用账号缓存
const showBackToMain = computed(() => {
  return isCurrentChildAccount()
})

// 获取可切换账号列表 - 使用账号缓存
const childAccounts = computed(() => {
  const accounts = getAvailableAccounts()
  // 过滤掉当前账号，只显示可切换的账号
  return accounts.filter(account => !account.isCurrent)
})

// 当前账号背景色
const currentAccountBg = computed(() => {
  return getAccountBg(currentAccount.value.name)
})

// 当前账号文字
const currentAccountText = computed(() => {
  return getAccountText(currentAccount.value.name)
})

// 获取账号背景色
const getAccountBg = (name) => {
  const colors = [
    'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
    'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
    'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
    'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)'
  ]
  const index = (name || '用户').charCodeAt(0) % colors.length
  return colors[index]
}

// 获取账号文字
const getAccountText = (name) => {
  return (name || '用户').charAt(0).toUpperCase()
}

// 处理账号切换
const handleSwitchAccount = async (account) => {
  try {
    message.loading('正在切换账号...', { duration: 0 })

    // 调用 toggleAdmin 接口
    await switchToAccount(account)

    message.destroyAll()
    message.success(`已切换到：${account.nickname || account.name}`)

    showModal.value = false

    // 不需要刷新页面，响应式数据已更新

  } catch (error) {
    message.destroyAll()
    message.error('切换账号失败：' + (error.message || '未知错误'))

  }
}

// 处理返回总店 - 使用账号缓存
const handleBackToMain = async () => {
  try {
    message.loading('正在返回总店...', { duration: 0 })

    const mainAccountData = getMainAccount()
    if (!mainAccountData) {
      throw new Error('无法获取总店账号信息')
    }

    await switchToAccount(mainAccountData)

    message.destroyAll()
    message.success(`已返回总店：${mainAccountData.nickname || mainAccountData.name}`)

    showModal.value = false

  } catch (error) {
    message.destroyAll()
    message.error('返回总店失败：' + (error.message || '未知错误'))

  }
}

// 切换到指定账号 - 使用账号缓存
const switchToAccount = async (account) => {
  const loginData = JSON.parse(localStorage.getItem('loginResponseData') || '{}')

  try {
    // 调用 toggleAdmin 接口
    const response = await toggleAdmin({
      admin_id: loginData.admin_id,
      to_admin_id: account.id
    })

    // 处理切换后的账号数据 - 使用账号缓存
    let updatedLoginData
    let newAuthList = account.auth_list || []

    if (response && response.data) {
      // 接口返回了新数据，合并使用
      updatedLoginData = {
        ...loginData,
        ...response.data,
        admin_id: account.id,
        admin_name: account.name,
        nickname: account.nickname,
        shop_name: account.shop_name,
        auth_list: response.data.auth_list || account.auth_list || []
      }
      newAuthList = response.data.auth_list || account.auth_list || []
    } else {
      // 接口调用失败或无返回，使用缓存数据
      updatedLoginData = {
        ...loginData,
        admin_id: account.id,
        admin_name: account.name,
        nickname: account.nickname,
        shop_name: account.shop_name,
        auth_list: account.auth_list || []
      }
      newAuthList = account.auth_list || []
    }

    // 保存更新后的登录数据
    localStorage.setItem('loginResponseData', JSON.stringify(updatedLoginData))

    // 更新账号缓存中的当前账号ID
    updateCurrentAccountId(account.id)

    // 立即更新响应式登录数据
    updateLoginDataNow()

    // 更新用户信息 - 确保左上角显示正确
    const userInfo = {
      id: account.id,
      username: account.name,
      nickname: account.nickname,
      shopName: account.shop_name,
      adminId: account.id, // 添加adminId字段
    }

    userStore.setUser(userInfo)

    // 强制更新用户store中的相关字段
    if (userStore.userInfo) {
      userStore.userInfo.id = account.id
      userStore.userInfo.username = account.name
      userStore.userInfo.nickname = account.nickname
      userStore.userInfo.shopName = account.shop_name
      userStore.userInfo.adminId = account.id
    }

    // 更新权限系统
    if (newAuthList && Array.isArray(newAuthList)) {
      permissionStore.setPermissions(newAuthList)
    } else {

      // 重新初始化权限系统
      await permissionStore.initPermissions()
    }

    return Promise.resolve(response)
  } catch (error) {

    // 接口调用失败时，直接抛出错误，不使用模拟数据
    throw new Error(`账号切换失败: ${error.message || '网络错误'}`)
  }
}

// 打开弹窗
const open = () => {
  showModal.value = true
}

// 暴露方法
defineExpose({
  open
})
</script>

<style scoped>
.account-switch-modal {
  padding: 8px 0;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color-1);
  margin: 0 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color);
}

.current-account-section {
  margin-bottom: 16px;
}

.back-to-main-section {
  margin-bottom: 24px;
}

.back-to-main-btn {
  border-style: dashed !important;
  transition: all 0.3s ease;
}

.back-to-main-btn:hover {
  border-style: solid !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--primary-color), 0.3);
}

.child-accounts-section {
  margin-bottom: 16px;
}

.account-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  transition: all 0.2s ease;
  position: relative;
}

.account-item.current {
  background: var(--primary-color-suppl);
  border: 1px solid var(--primary-color);
}

.account-item.clickable {
  cursor: pointer;
  border: 1px solid var(--border-color);
}

.account-item.clickable:hover {
  background: var(--hover-color);
  border-color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.account-avatar {
  position: relative;
  margin-right: 12px;
}

.current-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: var(--primary-color);
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
}

.account-info {
  flex: 1;
  min-width: 0;
}

.account-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color-1);
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.account-detail {
  font-size: 12px;
  color: var(--text-color-3);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.switch-icon {
  color: var(--text-color-3);
  opacity: 0.6;
  transition: all 0.2s ease;
}

.account-item.clickable:hover .switch-icon {
  opacity: 1;
  color: var(--primary-color);
}

.accounts-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.no-accounts {
  padding: 40px 20px;
  text-align: center;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 当前账号样式 */
.account-item.current-account {
  background: rgba(24, 160, 88, 0.05);
  border: 1px solid rgba(24, 160, 88, 0.2);
  cursor: default;
}

.account-item.current-account:hover {
  background: rgba(24, 160, 88, 0.05);
  transform: none;
}

.account-item.current-account .switch-icon {
  color: #18a058;
  opacity: 1;
}

/* 暗色主题适配 */
.dark .account-item.current {
  background: rgba(var(--primary-color), 0.2);
}

.dark .account-item.clickable {
  border-color: var(--border-color);
}

.dark .account-item.clickable:hover {
  background: var(--hover-color);
  border-color: var(--primary-color);
}
</style>
