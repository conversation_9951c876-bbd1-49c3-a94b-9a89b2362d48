<template>
  <n-modal
    v-model:show="show"
    preset="card"
    :style="{ width: '90vw', maxWidth: '1400px', height: '90vh', display: 'flex', flexDirection: 'column' }"
    :content-style="{ flex: 1, overflow: 'auto' }"
    :title="modalTitle"
    :mask-closable="false"
    class="fixed-footer-modal"
  >


    <div class="modal-content compact-layout">
      <n-form :model="form" label-placement="left" label-width="100" :rules="rules" ref="formRef" size="small">
        <!-- 销售信息 -->
        <n-card title="销售信息" size="small" class="compact-card">
          <n-grid :cols="24" :x-gap="6" :y-gap="1">
            <n-gi :span="5">
              <n-form-item label="销售类型" path="sellType" required>
                <n-select
                  v-model:value="form.sellType"
                  :options="sellTypeOptions"
                  :loading="isLoadingSellTypes"
                  placeholder="请选择销售类型"
                  size="small"
                  clearable
                  virtual-scroll
                  :show-arrow="false"
                  :max-tag-count="1"
                  @update:value="handleSellTypeChange"
                />
              </n-form-item>
            </n-gi>
            <n-gi :span="5">
              <n-form-item label="销售规则" path="saleRule" required>
                <n-select
                  v-model:value="form.saleRule"
                  :options="saleRuleOptions"
                  :loading="isLoadingSaleRules"
                  placeholder="请选择销售规则"
                  clearable
                  virtual-scroll
                  :show-arrow="false"
                  :max-tag-count="1"
                  @update:value="handleSaleRuleChange"
                />
              </n-form-item>
            </n-gi>
            <n-gi :span="4">
              <n-form-item label="入住类型" path="checkInType" required>
                <n-select
                  v-model:value="form.checkInType"
                  :options="checkInTypeOptions"
                  placeholder="请选择入住类型"
                />
              </n-form-item>
            </n-gi>
                <n-gi :span="4">
              <n-form-item label="保密房" path="isSecret">
                <n-switch v-model:value="form.isSecret" />
              </n-form-item>
            </n-gi>
            <n-gi :span="6">
              <n-form-item label="离店时间" path="checkOutTime">
                <n-date-picker
                  v-model:value="form.checkOutTime"
                  type="datetime"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="timestamp"
                  style="width: 100%;"
                  size="small"
                  :readonly="form.isStayDurationReadonly"
                  :disabled="form.isStayDurationReadonly"
                />
              </n-form-item>
            </n-gi>

            <!-- 第二排开始 -->

                 <n-gi :span="5">
              <n-form-item label="入住时长" path="stayDuration" required>
                <n-input-number
                  v-model:value="form.stayDuration"
                  :min="1"
                  :readonly="form.isStayDurationReadonly"
                  :disabled="form.isStayDurationReadonly"
                  style="width: 100%;"
                  size="small"
                  :placeholder="`请输入入住${durationUnit}`"
                  @update:value="handleStayDurationChange"
                >
                  <template #suffix>{{ durationUnit }}</template>
                </n-input-number>
              </n-form-item>
            </n-gi>
            <n-gi :span="5">
              <n-form-item label="价格方案" path="priceScheme" required>
                <n-select
                  v-model:value="form.priceScheme"
                  :options="priceSchemeOptions"
                  placeholder="请选择价格方案"
                  @update:value="handlePriceSchemeChange"
                />
              </n-form-item>
            </n-gi>
            <n-gi :span="6" v-if="showMemberGradeSelect">
              <n-form-item label="会员等级" path="memberGrade" required>
                <n-select
                  v-model:value="form.memberGrade"
                  :options="memberGradeOptions"
                  :loading="isLoadingMemberGrades"
                  placeholder="请选择会员等级"
                  clearable
                  @update:value="handleMemberGradeChange"
                />
              </n-form-item>
            </n-gi>
            <n-gi :span="6" v-if="showIntermediarySelect">
              <n-form-item :label="intermediaryLabel" path="intermediary">
                <n-select
                  v-model:value="form.intermediary"
                  :options="intermediaryOptions"
                  placeholder="请选择"
                />
              </n-form-item>
            </n-gi>
            <n-gi :span="5">
              <n-form-item label="订单来源" path="orderSource" required>
                <n-select
                  v-model:value="form.orderSource"
                  :options="orderSourceOptions"
                  placeholder="请选择订单来源"
                />
              </n-form-item>
            </n-gi>
            <n-gi :span="8">
              <n-form-item label="备注" path="remark">
                <n-input
                  v-model:value="form.remark"
                  type="textarea"
                  placeholder="可填写特殊要求等"
                  :rows="2"
                  size="small"
                />
              </n-form-item>
            </n-gi>

          </n-grid>
          <!-- 提示信息 -->
          <div v-if="form.isStayDurationReadonly" style="font-size: 12px; color: #ef4444; margin-top: 2px; line-height: 1.3;">
            此销售规则入住时长固定为 {{ form.stayDuration }} {{ durationUnit }}，离店时间根据入住时长自动计算
          </div>
        </n-card>

        <!-- 客户信息 -->
        <n-card title="客户信息" class="compact-card">
          <n-grid :cols="24" :x-gap="8" :y-gap="4">
            <n-gi :span="8">
              <n-form-item label="联系人姓名" path="contactName" required>
                <n-auto-complete
                  ref="contactNameAutoCompleteRef"
                  v-model:value="form.contactName"
                  :options="memberOptions"
                  placeholder="请输入联系人名字"
                  size="small"
                  @update:value="handleContactNameChange"
                  @select="handleMemberSelect"
                  :render-option="renderMemberOption"
                  :max-height="200"
                  style="width: 100%;"
                  :menu-props="{ style: 'min-width: 400px;' }"
                />
              </n-form-item>
            </n-gi>
            <n-gi :span="8">
              <n-form-item label="联系人电话" path="contactPhone" required>
                <div class="contact-phone-row">
                  <n-auto-complete
                    ref="contactPhoneAutoCompleteRef"
                    v-model:value="form.contactPhone"
                    :options="phoneOptions"
                    placeholder="请输入手机号"
                    size="small"
                    class="phone-input"
                    @update:value="handlePhoneChange"
                    @select="handlePhoneSelect"
                    :render-option="renderMemberOption"
                    :max-height="200"
                    :menu-props="{ style: 'min-width: 400px;' }"
                  />
                  <n-button size="small" type="primary" class="read-card-btn" @click="handleReadIdCard">读卡</n-button>
                </div>
              </n-form-item>
            </n-gi>
            <n-gi :span="6">
              <n-form-item label="外部订单号" path="externalOrderNo">
                <n-input v-model:value="form.externalOrderNo" placeholder="请输入外部订单号" size="small" />
              </n-form-item>
            </n-gi>
          </n-grid>

          <!-- 客史记录和优惠券 -->
          <div class="member-info-section" style="margin-top: 6px; padding-top: 6px; border-top: 1px solid #e0e0e6;">
            <div v-if="memberInfo" class="member-summary" style="margin-bottom: 8px;">
              <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 6px;">
                <span
                  class="member-name"
                  style="font-weight: 600; cursor: pointer; color: #18a058; text-decoration: underline;"
                  @click="handleViewMemberInfo"
                  :title="'点击查看会员详细信息'"
                >
                  {{ memberInfo.name || memberInfo.nickname || '会员' }}
                </span>
                <n-tag size="small" type="info">{{ memberInfo.grade_name || '普通会员' }}</n-tag>
                <span class="member-balance" style="color: #18a058;">余额: ¥{{ memberInfo.balance || 0 }}</span>
              </div>
              <!-- 显示入住偏好信息 -->
              <div v-if="memberDetailInfo && memberDetailInfo.stay" style="font-size: 12px; color: #666; display: flex; gap: 12px;">
                <span>入住{{ memberDetailInfo.stay.stay_count || 0 }}次</span>
                <span>平均房价: ¥{{ memberDetailInfo.stay.avg_price || 0 }}</span>
                <span v-if="memberDetailInfo.stay.favorite_room_type">偏好房型: {{ memberDetailInfo.stay.favorite_room_type }}</span>
                <span v-if="memberDetailInfo.stay.favorite_room">偏好房间: {{ memberDetailInfo.stay.favorite_room }}</span>
              </div>
            </div>
            <div class="member-actions" style="display: flex; gap: 8px;">
              <n-button size="small" @click="showHistoryModal = true">
                入住统计 ({{ historyCount }})
              </n-button>
              <n-button size="small" @click="showCouponModal = true">
                可用优惠券 ({{ couponCount }})
              </n-button>
            </div>
          </div>
        </n-card>



        <!-- 房间信息 -->
        <n-card title="房间信息" size="small" :bordered="true" class="compact-card">
          <template #header-extra>
            <n-button type="primary" size="small" @click="handleAddRoom">
              <template #icon><i class="i-carbon:add-alt" /></template>
              添加房间
            </n-button>
          </template>

          <div class="rooms-container">
            <div v-for="(room, roomIndex) in form.roomsToBook" :key="room.id" class="room-card">
              <!-- 房间序号标识 -->
              <div class="room-index">{{ roomIndex + 1 }}</div>

              <!-- 房间基本信息 -->
              <div class="room-header">
                <div class="room-info">
                  <span class="room-number">{{ room.roomNumber }}</span>
                  <span class="room-type">{{ room.roomTypeName }}</span>
                  <div class="room-summary">
                    <span class="price-summary">¥{{ room.price || 0 }}</span>
                    <span class="guest-summary">{{ room.guests?.length || 0 }}人</span>
                  </div>
                </div>
                <div class="room-actions">
                  <n-button size="tiny" type="primary" ghost @click="handleChangeRoom(roomIndex)">换房</n-button>
                  <n-button
                    size="tiny"
                    type="error"
                    ghost
                    :disabled="form.roomsToBook.length <= 1"
                    @click="handleRemoveRoom(roomIndex)"
                  >
                    删除
                  </n-button>
                </div>
              </div>

              <!-- 房间价格信息 -->
              <div class="room-pricing">
                <n-grid :cols="3" :x-gap="6">
                  <n-gi>
                    <n-form-item label="房价" size="small">
                      <div v-if="!room.priceLoading" style="display: flex; align-items: center; gap: 8px;">
                        <n-input-number
                          v-model:value="room.price"
                          :min="0"
                          :precision="2"
                          size="small"
                          style="flex: 1"
                        >
                          <template #suffix>元</template>
                        </n-input-number>
                        <!-- 多天价格编辑按钮 -->
                        <n-button
                          v-if="room.dailyPrices && room.dailyPrices.length > 1"
                          size="small"
                          type="primary"
                          ghost
                          @click="openDailyPriceEditor(roomIndex)"
                          title="编辑每日价格"
                        >
                          编辑
                        </n-button>


                      </div>
                      <n-skeleton v-else height="32px" />
                    </n-form-item>
                  </n-gi>
                  <n-gi>
                    <n-form-item label="押金" size="small">
                      <n-input-number
                        v-model:value="room.deposit"
                        :min="0"
                        :precision="2"
                        size="small"
                        style="width: 100%"
                      >
                        <template #suffix>元</template>
                      </n-input-number>
                    </n-form-item>
                  </n-gi>
                  <n-gi>
                    <n-form-item label="套餐" size="small">
                      <n-select
                        v-if="!room.priceLoading"
                        v-model:value="room.packageId"
                        :options="room.packageOptions || []"
                        placeholder="选择套餐"
                        size="small"
                        @update:value="(value) => updateRoomPackagePrice(roomIndex, value)"
                      />
                      <n-skeleton v-else height="32px" />
                    </n-form-item>
                  </n-gi>
                </n-grid>

                <!-- 多天价格预览 -->
                <div v-if="room.dailyPrices && room.dailyPrices.length > 1" class="daily-prices-preview">
                  <n-text depth="3" style="font-size: 12px;">
                    共{{ room.dailyPrices.length }}天，总计: ¥{{ calculateTotalDailyPrice(room.dailyPrices) }}
                  </n-text>
                </div>
              </div>

              <!-- 入住人信息 -->
              <div class="guests-section">
                <div class="guests-header">
                  <span class="guests-title">入住人信息</span>
                  <n-button size="tiny" type="primary" dashed @click="addGuest(roomIndex)">
                    <template #icon><i class="i-carbon:add" /></template>
                    添加入住人
                  </n-button>
                </div>

                <div class="guests-list">
                  <div v-for="(guest, guestIndex) in room.guests" :key="guest.id" class="guest-item">
                    <n-grid :cols="24" :x-gap="6" :y-gap="2">
                      <n-gi :span="4">
                        <n-auto-complete
                          v-model:value="guest.name"
                          :options="memberOptions"
                          placeholder="姓名"
                          size="small"
                          clearable
                          @select="(value, option) => handleGuestMemberSelect(roomIndex, guestIndex, option)"
                          @update:value="(value) => handleGuestNameSearch(roomIndex, guestIndex, value)"
                          :render-option="renderGuestMemberOption"
                          :menu-props="{ style: 'min-width: 500px; max-height: 300px;' }"
                        />
                      </n-gi>
                      <n-gi :span="3">
                        <n-select
                          v-model:value="guest.gender"
                          :options="[
                            { label: '男', value: '男' },
                            { label: '女', value: '女' }
                          ]"
                          size="small"
                        />
                      </n-gi>
                      <n-gi :span="4">
                        <n-auto-complete
                          v-model:value="guest.phone"
                          :options="memberOptions"
                          placeholder="手机号"
                          size="small"
                          clearable
                          @select="(value, option) => handleGuestMemberSelect(roomIndex, guestIndex, option)"
                          @update:value="(value) => handleGuestPhoneSearch(roomIndex, guestIndex, value)"
                          :render-option="renderGuestMemberOption"
                          :menu-props="{ style: 'min-width: 500px; max-height: 300px;' }"
                        />
                      </n-gi>
                      <n-gi :span="3">
                        <n-select
                          v-model:value="guest.idType"
                          :options="[
                            { label: '身份证', value: '身份证' },
                            { label: '护照', value: '护照' },
                            { label: '港澳通行证', value: '港澳通行证' },
                            { label: '台湾通行证', value: '台湾通行证' }
                          ]"
                          size="small"
                        />
                      </n-gi>
                      <n-gi :span="6">
                        <n-input
                          v-model:value="guest.idNumber"
                          placeholder="请输入证件号"
                          size="small"
                        />
                      </n-gi>
                      <n-gi :span="2">
                        <n-button size="small" type="primary" ghost>证</n-button>
                      </n-gi>
                      <n-gi :span="1">
                        <n-button
                          size="small"
                          type="error"
                          ghost
                          :disabled="room.guests.length <= 1"
                          @click="removeGuest(roomIndex, guestIndex)"
                        >
                          <template #icon><i class="i-carbon:subtract" /></template>
                        </n-button>
                      </n-gi>
                      <n-gi :span="1">
                        <n-button size="small" type="success" ghost @click="addGuest(roomIndex, guestIndex + 1)">
                          <template #icon><i class="i-carbon:add" /></template>
                        </n-button>
                      </n-gi>
                    </n-grid>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </n-card>

      </n-form>
    </div>
    <template #footer>
      <div class="modal-footer">
        <!-- 总应收款显示 -->
        <div class="footer-amount-display">
          <div class="amount-main">
            <span class="amount-label">总应收款</span>
            <span class="amount-value">¥{{ totalAmount.toFixed(2) }}</span>
          </div>
          <div class="amount-breakdown">
            <span class="room-price">房费: ¥{{ totalRoomPrice.toFixed(2) }}</span>
            <span class="deposit">押金: ¥{{ totalDeposit.toFixed(2) }}</span>
            <span v-if="totalPackagePrice > 0" class="package">套餐: ¥{{ totalPackagePrice.toFixed(2) }}</span>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="footer-actions">
          <n-button @click="show = false">取消</n-button>
          <n-button type="primary" :loading="loading" @click="handleSubmit">确认入住</n-button>
        </div>
      </div>
    </template>

    <!-- 房间选择组件 -->
    <RoomSelector
      v-model:show="showRoomSelect"
      :check-in-info="checkInInfo"
      @confirm="handleRoomSelectorConfirm"
      @cancel="handleRoomSelectorCancel"
    />
  </n-modal>

  <!-- 客史记录弹窗 -->
  <n-modal v-model:show="showHistoryModal" preset="card" title="入住统计信息" style="width: 800px; max-width: 95vw;">
    <n-data-table
      :columns="historyColumns"
      :data="historyList"
      :loading="historyLoading"
      size="small"
      :pagination="false"
      max-height="400px"
    />
  </n-modal>

  <!-- 优惠券弹窗 -->
  <n-modal v-model:show="showCouponModal" preset="card" title="可用优惠券" style="width: 900px; max-width: 95vw;">
    <n-data-table
      :columns="couponColumns"
      :data="couponList"
      :loading="couponLoading"
      size="small"
      :pagination="false"
      max-height="400px"
      :row-props="couponRowProps"
    />
  </n-modal>

  <!-- 会员信息弹窗 -->
  <MemberInfoModal
    :show="memberInfoModal.show"
    :common-code="memberInfoModal.commonCode"
    @update:show="memberInfoModal.show = $event"
  />

  <!-- 每日价格编辑弹窗 -->
  <n-modal
    v-model:show="dailyPriceModal.show"
    preset="card"
    title="编辑每日价格"
    style="width: 600px; max-height: 80vh;"
    :mask-closable="false"
  >
    <div v-if="dailyPriceModal.roomData" class="daily-price-editor">
      <div class="room-info">
        <n-text strong>房间：{{ dailyPriceModal.roomData.roomNumber }}</n-text>
        <n-text depth="3" style="margin-left: 12px;">
          共{{ dailyPriceModal.prices.length }}天
        </n-text>
      </div>

      <n-divider />

      <div class="price-list">
        <div
          v-for="(dayPrice, index) in dailyPriceModal.prices"
          :key="index"
          class="price-item"
        >
          <div class="date-label">
            <n-text>{{ formatDate(dayPrice.date) }}</n-text>
            <n-text depth="3" style="font-size: 12px;">
              {{ getDayOfWeek(dayPrice.date) }}
            </n-text>
          </div>
          <div class="price-input">
            <n-input-number
              v-model:value="dayPrice.price"
              :min="0"
              :precision="2"
              size="small"
              style="width: 120px"
            >
              <template #suffix>元</template>
            </n-input-number>
          </div>
        </div>
      </div>

      <n-divider />

      <div class="price-summary">
        <n-text>总计：¥{{ calculateTotalPrice(dailyPriceModal.prices) }}</n-text>
        <n-text depth="3" style="margin-left: 12px;">
          平均：¥{{ calculateAveragePrice(dailyPriceModal.prices) }}
        </n-text>
      </div>
    </div>

    <template #footer>
      <div style="display: flex; justify-content: space-between;">
        <n-button @click="resetDailyPrices">重置为统一价格</n-button>
        <div>
          <n-button @click="dailyPriceModal.show = false">取消</n-button>
          <n-button type="primary" @click="saveDailyPrices" style="margin-left: 8px;">
            保存
          </n-button>
        </div>
      </div>
    </template>
  </n-modal>
</template>

<style scoped>
/* 底部固定总应收款样式 */
.modal-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  padding: 16px 24px;
  background: #fafafa;
  border-top: 1px solid #e0e0e6;
  flex-wrap: wrap;
}

.footer-amount-display {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
  min-width: 0;
}

.amount-main {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.amount-label {
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

.amount-value {
  color: #18a058;
  font-size: 22px;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(24, 160, 88, 0.1);
}

.amount-breakdown {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: #999;
  flex-wrap: wrap;
}

.amount-breakdown span {
  background: #f0f0f0;
  padding: 4px 10px;
  border-radius: 6px;
  white-space: nowrap;
  border: 1px solid #e0e0e6;
  transition: all 0.2s ease;
}

.amount-breakdown span:hover {
  background: #e8e8e8;
  border-color: #d0d0d6;
}

/* 费用类型颜色区分 */
.amount-breakdown .room-price {
  background: #e8f5e8;
  color: #18a058;
  border-color: #b3d9b3;
}

.amount-breakdown .deposit {
  background: #fff3e0;
  color: #f57c00;
  border-color: #ffcc80;
}

.amount-breakdown .package {
  background: #e3f2fd;
  color: #1976d2;
  border-color: #90caf9;
}

.amount-breakdown .room-price:hover {
  background: #d4edda;
  border-color: #a3d1a3;
}

.amount-breakdown .deposit:hover {
  background: #ffe0b2;
  border-color: #ffb74d;
}

.amount-breakdown .package:hover {
  background: #bbdefb;
  border-color: #64b5f6;
}

.footer-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

/* 暗色主题适配 */
.dark .modal-footer {
  background: #2a2a2a;
  border-top-color: #3a3a3a;
}

.dark .amount-label {
  color: #ccc;
}

.dark .amount-breakdown {
  color: #999;
}

.dark .amount-breakdown span {
  background: #3a3a3a;
}

/* 暗色主题费用颜色 */
.dark .amount-breakdown .room-price {
  background: #1a3a1a;
  color: #4caf50;
  border-color: #2e5d2e;
}

.dark .amount-breakdown .deposit {
  background: #3a2a1a;
  color: #ff9800;
  border-color: #5d4a2e;
}

.dark .amount-breakdown .package {
  background: #1a2a3a;
  color: #2196f3;
  border-color: #2e4a5d;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modal-footer {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .footer-amount-display {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .amount-breakdown {
    gap: 8px;
  }

  .amount-breakdown span {
    font-size: 11px;
    padding: 3px 6px;
  }

  .footer-actions {
    justify-content: flex-end;
  }
}

/* 紧凑布局优化 */
.compact-layout {
  padding: 12px 16px;
}

.compact-card {
  margin-bottom: 8px;
}

.compact-card :deep(.n-card-header) {
  padding: 8px 16px 6px 16px;
  min-height: auto;
}

.compact-card :deep(.n-card__content) {
  padding: 6px 16px 8px 16px;
}

/* 表单项间距优化 */
.compact-layout :deep(.n-form-item) {
  margin-bottom: 6px;
}

.compact-layout :deep(.n-form-item-label) {
  padding-bottom: 2px;
  font-size: 14px;
  line-height: 1.2;
}

.compact-layout :deep(.n-grid) {
  row-gap: 4px !important;
}

/* 销售信息卡片特殊优化 */
.compact-card:first-child :deep(.n-card-header) {
  padding: 6px 16px 4px 16px;
}

.compact-card:first-child :deep(.n-card__content) {
  padding: 4px 16px 6px 16px;
}

.compact-card:first-child :deep(.n-form-item) {
  margin-bottom: 4px;
}

.compact-card:first-child :deep(.n-form-item-label) {
  padding-bottom: 1px;
}

/* 房间卡片紧凑化 */
.room-card {
  border: 1px solid #e0e0e6;
  border-radius: 6px;
  padding: 8px;
  margin-bottom: 8px;
  background: #fafafa;
}

.room-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  padding-bottom: 6px;
}

.room-pricing {
  margin-bottom: 6px;
}

.room-pricing :deep(.n-form-item) {
  margin-bottom: 2px;
}

.room-pricing :deep(.n-form-item-label) {
  padding-bottom: 1px;
  font-size: 12px;
  line-height: 1.1;
}

.guests-section {
  margin-top: 6px;
}

.guests-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  font-size: 13px;
  font-weight: 500;
  color: #666;
}

.guest-item {
  margin-bottom: 6px;
  padding: 6px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
}

.guest-item :deep(.n-form-item) {
  margin-bottom: 2px;
}

.guest-item :deep(.n-form-item-label) {
  display: none; /* 隐藏入住人信息的标签，节省空间 */
}

/* 暗色主题房间卡片 */
.dark .room-card {
  background: #2a2a2a;
  border-color: #3a3a3a;
}

.dark .room-header {
  border-bottom-color: #3a3a3a;
}

.dark .guest-item {
  background: #333;
  border-color: #444;
}

@keyframes shine {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

/* 每日价格预览样式 */
.daily-prices-preview {
  margin-top: 8px;
  padding: 4px 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
  text-align: center;
}

/* 每日价格编辑器样式 */
.daily-price-editor {
  max-height: 60vh;
  overflow-y: auto;
}

.room-info {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.price-list {
  max-height: 400px;
  overflow-y: auto;
}

.price-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.price-item:last-child {
  border-bottom: none;
}

.date-label {
  display: flex;
  flex-direction: column;
  min-width: 80px;
}

.price-input {
  display: flex;
  align-items: center;
}

.price-summary {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  margin-top: 16px;
}
</style>

<script setup>
import { ref, watch, defineProps, defineEmits, computed, h, nextTick } from 'vue'
import { useMessage, useDialog } from 'naive-ui'
import { NButton, NInputNumber, NSelect } from 'naive-ui'
import { useI18n } from 'vue-i18n'
import RoomSelector from './RoomSelector.vue'
import MemberInfoModal from './MemberInfoModal.vue'
import * as api from '@/api/hotel/roomStatus'
import { truncate } from 'lodash-es'

const message = useMessage()
const dialog = useDialog()
const { t } = useI18n()

// 模态框标题计算属性
const modalTitle = computed(() => {
  const roomNumber = form.value?.roomsToBook?.[0]?.roomNumber || ''
  if (roomNumber) {
    return `${t('roomStatus.checkin.title')} - ${t('roomStatus.checkin.roomNumber')} ${roomNumber}`
  }
  return t('roomStatus.checkin.title')
})

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  roomData: {
    type: Object,
    default: () => ({})
  },
  allRooms: Array
})
const emit = defineEmits(['update:show', 'success', 'refreshRoomStatus'])

const show = ref(false)
const formRef = ref(null)
// AutoComplete组件引用
const contactNameAutoCompleteRef = ref(null)
const contactPhoneAutoCompleteRef = ref(null)
const form = ref({})

// 各种选项数据 - 使用 shallowRef 优化大数组性能
const sellTypeOptions = shallowRef([])
const saleRuleOptions = shallowRef([])
const orderSourceOptions = shallowRef([])
const intermediaryOptions = shallowRef([])
const memberOptions = shallowRef([])
const phoneOptions = shallowRef([]) // 手机号搜索选项
const outTime = shallowRef([])
const memberGradeOptions = shallowRef([]) // 会员等级选项

// 会员信息相关
const memberInfo = ref(null)
const selectedMember = ref(null)
const memberDetailInfo = ref(null) // 存储getUserInfoHistory返回的详细信息

// 会员信息弹窗状态（复用 OrderDetailModal 中的模式）
const memberInfoModal = ref({
  show: false,
  commonCode: ''
})

// 每日价格编辑弹窗状态
const dailyPriceModal = ref({
  show: false,
  roomIndex: -1,
  roomData: null,
  prices: []
})

// 客史记录和优惠券相关
const showHistoryModal = ref(false)
const showCouponModal = ref(false)
const historyList = ref([])
const couponList = ref([])
const historyLoading = ref(false)
const couponLoading = ref(false)

// 客史记录和优惠券数量
const historyCount = computed(() => historyList.value.length)
const couponCount = computed(() => couponList.value.length)

// 客史记录表格列定义（显示入住统计信息）
const historyColumns = [
  { title: '入住次数', key: 'stay_count', width: 100 },
  { title: '平均房价', key: 'avg_price', width: 100, render: (row) => `¥${row.avg_price}` },
  { title: '偏好房型', key: 'favorite_room_type', width: 120 },
  { title: '偏好房间', key: 'favorite_room', width: 100 },
  { title: '最后入住时间', key: 'last_stay_time', width: 140 }
]

// 优惠券表格列定义
const couponColumns = [
  { title: '优惠券名称', key: 'title', minWidth: 140 },
  { title: '类型', key: 'type_name', width: 100 },
  { title: '金额', key: 'amount', width: 80 },
  { title: '使用门槛', key: 'threshold', width: 100 },
  { title: '过期时间', key: 'expire_time', width: 140 },
  {
    title: '操作',
    key: 'actions',
    width: 80,
    render: (row) => h(NButton, {
      size: 'small',
      type: 'primary',
      onClick: () => selectCoupon(row)
    }, { default: () => '选择' })
  }
]

// 优惠券行属性
const couponRowProps = (row) => {
  return {
    style: 'cursor: pointer;',
    onClick: () => selectCoupon(row)
  }
}

// 缓存和加载状态 - 全局缓存提高性能
const globalSaleRuleCache = new Map() // 全局销售规则缓存
const globalSellTypeCache = new Map() // 全局销售类型缓存
const isLoadingSaleRules = ref(false) // 销售规则加载状态
const isLoadingSellTypes = ref(false) // 销售类型加载状态
const isLoadingMemberGrades = ref(false) // 会员等级加载状态
const loading = ref(false) // 提交入住加载状态

// 性能优化标志
const isRenderOptimized = ref(false)
const lastSellTypeId = ref(null)

// 固定选项
const checkInTypeOptions = [
  { label: '正常', value: 'normal' },
  { label: '免费', value: 'free' },
  { label: '自用', value: 'self' }
]

const priceSchemeOptions = [
  { label: '门市价', value: 'market' },
  { label: '会员价', value: 'member' },
  { label: '单位', value: 'unit' },
  { label: '中介', value: 'intermediary' }
]

// 监听props.show变化
watch(() => props.show, async (newVal) => {
  show.value = newVal
  if (newVal) {
    console.log('🎉 办理入住弹窗打开，房间数据:', props.roomData)
    // 首先初始化表单和房间数据
    initFormWithRoom(props.roomData)

    await initData()

    // 设置固定选项的默认值
    if (!form.value.checkInType) {
      form.value.checkInType = checkInTypeOptions[0].value // 默认选择"正常"
    }

    if (!form.value.priceScheme) {
      form.value.priceScheme = priceSchemeOptions[0].value // 默认选择"门市价"
    }

    // 确保销售类型数据加载完成后设置默认值
    if (sellTypeOptions.value.length > 0 && (!form.value.sellType || form.value.sellType === null)) {
      const defaultSellType = sellTypeOptions.value[0].value
      form.value.sellType = defaultSellType
      console.log('🎯 设置销售类型默认值:', defaultSellType)

      // 立即加载销售规则
      await fetchSaleRules(defaultSellType)

      // 设置销售规则的默认值
      if (saleRuleOptions.value.length > 0 && (!form.value.saleRule || form.value.saleRule === null)) {
        form.value.saleRule = saleRuleOptions.value[0].value
        console.log('🎯 设置销售规则默认值:', form.value.saleRule)
      }
    }

    // 确保订单来源有默认值
    if (orderSourceOptions.value.length > 0 && (form.value.orderSource === null || form.value.orderSource === undefined)) {
      form.value.orderSource = orderSourceOptions.value[0].value
    }

    console.log('销售规则已准备，开始获取房价:', {
      saleRule: form.value.saleRule,
      saleRuleOptionsLength: saleRuleOptions.value.length
    })

    // 为所有房间拉取房价与套餐（此时应该有销售规则了）
    await fetchAllRoomPrices(form.value.memberGrade || null)
    // 同时更新表单顶部的房价显示（取第一个房间的价格）
    if (form.value.roomsToBook.length > 0) {
      form.value.roomPrice = form.value.roomsToBook[0].price
    }

    // 设置固定选项的默认值
    if (!form.value.checkInType) {
      form.value.checkInType = checkInTypeOptions[0].value // 默认选择"正常"
    }

    if (!form.value.priceScheme) {
      form.value.priceScheme = priceSchemeOptions[0].value // 默认选择"门市价"
    }

    // 确保数据加载完成后再设置默认值
    console.log('🔍 检查销售类型选项:', {
      sellTypeOptionsLength: sellTypeOptions.value.length,
      currentSellType: form.value.sellType,
      sellTypeOptions: sellTypeOptions.value
    })

    if (sellTypeOptions.value.length > 0 && (!form.value.sellType || form.value.sellType === null)) {
      const defaultSellType = sellTypeOptions.value[0].value
      form.value.sellType = defaultSellType
      console.log('🎯 设置销售类型默认值:', defaultSellType)

      // 立即加载销售规则（不使用异步延迟）
      await fetchSaleRules(defaultSellType)

      // 设置销售规则的默认值
      if (saleRuleOptions.value.length > 0 && (!form.value.saleRule || form.value.saleRule === null)) {
        form.value.saleRule = saleRuleOptions.value[0].value
        console.log('🎯 设置销售规则默认值:', form.value.saleRule)
      }
    } else {
      console.log('⚠️ 无法设置销售类型默认值，原因:', {
        sellTypeOptionsEmpty: sellTypeOptions.value.length === 0,
        sellTypeAlreadySet: form.value.sellType !== null && form.value.sellType !== undefined
      })
    }

    // 确保订单来源有默认值
    if (orderSourceOptions.value.length > 0 && (form.value.orderSource === null || form.value.orderSource === undefined)) {
      form.value.orderSource = orderSourceOptions.value[0].value
    }

    // 房价信息已通过其他函数获取

  }
})

// 重置所有选择框状态
function resetAllSelectStates() {
  // 重置表单数据到初始状态
  form.value = {
    sellType: null,
    saleRule: null,
    checkInType: null,
    isPrivateRoom: false,
    priceScheme: null,
    contactName: '',
    contactPhone: '',
    checkOutTime: null,
    orderSource: null,
    roomPrice: 0,
    stayDuration: 1,
    memberGrade: null,
    intermediary: null,
    roomsToBook: []
  }

  // 只重置动态选项数据，保留固定选项
  // 注意：checkInTypeOptions 和 priceSchemeOptions 是固定选项，不需要重置
  // sellTypeOptions 和 orderSourceOptions 会在下次打开时重新加载

  // 清空依赖于API的动态选项
  sellTypeOptions.value = []           // 销售类型（从API加载）
  saleRuleOptions.value = []           // 销售规则（依赖销售类型）
  orderSourceOptions.value = []        // 订单来源（从API加载）
  intermediaryOptions.value = []       // 中介选项（依赖价格方案）
  memberGradeOptions.value = []        // 会员等级（依赖价格方案）
  memberOptions.value = []             // 会员搜索结果
  phoneOptions.value = []              // 手机号搜索结果
  outTime.value = []                   // 退房时间选项

  // 重置加载状态
  isLoadingMemberGrades.value = false
  isLoadingSaleRules.value = false
  isLoadingSellTypes.value = false

  // 重置缓存相关状态
  lastSellTypeId.value = null

  // 重置房间选择相关状态
  showRoomSelect.value = false
  changingRoomIndex = -1

  // 清理表单验证状态
  if (formRef.value) {
    formRef.value.restoreValidation()
  }
}

// 监听show变化，同步到父组件
watch(show, (newVal) => {
  emit('update:show', newVal)

  // 模态框关闭时重置所有状态
  if (!newVal) {
    // 重置选择框状态
    resetAllSelectStates()

    // 清理定时器
    // if (sellTypeChangeTimer) {
    //   clearTimeout(sellTypeChangeTimer)
    //   sellTypeChangeTimer = null
    // }

    // 可选：清理缓存以释放内存（如果数据变化频繁可以启用）
    // saleRuleCache.value.clear()
  }
})

// 计算属性
const mainRoom = computed(() => form.value.roomsToBook?.[0])

const showIntermediarySelect = computed(() => {
  return form.value.priceScheme === 'unit' || form.value.priceScheme === 'intermediary'
})

const showMemberGradeSelect = computed(() => {
  return form.value.priceScheme === 'member'
})

// 当前选中的销售规则 - 缓存优化
const currentSaleRule = computed(() => {
  if (!form.value.saleRule) return null
  return saleRuleOptions.value.find(rule => rule.value === form.value.saleRule)
})

const intermediaryLabel = computed(() => {
  return form.value.priceScheme === 'unit' ? '选择单位' : '选择中介'
})

// 入住时长单位 - 使用预计算属性提高性能
const durationUnit = computed(() => {
  const selectedRule = currentSaleRule.value
  if (selectedRule) {
    // 使用预计算的 displayUnit 属性（如果存在）
    if (selectedRule.displayUnit) {
      return selectedRule.displayUnit
    }

    // 回退到原有逻辑
    if (selectedRule.sign === 'standard') {
      return '天'
    } else if (selectedRule.sign === 'hour') {
      return '小时'
    } else if (selectedRule.sign === 'long_standard') {
      return '月'
    }
  }

  return '天'
})

// 初始化房价（弹窗打开时调用）
async function initRoomPrice() {
  try {
    // 如果还没有销售类型和销售规则，等待默认值设置
    if (!form.value.sellType && !form.value.saleRule) {
      return
    }

    // 优先使用销售规则获取房价（更精确）
    if (form.value.saleRule) {
      await fetchRoomPrice()
    }

  } catch (error) {
  }
}

// 初始化数据（优化版）
async function initData() {
  console.log('🚀 办理入住弹窗初始化开始，将调用以下接口:')
  console.log('1. getRoomSellType - 获取销售类型')
  console.log('2. getRoomSaleType - 获取销售规则（基于销售类型）')
  console.log('3. getRoomPriceByDate - 获取房价和套餐（基于销售规则）')
  console.log('4. getOrderSource - 获取订单来源')
  console.log('5. getCheckOutTime - 获取离店时间')

  // 并行加载基础数据
  await Promise.all([
    fetchSellTypes(),
    fetchOrderSources(),
    fetchCheckOutTime()
  ])

  console.log('✅ 基础数据加载完成')

  // 后台预加载常用销售规则（不阻塞主流程）
  // setTimeout(() => {
  //   preloadCommonSaleRules()
  // }, 300) // 延迟300ms，避免影响主要功能的加载
}

// 获取销售类型 - 极致性能优化版
async function fetchSellTypes() {
  // 检查全局缓存
  const cacheKey = 'sellTypes_status_1'
  if (globalSellTypeCache.has(cacheKey)) {
    const cachedData = globalSellTypeCache.get(cacheKey)
    sellTypeOptions.value = cachedData
    return
  }

  if (isLoadingSellTypes.value) {
    return
  }

  try {
    isLoadingSellTypes.value = true

    console.log('=== getRoomSellType 接口调用 ===')
    console.log('请求参数:', { status: 1 })

    const response = await api.getRoomSellType({ status: 1 })

    console.log('getRoomSellType 接口响应:', response)
    console.log('响应数据结构:', {
      code: response.code,
      message: response.message,
      dataType: typeof response.data,
      dataLength: Array.isArray(response.data) ? response.data.length : 'not array',
      sampleData: response.data?.[0] || null
    })

    // 使用 Object.freeze 优化不可变数据，预计算常用属性
    const options = Object.freeze(response.data.map(item => Object.freeze({
      label: item.name,
      value: item.id,
      sign: item.sign,
      // 预计算常用属性提高渲染速度
      isStandard: item.sign === 'standard'
    })))

    // 批量更新避免多次渲染
    sellTypeOptions.value = options
    globalSellTypeCache.set(cacheKey, options)
  } catch (error) {
    message.error('获取销售类型失败')
  } finally {
    isLoadingSellTypes.value = false
  }
}

// 获取销售规则 - 极致性能优化版
async function fetchSaleRules(sellType) {
  if (!sellType) return

  // 检查全局缓存
  const cacheKey = `saleRules_${sellType}`
  if (globalSaleRuleCache.has(cacheKey)) {
    const cachedData = globalSaleRuleCache.get(cacheKey)
    saleRuleOptions.value = cachedData

    // 快速设置默认值
    if (cachedData.length > 0 && !form.value.saleRule) {
      form.value.saleRule = cachedData[0].value
      // 使用 requestIdleCallback 在空闲时处理，避免阻塞渲染
      if (window.requestIdleCallback) {
        requestIdleCallback(async () => {
          await initRoomPrice()
          handleSaleRuleChange(cachedData[0])
        })
      } else {
        setTimeout(async () => { await initRoomPrice(); handleSaleRuleChange(cachedData[0]) }, 0)
      }
    }
    return
  }

  // 防止重复请求
  if (isLoadingSaleRules.value) return

  try {
    isLoadingSaleRules.value = true

    console.log('=== getRoomSaleType 接口调用 ===')
    console.log('请求参数:', { sell_type: sellType, status: 1 })

    const response = await api.getRoomSaleType({ sell_type: sellType, status: 1 })

    console.log('getRoomSaleType 接口响应:', response)
    console.log('响应数据结构:', {
      code: response.code,
      message: response.message,
      dataType: typeof response.data,
      dataLength: Array.isArray(response.data) ? response.data.length : 'not array',
      sampleData: response.data?.[0] || null
    })

    // 使用 Object.freeze 优化不可变数据，预计算常用属性
    const rules = Object.freeze(response.data.map(item => Object.freeze({
      label: item.name,
      value: item.id,
      ...item,
      // 预计算常用属性提高渲染速度
      isStandard: item.sign === 'standard',
      isHourly: item.sign === 'hour',
      displayUnit: item.sign === 'standard' ? '天' : (item.sign === 'hour' ? '小时' : (item.sign === 'long_standard' ? '月' : item.unit || '天'))
    })))

    // 批量更新避免多次渲染
    saleRuleOptions.value = rules
    globalSaleRuleCache.set(cacheKey, rules)

    // 快速设置默认值
    if (rules.length > 0 && !form.value.saleRule) {
      form.value.saleRule = rules[0].value
      // 使用 requestIdleCallback 在空闲时处理，避免阻塞渲染
      if (window.requestIdleCallback) {
        requestIdleCallback(async () => {
          await initRoomPrice()
          handleSaleRuleChange(rules[0])
        })
      } else {
        setTimeout(async () => {
          await initRoomPrice()
          handleSaleRuleChange(rules[0])
        }, 0)
      }
    }
  } catch (error) {
    message.error('获取销售规则失败')
  } finally {
    isLoadingSaleRules.value = false
  }
}

// 获取订单来源
async function fetchOrderSources() {
  try {
    const response = await api.getBillSource()
    orderSourceOptions.value = response.data.map(item => ({
      label: item.source_name,
      value: item.id,
      sign: item.sign
    }))

    // 默认选择第一个
    if (orderSourceOptions.value.length > 0 && (form.value.orderSource === null || form.value.orderSource === undefined)) {
      form.value.orderSource = orderSourceOptions.value[0].value
    }
  } catch (error) {
  }
}

// 获取离店时间
async function fetchCheckOutTime() {
  try {
    const response = await api.getCheckOutTime()
    const timeStr = response.data // "12:00"
    const [hours, minutes] = timeStr.split(':')
    const date = new Date()
    date.setHours(parseInt(hours), parseInt(minutes), 0, 0)
    // TimePicker需要时间戳格式
    outTime.value = [parseInt(hours), parseInt(minutes)]
  } catch (error) {
  }
}

// 获取单位/中介列表
async function fetchIntermediaryList(type) {
  try {
    const response = await api.getIntermediaryList({ type })
    intermediaryOptions.value = response.data.map(item => ({
      label: item.name,
      value: item.id
    }))
  } catch (error) {
  }
}

// 获取指定会员等级的价格
async function fetchPriceForMemberGrade(memberGradeId) {
  try {
    // 检查必要条件
    if (!form.value.saleRule || !mainRoom.value?.roomTypeId) {
      return null
    }

    const today = new Date().toISOString().split('T')[0]
    const params = {
      date: today,
      days: form.value.stayDuration || 1,
      room_sale_type_id: form.value.saleRule,
      room_type_id: mainRoom.value.roomTypeId,
      member_grade_id: memberGradeId
    }
    const response = await api.getRoomPriceByDate(params)

    // 根据实际API数据结构解析价格
    if (response && response.data && response.data.room_price && response.data.room_price.length > 0) {
      const memberPrice = response.data.room_price[0].room_price
      return memberPrice
    }
    return null
  } catch (error) {
    return null
  }
}

// 等待会员等级选项加载完成
async function waitForMemberGradesLoaded(maxWaitTime = 3000) {
  const startTime = Date.now()

  while (isLoadingMemberGrades.value && (Date.now() - startTime) < maxWaitTime) {
    await new Promise(resolve => setTimeout(resolve, 50))
  }

  // 额外等待一点时间确保数据完全加载
  await new Promise(resolve => setTimeout(resolve, 100))
  return memberGradeOptions.value.length > 0
}

// 查找匹配的会员等级（增强版）
function findMatchingMemberGrade(gradeName) {
  if (!gradeName || !memberGradeOptions.value.length) {
    return null
  }

  console.log(`查找匹配的会员等级: ${gradeName}`)
  console.log('可用的会员等级:', memberGradeOptions.value.map(g => g.gradeName))

  // 1. 精确匹配
  let matchingGrade = memberGradeOptions.value.find(grade =>
    grade.gradeName === gradeName
  )

  if (matchingGrade) {
    console.log(`精确匹配成功: ${matchingGrade.gradeName}`)
    return matchingGrade
  }

  // 2. 忽略大小写的精确匹配
  matchingGrade = memberGradeOptions.value.find(grade =>
    grade.gradeName.toLowerCase() === gradeName.toLowerCase()
  )

  if (matchingGrade) {
    console.log(`忽略大小写匹配成功: ${matchingGrade.gradeName}`)
    return matchingGrade
  }

  // 3. 包含匹配（会员等级名称包含输入的名称）
  matchingGrade = memberGradeOptions.value.find(grade =>
    grade.gradeName.includes(gradeName) || gradeName.includes(grade.gradeName)
  )

  if (matchingGrade) {
    console.log(`包含匹配成功: ${matchingGrade.gradeName}`)
    return matchingGrade
  }

  // 4. 模糊匹配（去除空格、特殊字符后匹配）
  const normalizeString = (str) => str.replace(/[\s\-_]/g, '').toLowerCase()
  const normalizedGradeName = normalizeString(gradeName)

  matchingGrade = memberGradeOptions.value.find(grade => {
    const normalizedOptionName = normalizeString(grade.gradeName)
    return normalizedOptionName.includes(normalizedGradeName) ||
           normalizedGradeName.includes(normalizedOptionName)
  })

  if (matchingGrade) {
    console.log(`模糊匹配成功: ${matchingGrade.gradeName}`)
    return matchingGrade
  }

  console.log(`未找到匹配的会员等级: ${gradeName}`)
  return null
}

// 统一的会员等级处理函数
async function processMemberGradeSelection(member, memberStatus) {
  try {
    // 触发价格方案变化处理，加载会员等级选项
    handlePriceSchemeChange('member')

    // 设置匹配回调，在基础数据加载完成后立即匹配
    const memberGradeName = member.grade_name
    window.memberGradeLoadedCallback = () => {
      console.log(`尝试匹配会员等级: ${memberGradeName}`)
      const matchingGrade = findMatchingMemberGrade(memberGradeName)

      if (matchingGrade) {
        form.value.memberGrade = matchingGrade.value
        console.log(`已自动选择会员等级: ${matchingGrade.gradeName}`)

        // 异步获取价格，不阻塞等级选择
        fetchAllRoomPrices(matchingGrade.value).then(() => {
          if (form.value.roomsToBook.length > 0) {
            form.value.roomPrice = form.value.roomsToBook[0].price
          }
          message.success(`已为${memberStatus}会员 ${member.name} 设置专属价格`)
        }).catch(error => {
          console.error('获取会员价格失败:', error)
          message.warning('会员等级已选择，但获取价格失败')
        })
      } else {
        message.warning(`${memberStatus}会员 ${member.name} 信息已填充，请手动选择会员等级`)
      }

      // 清除回调
      window.memberGradeLoadedCallback = null
    }

    // 等待会员等级选项加载完成（只等待基础数据）
    await waitForMemberGradesLoaded()

    // 如果回调还没被触发（可能是使用了缓存），手动触发
    if (window.memberGradeLoadedCallback) {
      window.memberGradeLoadedCallback()
    }

  } catch (error) {
    console.error('处理会员价格时出错:', error)
    message.error('获取会员价格失败，请稍后重试')
    // 清除回调
    window.memberGradeLoadedCallback = null
  }
}

// 关闭AutoComplete下拉框
function closeAutoCompleteDropdowns() {
  try {
    // 立即关闭联系人姓名下拉框
    if (contactNameAutoCompleteRef.value) {
      // 清空选项来关闭下拉框
      memberOptions.value = []
    }

    // 立即关闭联系人手机号下拉框
    if (contactPhoneAutoCompleteRef.value) {
      // 清空选项来关闭下拉框
      phoneOptions.value = []
    }
  } catch (error) {
    console.warn('关闭下拉框时出错:', error)
  }
}

// 获取会员等级列表
async function fetchMemberGrades() {
  try {
    isLoadingMemberGrades.value = true
    const response = await api.getMemberGrade()

    // 先创建基础的会员等级选项
    const baseOptions = response.data.map(item => ({
      label: item.grade_name, // 临时标签，稍后会更新为包含价格的标签
      value: item.id,
      gradeId: item.id,
      gradeName: item.grade_name,
      ...item
    }))
    // 为每个会员等级查询对应的价格
    const optionsWithPrice = await Promise.all(
      baseOptions.map(async (option) => {
        try {
          const price = await fetchPriceForMemberGrade(option.gradeId)
          return {
            ...option,
            price: price,
            label: price !== null ? `${option.gradeName} - ¥${price}` : option.gradeName
          }
        } catch (error) {
          return {
            ...option,
            price: null,
            label: option.gradeName
          }
        }
      })
    )

    memberGradeOptions.value = optionsWithPrice
  } catch (error) {
    message.error('获取会员等级失败')
  } finally {
    isLoadingMemberGrades.value = false
  }
}

// 搜索用户
async function searchMembers(searchWord) {
  if (!searchWord || searchWord.length < 1) {
    memberOptions.value = []
    return
  }

  try {
    const response = await api.searchUser({
      page: 1,
      limit: 10,
      search_word: searchWord
    })

    if (response.data?.list && response.data.list.length > 0) {

      memberOptions.value = response.data.list.map(item => {
        const memberStatus = item.grade_name || '普通用户'
        const idCardLast4 = item.identification_number ? item.identification_number.slice(-4) : '****'

        return {
          label: item.name,
          value: item.name,
          memberData: item,
          displayText: `${memberStatus} | ${item.name} | ${item.phone} | 身份证尾号${idCardLast4}`
        }
      })
    } else {
      memberOptions.value = []
    }

  } catch (error) {
    memberOptions.value = []
  }
}



// 入住人姓名搜索
function handleGuestNameSearch(roomIndex, guestIndex, value) {
  // 设置当前选择的入住人位置
  currentGuestRoomIndex.value = roomIndex
  currentGuestIndex.value = guestIndex
  // 复用客户信息的搜索逻辑
  handleContactNameChange(value)
}

// 入住人手机号搜索
function handleGuestPhoneSearch(roomIndex, guestIndex, value) {
  // 设置当前选择的入住人位置
  currentGuestRoomIndex.value = roomIndex
  currentGuestIndex.value = guestIndex
  // 复用客户信息的搜索逻辑
  handleContactNameChange(value)
}

// 入住人会员选项渲染函数（优化显示）
const renderGuestMemberOption = ({ node, option }) => {
  if (!option.memberData) return null

  const member = option.memberData
  const gradeName = member.grade_name || '普通用户'

  // 判断是否为普通用户
  const isRegularUser = () => {
    return !gradeName || gradeName === '普通用户' || gradeName === '散客'
  }

  return h('div', {
    style: 'display: flex; align-items: center; gap: 12px; padding: 12px 16px; min-height: 60px; cursor: pointer; border-bottom: 1px solid #f0f0f0; transition: all 0.2s ease; background: transparent;',
    onClick: () => handleGuestMemberSelect(currentGuestRoomIndex.value, currentGuestIndex.value, option),
    onMouseenter: (e) => {
      e.target.style.backgroundColor = '#f8f9fa'
      e.target.style.transform = 'translateX(2px)'
    },
    onMouseleave: (e) => {
      e.target.style.backgroundColor = 'transparent'
      e.target.style.transform = 'translateX(0)'
    }
  }, [
    // 头像
    h('div', {
      style: 'width: 40px; height: 40px; border-radius: 50%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 14px; flex-shrink: 0;'
    }, (member.name || '未知').charAt(0)),

    // 主要信息
    h('div', { style: 'flex: 1; min-width: 0;' }, [
      h('div', { style: 'display: flex; align-items: center; gap: 8px; margin-bottom: 4px;' }, [
        h('span', { style: 'font-weight: 600; color: #333; font-size: 14px;' }, member.name || '未知姓名'),
        h('span', {
          style: `padding: 2px 8px; border-radius: 12px; font-size: 11px; font-weight: 500; ${
            isRegularUser()
              ? 'background: #f0f0f0; color: #666;'
              : 'background: linear-gradient(135deg, #ffd700, #ffed4e); color: #8b5a00;'
          }`
        }, gradeName)
      ]),
      h('div', { style: 'color: #666; font-size: 13px; margin-bottom: 2px;' }, `📱 ${member.phone || member.mobile || '无手机号'}`),
      h('div', { style: 'color: #888; font-size: 12px;' }, `🆔 ${member.identification_number || '无身份证号'}`)
    ])
  ])
}

// 当前选择的入住人位置（用于渲染函数中的点击事件）
const currentGuestRoomIndex = ref(0)
const currentGuestIndex = ref(0)

// 入住人选择会员
function handleGuestMemberSelect(roomIndex, guestIndex, option) {
  if (!option?.memberData) return

  const member = option.memberData
  const guest = form.value.roomsToBook[roomIndex].guests[guestIndex]

  console.log('选择的会员数据:', member)

  // 自动填写会员信息 - 兼容多种字段名
  guest.name = member.name || member.user_name || ''
  guest.phone = member.phone || member.mobile || member.user_phone || ''

  // 处理性别：将数字转换为文字
  const genderValue = member.gender || member.sex
  if (genderValue === 1 || genderValue === '1') {
    guest.gender = '男'
  } else if (genderValue === 2 || genderValue === '2') {
    guest.gender = '女'
  } else if (typeof genderValue === 'string') {
    guest.gender = genderValue
  } else {
    guest.gender = '男' // 默认值
  }

  // 处理证件类型：将数字转换为文字
  const idTypeValue = member.identification_type || member.id_type
  if (idTypeValue === 1 || idTypeValue === '1') {
    guest.idType = '身份证'
  } else if (idTypeValue === 2 || idTypeValue === '2') {
    guest.idType = '护照'
  } else if (idTypeValue === 3 || idTypeValue === '3') {
    guest.idType = '港澳通行证'
  } else if (idTypeValue === 4 || idTypeValue === '4') {
    guest.idType = '台湾通行证'
  } else if (typeof idTypeValue === 'string') {
    guest.idType = idTypeValue
  } else {
    guest.idType = '身份证' // 默认值
  }

  guest.idNumber = member.identification_number || member.id_number || member.user_id_card || ''
  guest.nation = member.nation || member.nationality || '汉族'
  guest.address = member.address || member.user_address || ''
  guest.birthday = member.birthday || member.birth_date || ''
  guest.commonCode = member.common_code || member.user_code || ''

  console.log('填写后的入住人信息:', guest)

  // 清空搜索选项
  memberOptions.value = []

  message.success(`已自动填写会员 ${guest.name} 的信息`)
}

// 获取房价（基于销售规则）
async function fetchRoomPrice() {
  if (!form.value.saleRule || !mainRoom.value?.roomTypeId) {

    return
  }

  try {
    const today = new Date().toISOString().split('T')[0]
    const requestParams = {
      date: today,
      days: form.value.stayDuration || 1,
      room_sale_type_id: form.value.saleRule,
      room_type_id: mainRoom.value.roomTypeId
    }
    const response = await api.getRoomPriceByDate(requestParams)

    if (response.data) {
      // 设置房价
      form.value.roomPrice = response.data.room_price?.[0]?.room_price || 0

      // 处理房间服务/套餐信息
      if (response.data.room_service && Array.isArray(response.data.room_service)) {
        roomServiceList.value = response.data.room_service
        const availableServices = response.data.room_service.filter(service => service.status === 1 && service.is_show === 1)

        packageOptions.value = availableServices.map(service => ({
          label: `${service.service_name}${service.price > 0 ? ` (+¥${service.price})` : ''}`,
          value: service.id,
          price: service.price,
          serviceName: service.service_name,
          serviceContent: service.service_content,
          cancelable: service.cancelable,
          beforeEndTime: service.before_end_time
        }))

        // 为第一个房间设置默认套餐（如果还没有选择套餐且有可用服务）
        if (form.value.roomsToBook.length > 0 && availableServices.length > 0) {
          const firstRoom = form.value.roomsToBook[0]
          if (!firstRoom.packageId) {
            const defaultService = availableServices[0]
            firstRoom.packageId = defaultService.id
            firstRoom.packagePrice = defaultService.price
            firstRoom.packageName = defaultService.service_name
            firstRoom.packageOptions = packageOptions.value
            console.log(`房间 ${firstRoom.roomNumber} 默认选择套餐: ${defaultService.service_name}, 价格: ¥${defaultService.price}`)
          }
        }
      }

      // 保存其他有用的信息
      if (response.data.over_time_price) {
        // 可以保存超时价格等信息供后续使用
        console.log('超时价格:', response.data.over_time_price)
      }
    }
  } catch (error) {
  }
}

// 为单个房间获取房价和服务
async function fetchRoomPriceForRoom(roomIndex, memberGradeId = null) {
  const room = form.value.roomsToBook[roomIndex]
  if (!room?.roomTypeId) return

  room.priceLoading = true
  try {
    const today = new Date().toISOString().split('T')[0]
    const requestParams = {
      date: today,
      days: form.value.stayDuration || 1,
      room_type_id: room.roomTypeId
    }

    // 销售规则是必需参数，确保一定要传递
    if (form.value.saleRule) {
      requestParams.room_sale_type_id = form.value.saleRule
    } else if (saleRuleOptions.value.length > 0) {
      // 如果表单中没有销售规则，但选项中有，使用第一个
      requestParams.room_sale_type_id = saleRuleOptions.value[0].value
      // 同时更新表单中的销售规则
      form.value.saleRule = saleRuleOptions.value[0].value
      console.log(`房间${room.roomNumber}使用默认销售规则: ${saleRuleOptions.value[0].value}`)
    } else {
      // 如果完全没有销售规则，跳过此次请求
      console.error(`房间${room.roomNumber}无法获取价格：缺少销售规则`)
      room.priceLoading = false
      return
    }

    // 如果有会员等级，添加到请求参数中
    if (memberGradeId) {
      requestParams.member_grade_id = memberGradeId
    }

    console.log('=== getRoomPriceByDate 接口调用 ===')
    console.log(`房间${room.roomNumber}请求参数:`, requestParams)

    const response = await api.getRoomPriceByDate(requestParams)

    console.log(`getRoomPriceByDate 接口响应 (房间${room.roomNumber}):`, response)
    console.log('响应数据详细结构:', {
      code: response.code,
      message: response.message,
      dataType: typeof response.data,
      hasRoomPrice: !!response.data?.room_price,
      roomPriceLength: Array.isArray(response.data?.room_price) ? response.data.room_price.length : 'not array',
      hasRoomService: !!response.data?.room_service,
      roomServiceLength: Array.isArray(response.data?.room_service) ? response.data.room_service.length : 'not array',
      sampleRoomPrice: response.data?.room_price?.[0] || null,
      sampleRoomService: response.data?.room_service?.[0] || null
    })

    if (response?.data) {
      console.log(`房间${room.roomNumber}响应数据结构:`, {
        room_price: response.data.room_price,
        room_service: response.data.room_service,
        hasRoomPrice: !!response.data.room_price?.[0],
        roomPriceValue: response.data.room_price?.[0]?.room_price
      })

      // 处理多天价格数据
      console.log(`🔍 房间${room.roomNumber}价格数据处理:`, {
        hasRoomPrice: !!response.data.room_price,
        isArray: Array.isArray(response.data.room_price),
        length: response.data.room_price?.length,
        firstItem: response.data.room_price?.[0],
        stayDuration: form.value.stayDuration
      })

      if (response.data.room_price && Array.isArray(response.data.room_price) && response.data.room_price.length > 0) {
        // 保存完整的每日价格数据
        room.dailyPrices = response.data.room_price.map(priceItem => ({
          date: priceItem.date || priceItem.data, // 兼容不同的字段名
          price: Number(priceItem.room_price) || 0,
          originalPrice: priceItem.room_price // 保留原始价格用于显示
        }))

        // 设置房间的基础价格（使用第一天的价格）
        const firstDayPrice = Number(response.data.room_price[0]?.room_price) || 0
        room.price = firstDayPrice

        console.log(`✅ 房间${room.roomNumber}多天价格数据:`, room.dailyPrices)
        console.log(`✅ 房间${room.roomNumber}基础价格: ¥${firstDayPrice}`)
        console.log(`✅ 编辑按钮显示条件: dailyPrices.length=${room.dailyPrices.length} > 1 = ${room.dailyPrices.length > 1}`)
      } else {
        // 如果没有多天价格数据，使用单一价格
        const roomPrice = response.data.room_price?.[0]?.room_price
        const finalPrice = Number(roomPrice) || 0
        room.price = finalPrice

        console.log(`⚠️ 房间${room.roomNumber}没有多天价格数据，使用单一价格: ¥${finalPrice}`)
        room.dailyPrices = [] // 清空多天价格数据
        console.log(`房间${room.roomNumber}单一价格更新: 原始值=${roomPrice}, 转换后=${finalPrice}`)
      }

      // 更新房间的服务信息
      if (response.data.room_service && Array.isArray(response.data.room_service) && response.data.room_service.length > 0) {
        // 保存完整的服务列表用于套餐选择
        room.roomServiceList = response.data.room_service
        const availableServices = response.data.room_service.filter(s => s.status === 1 && s.is_show === 1)

        room.packageOptions = availableServices.map(s => ({
          label: `${s.service_name}${s.price > 0 ? ` (+¥${s.price})` : ''}`,
          value: s.id,
          price: s.price,
          serviceName: s.service_name,
          serviceContent: s.service_content,
          cancelable: s.cancelable,
          beforeEndTime: s.before_end_time
        }))

        // 如果房间还没有选择套餐，且有可用的服务，默认选择第一个服务
        if (!room.packageId && availableServices.length > 0) {
          const defaultService = availableServices[0]
          room.packageId = defaultService.id
          room.packagePrice = defaultService.price
          room.packageName = defaultService.service_name
          console.log(`房间 ${room.roomNumber} 默认选择套餐: ${defaultService.service_name}, 价格: ¥${defaultService.price}`)
        }
      } else {
        // 没有服务时的默认值
        room.packageOptions = []
      }

      // 如果当前选择的套餐在新的选项中不存在，清空选择
      if (room.packageId && !room.packageOptions.find(opt => opt.value === room.packageId)) {
        room.packageId = null
        room.packagePrice = 0
        room.packageName = null
      }
    } else {
      console.error(`房间${room.roomNumber}获取价格失败: 响应无数据`, response)
      room.price = 0
    }
  } catch (error) {
    console.error(`获取房间${room.roomNumber}价格失败:`, error)
  } finally {
    room.priceLoading = false
  }
}

// 价格请求缓存，避免重复请求
const priceCache = new Map()

// 生成缓存键
function generateCacheKey(roomTypeId, memberGradeId, saleRule, stayDuration) {
  return `${roomTypeId}-${memberGradeId || 'null'}-${saleRule}-${stayDuration}`
}

// 为所有房间获取房价和服务（优化版）
async function fetchAllRoomPrices(memberGradeId = null) {
  console.log('🔍 fetchAllRoomPrices 开始执行:', {
    memberGradeId,
    roomsToBookLength: form.value.roomsToBook.length,
    saleRule: form.value.saleRule,
    stayDuration: form.value.stayDuration
  })

  // 检查房间数据
  form.value.roomsToBook.forEach((room, index) => {
    console.log(`房间 ${index + 1} (${room.roomNumber}) 数据:`, {
      roomTypeId: room.roomTypeId,
      roomNumber: room.roomNumber,
      hasRoomTypeId: !!room.roomTypeId
    })
  })

  // 按房型分组，相同房型的房间可以共享价格数据
  const roomTypeGroups = new Map()

  form.value.roomsToBook.forEach((room, index) => {
    const key = room.roomTypeId
    if (!roomTypeGroups.has(key)) {
      roomTypeGroups.set(key, [])
    }
    roomTypeGroups.get(key).push({ room, index })
  })

  console.log('房型分组结果:', Array.from(roomTypeGroups.entries()).map(([roomTypeId, rooms]) => ({
    roomTypeId,
    roomCount: rooms.length,
    roomNumbers: rooms.map(r => r.room.roomNumber)
  })))

  // 为每个房型获取一次价格，然后应用到所有相同房型的房间
  const promises = Array.from(roomTypeGroups.entries()).map(async ([roomTypeId, roomsData]) => {
    console.log(`开始处理房型 ${roomTypeId}:`, {
      roomTypeId,
      roomCount: roomsData.length,
      firstRoomNumber: roomsData[0]?.room?.roomNumber
    })

    const cacheKey = generateCacheKey(roomTypeId, memberGradeId, form.value.saleRule, form.value.stayDuration)

    // 检查缓存
    if (priceCache.has(cacheKey)) {
      console.log(`房型 ${roomTypeId} 使用缓存数据`)
      const cachedData = priceCache.get(cacheKey)
      // 应用缓存数据到所有相同房型的房间
      roomsData.forEach(({ room, index }) => {
        applyPriceDataToRoom(room, cachedData)
      })
      return
    }

    // 获取第一个房间的价格数据
    const firstRoom = roomsData[0]
    console.log(`房型 ${roomTypeId} 调用 fetchRoomPriceForRoom:`, {
      roomIndex: firstRoom.index,
      roomNumber: firstRoom.room.roomNumber,
      memberGradeId
    })

    await fetchRoomPriceForRoom(firstRoom.index, memberGradeId)

    // 缓存价格数据
    const priceData = {
      price: firstRoom.room.price,
      packageOptions: firstRoom.room.packageOptions,
      packageId: firstRoom.room.packageId,
      packagePrice: firstRoom.room.packagePrice,
      packageName: firstRoom.room.packageName
    }
    priceCache.set(cacheKey, priceData)

    console.log(`房型 ${roomTypeId} 价格数据已缓存:`, priceData)

    // 应用到其他相同房型的房间
    roomsData.slice(1).forEach(({ room }) => {
      applyPriceDataToRoom(room, priceData)
    })
  })

  await Promise.all(promises)
  console.log('✅ fetchAllRoomPrices 执行完成')
}

// 应用价格数据到房间
function applyPriceDataToRoom(room, priceData) {
  room.price = priceData.price
  room.packageOptions = [...priceData.packageOptions]
  room.packageId = priceData.packageId
  room.packagePrice = priceData.packagePrice
  room.packageName = priceData.packageName
  room.priceLoading = false
}

// 清除价格缓存
function clearPriceCache() {
  priceCache.clear()
}

// 销售类型变化处理 - 极致性能优化版
async function handleSellTypeChange(value) {
  // 防抖优化：避免快速切换时的重复处理
  if (lastSellTypeId.value === value) {
    return
  }

  // 清除价格缓存，因为销售类型变化会影响价格
  clearPriceCache()

  lastSellTypeId.value = value

  // 立即清空销售规则，提供即时反馈
  form.value.saleRule = null

  if (!value) {
    saleRuleOptions.value = []
    return
  }

  try {
    // 异步加载销售规则，不阻塞UI
    if (window.requestIdleCallback) {
      requestIdleCallback(() => {
        fetchSaleRules(value)
      })
    } else {
      setTimeout(() => {
        fetchSaleRules(value)
      }, 0)
    }
  } catch (error) {
  }
}

function handleSaleRuleChange(selectedRule) {
  // 如果没有传入选中的规则，从选项中查找

  if (!selectedRule && form.value.saleRule) {
    selectedRule = saleRuleOptions.value.find(rule => rule.value === form.value.saleRule)
  }

  if (!selectedRule) {
    return
  }

  // 批量更新表单状态，减少响应式触发次数
  const updates = {}

  // 根据销售规则类型设置入住时长控制
  if (selectedRule.sign === 'standard') {
    // 全天房：可以自由设置入住时长，显示天数，默认1天
    updates.isStayDurationReadonly = false
    updates.stayDuration = 1
  } else if (selectedRule.sign === 'long_standard') {
    // 月租房：可以自由设置入住时长，显示月数，默认1月
    updates.isStayDurationReadonly = false
    updates.stayDuration = 1
  } else if (selectedRule.sign === 'hour') {
    // 时租房：根据是否有固定时长决定
    if (selectedRule.stay_time && selectedRule.stay_time > 0) {
      // 如果有固定时长，设置为只读
      updates.stayDuration = selectedRule.stay_time
      updates.isStayDurationReadonly = true
    } else {
      // 如果没有固定时长，允许编辑，默认1小时
      updates.isStayDurationReadonly = false
      if (!form.value.stayDuration || form.value.stayDuration === 0) {
        updates.stayDuration = 1
      }
    }
  } else {
    // 其他类型：默认允许编辑
    updates.isStayDurationReadonly = false
    if (!form.value.stayDuration || form.value.stayDuration === 0) {
      updates.stayDuration = 1
    }
  }

  // 批量应用更新
  Object.assign(form.value, updates)

  // 使用 nextTick 确保 DOM 更新后再执行后续操作
  nextTick(() => {
    updateCheckOutTime()
    // fetchRoomPrice()
  })
}

// 更新离店时间
function updateCheckOutTime() {
  if (!form.value.stayDuration) {

    return
  }

  // 获取当前选中的销售规则（兼容两种查找方式）
  let selectedRule = saleRuleOptions.value.find(rule => rule.value === form.value.saleRule)

  if (!selectedRule) {
    selectedRule = saleRuleOptions.value.find(rule => rule.value === form.value.saleRule)
  }

  if (selectedRule) {
    const now = new Date()
    let checkOutDate = new Date(now)

    // 判断是否为全天房类型（根据 standard 字段）
    const isAllDayRoom = selectedRule.sign === 'standard'

    if (isAllDayRoom) {
      // 全天房：按天数计算，默认12:00离店
      checkOutDate.setDate(now.getDate() + form.value.stayDuration)
      checkOutDate.setHours(outTime.value[0], outTime.value[1], 0, 0)
    } else {
      // 非全天房：根据单位类型计算
      if (selectedRule.sign === 'hour') {
        // 按小时计算，精确到秒
        const hoursToAdd = selectedRule.stay_time || form.value.stayDuration
        checkOutDate.setHours(now.getHours() + hoursToAdd, now.getMinutes(), now.getSeconds(), now.getMilliseconds())
      } else if (selectedRule.sign === 'long_standard') {
        // 按月计算
        checkOutDate.setMonth(now.getMonth() + form.value.stayDuration)
        checkOutDate.setHours(outTime.value[0], outTime.value[1], 0, 0)
      } else {
        // 其他类型，默认按小时处理
        const hoursToAdd = selectedRule.stay_time || form.value.stayDuration
        checkOutDate.setHours(now.getHours() + hoursToAdd, now.getMinutes(), now.getSeconds(), now.getMilliseconds())
      }
    }

    // 设置为时间戳（Naive UI DatePicker需要）
    form.value.checkOutTime = checkOutDate.getTime()
  }
}

// 处理入住时长变化
function handleStayDurationChange(value) {
  if (value && value > 0) {
    // 清除价格缓存，因为入住时长变化会影响价格
    clearPriceCache()

    // 立即更新离店时间
    nextTick(() => {
      updateCheckOutTime()
      // 同时为所有房间更新房价
      fetchAllRoomPrices(form.value.memberGrade || null)
    })
  }
}

function handlePriceSchemeChange(value) {
  // 清空相关字段
  form.value.intermediary = null
  form.value.memberGrade = null
  intermediaryOptions.value = []
  memberGradeOptions.value = []

  if (value === 'unit') {
    fetchIntermediaryList(1) // 单位
  } else if (value === 'intermediary') {
    fetchIntermediaryList(2) // 中介
  } else if (value === 'member') {
    fetchMemberGrades() // 会员等级
  }
}

// 处理会员等级变化
async function handleMemberGradeChange(value) {
  if (value) {
    const selectedGrade = memberGradeOptions.value.find(grade => grade.value === value)
    if (selectedGrade) {
      console.log(`🔄 会员等级变化: ${selectedGrade.gradeName} (ID: ${value})`)

      // 重新获取所有房间的会员价格
      try {
        await fetchAllRoomPrices(value)

        // 更新表单顶部的房价显示（取第一个房间的价格）
        if (form.value.roomsToBook.length > 0) {
          form.value.roomPrice = form.value.roomsToBook[0].price
        }

        message.success(`已选择会员等级：${selectedGrade.gradeName}，价格已更新`)
      } catch (error) {
        console.error('获取会员价格失败:', error)
        message.error('获取会员价格失败，请重试')
      }
    }
  } else {
    // 清除会员等级时，重新获取门市价
    console.log('🔄 清除会员等级，获取门市价')
    try {
      await fetchAllRoomPrices(null)

      // 更新表单顶部的房价显示
      if (form.value.roomsToBook.length > 0) {
        form.value.roomPrice = form.value.roomsToBook[0].price
      }
    } catch (error) {
      console.error('获取门市价格失败:', error)
    }
  }
}

// 根据会员等级查询房价
async function fetchRoomPriceWithMemberGrade(memberGradeId) {
  try {
    // 使用与 fetchRoomPrice 相同的房间信息获取方式
    if (!form.value.saleRule || !mainRoom.value?.roomTypeId) {
      return
    }

    const today = new Date().toISOString().split('T')[0]
    const params = {
      date: today,
      days: form.value.stayDuration || 1,
      room_sale_type_id: form.value.saleRule,
      room_type_id: mainRoom.value.roomTypeId, // 使用正确的房间类型ID获取方式
      member_grade_id: memberGradeId // 传入会员等级ID
    }
    const response = await api.getRoomPriceByDate(params)

    if (response.data) {
      // 更新房价显示（与普通房价查询保持一致的处理方式）
      if (response.data.room_price && response.data.room_price.length > 0) {
        const memberPrice = response.data.room_price[0]?.room_price || 0
        form.value.roomPrice = memberPrice
        message.success(`已获取会员价格：¥${memberPrice}`)
      } else {
        message.warning('未获取到会员价格信息')
      }

      // 处理房间服务/套餐信息
      if (response.data.room_service && Array.isArray(response.data.room_service)) {
        roomServiceList.value = response.data.room_service
        const availableServices = response.data.room_service.filter(service => service.status === 1 && service.is_show === 1)

        packageOptions.value = availableServices.map(service => ({
          label: `${service.service_name}${service.price > 0 ? ` (+¥${service.price})` : ''}`,
          value: service.id,
          price: service.price,
          serviceName: service.service_name,
          serviceContent: service.service_content,
          cancelable: service.cancelable,
          beforeEndTime: service.before_end_time
        }))

        // 为第一个房间设置默认套餐（如果还没有选择套餐且有可用服务）
        if (form.value.roomsToBook.length > 0 && availableServices.length > 0) {
          const firstRoom = form.value.roomsToBook[0]
          if (!firstRoom.packageId) {
            const defaultService = availableServices[0]
            firstRoom.packageId = defaultService.id
            firstRoom.packagePrice = defaultService.price
            firstRoom.packageName = defaultService.service_name
            firstRoom.packageOptions = packageOptions.value
            console.log(`房间 ${firstRoom.roomNumber} 默认选择套餐: ${defaultService.service_name}, 价格: ¥${defaultService.price}`)
          }
        }
      }
    }

  } catch (error) {
    message.error('查询会员价格失败')
  }
}

// 搜索手机号对应的用户
async function searchMembersByPhone(searchWord) {
  if (!searchWord || searchWord.length < 2) {
    phoneOptions.value = []
    return
  }

  try {
    const response = await api.searchUser({
      page: 1,
      limit: 10,
      search_word: searchWord
    })

    if (response.data?.list && response.data.list.length > 0) {
      phoneOptions.value = response.data.list.map(item => {
        const memberStatus = item.grade_name || '普通用户'
        const idCardLast4 = item.identification_number ? item.identification_number.slice(-4) : '****'

        return {
          label: item.phone,
          value: item.phone,
          memberData: item,
          displayText: `${memberStatus} | ${item.name} | ${item.phone} | 身份证尾号${idCardLast4}`
        }
      })
    } else {
      phoneOptions.value = []
    }

  } catch (error) {
    phoneOptions.value = []
  }
}

// 处理联系人姓名输入变化
const handleContactNameChange = (value) => {
  // 检查是否是从选项中选择的
  const selectedOption = memberOptions.value.find(option => option.value === value)
  if (selectedOption) {
    handleMemberSelect(value, selectedOption)
    return
  }

  if (value && value.length >= 0) {
    searchMembers(value)
  } else {
    memberOptions.value = []
  }
}

// 处理手机号输入变化
const handlePhoneChange = (value) => {
  // 检查是否是从选项中选择的
  const selectedOption = phoneOptions.value.find(option => option.value === value)
  if (selectedOption) {
    handlePhoneSelect(value, selectedOption)
    return
  }

  if (value && value.length >= 2) {
    searchMembersByPhone(value)
  } else {
    phoneOptions.value = []
  }
}

// 处理会员选择
const handleMemberSelect = async (value, option) => {
  if (option && option.memberData) {
    const member = option.memberData

    // 立即关闭下拉框，提升用户体验
    closeAutoCompleteDropdowns()

    // 设置会员信息
    memberInfo.value = member
    selectedMember.value = member

    // 设置姓名和手机号
    form.value.contactName = member.name
    form.value.contactPhone = member.phone
    // 设置会员唯一标识码
    form.value.commonCode = member.common_code || ''

    const memberStatus = member.grade_name || '普通用户'

    // 立即设置价格方案，提供即时反馈
    if (member.grade_name && member.grade_name !== '普通用户' && member.grade_name !== '散客') {
      form.value.priceScheme = 'member'
    } else {
      form.value.priceScheme = 'market'
      form.value.memberGrade = null
    }

    // 异步获取客史记录、优惠券和详细信息（不阻塞主流程）
    if (member.common_code) {
      Promise.all([
        fetchMemberHistoryAndDetail(member.common_code),
        fetchMemberCoupons(member.common_code)
      ]).catch(error => {
        console.warn('获取会员详细信息失败:', error)
      })
    }

    // 异步处理会员等级和价格更新，不阻塞UI
    if (member.grade_name && member.grade_name !== '普通用户' && member.grade_name !== '散客') {
      // 立即显示成功消息，提供即时反馈
      message.success(`正在为${memberStatus}会员 ${member.name} 加载专属价格...`)

      // 异步处理会员等级和价格
      processMemberGradeSelection(member, memberStatus)
    } else {
      // 散客或普通用户，异步获取门市价
      message.success(`已填充${memberStatus}信息：${member.name} - ${member.phone}`)

      // 异步获取门市价格，不阻塞UI
      fetchAllRoomPrices().then(() => {
        // 更新表单顶部的房价显示
        if (form.value.roomsToBook.length > 0) {
          form.value.roomPrice = form.value.roomsToBook[0].price
        }
      }).catch(error => {
        console.error('获取门市价格失败:', error)
      })
    }
  } else {
    // 立即关闭下拉框
    closeAutoCompleteDropdowns()

    // 如果没有会员数据，只设置姓名，保持门市价
    form.value.contactName = value
    form.value.priceScheme = 'market'
    form.value.memberGrade = null
  }
}

// 处理手机号选择
const handlePhoneSelect = async (value, option) => {
  if (option && option.memberData) {
    const member = option.memberData

    // 立即关闭下拉框，提升用户体验
    closeAutoCompleteDropdowns()

    // 设置姓名和手机号
    form.value.contactName = member.name
    form.value.contactPhone = member.phone
    // 设置会员唯一标识码
    form.value.commonCode = member.common_code || ''

    const memberStatus = member.grade_name || '普通用户'

    // 立即设置价格方案，提供即时反馈
    if (member.grade_name && member.grade_name !== '普通用户' && member.grade_name !== '散客') {
      form.value.priceScheme = 'member'
      message.success(`正在为${memberStatus}会员 ${member.name} 加载专属价格...`)

      // 使用统一的会员等级处理函数
      processMemberGradeSelection(member, memberStatus)
    } else {
      // 散客或普通用户，设置为门市价
      form.value.priceScheme = 'market'
      form.value.memberGrade = null
      message.success(`已填充${memberStatus}信息：${member.name} - ${member.phone}`)
    }
  } else {
    // 立即关闭下拉框
    closeAutoCompleteDropdowns()

    // 如果没有会员数据，只设置手机号，保持门市价
    form.value.contactPhone = value
    form.value.priceScheme = 'market'
    form.value.memberGrade = null
  }
}

// 渲染会员选项
const renderMemberOption = ({ option }) => {
  const member = option.memberData
  if (!member) return option.label

  const memberStatus = member.grade_name || '普通用户'
  const idCardLast4 = member.id_card ? member.id_card.slice(-4) : '****'

  // 根据会员等级设置不同颜色
  const getGradeColor = (gradeName) => {
    if (!gradeName || gradeName === '普通用户' || gradeName === '散客') return '#e5e7eb'
    if (gradeName.includes('金卡') || gradeName.includes('金牌')) return '#FFD700'
    if (gradeName.includes('银卡') || gradeName.includes('银牌')) return '#C0C0C0'
    if (gradeName.includes('铜卡') || gradeName.includes('铜牌')) return '#CD7F32'
    if (gradeName.includes('钻石')) return '#B9F2FF'
    if (gradeName.includes('白金')) return '#E5E4E2'
    return '#18a058' // 默认绿色
  }

  // 获取会员等级深色（用于渐变）
  const getGradeColorDark = (gradeName) => {
    if (!gradeName || gradeName === '普通用户' || gradeName === '散客') return '#d1d5db'
    if (gradeName.includes('金卡') || gradeName.includes('金牌')) return '#DAA520'
    if (gradeName.includes('银卡') || gradeName.includes('银牌')) return '#A0A0A0'
    if (gradeName.includes('铜卡') || gradeName.includes('铜牌')) return '#B8860B'
    if (gradeName.includes('钻石')) return '#87CEEB'
    if (gradeName.includes('白金')) return '#D3D3D3'
    return '#16a085' // 默认深绿色
  }

  // 判断是否为散客/普通用户
  const isRegularCustomer = (gradeName) => {
    return !gradeName || gradeName === '普通用户' || gradeName === '散客'
  }

  return h('div', {
    style: 'display: flex; align-items: center; gap: 10px; padding: 10px 12px; min-height: 40px; cursor: pointer; border-bottom: 1px solid #f0f0f0; transition: all 0.2s ease; background: transparent;',
    onClick: () => handleMemberSelect(option.value, option),
    onMouseenter: (e) => {
      e.target.style.backgroundColor = '#f8f9fa'
      e.target.style.transform = 'translateX(2px)'
    },
    onMouseleave: (e) => {
      e.target.style.backgroundColor = 'transparent'
      e.target.style.transform = 'translateX(0)'
    }
  }, [
    // 根据是否为散客渲染不同样式的标签
    isRegularCustomer(memberStatus) ?
      // 散客样式 - 简约朴素
      h('span', {
        style: `
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 10px;
          color: #6b7280;
          background: #f9fafb;
          min-width: 50px;
          text-align: center;
          font-weight: 400;
          white-space: nowrap;
          flex-shrink: 0;
          border: 1px dashed #d1d5db;
          position: relative;
          transform: scale(1);
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 2px;
        `,
        onMouseenter: (e) => {
          e.target.style.backgroundColor = '#f3f4f6'
          e.target.style.borderColor = '#9ca3af'
          e.target.style.color = '#4b5563'
        },
        onMouseleave: (e) => {
          e.target.style.backgroundColor = '#f9fafb'
          e.target.style.borderColor = '#d1d5db'
          e.target.style.color = '#6b7280'
        }
      }, [
        // 添加散客图标
        h('span', {
          style: 'font-size: 8px; opacity: 0.7;'
        }, '👤'),
        memberStatus || '散客'
      ]) :
      // 会员样式 - 炫酷渐变
      h('span', {
        style: `
          padding: 5px 10px;
          border-radius: 8px;
          font-size: 11px;
          color: white;
          background: linear-gradient(135deg, ${getGradeColor(memberStatus)}, ${getGradeColorDark(memberStatus)});
          min-width: 40px;
          text-align: center;
          font-weight: 700;
          white-space: nowrap;
          flex-shrink: 0;
          box-shadow: 0 3px 6px rgba(0,0,0,0.2), 0 1px 3px rgba(0,0,0,0.1);
          text-shadow: 0 1px 2px rgba(0,0,0,0.4);
          border: 1px solid rgba(255,255,255,0.3);
          position: relative;
          overflow: hidden;
          transform: scale(1);
          transition: all 0.2s ease;
        `,
        onMouseenter: (e) => {
          e.target.style.transform = 'scale(1.05)'
          e.target.style.boxShadow = '0 4px 8px rgba(0,0,0,0.25), 0 2px 4px rgba(0,0,0,0.15)'
        },
        onMouseleave: (e) => {
          e.target.style.transform = 'scale(1)'
          e.target.style.boxShadow = '0 3px 6px rgba(0,0,0,0.2), 0 1px 3px rgba(0,0,0,0.1)'
        }
      }, [
        // 添加光泽效果（仅会员有）
        h('span', {
          style: `
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shine 2s infinite;
          `
        }),
        memberStatus
      ]),
    h('span', {
      style: 'font-weight: 600; font-size: 13px; color: #333; min-width: 60px; flex-shrink: 0;'
    }, member.name),
    h('span', {
      style: 'color: #666; font-size: 13px; min-width: 90px; flex-shrink: 0;'
    }, member.phone),
    h('span', {
      style: 'color: #999; font-size: 11px; flex: 1;'
    }, `尾号${idCardLast4}`)
  ])
}

// 读取身份证
async function handleReadIdCard() {
  try {
    const response = await api.readIdCard()
    if (response && response.name) {
      form.value.contactName = response.name
      form.value.contactPhone = response.phone || ''

      // 身份证读取后，默认设置为门市价（因为身份证读取无法获取会员信息）
      form.value.priceScheme = 'market'
      form.value.memberGrade = null

      message.success('身份证读取成功，已设置为门市价')
    } else {
      message.warning('未读取到身份证信息')
    }
  } catch (error) {
    message.error('读取身份证失败，请检查设备连接')
  }
}
// 初始化表单数据
function initFormWithRoom(room) {
  // 只在表单为空或未初始化时才初始化基础结构
  if (!form.value || Object.keys(form.value).length === 0) {
    form.value = {
      // 销售信息 - 保持为null，等待API数据加载后设置默认值
      sellType: null,
      saleRule: null,
      checkInType: 'normal',
      isSecret: false,
      priceScheme: 'market',
      intermediary: null,
      memberGrade: null,
      orderSource: null,

      // 客户信息
      contactName: '',
      contactPhone: '',
      commonCode: '', // 添加客人唯一标识码字段
      externalOrderNo: '',

      // 入住信息
      stayDuration: 1,
      isStayDurationReadonly: false, // 控制入住时长是否只读
      checkInTime: new Date(), // 添加入住时间字段，默认为当前时间
      checkOutTime: null,
      roomPrice: 0,
      remark: '',

      // 优惠券信息
      selectedCoupons: [],

      // 房间信息
      roomsToBook: []
    }
  }

  // 处理房间数据（总是更新房间列表）
  if (room) {

    // 如果传入的是单个房间，转换为房间数组
    const roomData = Array.isArray(room) ? room : [room]
    form.value.roomsToBook = roomData.map(r => ({
      id: r.id || r.room_id,
      roomNumber: r.roomNumber || r.room_number,
      roomTypeName: r.roomTypeName || r.room_type_name || r.roomType,
      roomTypeId: r.roomTypeId || r.room_type_id,
      price: null, // 初始不显示0，等待接口写入
      deposit: 0,
      packageId: null,
      packagePrice: 0,
      packageName: null,
      // 服务首项默认
      serviceName: '无服务',
      servicePrice: 0,
      // 每个房间独立的套餐选项和服务数据
      packageOptions: [],
      roomServiceList: [],
      priceLoading: true,
      // 入住人信息
      guests: [
        {
          id: Date.now() + Math.random(), // 临时ID
          name: '',
          gender: '男',
          phone: '',
          idType: '身份证',
          idNumber: '',
          nation: '汉族',
          address: '',
          birthday: '',
          commonCode: ''
        }
      ]
    }))
  } else if (!form.value.roomsToBook) {
    // 如果没有传入房间数据且表单中也没有房间数据，初始化为空数组
    form.value.roomsToBook = []
  }

}

// 监听props.roomData变化，自动填充表单
watch(() => props.roomData, (newRoomData) => {
  if (newRoomData && Object.keys(newRoomData).length > 0) {
    initFormWithRoom(newRoomData)
  }
}, { immediate: true })

// defineExpose a method to open the modal
const open = (room) => {
  initFormWithRoom(room)
  show.value = true
}

defineExpose({ open })

// 表单验证规则
const rules = computed(() => ({
  sellType: {
    required: true,
    message: t('hotel.checkin.validation.sellTypeRequired'),
    trigger: ['change', 'blur'],
    validator: (_rule, value) => {
      if (!value && value !== 0) {
        return new Error(t('hotel.checkin.validation.sellTypeRequired'))
      }
      return true
    }
  },
  saleRule: {
    required: true,
    message: t('hotel.checkin.validation.saleRuleRequired'),
    trigger: ['change', 'blur'],
    validator: (_rule, value) => {
      if (!value && value !== 0) {
        return new Error(t('hotel.checkin.validation.saleRuleRequired'))
      }
      return true
    }
  },
  checkInType: { required: true, message: t('hotel.checkin.validation.checkinTypeRequired'), trigger: ['change', 'blur'] },
  priceScheme: { required: true, message: t('hotel.checkin.validation.priceSchemeRequired'), trigger: ['change', 'blur'] },
  memberGrade: {
    required: true,
    message: '请选择会员等级',
    trigger: ['change', 'blur'],
    validator: (_rule, value) => {
      if (form.value.priceScheme === 'member' && !value) {
        return new Error('选择会员价时必须选择会员等级')
      }
      return true
    }
  },
  orderSource: {
    required: true,
    message: t('hotel.checkin.validation.orderSourceRequired'),
    trigger: ['change', 'blur'],
    validator: (_rule, value) => {
      if (value === null || value === undefined || value === '') {
        return new Error(t('hotel.checkin.validation.orderSourceRequired'))
      }
      return true
    }
  },
  contactName: { required: true, message: t('hotel.checkin.validation.contactNameRequired'), trigger: ['blur', 'input'] },
  contactPhone: { required: true, message: t('hotel.checkin.validation.contactPhoneRequired'), trigger: ['blur', 'input'] },
  stayDuration: { required: true, type: 'number', message: t('hotel.checkin.validation.stayDurationRequired'), trigger: ['blur', 'change'] }
}))
// 根据售卖方式获取对应的入住API函数
function getCheckInApiFunction(sellType) {
  // 首先尝试通过销售规则的sign字段来判断
  const currentSaleRule = saleRuleOptions.value.find(rule => rule.value === form.value.saleRule)
  if (currentSaleRule && currentSaleRule.sign) {
    console.log(`🔍 根据销售规则sign判断API: ${currentSaleRule.sign}`)
    switch (currentSaleRule.sign) {
      case 'standard':
        return api.standardRoomCheckIn
      case 'hour':
        return api.hourRoomCheckIn
      case 'long_standard':
        return api.longStandardRoomCheckIn
      default:
        console.warn(`未知的销售规则sign: ${currentSaleRule.sign}`)
    }
  }

  // 如果没有销售规则信息，使用销售类型ID映射（兼容旧逻辑）
  const apiMap = {
    1: api.standardRoomCheckIn,     // 全日房
    2: api.hourRoomCheckIn,         // 时租房
    3: api.longStandardRoomCheckIn, // 月租房
    4: api.longStandardRoomCheckIn, // 会议室
    398: api.standardRoomCheckIn,   // 全日房（实际ID）
    399: api.longStandardRoomCheckIn // 月租房（实际ID）
  }

  console.log(`🔍 根据销售类型ID判断API: ${sellType} -> ${apiMap[sellType] ? apiMap[sellType].name : 'standardRoomCheckIn(默认)'}`)
  return apiMap[sellType] || api.standardRoomCheckIn
}

// 构建入住请求参数
function buildCheckInParams() {
  const baseParams = {
    // 房间销售类型
    room_sale_type: form.value.saleRule || 385,

    // 联系人信息
    user_info: {
      link_man: form.value.contactName,
      link_phone: form.value.contactPhone,
      common_code: form.value.commonCode || ''
    },

    // 价格方案
    price_project: form.value.sellType === 1 ? 2 : 1,

    // 计划入住时间
    enter_time_plan: getValidTimestamp(form.value.checkInTime),

    // 入住天数
    times: form.value.stayDuration || 1,

    // 账单来源
    bill_source: form.value.orderSource || 9,

    // 房间列表
    room_list: form.value.roomsToBook.map(room => ({
      room_type_id: room.roomTypeId,
      room_id: room.id,
      room_service_selected: room.packageId || 204, // 默认服务ID
      user_info: room.guests.map(guest => ({
        name: guest.name || '',
        gender: guest.gender === '男' ? 1 : 2,
        phone: guest.phone || '',
        identification_type: guest.idType === '身份证' ? 1 : 2,
        identification_number: guest.idNumber || ''
      })),
      custom_price: {
        custom_cash_pledge: room.deposit || 10,
        custom_room_price: generateCustomRoomPrices(room)
      }
    })),

    // 住宿类型
    stay_type: 1,

    // 是否保密
    secrecy: form.value.isSecret ? 1 : 0
  }

  return baseParams
}

// 生成自定义房间价格数组（从API获取实际价格数据）
function generateCustomRoomPrices(room) {
  // 如果房间已经有详细的价格数据（从API获取），直接使用
  if (room.dailyPrices && Array.isArray(room.dailyPrices) && room.dailyPrices.length > 0) {
    return room.dailyPrices.map(dayPrice => ({
      data: dayPrice.date,
      price: dayPrice.price.toString()
    }))
  }

  // 如果没有详细价格数据，生成基于单价的价格数组
  const prices = []

  // 安全地处理入住时间
  let startDate
  try {
    if (form.value.checkInTime) {
      startDate = new Date(form.value.checkInTime)
      // 检查日期是否有效
      if (isNaN(startDate.getTime())) {
        throw new Error('Invalid date')
      }
    } else {
      // 如果没有入住时间，使用当前日期
      startDate = new Date()
    }
  } catch (error) {
    console.warn('入住时间无效，使用当前日期:', form.value.checkInTime)
    startDate = new Date()
  }

  const stayDuration = form.value.stayDuration || 1

  // 为每一天生成价格记录
  for (let i = 0; i < stayDuration; i++) {
    const currentDate = new Date(startDate)
    currentDate.setDate(startDate.getDate() + i)

    // 确保日期有效
    if (!isNaN(currentDate.getTime())) {
      prices.push({
        data: currentDate.toISOString().split('T')[0], // 格式：YYYY-MM-DD
        price: (room.price || 0).toString() // 确保价格是字符串格式
      })
    }
  }

  // 如果没有生成任何价格记录，至少返回一个当天的记录
  if (prices.length === 0) {
    const today = new Date()
    prices.push({
      data: today.toISOString().split('T')[0],
      price: (room.price || 0).toString()
    })
  }

  return prices
}

// 计算每日价格总计
function calculateTotalDailyPrice(dailyPrices) {
  if (!dailyPrices || !Array.isArray(dailyPrices)) return 0
  return dailyPrices.reduce((total, dayPrice) => total + (dayPrice.price || 0), 0).toFixed(2)
}

// 打开每日价格编辑器
function openDailyPriceEditor(roomIndex) {
  const room = form.value.roomsToBook[roomIndex]
  if (!room || !room.dailyPrices || room.dailyPrices.length === 0) {
    message.warning('该房间没有多天价格数据')
    return
  }

  // 深拷贝价格数据，避免直接修改原数据
  dailyPriceModal.value = {
    show: true,
    roomIndex,
    roomData: room,
    prices: room.dailyPrices.map(dayPrice => ({
      date: dayPrice.date,
      price: dayPrice.price,
      originalPrice: dayPrice.originalPrice
    }))
  }
}

// 保存每日价格
function saveDailyPrices() {
  const { roomIndex, prices } = dailyPriceModal.value
  if (roomIndex >= 0 && roomIndex < form.value.roomsToBook.length) {
    const room = form.value.roomsToBook[roomIndex]

    // 更新房间的每日价格数据
    room.dailyPrices = prices.map(dayPrice => ({
      date: dayPrice.date,
      price: dayPrice.price,
      originalPrice: dayPrice.originalPrice
    }))

    // 更新房间的基础价格（使用第一天的价格）
    if (prices.length > 0) {
      room.price = prices[0].price
    }

    message.success('每日价格已更新')
    dailyPriceModal.value.show = false
  }
}

// 重置为统一价格
function resetDailyPrices() {
  const { roomData, prices } = dailyPriceModal.value
  if (!roomData || !prices.length) return

  const uniformPrice = roomData.price || 0
  prices.forEach(dayPrice => {
    dayPrice.price = uniformPrice
  })

  message.info(`已重置为统一价格：¥${uniformPrice}`)
}

// 计算总价格
function calculateTotalPrice(prices) {
  if (!prices || !Array.isArray(prices)) return '0.00'
  return prices.reduce((total, dayPrice) => total + (dayPrice.price || 0), 0).toFixed(2)
}

// 计算平均价格
function calculateAveragePrice(prices) {
  if (!prices || !Array.isArray(prices) || prices.length === 0) return '0.00'
  const total = prices.reduce((sum, dayPrice) => sum + (dayPrice.price || 0), 0)
  return (total / prices.length).toFixed(2)
}

// 格式化日期显示
function formatDate(dateStr) {
  try {
    const date = new Date(dateStr)
    return date.toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit'
    })
  } catch (error) {
    return dateStr
  }
}

// 获取星期几
function getDayOfWeek(dateStr) {
  try {
    const date = new Date(dateStr)
    const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    return days[date.getDay()]
  } catch (error) {
    return ''
  }
}

// 获取有效的时间戳
function getValidTimestamp(timeValue) {
  try {
    if (!timeValue) {
      // 如果没有时间值，使用当前时间
      return Math.floor(Date.now() / 1000)
    }

    const date = new Date(timeValue)
    if (isNaN(date.getTime())) {
      // 如果时间无效，使用当前时间
      console.warn('入住时间无效，使用当前时间:', timeValue)
      return Math.floor(Date.now() / 1000)
    }

    return Math.floor(date.getTime() / 1000)
  } catch (error) {
    console.error('处理入住时间时出错:', error)
    return Math.floor(Date.now() / 1000)
  }
}

// 获取客人的 common_code
async function getGuestCommonCode(phone) {
  try {
    // 1. 先尝试通过手机号查询会员信息
    const memberResponse = await api.getUserInfo({ phone })
    if (memberResponse && memberResponse.data && memberResponse.data.common_code) {
      console.log('从会员系统获取到 common_code:', memberResponse.data.common_code)
      return memberResponse.data.common_code
    }

    // 2. 如果没有找到会员，返回空字符串（后续需要创建新会员）
    console.log('未找到会员信息，需要创建新会员')
    return ''
  } catch (error) {
    console.error('获取客人 common_code 失败:', error)
    return ''
  }
}

// 创建新客人并获取 common_code
async function createGuestCommonCode(guestInfo) {
  try {
    // 这里应该调用创建会员的接口
    // 暂时返回一个临时的 common_code
    const tempCommonCode = `TEMP_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    console.log('创建临时 common_code:', tempCommonCode)
    return tempCommonCode
  } catch (error) {
    console.error('创建客人 common_code 失败:', error)
    return ''
  }
}

// 检查是否有脏房
function checkForDirtyRooms() {
  console.log('🔍 checkForDirtyRooms 开始执行')

  // 检查当前选择的房间是否有脏房
  if (form.value.roomsToBook && form.value.roomsToBook.length > 0) {
    console.log('📋 检查表单中的房间数据:', form.value.roomsToBook)
    // 检查表单中的房间数据
    const hasDirtyInForm = form.value.roomsToBook.some(room => {
      const isDirty = room.cleanStatus === '脏' || room.cleanStatus === 4 ||
                     room.cleanStatusName === '脏' || room.cleanStatusName === 4
      console.log(`房间 ${room.roomNumber} 清洁状态: cleanStatus=${room.cleanStatus}, cleanStatusName=${room.cleanStatusName}, 是否脏房: ${isDirty}`)
      return isDirty
    })
    console.log('📋 表单中是否有脏房:', hasDirtyInForm)
    if (hasDirtyInForm) return true
  }

  // 检查原始房间数据（props.roomData）
  if (props.roomData) {
    console.log('🏠 检查Props中的房间数据:', props.roomData)
    const roomData = Array.isArray(props.roomData) ? props.roomData : [props.roomData]
    const hasDirtyInProps = roomData.some(room => {
      // 检查多种可能的清洁状态字段
      const isDirty = room.cleanStatus === '脏' ||
             room.clean_status === '脏' ||
             room.cleanStatus === 4 ||
             room.clean_status === 4 ||
             room.cleanStatusName === '脏' ||
             room.clear_status_name === '脏' ||
             room.cleanStatusName === 4 ||
             room.clear_status_name === 4 ||
             // 检查清洁状态ID（根据代码分析，脏房的ID可能是2）
             room.cleanStatus === 2 ||
             room.clean_status === 2 ||
             room.clear_status_id === 2
      console.log(`Props房间 ${room.roomNumber || room.room_number} 清洁状态详情:`, {
        cleanStatus: room.cleanStatus,
        clean_status: room.clean_status,
        cleanStatusName: room.cleanStatusName,
        clear_status_name: room.clear_status_name,
        clear_status_id: room.clear_status_id,
        isDirty: isDirty
      })
      return isDirty
    })
    console.log('🏠 Props中是否有脏房:', hasDirtyInProps)
    if (hasDirtyInProps) return true
  }

  console.log('✅ 未检测到脏房')
  return false
}

// 显示脏房确认对话框
function showDirtyRoomConfirmDialog() {
  return new Promise((resolve) => {
    dialog.warning({
      title: '脏房入住确认',
      content: '您选择的房间中包含脏房，确认要继续办理入住吗？',
      positiveText: '确认入住',
      negativeText: '取消',
      onPositiveClick: () => {
        console.log('用户确认脏房入住')
        resolve(true)
      },
      onNegativeClick: () => {
        console.log('用户取消脏房入住')
        resolve(false)
      },
      onClose: () => {
        resolve(false)
      }
    })
  })
}

// 提交表单
async function handleSubmit() {
  // 检查是否有选择房间
  if (!form.value.roomsToBook || form.value.roomsToBook.length === 0) {
    message.error('请至少选择一间房间')
    return
  }

  // 检查是否有脏房，如果有则需要确认
  console.log('🔍 开始检查脏房状态...')
  console.log('表单房间数据:', form.value.roomsToBook)
  console.log('Props房间数据:', props.roomData)

  const hasDirtyRoom = checkForDirtyRooms()
  console.log('🏠 脏房检测结果:', hasDirtyRoom)

  if (hasDirtyRoom) {
    console.log('⚠️ 检测到脏房，显示确认对话框')
    const confirmed = await showDirtyRoomConfirmDialog()
    console.log('👤 用户确认结果:', confirmed)
    if (!confirmed) {
      console.log('❌ 用户取消了脏房入住操作')
      return // 用户取消了操作
    }
    console.log('✅ 用户确认继续脏房入住')
  } else {
    console.log('✅ 未检测到脏房，继续正常入住流程')
  }

  // 手动触发表单验证
  formRef.value?.validate(async (errors) => {
    if (errors) {
      // 找到第一个错误并显示
      const firstError = Object.values(errors)[0]
      if (firstError && firstError[0]) {
        message.error(firstError[0].message)
      }
      return
    }

    try {
      loading.value = true

      // 1. 处理 common_code（非会员用户可以为空）
      if (!form.value.commonCode && form.value.contactPhone) {
        console.log('正在获取客人 common_code...')
        let commonCode = await getGuestCommonCode(form.value.contactPhone)

        if (commonCode) {
          // 只有找到现有会员时才设置 common_code
          form.value.commonCode = commonCode
          console.log('已设置会员 common_code:', commonCode)
        } else {
          // 非会员用户，common_code 保持为空字符串
          form.value.commonCode = ''
          console.log('非会员用户，common_code 设置为空字符串')
        }
      }

      // 2. 构建请求参数
      const params = buildCheckInParams()
      console.log('入住请求参数:', JSON.stringify(params, null, 2))

      // 获取对应的API函数
      const checkInApiFunction = getCheckInApiFunction(form.value.sellType)

      // 调用入住接口
      const response = await checkInApiFunction(params)
      console.log('入住响应:', response)

      // 根据接口文档处理响应：code=0成功，code=1失败，code=886过期
      if (response.code === 0) {
        message.success(`成功办理入住 ${form.value.roomsToBook.length} 间房间`)

        // 发送成功事件
        emit('success', {
          ...form.value,
          totalAmount: form.value.roomsToBook.reduce((sum, room) =>
            sum + (room.price || 0) + (room.deposit || 0), 0),
          roomCount: form.value.roomsToBook.length
        })

        // 触发房态刷新事件
        emit('refreshRoomStatus')

        // 关闭弹窗
        show.value = false

      } else if (response.code === 886) {
        message.error('登录已过期，请重新登录')
      } else if (response.code === 1) {
        message.error(response.message || '办理入住失败')
      } else {
        message.error(response.message || `办理入住失败，错误代码：${response.code}`)
      }
    } catch (error) {
      console.error('办理入住失败:', error)
      message.error(error.message || '办理入住失败，请重试')
    } finally {
      loading.value = false
    }
  })
}
// 房间选择相关
const showRoomSelect = ref(false)
let changingRoomIndex = -1 // 标记正在换房的索引

// 入住信息（传递给RoomSelector组件）
const checkInInfo = computed(() => ({
  actualDate: '全天房',
  priceScheme: '散客'
}))

// 添加房间
function handleAddRoom() {
  changingRoomIndex = -1 // 确保是添加模式
  showRoomSelect.value = true
}

// 换房
function handleChangeRoom(index) {
  changingRoomIndex = index
  showRoomSelect.value = true
}

// 处理房间选择器确认事件
async function handleRoomSelectorConfirm(selectedRooms) {
  if (changingRoomIndex > -1) {
    // 换房逻辑
    if (selectedRooms.length === 1) {
      const newRoom = selectedRooms[0]
      const oldRoom = form.value.roomsToBook[changingRoomIndex]

      // 更新房间信息（重置价格与服务为加载中，再去取价，但保留入住人信息）
      form.value.roomsToBook[changingRoomIndex] = {
        ...oldRoom,
        id: newRoom.room_id,
        roomNumber: newRoom.room_number,
        roomTypeId: newRoom.room_type_id,
        roomTypeName: newRoom.room_type_name,
        price: 0,
        deposit: oldRoom.deposit || 0,
        packageId: null,
        packagePrice: 0,
        packageName: null,
        serviceName: '无服务',
        servicePrice: 0,
        packageOptions: [],
        roomServiceList: [],
        priceLoading: true,
        // 保留原有的入住人信息
        guests: oldRoom.guests || [
          {
            id: Date.now() + Math.random(),
            name: '',
            gender: '男',
            phone: '',
            idType: '身份证',
            idNumber: ''
          }
        ]
      }

      // 重新为该房间获取价格与服务
      await fetchRoomPriceForRoom(changingRoomIndex, form.value.memberGrade || null)

      message.success(`已将房间更换为 ${newRoom.room_number}`)
      changingRoomIndex = -1
    } else {
      message.warning('换房时只能选择一个房间')
      return
    }
  } else {
    // 添加房间逻辑
    if (selectedRooms.length > 0) {
      const startIndex = form.value.roomsToBook.length
      const newRooms = selectedRooms.map(room => ({
        id: room.room_id,
        roomNumber: room.room_number,
        roomTypeId: room.room_type_id,
        roomTypeName: room.room_type_name,
        price: 0,
        deposit: 0,
        packageId: null,
        packagePrice: 0,
        packageName: null,
        serviceName: '无服务',
        servicePrice: 0,
        packageOptions: [],
        roomServiceList: [],
        priceLoading: true,
        // 为新房间添加默认入住人
        guests: [
          {
            id: Date.now() + Math.random(),
            name: '',
            gender: '男',
            phone: '',
            idType: '身份证',
            idNumber: ''
          }
        ]
      }))

      form.value.roomsToBook.push(...newRooms)

      // 批量为新加入的房间获取价格与服务
      const memberGradeId = form.value.memberGrade || null
      const tasks = newRooms.map((_, idx) => fetchRoomPriceForRoom(startIndex + idx, memberGradeId))
      await Promise.all(tasks)

      message.success(`已添加 ${selectedRooms.length} 间房间`)
    }
  }

  showRoomSelect.value = false
}

// 处理房间选择器取消事件
function handleRoomSelectorCancel() {
  showRoomSelect.value = false
  changingRoomIndex = -1
}

// 移除房间
function handleRemoveRoom(index) {
  if (form.value.roomsToBook.length > 1) {
    form.value.roomsToBook.splice(index, 1)
    message.success('已移除房间')
  } else {
    message.warning('至少需要保留一间房间')
  }
}

// 套餐选项 - 从API获取
const packageOptions = ref([])

// 从getRoomPriceByDate接口获取的套餐信息
const availablePackages = ref([])
const roomServiceList = ref([]) // 存储room_service数据

// 获取会员历史和详细信息（合并调用，避免重复请求）
async function fetchMemberHistoryAndDetail(commonCode) {
  if (!commonCode) return

  historyLoading.value = true
  try {
    const response = await api.getUserHistory({
      common_code: commonCode
    })

    if (response.code === 0 && response.data) {
      // 保存完整的会员详细信息
      memberDetailInfo.value = response.data

      // 从stay字段获取入住统计信息用于历史记录显示
      const stayInfo = response.data.stay || {}

      // 创建一个基于统计信息的显示数据
      historyList.value = [{
        summary: true,
        stay_count: stayInfo.stay_count || 0,
        avg_price: stayInfo.avg_price || 0,
        favorite_room_type: stayInfo.favorite_room_type || '-',
        favorite_room: stayInfo.favorite_room || '-',
        last_stay_time: stayInfo.last_stay_time ? new Date(stayInfo.last_stay_time * 1000).toLocaleDateString() : '-'
      }]
    } else {
      historyList.value = []
      memberDetailInfo.value = null
    }
  } catch (error) {
    console.error('获取会员信息失败:', error)
    historyList.value = []
    memberDetailInfo.value = null
  } finally {
    historyLoading.value = false
  }
}

// 获取优惠券列表
async function fetchMemberCoupons(commonCode) {
  if (!commonCode) return

  couponLoading.value = true
  try {
    const response = await api.getUserCoupon({
      common_code: commonCode,
      page: 1,
      limit: 50
    })

    if (response.code === 0 && response.data) {
      couponList.value = response.data.list || []
    } else {
      couponList.value = []
    }
  } catch (error) {
    console.error('获取优惠券列表失败:', error)
    couponList.value = []
  } finally {
    couponLoading.value = false
  }
}



// 查看会员信息（复用 OrderDetailModal 中的逻辑）
function handleViewMemberInfo() {
  if (!selectedMember.value || !selectedMember.value.common_code) {
    message.warning('该用户没有会员信息')
    return
  }

  memberInfoModal.value.commonCode = selectedMember.value.common_code
  memberInfoModal.value.show = true
}

// 选择优惠券
function selectCoupon(coupon) {
  // 这里可以添加选择优惠券的逻辑
  // 比如将优惠券信息保存到表单中，或者应用优惠券折扣
  console.log('选择优惠券:', coupon)

  // 示例：将选中的优惠券保存到表单中
  if (!form.value.selectedCoupons) {
    form.value.selectedCoupons = []
  }

  // 检查是否已经选择了这张优惠券
  const existingIndex = form.value.selectedCoupons.findIndex(c => c.id === coupon.id)
  if (existingIndex === -1) {
    form.value.selectedCoupons.push(coupon)
    message.success(`已选择优惠券：${coupon.title}`)
  } else {
    message.warning('该优惠券已经选择过了')
  }

  // 关闭优惠券弹窗
  showCouponModal.value = false
}

// 更新房间套餐价格
function updateRoomPackagePrice(roomIndex, packageId) {
  const room = form.value.roomsToBook[roomIndex]
  if (!room) return

  // 找到选中的套餐信息（从房间自己的套餐选项中查找）
  const selectedPackage = room.packageOptions?.find(pkg => pkg.value === packageId)

  if (selectedPackage && selectedPackage.price !== undefined) {
    // 保存套餐信息到房间数据中
    room.packagePrice = selectedPackage.price
    room.packageName = selectedPackage.serviceName

    console.log(`房间 ${room.roomNumber} 选择套餐: ${selectedPackage.serviceName}, 价格: ¥${selectedPackage.price}`)
  } else {
    // 清空套餐信息
    room.packagePrice = 0
    room.packageName = null
  }
}

// 总应收款计算
const totalRoomPrice = computed(() => {
  return form.value.roomsToBook.reduce((total, room) => {
    return total + (room.price || 0)
  }, 0)
})

const totalDeposit = computed(() => {
  return form.value.roomsToBook.reduce((total, room) => {
    return total + (room.deposit || 0)
  }, 0)
})

const totalPackagePrice = computed(() => {
  return form.value.roomsToBook.reduce((total, room) => {
    return total + (room.packagePrice || 0)
  }, 0)
})

const totalAmount = computed(() => {
  return totalRoomPrice.value + totalDeposit.value + totalPackagePrice.value
})

// 入住人管理函数
function addGuest(roomIndex, insertIndex = null) {
  const newGuest = {
    id: Date.now() + Math.random(),
    name: '',
    gender: '男',
    phone: '',
    idType: '身份证',
    idNumber: '',
    nation: '汉族',
    address: '',
    birthday: '',
    commonCode: ''
  }

  if (insertIndex !== null) {
    // 在指定位置插入
    form.value.roomsToBook[roomIndex].guests.splice(insertIndex, 0, newGuest)
  } else {
    // 添加到末尾
    form.value.roomsToBook[roomIndex].guests.push(newGuest)
  }
}

function removeGuest(roomIndex, guestIndex) {
  if (form.value.roomsToBook[roomIndex].guests.length > 1) {
    form.value.roomsToBook[roomIndex].guests.splice(guestIndex, 1)
  } else {
    message.warning('每个房间至少需要一个入住人')
  }
}

// 监听销售规则变化，更新房价和入住时长控制（优化版）
watch(() => form.value.saleRule, (newVal, oldVal) => {
  // 避免初始化时的重复触发
  if (newVal === oldVal) return

  if (newVal) {
    const selectedRule = saleRuleOptions.value.find(rule => rule.value === newVal)
    if (selectedRule) {
      // 使用防抖避免快速切换时的性能问题
      // clearTimeout(sellTypeChangeTimer)
      // sellTypeChangeTimer = setTimeout(() => {
        handleSaleRuleChange(selectedRule)

        // 如果已经有房间，重新获取所有房间的价格
        if (form.value.roomsToBook.length > 0) {
          console.log('🔄 销售规则变化，重新获取所有房间价格')
          fetchAllRoomPrices()
        } else {
          // 如果没有房间，只获取基础房价
          fetchRoomPrice()
        }
      // }, 100)
    }
  }
}, { flush: 'post' }) // 在 DOM 更新后执行

// 组件挂载时预加载数据 - 极致性能优化
onMounted(() => {
  // 预加载销售类型数据（在空闲时进行，不阻塞UI）
  if (window.requestIdleCallback) {
    requestIdleCallback(() => {
      if (sellTypeOptions.value.length === 0) {
        fetchSellTypes()
      }
    })
  } else {
    setTimeout(() => {
      if (sellTypeOptions.value.length === 0) {
        fetchSellTypes()
      }
    }, 100)
  }
})

// 组件卸载时清理资源
onUnmounted(() => {
  // 清理定时器
  // if (sellTypeChangeTimer) {
  //   clearTimeout(sellTypeChangeTimer)
  // }

  // 清理缓存
  // saleRuleCache.value.clear()

})

// 监听入住时长变化，更新房价和离店时间
watch(() => form.value.stayDuration, (newVal) => {
  if (newVal && newVal > 0) {
    // 先更新离店时间，再获取房价
    updateCheckOutTime()
    // fetchRoomPrice()
  }
}, { flush: 'post' }) // 在 DOM 更新后执行

// 监听订单来源变化
watch(() => form.value.orderSource, () => {
})

// 监听联系人姓名变化，当清空时重置价格方案
watch(() => form.value.contactName, (newVal) => {
  // 如果联系人姓名被清空，重置价格方案为门市价
  if (!newVal || newVal.trim() === '') {
    form.value.priceScheme = 'market'
    form.value.memberGrade = null
  }
})

// 监听联系人手机号变化，当清空时重置价格方案
watch(() => form.value.contactPhone, (newVal) => {
  // 如果联系人手机号被清空，重置价格方案为门市价
  if (!newVal || newVal.trim() === '') {
    form.value.priceScheme = 'market'
    form.value.memberGrade = null
  }
})

// 监听会员等级变化，重新获取房间价格（仅用于自动填充场景）
watch(() => form.value.memberGrade, async (newVal, oldVal) => {
  // 避免初始化时的重复触发
  if (newVal === oldVal) return

  // 如果是通过handleMemberGradeChange手动选择的，不需要重复处理
  // 这个watch主要用于自动填充会员信息时的价格更新
  console.log('🔍 会员等级watch触发:', { newVal, oldVal, priceScheme: form.value.priceScheme })

  // 只在特定条件下自动获取价格（避免与手动选择冲突）
  if (newVal && form.value.priceScheme === 'member' && window.memberGradeLoadedCallback) {
    console.log('🔄 自动填充场景：重新获取会员价格')
    try {
      await fetchAllRoomPrices(newVal)

      // 更新表单顶部的房价显示
      if (form.value.roomsToBook.length > 0) {
        form.value.roomPrice = form.value.roomsToBook[0].price
      }
    } catch (error) {
      console.error('自动获取会员价格失败:', error)
    }
  }
}, { flush: 'post' })




</script>

<style scoped>
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 8px;
}
.n-button[type="primary"], .modal-footer .n-button[type="primary"] {
  background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-color-suppl) 100%);
  color: #fff;
  border: none;
  box-shadow: 0 2px 8px 0 rgba(var(--primary-color-rgb), 0.08);
  transition: box-shadow 0.2s, background 0.2s;
}
.n-button[type="primary"]:hover, .modal-footer .n-button[type="primary"]:hover {
  background: linear-gradient(90deg, var(--primary-color-hover) 0%, var(--primary-color-pressed) 100%);
  box-shadow: 0 4px 16px 0 rgba(var(--primary-color-rgb), 0.16);
}
.n-modal {
  background: rgba(var(--primary-color-rgb), 0.02);
  border-radius: 14px;
  box-shadow: 0 4px 32px 0 rgba(var(--primary-color-rgb), 0.08);
}
.n-form-item-label {
  color: rgba(0, 0, 0, 0.65);
}
.n-input, .n-select, .n-date-picker, .n-input-number {
  background: #fafdff;
  border-radius: 8px;
}
.n-input:focus, .n-select:focus, .n-date-picker:focus, .n-input-number:focus {
  box-shadow: 0 0 0 2px #4fd1ff33;
}

/* 添加房间选择样式 */
.room-selection-header {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.room-selection-filters {
  flex: 1;
}

.room-selection-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 12px;
  max-height: 500px;
  overflow-y: auto;
  padding: 8px 4px;
}

.room-selection-list {
  max-height: 500px;
  overflow-y: auto;
}

.room-selection-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  background: #f9fafc;
}

.room-selection-item:hover {
  border-color: #4fd1ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(79, 209, 255, 0.1);
}

.room-selection-item.selected {
  border-color: #4fd1ff;
  background: #f0f9ff;
  box-shadow: 0 0 0 1px rgba(79, 209, 255, 0.5);
}

/* 不同状态的房间样式 */
.room-selection-item.status-vacant {
  background-color: #f0f9ff;
  border-color: #4fd1ff;
}

.room-selection-item.status-occupied {
  background-color: #e6f7f1;
  border-color: #2eae80;
  opacity: 0.7;
}

.room-selection-item.status-reserved {
  background-color: #f3f5f7;
  border-color: #a98fff;
  opacity: 0.7;
}

.room-selection-item.status-cleaning {
  background-color: rgba(var(--primary-color-rgb), 0.05);
  border-color: var(--primary-color);
  opacity: 0.7;
}

.room-selection-item.status-maintenance {
  background-color: #f8f5f1;
  border-color: #ffd93b;
  opacity: 0.7;
}

.room-selection-item.status-locked {
  background-color: #f3f4f6;
  border-color: #7b8fa3;
  opacity: 0.7;
}

/* 表格行样式 */
:deep(.selected-row) {
  background-color: rgba(var(--primary-color-rgb), 0.1) !important;
}

:deep(.status-vacant) {
  background-color: rgba(var(--primary-color-rgb), 0.08);
}

:deep(.status-occupied) {
  background-color: #e6f7f1;
}

:deep(.status-reserved) {
  background-color: rgba(var(--primary-color-rgb), 0.05);
}

:deep(.status-cleaning) {
  background-color: rgba(var(--primary-color-rgb), 0.05);
}

:deep(.status-maintenance) {
  background-color: #f8f5f1;
}

:deep(.status-locked) {
  background-color: #f3f4f6;
}

.room-selection-status {
  position: absolute;
  top: 6px;
  right: 6px;
  font-size: 10px;
  background: rgba(255, 255, 255, 0.9);
  padding: 1px 6px;
  border-radius: 4px;
  color: #64748b;
}

.room-selection-number {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.room-selection-type {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 6px;
}

.room-selection-price {
  font-size: 14px;
  font-weight: 600;
  color: #f43f5e;
}

.room-selection-checkbox {
  position: absolute;
  top: 8px;
  left: 8px;
}

.room-selection-footer {
  margin-top: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #e5e7eb;
}

.room-selection-summary {
  font-size: 14px;
  color: #4b5563;
}

.room-count {
  font-weight: 600;
  color: #f43f5e;
  font-size: 16px;
}

.room-selection-actions {
  display: flex;
  gap: 8px;
}

.selected-rooms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 8px;
}

/* 优化滚动条样式 */
.modal-content {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f3f4f6;
  padding-right: 8px;
}

.modal-content::-webkit-scrollbar {
  width: 6px;
}

.modal-content::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 3px;
}

.modal-content::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.modal-content::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* 确保弹窗底部按钮固定 */
.modal-footer {
  display: flex !important;
  justify-content: flex-end !important;
  gap: 12px !important;
  padding: 6px 0 !important;
}

/* 固定底部弹窗样式 */
.fixed-footer-modal :deep(.n-card) {
  display: flex !important;
  flex-direction: column !important;
  height: 90vh !important;
  overflow: hidden !important;
}

.fixed-footer-modal :deep(.n-card__content) {
  flex: 1 1 auto !important;
  min-height: 0 !important; /* 允许内部滚动 */
  overflow: auto !important;
}

.fixed-footer-modal :deep(.n-card__footer),
.fixed-footer-modal :deep(.n-card__action) {
  flex-shrink: 0 !important;
  border-top: 1px solid #e0e0e6 !important;
  background: #fff !important;
  position: sticky !important;
  bottom: 0 !important;
  z-index: 2 !important;
}

/* 紧凑布局样式 */
.compact-layout {
  padding: 0 !important;
}

.compact-layout .compact-card {
  margin-bottom: 8px;
}

.compact-layout .compact-card:last-child {
  margin-bottom: 0;
}

.compact-layout .n-card {
  border-radius: 6px;
}

.compact-layout .n-card__header {
  padding: 8px 12px !important;
  font-size: 13px !important;
  font-weight: 600;
}

.compact-layout .n-card__content {
  padding: 8px 12px !important;
}

.compact-layout .n-form-item {
  margin-bottom: 6px !important;
}

.compact-layout .n-form-item__label {
  font-size: 12px !important;
  padding-bottom: 2px !important;
  line-height: 1.2 !important;
}

.compact-layout .n-input,
.compact-layout .n-select,
.compact-layout .n-auto-complete,
.compact-layout .n-input-number,
.compact-layout .n-date-picker {
  font-size: 13px !important;
  min-height: 30px !important;
}

.compact-layout .n-input__input,
.compact-layout .n-base-selection__label,
.compact-layout .n-base-selection-input__content {
  font-size: 13px !important;
  line-height: 1.3 !important;
}

.compact-layout .n-base-selection {
  min-height: 28px !important;
}

.compact-layout .n-base-selection__border,
.compact-layout .n-base-selection__state-border {
  border-radius: 4px !important;
}

.compact-layout .n-data-table {
  font-size: 11px !important;
}

.compact-layout .n-data-table th,
.compact-layout .n-data-table td {
  padding: 4px 6px !important;
  line-height: 1.3 !important;
}

/* 去掉表单项反馈区域的多余高度 */
.room-pricing :deep(.n-form-item .n-form-item-feedback-wrapper) {
  min-height: 0 !important;
  height: 0 !important;
  overflow: hidden;
}

.compact-layout :deep(.n-form-item .n-form-item-feedback-wrapper) {
  min-height: 0 !important;
  height: 0 !important;
  overflow: hidden;
}

/* 房间卡片样式 */
.rooms-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 65vh;
  overflow-y: auto;
  padding-right: 8px;
}

.room-card {
  border: 1px solid #e0e0e6;
  border-left: 4px solid #18a058;
  border-radius: 8px;
  padding: 12px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  transition: all 0.3s ease;
  position: relative;
  margin-bottom: 4px;
}

.room-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0,0,0,0.12);
  border-left-color: #36ad6a;
}

/* 房间序号标识 */
.room-index {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  background: #18a058;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  z-index: 10;
  border: 2px solid #fff;
}

.room-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 2px solid #f0f0f0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  margin: -12px -12px 10px -12px;
  padding: 10px 12px 8px 12px;
  border-radius: 8px 8px 0 0;
}

.room-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.room-summary {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: auto;
}

.price-summary {
  font-size: 14px;
  font-weight: 600;
  color: #f56c6c;
}

.guest-summary {
  font-size: 12px;
  color: #909399;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 8px;
}

.room-number {
  font-size: 18px;
  font-weight: 700;
  color: #18a058;
  text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.room-type {
  font-size: 12px;
  color: #666;
  background: rgba(24, 160, 88, 0.1);
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid rgba(24, 160, 88, 0.2);
  font-weight: 500;
}

.room-actions {
  display: flex;
  gap: 8px;
}

.room-pricing {
  margin-bottom: 6px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}
/* 紧凑化房价/押金/套餐区域 */
.room-pricing :deep(.n-form-item) {
  margin-bottom: 0;
}
.room-pricing :deep(.n-form-item-label) {
  padding-bottom: 2px;
  font-size: 13px;
  line-height: 1.1;
}

/* 入住人信息样式 */
.guests-section {
  background: #fff;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
  padding: 10px;
}

.guests-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid #f0f0f0;
}

.guests-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.guests-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.guest-item {
  padding: 6px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.guest-item:hover {
  background: #f1f3f4;
  border-color: #d1d5db;
}

/* 总应收款显示样式 */
.total-amount-display {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: center;
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
  height: 100%;
  min-height: 80px;
}

.total-amount-label {
  font-size: 14px;
  font-weight: 500;
  opacity: 0.9;
  margin-bottom: 4px;
}

.total-amount-value {
  font-size: 28px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.total-amount-breakdown {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 12px;
  opacity: 0.8;
  text-align: right;
}

.total-amount-breakdown span {
  white-space: nowrap;
}

/* 销售信息卡片间距优化 */
.compact-card .n-form-item {
  margin-bottom: 8px !important;
}

.compact-card .n-form-item .n-form-item-label {
  padding-bottom: 2px !important;
}

.compact-layout .n-data-table th {
  font-size: 11px !important;
  font-weight: 600;
}

.compact-layout .member-info-section {
  margin-top: 6px !important;
  padding-top: 6px !important;
}

.compact-layout .member-actions {
  margin-top: 4px !important;
}

.compact-layout .member-summary {
  margin-bottom: 6px !important;
}

.compact-layout .n-button--small {
  font-size: 11px !important;
  padding: 0 8px !important;
  height: 24px !important;
}

.compact-layout .n-tag--small {
  font-size: 10px !important;
  padding: 0 6px !important;
  height: 18px !important;
}

.compact-layout .n-switch {
  transform: scale(0.8);
}

/* 确保内容区域可以正确滚动 */
.fixed-footer-modal .modal-content {
  padding-bottom: 8px;
}

/* 优化弹窗在小屏幕上的显示 */
@media (max-height: 800px) {
  .fixed-footer-modal .n-card__content {
    max-height: calc(95vh - 100px) !important;
  }
}

@media (max-height: 600px) {
  .fixed-footer-modal .n-card__content {
    max-height: calc(95vh - 80px) !important;
  }
}
.contact-phone-row {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: nowrap;
  width: 100%;
}

.contact-phone-row .phone-input {
  width: 160px; /* 固定宽度，给标题更多空间 */
  flex-shrink: 0;
}

.contact-phone-row .read-card-btn {
  white-space: nowrap;
  height: 28px;
  flex-shrink: 0; /* 按钮不收缩 */
}

.contact-phone-row :deep(.n-input),
.contact-phone-row :deep(.n-auto-complete) {
  height: 28px;
}

.contact-phone-row :deep(.n-input__input) {
  line-height: 28px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

/* 防止表单标签换行 */
:deep(.n-form-item-label) {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 防止表单标签换行 */
:deep(.n-form-item-label) {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  min-width: 0 !important;
}
</style>
