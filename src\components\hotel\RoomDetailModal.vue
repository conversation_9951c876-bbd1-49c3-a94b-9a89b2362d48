<template>
  <n-modal
    v-model:show="visible"
    :mask-closable="true"
    preset="card"
    title="房间详情"
    class="room-detail-modal"
    style="width: 1200px; max-height: 90vh;"
    :segmented="true"
  >
    <template #header>
      <div class="modal-header">
        <div class="room-title">
          <i class="i-mdi:bed"></i>
          <span>房间 {{ roomInfo.roomNumber }} 详情</span>
        </div>
        <div class="room-status-badge" :style="{ backgroundColor: roomInfo.statusColor }">
          {{ roomInfo.statusName }}
        </div>
      </div>
    </template>

    <div class="room-detail-content" v-if="roomInfo.id">
      <!-- 三列布局：左列房间信息，中列订单财务，右列操作 -->
      <div class="three-column-layout">

        <!-- 左列：房间和客人信息 -->
        <div class="left-column">
          <!-- 房间基本信息 -->
          <div class="info-section room-section">
            <div class="section-title">
              <i class="i-mdi:home"></i>
              <span>房间信息</span>
            </div>
            <div class="room-basic-info">
              <div class="room-number-line">
                <span class="room-number">{{ roomInfo.roomNumber }}</span>
                <span class="room-type">{{ roomInfo.roomType }}</span>
              </div>
              <div class="room-status-line">
                <n-tag :color="{ color: roomInfo.statusColor, textColor: '#fff' }" size="small">{{ roomInfo.statusName }}</n-tag>
                <n-tag :color="{ color: roomInfo.cleanStatusColor, textColor: '#fff' }" size="small">{{ roomInfo.cleanStatusName }}</n-tag>
              </div>
              <div class="room-details-compact">
                <span class="detail-item">楼层: {{ roomInfo.floor || '未知' }}</span>
                <span class="detail-item" v-if="roomInfo.buildingName">楼栋: {{ roomInfo.buildingName }}</span>
                <span class="detail-item connect-code" v-if="roomInfo.connectCode">联房: {{ roomInfo.connectCode }}</span>
              </div>
            </div>
          </div>

          <!-- 客人信息 -->
          <div class="info-section guest-section" v-if="guestInfo.name">
            <div class="section-title">
              <i class="i-mdi:account"></i>
              <span>客人信息</span>
            </div>
            <div class="guest-info-compact">
              <div class="guest-name-line">
                <span class="guest-name">{{ guestInfo.name }}</span>
                <span class="vip-level" v-if="guestInfo.vipLevel">{{ guestInfo.vipLevel }}</span>
              </div>
              <div class="guest-contact">
                <span class="phone">{{ guestInfo.phone || '未提供' }}</span>
              </div>
              <div class="guest-id" v-if="guestInfo.idCard">
                <span class="id-card">{{ guestInfo.idCard }}</span>
              </div>
            </div>
          </div>

          <!-- 联房信息 -->
          <div class="info-section connect-section" v-if="connectRooms.length > 0">
            <div class="section-title">
              <i class="i-mdi:home-group"></i>
              <span>联房 ({{ connectRooms.length }}间)</span>
            </div>
            <div class="connect-rooms-compact">
              <div
                v-for="room in connectRooms.slice(0, 3)"
                :key="room.bill_id"
                class="connect-room-compact"
                :class="{ 'current': room.bill_id === currentBillId }"
              >
                <span class="room-num">{{ room.room_number }}</span>
                <span class="room-price">¥{{ room.room_price || 0 }}</span>
                <n-tag v-if="room.main_room" size="tiny" type="success">主</n-tag>
              </div>
              <div v-if="connectRooms.length > 3" class="more-rooms">
                +{{ connectRooms.length - 3 }}间
              </div>
            </div>
          </div>
        </div>

        <!-- 中列：订单和财务信息 -->
        <div class="middle-column">
          <div class="info-section order-section">
            <div class="section-title">
              <i class="i-mdi:receipt"></i>
              <span>订单财务</span>
              <n-button
                v-if="orderInfo && orderInfo.billId"
                size="tiny"
                type="primary"
                quaternary
                circle
                @click="handleOpenOrderDetail"
                class="order-detail-btn-icon"
                title="查看订单详情"
              >
                <template #icon>
                  <i class="i-mdi:open-in-new"></i>
                </template>
              </n-button>
            </div>

            <!-- 有订单时显示订单信息 -->
            <div v-if="hasOrderInfo">
              <!-- 订单基本信息 -->
              <div class="order-info-compact">
                <div class="order-line">
                  <span class="label">订单号:</span>
                  <span class="value">{{ getOrderDisplayText() }}</span>
                </div>
                <div class="order-line">
                  <span class="label">住宿:</span>
                  <span class="value">{{ (orderInfo && orderInfo.stayDays) || calculateStayDays() }}天</span>
                </div>
                <div class="order-line">
                  <span class="label">入住:</span>
                  <span class="value">{{ formatTime(orderInfo && orderInfo.checkInTime, 'short') }}</span>
                </div>
                <div class="order-line">
                  <span class="label">退房:</span>
                  <span class="value">{{ formatTime(orderInfo && orderInfo.checkOutTime, 'short') }}</span>
                </div>
              </div>

              <!-- 财务信息 -->
              <div class="finance-compact">
                <div class="finance-grid">
                  <div class="finance-item">
                    <span class="label">消费</span>
                    <span class="value consume">¥{{ (orderInfo && (orderInfo.consumeAmount || orderInfo.totalAmount)) || 0 }}</span>
                  </div>
                  <div class="finance-item">
                    <span class="label">付款</span>
                    <span class="value payment">¥{{ (orderInfo && (orderInfo.payAmount || orderInfo.paidAmount)) || 0 }}</span>
                  </div>
                  <div class="finance-item">
                    <span class="label">押金</span>
                    <span class="value deposit">¥{{ (orderInfo && (orderInfo.pledgeAmount || orderInfo.deposit)) || 0 }}</span>
                  </div>
                  <div class="finance-item balance-item">
                    <span class="label">余额</span>
                    <span class="value balance" :class="getBalanceClass((orderInfo && (orderInfo.balanceAmount || orderInfo.balance)) || 0)">
                      ¥{{ (orderInfo && (orderInfo.balanceAmount || orderInfo.balance)) || 0 }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 订单详情按钮 -->
              <div class="order-detail-action">
                <n-button
                  type="primary"
                  size="small"
                  ghost
                  @click="handleShowOrderDetail"
                  class="order-detail-btn"
                >
                  <template #icon><i class="i-mdi:receipt-text"></i></template>
                  查看订单详情
                </n-button>
              </div>
            </div>

            <!-- 无订单时显示提示 -->
            <div v-else class="no-order-info">
              <div class="no-order-icon">
                <i class="i-mdi:receipt-text-outline"></i>
              </div>
              <div class="no-order-text">该房间暂无订单信息</div>
              <div class="no-order-hint">房间状态：{{ roomInfo.statusName }}</div>
            </div>
          </div>
        </div>

        <!-- 右列：操作按钮 -->
        <div class="right-column">
          <div class="info-section action-section">
            <div class="section-title">
              <i class="i-mdi:lightning-bolt"></i>
              <span>快捷操作</span>
            </div>

            <!-- 主要业务操作 -->
            <div class="action-group-container">
              <div class="action-group primary-business">
                <div class="group-label">
                  <i class="i-mdi:account-check"></i>
                  <span>业务办理</span>
                </div>
                <div class="action-buttons-grid">
                  <n-button
                    type="primary"
                    size="small"
                    @click="handleReservation"
                    v-if="canReserve"
                    class="action-btn-modern"
                  >
                    <template #icon><i class="i-mdi:calendar-plus"></i></template>
                    办理预订
                  </n-button>
                  <n-button
                    type="success"
                    size="small"
                    @click="handleCheckIn"
                    v-if="canCheckIn"
                    class="action-btn-modern"
                  >
                    <template #icon><i class="i-mdi:login"></i></template>
                    办理入住
                  </n-button>
                  <n-button
                    type="warning"
                    size="small"
                    @click="handleCheckOut"
                    v-if="canCheckOut"
                    class="action-btn-modern"
                  >
                    <template #icon><i class="i-mdi:logout"></i></template>
                    办理退房
                  </n-button>
                  <n-button
                    type="info"
                    size="small"
                    @click="handleRoomService"
                    v-if="roomInfo.statusName === '在住'"
                    class="action-btn-modern"
                  >
                    <template #icon><i class="i-mdi:room-service"></i></template>
                    客房服务
                  </n-button>
                </div>
              </div>

              <!-- 房间状态操作 -->
              <div class="action-group room-status">
                <div class="group-label">
                  <i class="i-mdi:home-edit"></i>
                  <span>房间状态</span>
                </div>
                <div class="action-buttons-grid">
                  <n-button
                    size="small"
                    @click="handleSetDirty"
                    class="action-btn-modern status-dirty"
                    :disabled="roomInfo.cleanStatusName === '脏'"
                  >
                    <template #icon><i class="i-mdi:delete-sweep"></i></template>
                    置脏
                  </n-button>
                  <n-button
                    size="small"
                    @click="handleSetClean"
                    class="action-btn-modern status-clean"
                    :disabled="roomInfo.cleanStatusName === '净'"
                  >
                    <template #icon><i class="i-mdi:check-circle"></i></template>
                    置净
                  </n-button>
                  <n-button
                    size="small"
                    @click="handleRoomMaintenance"
                    class="action-btn-modern status-maintenance"
                  >
                    <template #icon><i class="i-mdi:wrench"></i></template>
                    维修
                  </n-button>
                  <n-button
                    size="small"
                    @click="handleRoomCleaning"
                    class="action-btn-modern status-cleaning"
                  >
                    <template #icon><i class="i-mdi:broom"></i></template>
                    清洁中
                  </n-button>
                </div>
              </div>

              <!-- 其他操作 -->
              <div class="action-group other-actions">
                <div class="group-label">
                  <i class="i-mdi:cog"></i>
                  <span>其他操作</span>
                </div>
                <div class="action-buttons-grid">
                  <n-button
                    size="small"
                    @click="handleLockRoom"
                    class="action-btn-modern other-lock"
                  >
                    <template #icon><i class="i-mdi:lock"></i></template>
                    锁房
                  </n-button>
                  <n-button
                    size="small"
                    @click="handleCloseRoom"
                    class="action-btn-modern other-close"
                  >
                    <template #icon><i class="i-mdi:door-closed"></i></template>
                    关房
                  </n-button>
                  <n-button
                    size="small"
                    @click="handleChangeRoom"
                    class="action-btn-modern other-change"
                    v-if="canChangeRoom"
                  >
                    <template #icon><i class="i-mdi:swap-horizontal"></i></template>
                    换房
                  </n-button>
                  <n-button
                    size="small"
                    @click="handleRoomHistory"
                    class="action-btn-modern other-history"
                  >
                    <template #icon><i class="i-mdi:history"></i></template>
                    历史记录
                  </n-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <n-empty description="暂无房间数据" />
    </div>

    <template #action>
      <n-space justify="end">
        <n-button @click="handleClose">关闭</n-button>
      </n-space>
    </template>
  </n-modal>

  <!-- 订单详情弹窗（惰性挂载，避免未就绪数据导致错误） -->
  <OrderDetailModal
    v-if="showOrderDetail"
    v-model:show="showOrderDetail"
    :bill-id="resolvedBillId"
    :order-data="billDetail"
  />
</template>

<script setup>
import { ref, computed, watch, onUnmounted } from 'vue'
import { useMessage } from 'naive-ui'
import { getRoomBillDetail, getConnectBill } from '@/views/hotel/roomStatus/api'
import { request } from '@/utils/http'
import OrderDetailModal from './OrderDetailModal.vue'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  roomData: {
    type: Object,
    default: () => null
  }
})

// 监听房间数据变化
watch(() => props.roomData, (newRoomData) => {
  if (newRoomData) {
    console.log('RoomDetailModal: 接收到新的房间数据')

    // 检查关键字段
    if (newRoomData.bill_id) {
      console.log('RoomDetailModal: 发现房间数据中有 bill_id:', newRoomData.bill_id)
    }
  }
}, { immediate: true })

const emit = defineEmits([
  'update:show',
  'close',
  'reservation',
  'checkin',
  'checkout',
  'room-service',
  'room-maintenance',
  'room-cleaning',
  'set-dirty',
  'set-clean',
  'lock-room',
  'close-room',
  'change-room',
  'room-history',
  'confirmCheckin',
  'cancelReservation',
  'billDetail',
  'viewFullOrder'
])

const message = useMessage()
const visible = ref(false)
const loading = ref(false)
const billDetail = ref(null)
const connectBillData = ref(null)
const billFundData = ref({
  balance_amount: 0,
  consume_amount: 0,
  pay_amount: 0,
  cash_pledge_amount: 0
})

// 订单详情选中的billId（从点击时确定）
const selectedBillId = ref(null)

// 解析最终传给订单详情的billId（点击优先，其次房卡数据，再次已加载详情）
const resolvedBillId = computed(() => {
  return selectedBillId.value || props.roomData?.bill_id || props.roomData?.billId || currentBillId.value
})

// 订单详情弹窗状态
const showOrderDetail = ref(false)

// 组件生命周期管理
const isAlive = ref(true)
onUnmounted(() => {
  isAlive.value = false
})

// 监听显示状态
watch(() => props.show, (newVal) => {
  visible.value = newVal

  // 当弹窗打开时，检查是否需要加载订单详情
  if (newVal && props.roomData) {
    loadBillDetailIfNeeded()
  }
}, { immediate: true })

watch(visible, (newVal) => {
  emit('update:show', newVal)

  // 当弹窗关闭时，触发close事件并清理数据
  if (!newVal) {
    emit('close')
    billDetail.value = null
  }
})

// 监听房间数据变化
watch(() => props.roomData, (newData) => {
  if (newData && visible.value) {
    loadBillDetailIfNeeded()
  }
}, { deep: true })

// 房间信息计算属性
const roomInfo = computed(() => {
  if (!props.roomData) {
    return {
      id: null,
      roomNumber: '',
      roomType: '',
      floor: '',
      statusName: '',
      statusColor: '',
      cleanStatusName: '',
      cleanStatusColor: '',
      buildingName: '',
      connectCode: ''
    }
  }

  const room = props.roomData
  return {
    id: room.id || room.room_id,
    roomNumber: room.roomNumber || room.room_number || '未知',
    roomType: room.roomType || room.room_type_name || '未知',
    floor: room.floor_number || room.floorNumber || room.floor_name || room.floor || '未知',
    statusName: room.roomStatusName || room.room_status_name || room.status || '未知',
    statusColor: room.roomStatusColor || room.room_status_color || '#666',
    cleanStatusName: room.cleanStatusName || room.clear_status_name || '未知',
    cleanStatusColor: room.cleanStatusColor || room.clear_color || '#666',
    buildingName: room.buildingName || room.building_name || '',
    connectCode: room.connectCode || room.connect_code || ''
  }
})

// 客人信息计算属性
const guestInfo = computed(() => {
  // 优先使用房间数据中的 bill_info（立即可用）
  if (props.roomData && props.roomData.bill_info) {
    const billInfo = props.roomData.bill_info
    return {
      name: billInfo.guest_name || billInfo.name || billInfo.link_man || '',
      phone: billInfo.guest_phone || billInfo.phone || billInfo.link_phone || '',
      idCard: billInfo.guest_id_card || billInfo.id_card || '',
      vipLevel: billInfo.grade_name || billInfo.vip_level || ''
    }
  }

  // 使用API返回的详细数据
  if (billDetail.value && billDetail.value.users && billDetail.value.users.length > 0) {
    const mainGuest = billDetail.value.users.find(user => user.is_main) || billDetail.value.users[0]
    return {
      name: mainGuest.name || '',
      phone: mainGuest.phone || '',
      idCard: mainGuest.identification_number || '',
      vipLevel: billDetail.value.grade_name || ''
    }
  }

  // 回退到房间基本信息（兼容旧数据结构）
  if (props.roomData && props.roomData.billInfo) {
    const bill = props.roomData.billInfo
    return {
      name: bill.guest_name || bill.name || bill.link_man || '',
      phone: bill.guest_phone || bill.phone || bill.link_phone || '',
      idCard: bill.guest_id_card || bill.id_card || '',
      vipLevel: bill.grade_name || bill.vip_level || ''
    }
  }

  // 从房间数据中直接获取客人信息
  if (props.roomData) {
    return {
      name: props.roomData.guestName || props.roomData.guest_name || '',
      phone: props.roomData.guestPhone || props.roomData.guest_phone || '',
      idCard: props.roomData.guestIdCard || props.roomData.guest_id_card || '',
      vipLevel: props.roomData.memberGrade || props.roomData.vip_level || ''
    }
  }

  return { name: '', phone: '', idCard: '', vipLevel: '' }
})

// 联房信息计算属性
const connectRooms = computed(() => {
  // 优先使用 getConnectBill 接口返回的数据
  if (connectBillData.value && connectBillData.value.connect_bills) {
    return connectBillData.value.connect_bills || []
  }

  // 回退到 getRoomBillDetail 接口返回的数据
  if (billDetail.value && billDetail.value.connect_bills) {
    return billDetail.value.connect_bills || []
  }

  return []
})

// 当前账单ID
const currentBillId = computed(() => {
  return billDetail.value?.id || billDetail.value?.bill_id || null
})

// 订单信息计算属性
const orderInfo = computed(() => {
  // 添加调试信息
  console.log('RoomDetailModal: orderInfo 计算属性调试', {
    roomData: props.roomData,
    hasBillInfo: !!(props.roomData && props.roomData.bill_info),
    billDetail: billDetail.value
  })

  // 首先检查房间数据中的直接 bill_id 字段
  if (props.roomData && props.roomData.bill_id) {
    console.log('RoomDetailModal: 发现房间数据中有直接的 bill_id:', props.roomData.bill_id)
    return {
      billId: props.roomData.bill_id,
      checkInTime: props.roomData.enter_time || props.roomData.start_time_plan || '',
      checkOutTime: props.roomData.leave_time_plan || props.roomData.end_time_plan || '',
      roomRate: props.roomData.room_price || props.roomData.bill_amount || 0,
      roomNumber: props.roomData.room_number || props.roomData.roomNumber || '',
      guestName: props.roomData.link_man || props.roomData.guest_name || '',
      stayDays: props.roomData.stay_days || 0,
      totalAmount: props.roomData.total_amount || props.roomData.bill_amount || 0,
      consumeAmount: props.roomData.consume_amount || 0,
      payAmount: props.roomData.pay_amount || props.roomData.paid_amount || 0,
      pledgeAmount: props.roomData.pledge_amount || props.roomData.deposit || 0,
      balanceAmount: props.roomData.balance_amount || props.roomData.balance || 0
    }
  }

  // 然后使用房间数据中的 bill_info（立即可用）
  if (props.roomData && props.roomData.bill_info) {
    const billInfo = props.roomData.bill_info
    const extractedBillId = billInfo.bill_code || billInfo.id || props.roomData.bill_id
    const billInfoKeys = Object.keys(billInfo)
    console.log('RoomDetailModal: 从 bill_info 提取 billId', {
      billInfo,
      billInfoKeys,
      extractedBillId,
      bill_code: billInfo.bill_code,
      id: billInfo.id,
      roomData_bill_id: props.roomData.bill_id
    })

    // 输出所有字段名，寻找可能的 billId 字段
    console.log('RoomDetailModal: bill_info 所有字段名:', billInfoKeys)

    // 检查一些可能的字段名
    const possibleBillIdFields = ['bill_id', 'billId', 'order_id', 'orderId', 'bill_no', 'billNo', 'order_no', 'orderNo', 'code', 'number']
    const foundFields = {}
    possibleBillIdFields.forEach(field => {
      if (billInfo[field] !== undefined) {
        foundFields[field] = billInfo[field]
      }
    })
    console.log('RoomDetailModal: 找到的可能字段:', foundFields)

    // 如果没有有效的账单ID，但有客人信息，说明房间有人但没有正式的账单
    if (!extractedBillId && billInfo.link_man) {
      console.log('RoomDetailModal: 房间有客人但没有有效的账单ID，返回 null')
      return null // 返回 null 表示没有订单信息
    }

    // 只有当有有效的账单ID时才返回订单信息
    if (extractedBillId) {
      return {
        billId: extractedBillId,
        checkInTime: billInfo.enter_time || billInfo.start_time_plan,
        checkOutTime: billInfo.leave_time_plan || billInfo.end_time_plan,
        roomRate: billInfo.bill_amount || billInfo.room_amount || 0,
        // 额外的详细信息
        roomNumber: billInfo.room_number || props.roomData.roomNumber || '',
        roomType: billInfo.room_type_name || props.roomData.roomType || '',
        stayDays: billInfo.stay_days || billInfo.stay_time || 0,
        totalAmount: billInfo.bill_amount || billInfo.total_amount || 0,
        paidAmount: billInfo.already_pay || billInfo.paid_amount || 0,
        balance: billInfo.bill_balance || billInfo.balance || 0,
        deposit: billInfo.cash_pledge || billInfo.deposit || 0,
        // 消费金额等
        consumeAmount: billInfo.consume_amount || 0,
        payAmount: billInfo.pay_amount || 0,
        balanceAmount: billInfo.balance_amount || billInfo.balance || 0,
        pledgeAmount: billInfo.cash_pledge_amount || billInfo.cash_pledge || 0
      }
    }
  }

  // 使用API返回的详细数据（异步加载）
  if (billDetail.value) {
    const extractedBillId = billDetail.value.bill_code || billDetail.value.id || ''
    console.log('RoomDetailModal: 从 billDetail 提取 billId', {
      billDetail: billDetail.value,
      extractedBillId,
      bill_code: billDetail.value.bill_code,
      id: billDetail.value.id
    })
    return {
      billId: extractedBillId,
      checkInTime: billDetail.value.enter_time,
      checkOutTime: billDetail.value.leave_time_plan,
      roomRate: billDetail.value.bill_amount || 0,
      // 额外的详细信息
      roomNumber: billDetail.value.room_number || '',
      roomType: billDetail.value.room_type_name || '',
      stayDays: billDetail.value.stay_days || 0,
      totalAmount: billDetail.value.bill_amount || 0,
      paidAmount: billDetail.value.already_pay || 0,
      balance: billDetail.value.bill_balance || 0,
      deposit: billDetail.value.cash_pledge || 0,
      // 从财务API获取的详细数据
      consumeAmount: billFundData.value.consume_amount || 0,
      payAmount: billFundData.value.pay_amount || 0,
      balanceAmount: billFundData.value.balance_amount || 0,
      pledgeAmount: billFundData.value.cash_pledge_amount || 0
    }
  }

  // 回退到房间基本信息
  if (props.roomData) {
    const room = props.roomData
    const extractedBillId = room.billId || room.bill_id || ''
    console.log('RoomDetailModal: 从房间基本信息提取 billId', {
      room,
      extractedBillId,
      billId: room.billId,
      bill_id: room.bill_id
    })
    return {
      billId: extractedBillId,
      checkInTime: room.checkInTime || room.start_time_plan,
      checkOutTime: room.checkOutTime || room.leave_time_plan,
      roomRate: room.rate || room.room_rate || 0,
      stayDays: 0,
      totalAmount: 0,
      paidAmount: 0,
      balance: 0,
      deposit: 0,
      consumeAmount: 0,
      payAmount: 0,
      balanceAmount: 0,
      pledgeAmount: 0
    }
  }

  console.log('RoomDetailModal: 使用默认空值，没有找到任何订单信息')
  return {
    billId: '',
    checkInTime: '',
    checkOutTime: '',
    roomRate: 0,
    stayDays: 0,
    totalAmount: 0,
    paidAmount: 0,
    balance: 0,
    deposit: 0,
    consumeAmount: 0,
    payAmount: 0,
    balanceAmount: 0,
    pledgeAmount: 0
  }
})

// 获取订单显示文本的方法
const getOrderDisplayText = () => {
  // 如果有有效的订单信息和账单ID，显示账单ID
  if (orderInfo.value && orderInfo.value.billId) {
    return orderInfo.value.billId
  }

  // 如果房间有客人信息但没有账单ID，显示相应提示
  if (props.roomData && props.roomData.bill_info && props.roomData.bill_info.link_man) {
    return '客人已入住，暂无正式订单'
  }

  // 如果房间为空或没有任何信息
  return '暂无订单信息'
}

// 是否有订单信息计算属性
const hasOrderInfo = computed(() => {
  // 检查是否有 bill_info 或其他订单相关信息
  if (props.roomData && props.roomData.bill_info) {
    return true
  }

  // 检查是否有 billDetail 数据
  if (billDetail.value) {
    return true
  }

  // 检查房间数据中是否有订单相关字段
  if (props.roomData) {
    const room = props.roomData
    if (room.billId || room.bill_id || room.guestName || room.guest_name) {
      return true
    }
  }

  return false
})

// 操作权限计算属性
const canReserve = computed(() => {
  return roomInfo.value.statusName === '空' || roomInfo.value.statusName === '可用'
})

const canCheckIn = computed(() => {
  return roomInfo.value.statusName === '预订' || roomInfo.value.statusName === '空'
})

const canCheckOut = computed(() => {
  return roomInfo.value.statusName === '入住'
})

const canChangeRoom = computed(() => {
  return roomInfo.value.statusName === '入住' || roomInfo.value.statusName === '预订'
})

// 加载订单详情
async function loadBillDetailIfNeeded() {
  if (!props.roomData || !isAlive.value) {
    return
  }

  const billId = props.roomData.bill_id || props.roomData.billId
  if (!billId) {

    return
  }

  loading.value = true

  try {
    const response = await getRoomBillDetail({ bill_id: billId })

    // 检查组件是否还存活
    if (!isAlive.value) {

      return
    }

    if (response && response.data) {
      billDetail.value = response.data

      // 同时加载财务数据和联房信息
      await Promise.all([
        loadBillFundData(billId),
        loadConnectBillData(billId)
      ])
    } else {

      billDetail.value = null
    }
  } catch (error) {

    if (isAlive.value) {
      message.error('加载订单详情失败')
    }
    billDetail.value = null
  } finally {
    if (isAlive.value) {
      loading.value = false
    }
  }
}

// 加载财务数据
async function loadBillFundData(billId) {
  if (!billId || !isAlive.value) {
    return
  }

  try {
    const response = await request.post('/admin/RoomBill/getRoomBillFund', {
      bill_id: billId,
      page: 1,
      limit: 10,
      type: 'all'
    })

    if (!isAlive.value) {
      return
    }

    if (response && response.data) {
      billFundData.value = {
        balance_amount: response.data.balance_amount || 0,
        consume_amount: response.data.consume_amount || 0,
        pay_amount: response.data.pay_amount || 0,
        cash_pledge_amount: response.data.cash_pledge_amount || 0
      }

    }
  } catch (error) {

  }
}

// 加载联房数据
async function loadConnectBillData(billId) {
  if (!billId || !isAlive.value) {
    return
  }

  try {
    const response = await getConnectBill({ bill_id: billId })

    if (!isAlive.value) {
      return
    }

    if (response && response.data) {
      connectBillData.value = response.data

    } else {
      connectBillData.value = null
    }
  } catch (error) {

    connectBillData.value = null
  }
}

// 时间格式化
function formatTime(timestamp, format = 'full') {
  if (!timestamp) return '未设置'

  try {
    const date = typeof timestamp === 'number'
      ? new Date(timestamp * 1000)
      : new Date(timestamp)

    if (isNaN(date.getTime())) return '无效时间'

    if (format === 'short') {
      return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {

    return '格式错误'
  }
}

// 事件处理函数
function handleClose() {
  visible.value = false
  emit('update:show', false)
  emit('close')

  // 清理数据
  billDetail.value = null
  connectBillData.value = null
  billFundData.value = {
    balance_amount: 0,
    consume_amount: 0,
    pay_amount: 0,
    cash_pledge_amount: 0
  }
  showOrderDetail.value = false
}

// 显示订单详情
function handleShowOrderDetail() {
  showOrderDetail.value = true
}

function handleReservation() {
  emit('reservation', props.roomData)
  message.info('打开预订弹窗')
}

function handleCheckIn() {
  emit('checkin', props.roomData)
  message.info('打开入住弹窗')
}

function handleCheckOut() {
  emit('checkout', props.roomData)
  message.info('办理退房')
}

function handleRoomMaintenance() {
  emit('room-maintenance', props.roomData)
  message.info('房间维修管理')
}

function handleRoomCleaning() {
  emit('room-cleaning', props.roomData)
  message.info('房间清洁管理')
}

function handleRoomService() {
  emit('room-service', props.roomData)
  message.info('客房服务')
}

function handleSetDirty() {
  emit('set-dirty', props.roomData)
  message.info('房间已置脏')
}

function handleSetClean() {
  emit('set-clean', props.roomData)
  message.info('房间已置净')
}

function handleLockRoom() {
  emit('lock-room', props.roomData)
  message.info('房间已锁定')
}

function handleCloseRoom() {
  emit('close-room', props.roomData)
  message.info('房间已关闭')
}

function handleChangeRoom() {
  emit('change-room', props.roomData)
  message.info('换房操作')
}

function handleRoomHistory() {
  emit('room-history', props.roomData)
  message.info('查看房间历史记录')
}

function handleOpenOrderDetail() {
  // 从房卡轻量数据提取（不依赖 billDetail 加载完成）
  const billId = props.roomData?.bill_id || props.roomData?.billId || billDetail.value?.id || billDetail.value?.bill_id

  console.log('RoomDetailModal: handleOpenOrderDetail 调试信息', {
    roomData: props.roomData,
    billDetail: billDetail.value,
    extractedBillId: billId,
    selectedBillId: selectedBillId.value,
    resolvedBillId: resolvedBillId.value
  })

  if (billId) {
    selectedBillId.value = String(billId)
    showOrderDetail.value = true
    console.log('RoomDetailModal: 设置 selectedBillId 为', selectedBillId.value)
  } else {
    console.log('RoomDetailModal: 没有找到 billId，显示警告')
    message.warning('该房间暂无订单信息')
  }
}

// 获取余额样式类
function getBalanceClass(balance) {
  if (balance > 0) return 'positive'
  if (balance < 0) return 'negative'
  return 'zero'
}

// 计算住宿天数
function calculateStayDays() {
  if (!orderInfo.value || !orderInfo.value.checkInTime || !orderInfo.value.checkOutTime) return 0

  try {
    const checkIn = new Date(orderInfo.value.checkInTime)
    const checkOut = new Date(orderInfo.value.checkOutTime)
    const diffTime = Math.abs(checkOut - checkIn)
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays || 1
  } catch (error) {

    return 1
  }
}
</script>

<style scoped>
.room-detail-modal {
  --n-color: #fff;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.room-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
}

.room-status-badge {
  padding: 4px 12px;
  border-radius: 12px;
  color: white;
  font-size: 12px;
  font-weight: 500;
}

.room-detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.info-card {
  margin-bottom: 16px;
}

.info-card:last-child {
  margin-bottom: 0;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 三列紧凑布局 */
.three-column-layout {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px;
  height: 100%;
  max-height: 60vh;
}

.left-column,
.middle-column,
.right-column {
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-height: 0;
}

/* 通用信息区块样式 */
.info-section {
  background: #fefefe;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px;
  flex-shrink: 0;
}

/* 操作区域样式 */
.action-section {
  background: #f8fafc;
  border-color: #cbd5e1;
}

.action-group-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.action-group {
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.group-label {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 10px;
  font-size: 13px;
  font-weight: 600;
  color: #475569;
}

.group-label i {
  font-size: 14px;
  color: #64748b;
}

.action-buttons-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.action-btn-modern {
  height: 36px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.action-btn-modern:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.action-btn-modern:active {
  transform: translateY(0);
}

/* 业务办理按钮样式 */
.primary-business {
  border-left: 4px solid #3b82f6;
}

.primary-business .action-btn-modern {
  border-width: 1px;
}

/* 房间状态按钮样式 */
.room-status {
  border-left: 4px solid #f59e0b;
}

.status-dirty {
  background: #fef3c7;
  border-color: #f59e0b;
  color: #92400e;
}

.status-dirty:hover {
  background: #fde68a;
  border-color: #d97706;
}

.status-clean {
  background: #d1fae5;
  border-color: #10b981;
  color: #065f46;
}

.status-clean:hover {
  background: #a7f3d0;
  border-color: #059669;
}

.status-maintenance {
  background: #fee2e2;
  border-color: #ef4444;
  color: #991b1b;
}

.status-maintenance:hover {
  background: #fecaca;
  border-color: #dc2626;
}

.status-cleaning {
  background: #e0e7ff;
  border-color: #6366f1;
  color: #3730a3;
}

.status-cleaning:hover {
  background: #c7d2fe;
  border-color: #4f46e5;
}

/* 其他操作按钮样式 */
.other-actions {
  border-left: 4px solid #6b7280;
}

.other-lock {
  background: #f3f4f6;
  border-color: #6b7280;
  color: #374151;
}

.other-lock:hover {
  background: #e5e7eb;
  border-color: #4b5563;
}

.other-close {
  background: #fef2f2;
  border-color: #f87171;
  color: #7f1d1d;
}

.other-close:hover {
  background: #fee2e2;
  border-color: #ef4444;
}

.other-change {
  background: #f0f9ff;
  border-color: #0ea5e9;
  color: #0c4a6e;
}

.other-change:hover {
  background: #e0f2fe;
  border-color: #0284c7;
}

.other-history {
  background: #faf5ff;
  border-color: #a855f7;
  color: #581c87;
}

.other-history:hover {
  background: #f3e8ff;
  border-color: #9333ea;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.detail-btn {
  margin-left: auto;
  font-size: 11px;
  height: 20px;
  padding: 0 6px;
}

.order-detail-btn-main {
  margin-left: auto;
  font-size: 12px;
  height: 28px;
  padding: 0 12px;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.order-detail-btn-main:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
}

/* 房间信息样式 */
.room-section {
  background: #f8fafc;
  border-color: #cbd5e1;
}

.room-number-line {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.room-number {
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
}

.room-type {
  font-size: 12px;
  background: #e2e8f0;
  padding: 2px 6px;
  border-radius: 3px;
  color: #64748b;
}

.room-status-line {
  display: flex;
  gap: 6px;
  margin-bottom: 8px;
}

.room-details-compact {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.detail-item {
  font-size: 12px;
  color: #6b7280;
}

.detail-item.connect-code {
  color: #7c3aed;
  font-weight: 600;
}

/* 客人信息样式 */
.guest-section {
  background: #fefefe;
  border-color: #e5e7eb;
}

.guest-name-line {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.guest-name {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.vip-level {
  font-size: 10px;
  background: #fef2f2;
  color: #dc2626;
  padding: 1px 4px;
  border-radius: 3px;
}

.guest-contact,
.guest-id {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 2px;
}

/* 联房信息样式 */
.connect-section {
  background: #f0f9ff;
  border-color: #bae6fd;
}

.connect-rooms-compact {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.connect-room-compact {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 6px;
  background: white;
  border-radius: 3px;
  font-size: 12px;
}

.connect-room-compact.current {
  background: #dbeafe;
  border: 1px solid #3b82f6;
}

.room-num {
  font-weight: 600;
  color: #1e40af;
}

.room-price {
  color: #059669;
  font-weight: 500;
}

.more-rooms {
  font-size: 11px;
  color: #6b7280;
  text-align: center;
  padding: 2px;
}

/* 订单信息样式 */
.order-section {
  background: #fefefe;
  border-color: #e5e7eb;
  flex: 1;
  min-height: 0;
}

/* 无订单信息样式 */
.no-order-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 12px;
  text-align: center;
  color: #9ca3af;
}

.no-order-icon {
  font-size: 32px;
  margin-bottom: 8px;
  opacity: 0.6;
}

.no-order-text {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 4px;
}

.no-order-hint {
  font-size: 12px;
  color: #9ca3af;
}

.order-info-compact {
  margin-bottom: 12px;
}

.order-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3px;
  font-size: 12px;
}

.order-line .label {
  color: #6b7280;
  font-weight: 500;
}

.order-line .value {
  color: #374151;
  font-weight: 600;
}

/* 财务信息样式 */
.finance-compact {
  background: #f8fafc;
  border-radius: 4px;
  padding: 8px;
}

.finance-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.finance-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  padding: 6px;
  background: white;
  border-radius: 3px;
  border: 1px solid #e5e7eb;
}

.finance-item.balance-item {
  grid-column: 1 / -1;
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  color: white;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.finance-item .label {
  font-size: 10px;
  color: #6b7280;
  font-weight: 500;
}

.balance-item .label {
  color: rgba(255, 255, 255, 0.95);
  font-weight: 600;
}

.finance-item .value {
  font-size: 13px;
  font-weight: 700;
}

.finance-item .value.consume {
  color: #dc2626;
}

.finance-item .value.payment {
  color: #16a34a;
}

.finance-item .value.deposit {
  color: #2563eb;
}

.finance-item .value.balance {
  color: white;
  font-size: 14px;
  font-weight: 700;
}

.finance-item .value.balance.positive {
  color: #34d399; /* 更亮的绿色，在深色背景上更清晰 */
}

.finance-item .value.balance.negative {
  color: #fbbf24; /* 改为黄色，在深色背景上更清晰 */
}

.finance-item .value.balance.zero {
  color: #d1d5db; /* 更亮的灰色 */
}

/* 订单详情按钮样式 */
.order-detail-action {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #e5e7eb;
}

.order-detail-btn {
  width: 100%;
  height: 32px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.order-detail-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
}

/* 操作按钮样式 */
.action-section {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 旧的操作按钮样式已被新的现代化样式替换 */

/* 订单详情小图标按钮样式 */
.order-detail-btn-icon {
  margin-left: auto;
  width: 24px;
  height: 24px;
  min-width: 24px;
  min-height: 24px;
  font-size: 14px;
}

.order-detail-btn-icon:hover {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .three-column-layout {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
  }

  .right-column {
    grid-column: 1 / -1;
  }

  .action-group-container {
    flex-direction: row;
    gap: 16px;
  }

  .action-group {
    flex: 1;
  }

  .action-buttons-grid {
    grid-template-columns: 1fr;
    gap: 6px;
  }
}

</style>
