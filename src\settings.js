/**********************************
 * @Author: <PERSON>
 * @LastEditor: <PERSON>
 * @LastEditTime: 2023/12/13 20:54:36
 * @Email: <EMAIL>
 * Copyright © 2023 <PERSON>(大脸怪) | https://isme.top
 **********************************/

export const defaultLayout = 'normal'

export const defaultPrimaryColor = '#8A9CFF'

// 控制 LayoutSetting 组件是否可见
export const layoutSettingVisible = true

export const naiveThemeOverrides = {
  common: {
    primaryColor: '#8A9CFF',
    primaryColorHover: '#6E8AF8',
    primaryColorPressed: '#4C73E6',
    primaryColorSuppl: '#A8B5FF',
  },
}

// 主题分类
export const themeCategories = {
  light: '明亮主题',
  dark: '暗色主题'
}

export const basePermissions = [
  // {
  //   code: 'ExternalLink',
  //   name: '外链(可内嵌打开)',
  //   type: 'MENU',
  //   icon: 'i-fe:external-link',
  //   order: 98,
  //   enable: true,
  //   show: true,
  //   children: [
  //     {
  //       code: 'ShowDocs',
  //       name: '项目文档',
  //       type: 'MENU',
  //       path: 'https://isme.top',
  //       icon: 'i-me:docs',
  //       order: 1,
  //       enable: true,
  //       show: true,
  //     }
  //   ],
  // },
]
