<template>
  <NConfigProvider
    :theme="appStore.isDark ? darkTheme : undefined"
    :theme-overrides="appStore.naiveThemeOverrides"
    :locale="naiveLocale"
    :date-locale="naiveDateLocale"
  >
    <NLoadingBarProvider>
      <NDialogProvider>
        <NNotificationProvider>
          <NMessageProvider>
            <router-view />
          </NMessageProvider>
        </NNotificationProvider>
      </NDialogProvider>
    </NLoadingBarProvider>
  </NConfigProvider>
</template>

<script setup>
import { computed } from 'vue'
import { darkTheme, dateZhCN, zhCN, dateEnUS, enUS } from 'naive-ui'
import { useAppStore } from '@/store'
import { useI18n } from 'vue-i18n'
const appStore = useAppStore()
const { locale } = useI18n()

// 根据当前语言设置 Naive UI 的语言包
const naiveLocale = computed(() => {
  return locale.value === 'en-US' ? enUS : zhCN
})

const naiveDateLocale = computed(() => {
  return locale.value === 'en-US' ? dateEnUS : dateZhCN
})
</script>
