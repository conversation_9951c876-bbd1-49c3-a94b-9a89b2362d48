/**
 * 账号缓存管理工具
 * 用于管理总店和分店账号的缓存，确保账号切换功能的稳定性
 */

const CACHE_KEY = 'accountsCache'

/**
 * 账号缓存数据结构
 * {
 *   mainAccount: { id, name, nickname, shop_name, auth_list },
 *   childAccounts: [{ id, name, nickname, shop_name, auth_list }],
 *   originalLoginData: { ... },
 *   currentAccountId: number,
 *   initialized: boolean
 * }
 */

/**
 * 初始化账号缓存
 * @param {Object} loginData - 登录响应数据
 */
export function initAccountsCache(loginData) {
  try {

    const childrenAdminList = loginData.children_admin_list || []

    if (childrenAdminList.length === 0) {

      // 没有子账号，只缓存当前账号
      const cache = {
        mainAccount: {
          id: loginData.admin_id,
          name: loginData.admin_name || loginData.name,
          nickname: loginData.nickname,
          shop_name: loginData.shop_name,
          auth_list: loginData.auth_list || []
        },
        childAccounts: [],
        originalLoginData: { ...loginData },
        currentAccountId: loginData.admin_id,
        initialized: true
      }

      localStorage.setItem(CACHE_KEY, JSON.stringify(cache))
      return cache
    }

    // 有子账号数据，解析总店和分店信息
    const mainAccount = childrenAdminList[0] // 总店账号
    const childAccounts = []

    // 收集所有子账号
    childrenAdminList.forEach(admin => {
      if (admin.children && admin.children.length > 0) {
        admin.children.forEach(child => {
          childAccounts.push({
            id: child.id,
            name: child.name,
            nickname: child.nickname,
            shop_name: child.shop_name,
            auth_list: child.auth_list || []
          })
        })
      }
    })

    const cache = {
      mainAccount: {
        id: mainAccount.id,
        name: mainAccount.name,
        nickname: mainAccount.nickname,
        shop_name: mainAccount.shop_name,
        auth_list: mainAccount.auth_list || []
      },
      childAccounts,
      originalLoginData: { ...loginData },
      currentAccountId: loginData.admin_id,
      initialized: true
    }

    localStorage.setItem(CACHE_KEY, JSON.stringify(cache))

    return cache
  } catch (error) {

    return null
  }
}

/**
 * 获取账号缓存
 * @returns {Object|null} 缓存数据
 */
export function getAccountsCache() {
  try {
    const cacheStr = localStorage.getItem(CACHE_KEY)
    if (!cacheStr) return null

    const cache = JSON.parse(cacheStr)
    return cache.initialized ? cache : null
  } catch (error) {

    return null
  }
}

/**
 * 获取所有可切换的账号列表
 * @returns {Array} 账号列表
 */
export function getAvailableAccounts() {
  const cache = getAccountsCache()
  if (!cache) return []

  const accounts = []

  // 添加总店账号
  accounts.push({
    ...cache.mainAccount,
    isMainAccount: true,
    isCurrent: cache.currentAccountId === cache.mainAccount.id
  })

  // 添加分店账号
  cache.childAccounts.forEach(account => {
    accounts.push({
      ...account,
      isMainAccount: false,
      isCurrent: cache.currentAccountId === account.id
    })
  })

  return accounts
}

/**
 * 获取当前账号信息
 * @returns {Object|null} 当前账号信息
 */
export function getCurrentAccount() {
  const cache = getAccountsCache()
  if (!cache) return null

  // 先检查是否是总店账号
  if (cache.currentAccountId === cache.mainAccount.id) {
    return { ...cache.mainAccount, isMainAccount: true }
  }

  // 查找分店账号
  const childAccount = cache.childAccounts.find(account => account.id === cache.currentAccountId)
  if (childAccount) {
    return { ...childAccount, isMainAccount: false }
  }

  // 默认返回总店账号
  return { ...cache.mainAccount, isMainAccount: true }
}

/**
 * 获取总店账号信息
 * @returns {Object|null} 总店账号信息
 */
export function getMainAccount() {
  const cache = getAccountsCache()
  return cache ? cache.mainAccount : null
}

/**
 * 更新当前账号ID
 * @param {number} accountId - 账号ID
 */
export function updateCurrentAccountId(accountId) {
  const cache = getAccountsCache()
  if (!cache) return false

  cache.currentAccountId = accountId
  localStorage.setItem(CACHE_KEY, JSON.stringify(cache))

  return true
}

/**
 * 根据账号ID获取账号信息
 * @param {number} accountId - 账号ID
 * @returns {Object|null} 账号信息
 */
export function getAccountById(accountId) {
  const cache = getAccountsCache()
  if (!cache) return null

  // 检查是否是总店账号
  if (cache.mainAccount.id === accountId) {
    return { ...cache.mainAccount, isMainAccount: true }
  }

  // 查找分店账号
  const childAccount = cache.childAccounts.find(account => account.id === accountId)
  if (childAccount) {
    return { ...childAccount, isMainAccount: false }
  }

  return null
}

/**
 * 检查是否有子账号
 * @returns {boolean} 是否有子账号
 */
export function hasChildAccounts() {
  const cache = getAccountsCache()
  return cache ? cache.childAccounts.length > 0 : false
}

/**
 * 检查当前是否为子账号
 * @returns {boolean} 是否为子账号
 */
export function isCurrentChildAccount() {
  const cache = getAccountsCache()
  if (!cache) return false

  return cache.currentAccountId !== cache.mainAccount.id
}

/**
 * 清除账号缓存
 */
export function clearAccountsCache() {
  localStorage.removeItem(CACHE_KEY)

}

export default {
  initAccountsCache,
  getAccountsCache,
  getAvailableAccounts,
  getCurrentAccount,
  getMainAccount,
  updateCurrentAccountId,
  getAccountById,
  hasChildAccounts,
  isCurrentChildAccount,
  clearAccountsCache
}
