<template>
  <n-modal
    v-model:show="visible"
    preset="card"
    title="选择房间"
    class="room-selector-modal"
    style="width: 80vw; max-width: 96vw; height: 90vh; max-height: 90vh;"
    @update:show="handleModalClose"
  >
    <!-- 固定顶部区域 -->
    <div class="fixed-header">
      <!-- 入住信息展示区域 -->
      <div class="check-in-info-section">
        <div class="info-card">
          <div class="info-row">
            <div class="info-item">
              <div class="info-label">销售类型</div>
              <div class="info-value">{{ checkInInfo.actualDate }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">价格方案</div>
              <div class="info-value">{{ checkInInfo.priceScheme }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">入住时间</div>
              <div class="info-value time-value">{{ formatDateTime(filters.start_time) }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">离店时间</div>
              <div class="info-value time-value">{{ formatDateTime(filters.end_time) }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 筛选条件区域 -->
      <div class="room-selector-header">

      <div class="filter-row">
        <div class="filter-group">
          <label>楼栋:</label>
          <n-select
            v-model:value="filters.building_id"
            :options="buildingOptions"
            placeholder="请选择楼栋"
            style="width: 120px"
            @update:value="handleBuildingChange"
          />
        </div>
        <div class="filter-group">
          <label>楼层:</label>
          <n-select
            v-model:value="filters.floor_id"
            :options="floorOptions"
            placeholder="请选择楼层"
            style="width: 120px"
          />
        </div>
        <div class="filter-group">
          <label>房型:</label>
          <n-select
            v-model:value="filters.room_type_id"
            :options="roomTypeOptions"
            placeholder="请选择房型"
            style="width: 120px"
          />
        </div>
        <div class="filter-group">
          <label>房间号:</label>
          <n-input
            v-model:value="filters.room_number"
            placeholder="房间号"
            style="width: 100px"
          />
        </div>
      </div>

      <div class="filter-row status-row">
        <div class="filter-group">
          <label>房间业务状态:</label>
          <div class="status-buttons business-status-buttons">
            <n-button
              v-for="status in businessStatusList"
              :key="status.value"
              :type="filters.room_record_status.includes(status.value) ? 'primary' : 'default'"
              size="small"
              class="business-status-btn"
              @click="toggleBusinessStatus(status.value)"
              :style="{
                backgroundColor: filters.room_record_status.includes(status.value) ? (status.color || 'var(--primary-color)') : 'transparent',
                borderColor: status.color || 'var(--primary-color)',
                color: filters.room_record_status.includes(status.value) ? '#fff' : (status.color || 'var(--primary-color)')
              }"
            >
              {{ status.label }}
            </n-button>
          </div>
        </div>

        <div class="filter-group">
          <label>房间清洁状态:</label>
          <div class="status-buttons clean-status-buttons">
            <n-button
              v-for="status in cleanStatusList"
              :key="status.value"
              :type="filters.room_clear_status.includes(status.value) ? 'primary' : 'default'"
              size="small"
              class="clean-status-btn"
              @click="toggleCleanStatus(status.value)"
              :style="{
                backgroundColor: filters.room_clear_status.includes(status.value) ? status.color : 'transparent',
                borderColor: status.color,
                color: filters.room_clear_status.includes(status.value) ? '#fff' : status.color
              }"
            >
              {{ status.label }}
            </n-button>
          </div>
        </div>
      </div>
      </div>
    </div>

    <!-- 可滚动内容区域 -->
    <div class="scrollable-content" ref="scrollableRef">
      <!-- 已选房间展示 -->
      <div class="selected-rooms-section" v-if="selectedRooms.length > 0" ref="selectedRef">
        <div class="selected-rooms-header">
          <span class="selected-title">已选房间 ({{ selectedRooms.length }})</span>
          <n-button size="small" text @click="clearAllSelection">
            <template #icon>
              <i class="i-material-symbols:close"></i>
            </template>
            清空
          </n-button>
        </div>
        <div class="selected-rooms-list">
          <div
            v-for="room in selectedRooms"
            :key="room.id"
            class="selected-room-chip"
            :style="{ borderColor: room.color || room.room_status_color }"
            @click="removeRoomSelection(room)"
          >
            <span class="chip-room-number">{{ room.room_number }}</span>
            <i class="chip-close i-material-symbols:close"></i>
          </div>
        </div>
      </div>

      <!-- 房间列表区域 -->
      <div
        class="room-list-container"
        :class="{ 'has-selected': selectedRooms.length > 0 }"
        ref="roomListRef"
      >
        <!-- 骨架屏 -->
        <div v-if="loading" class="room-skeleton">
          <div class="skeleton-category" v-for="i in 3" :key="i">
            <div class="skeleton-category-header">
              <n-skeleton text :width="120" />
            </div>
            <div class="skeleton-room-grid">
              <div class="skeleton-room-card" v-for="j in 12" :key="j">
                <n-skeleton height="45px" />
              </div>
            </div>
          </div>
        </div>

        <!-- 房间列表 -->
        <div v-else>
          <div class="room-category" v-for="category in roomCategories" :key="category.name">
            <div class="category-header">
              <span class="category-name">{{ category.name }}</span>
              <span class="category-count">({{ category.rooms.length }})</span>
            </div>

            <div class="room-grid">
              <div
                v-for="room in category.rooms"
                :key="room.id"
                :class="[
                  'room-card',
                  {
                    'selected': selectedRooms.some(r => r.id === room.id),
                    'disabled': !isRoomSelectable(room)
                  }
                ]"
                :style="{
                  borderColor: room.color || room.room_status_color,
                  backgroundColor: selectedRooms.some(r => r.id === room.id) ? (room.color || room.room_status_color) + '20' : 'transparent'
                }"
                @click="toggleRoomSelection(room)"
              >
                <div class="room-header">
                  <div class="room-number">{{ room.room_number }}</div>
                </div>
                <div class="room-footer">
                  <div class="status-indicators">
                    <span class="status-text" :style="{ color: room.color || room.room_status_color }">
                      {{ room.room_status_name }}
                    </span>
                    <span class="clean-text" :style="{ color: room.clear_color }">
                      {{ room.clear_status_name }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作区域 -->
    <template #footer>
      <div class="room-selector-footer">
        <n-button @click="handleCancel">取消</n-button>
        <n-button
          type="primary"
          @click="handleConfirm"
          :disabled="selectedRooms.length === 0"
        >
          确认 ({{ selectedRooms.length }})
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { useMessage } from 'naive-ui'
import * as api from '@/api/hotel/roomStatus'

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  checkInInfo: {
    type: Object,
    default: () => ({
      actualDate: '全天房',
      priceScheme: '散客'
    })
  },
  defaultFilters: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:show', 'confirm', 'cancel'])

const message = useMessage()

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const selectedRooms = ref([])

// 筛选条件
const filters = ref({
  room_clear_status: [],
  room_record_status: [],
  floor_id: '',
  building_id: '',
  room_type_id: '',
  room_number: '',
  room_sale_type: '',
  intermediaries_id: '',
  start_time: Date.now(),
  end_time: Date.now() + 24 * 60 * 60 * 1000 // 默认明天
})

// 选项数据
const buildingOptions = ref([])
const floorOptions = ref([{ label: '全部', value: '' }]) // 初始化楼层选项
const roomTypeOptions = ref([{ label: '全部', value: '' }]) // 初始化房型选项
const businessStatusList = ref([])
const cleanStatusList = ref([])
const roomList = ref([])

// 动态高度相关
const scrollableRef = ref(null)
const selectedRef = ref(null)
const roomListRef = ref(null)

// 房间分类 - 按楼栋和楼层组织
const roomCategories = computed(() => {
  const categories = []

  roomList.value.forEach(buildingData => {
    buildingData.floor_list.forEach(floorData => {
      if (floorData.room_list && floorData.room_list.length > 0) {
        categories.push({
          name: `${buildingData.building} - ${floorData.floor}`,
          building: buildingData.building,
          floor: floorData.floor,
          rooms: floorData.room_list
        })
      }
    })
  })

  return categories
})

// 初始化数据
async function initializeData() {
  loading.value = true
  try {
    await Promise.all([
      fetchBuildingList(),
      fetchRoomTypes(),
      fetchBusinessStatus(),
      fetchCleanStatus()
    ])
    await fetchRoomList()
  } catch (error) {
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 获取楼栋列表
async function fetchBuildingList() {
  try {
    const response = await api.getUsableRoomBuildingList()
    buildingOptions.value = [
      { label: '全部', value: '' }, // 添加全部选项
      ...response.data.map(item => ({
        label: item.name,
        value: item.id
      }))
    ]
  } catch (error) {
    buildingOptions.value = [{ label: '全部', value: '' }]
  }
}

// 获取房型列表
async function fetchRoomTypes() {
  try {
    const response = await api.getRoomType()
    roomTypeOptions.value = [
      { label: '全部', value: '' }, // 添加全部选项
      ...response.data.map(item => ({
        label: item.name,
        value: item.id
      }))
    ]
  } catch (error) {

    roomTypeOptions.value = [{ label: '全部', value: '' }]
  }
}

// 获取业务状态
async function fetchBusinessStatus() {
  try {
    const response = await api.getRoomRecordStatus()
    businessStatusList.value = response.data
      .filter(item => item.sign === 'empty' || item.sign === 'closed') // 只保留空房和关房状态
      .map(item => ({
        label: item.status_name,
        value: item.id,
        sign: item.sign,
        color: item.color
      }))

  } catch (error) {
    businessStatusList.value = []
  }
}

// 获取清洁状态
async function fetchCleanStatus() {
  try {
    const response = await api.getRoomClearStatus()
    cleanStatusList.value = response.data
      .filter(item => item.sign !== 'repair') // 过滤掉维修状态
      .map(item => ({
        label: item.status_name,
        value: item.id,
        sign: item.sign,
        color: item.color
      }))
  } catch (error) {
    cleanStatusList.value = []
  }
}

// 获取房间列表
async function fetchRoomList() {
  try {
    const params = {
      ...filters.value,
      start_time: filters.value.start_time,  // 直接使用时间戳
      end_time: filters.value.end_time       // 直接使用时间戳
    }

    const response = await api.selectRoom(params)
    roomList.value = response.data || []

  } catch (error) {
    roomList.value = []
  }
}

// 处理楼栋变化
async function handleBuildingChange(buildingId) {
  filters.value.floor_id = ''

  if (buildingId) {
    try {
      const response = await api.getRoomFloor({
        page: 1,
        limit: 99999999999,
        building_id: buildingId
      })
      floorOptions.value = [
        { label: '全部', value: '' }, // 添加全部选项
        ...response.data.list.map(item => ({
          label: item.name,
          value: item.id
        }))
      ]

    } catch (error) {
      floorOptions.value = [{ label: '全部', value: '' }]
    }
  } else {
    floorOptions.value = [{ label: '全部', value: '' }]
  }
  fetchRoomList()
}

// 切换业务状态
function toggleBusinessStatus(statusId) {
  const index = filters.value.room_record_status.indexOf(statusId)
  if (index > -1) {
    filters.value.room_record_status.splice(index, 1)
  } else {
    filters.value.room_record_status.push(statusId)
  }
  fetchRoomList()
}

// 切换清洁状态
function toggleCleanStatus(statusId) {
  const index = filters.value.room_clear_status.indexOf(statusId)
  if (index > -1) {
    filters.value.room_clear_status.splice(index, 1)
  } else {
    filters.value.room_clear_status.push(statusId)
  }
  fetchRoomList()
}

// 判断房间是否可选
function isRoomSelectable(room) {
  // 根据房间状态判断是否可选
  return room.status !== 'occupied' && room.status !== 'maintenance'
}

// 切换房间选择
function toggleRoomSelection(room) {
  if (!isRoomSelectable(room)) return

  const index = selectedRooms.value.findIndex(r => r.id === room.id)
  if (index > -1) {
    selectedRooms.value.splice(index, 1)
  } else {
    selectedRooms.value.push(room)
  }

  // 重新计算房间列表高度
  setTimeout(computeRoomListHeight, 50)
}

// 移除单个房间选择
function removeRoomSelection(room) {
  const index = selectedRooms.value.findIndex(r => r.id === room.id)
  if (index > -1) {
    selectedRooms.value.splice(index, 1)
  }

  // 重新计算房间列表高度
  setTimeout(computeRoomListHeight, 50)
}

// 清空所有选择
function clearAllSelection() {
  selectedRooms.value = []

  // 重新计算房间列表高度
  setTimeout(computeRoomListHeight, 50)
}

// 处理确认
function handleConfirm() {
  emit('confirm', selectedRooms.value)
  handleModalClose()
}

// 处理取消
function handleCancel() {
  emit('cancel')
  handleModalClose()
}

// 处理弹窗关闭
function handleModalClose() {
  visible.value = false
  emit('update:show', false)
  selectedRooms.value = []
}

// 格式化时间显示
function formatDateTime(timestamp) {
  if (!timestamp) return '--'
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 简化的高度计算函数 - 使用CSS Flexbox自动处理
function computeRoomListHeight() {
  // 移除复杂的高度计算，让CSS Flexbox自动处理
  // 这样可以避免时序问题和DOM查询的复杂性
  nextTick(() => {
    if (roomListRef.value) {
      // 触发重新渲染以确保布局更新
      roomListRef.value.style.display = 'none'
      roomListRef.value.offsetHeight // 强制重排
      roomListRef.value.style.display = ''
    }
  })
}

// 监听相关变化重新计算高度
watch([visible, selectedRooms], () => {
  if (visible.value) {
    setTimeout(computeRoomListHeight, 50)
  }
}, { flush: 'post' })

// 监听弹窗显示状态
watch(() => props.show, async (newVal) => {
  visible.value = newVal
  if (newVal) {
    // 先初始化数据
    await initializeData()
    // 数据加载完成后，等待 DOM 更新再计算高度
    await nextTick()
    setTimeout(computeRoomListHeight, 100)
  }
})

// 监听筛选条件变化
watch([
  () => filters.value.floor_id,
  () => filters.value.room_type_id,
  () => filters.value.room_number,
  () => filters.value.start_time,
  () => filters.value.end_time
], () => {
  fetchRoomList()
}, { deep: true })
</script>

<style scoped>
/* 弹窗布局优化 */
.room-selector-modal.room-selector-modal :deep(.n-card) {
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
  max-height: 100% !important;
}

.room-selector-modal :deep(.n-card__header) {
  flex-shrink: 0;
}

.room-selector-modal.room-selector-modal :deep(.n-card__content) {
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
  padding: 0 !important;
  min-height: 0 !important;
  overflow: hidden !important;
  max-height: 100% !important;
}

.room-selector-modal :deep(.n-card__footer) {
  flex-shrink: 0;
  background: #fff;
}

/* 固定顶部区域 */
.fixed-header {
  flex-shrink: 0;
  background: #fff;
  border-bottom: 1px solid #e0e0e6;
  padding: 12px 16px;
  position: sticky;
  top: 0;
  z-index: 3;
}

/* 可滚动内容区域 */
.scrollable-content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 12px;
  box-sizing: border-box;
}

/* 入住信息展示区域样式 */
.check-in-info-section {
  margin-bottom: 12px;
  padding: 0;
}

.info-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 8px 12px;
  box-shadow: none;
}

.info-row {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
  min-width: 100px;
}

.info-label {
  font-size: 10px;
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.info-value {
  font-size: 12px;
  color: #1e293b;
  font-weight: 600;
  padding: 4px 0;
  background: transparent;
  border: none;
  min-height: auto;
  display: flex;
  align-items: center;
}

.time-value {
  color: var(--primary-color);
  background: transparent;
  font-weight: 700;
}

.room-selector-header {
  margin-bottom: 6px;
  padding: 4px 6px;
  background: rgba(var(--primary-color-rgb), 0.08);
  border-radius: 4px;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 6px;
}

.filter-row.status-row {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 3px;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  font-size: 14px;
  color: rgba(0,0,0,0.55);
  white-space: nowrap;
  min-width: 60px;
}

.filter-value {
  font-size: 14px;
  color: rgba(0,0,0,0.82);
  font-weight: 500;
}

.status-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 业务状态按钮特殊样式 */
.business-status-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.business-status-btn {
  min-width: 60px !important;
  height: 28px !important;
  padding: 0 12px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  border-radius: 4px !important;
  transition: all 0.3s ease !important;
}

.business-status-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 清洁状态按钮特殊样式 */
.clean-status-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.clean-status-btn {
  min-width: 60px !important;
  height: 28px !important;
  padding: 0 12px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  border-radius: 4px !important;
  transition: all 0.3s ease !important;
}

.clean-status-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.selection-summary {
  padding: 12px 16px;
  background: #e3f2fd;
  border-radius: 6px;
  margin-bottom: 16px;
  font-size: 14px;
  color: #1976d2;
}

.selected-count {
  font-weight: 600;
  color: #1565c0;
}

.room-list-container {
  padding: 0;
  background: transparent;
  flex: 1 1 auto;
  min-height: 0;
  /* 高度由 JavaScript 动态计算，确保充分利用空间 */
  display: flex;
  flex-direction: column;
}

.room-category {
  margin-bottom: 24px;
}

.room-category:last-child {
  margin-bottom: 0;
}

.category-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 2px solid var(--primary-color);
}

.category-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--primary-color);
}

.category-count {
  font-size: 14px;
  color: #666;
}

.room-list-container .room-grid {
  overflow-y: auto;
  max-height: 100%;
  height: 100%;
}

.room-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(85px, 1fr));
  gap: 16px;
}

.room-card {
  width: 75px;
  height: 42px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #fff;
  padding: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

.room-card:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(138, 156, 255, 0.15);
  transform: translateY(-1px);
}

.room-card.selected {
  border-color: var(--primary-color);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-color-hover) 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(138, 156, 255, 0.3);
}

.room-card.disabled {
  background: #f8f9fa;
  color: #adb5bd;
  cursor: not-allowed;
  border-color: #e9ecef;
  opacity: 0.6;
}

.room-card.disabled:hover {
  border-color: #e9ecef;
  background: #f8f9fa;
  transform: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.room-header {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
}

.room-number {
  font-size: 13px;
  font-weight: 700;
  color: #2d3748;
  text-align: center;
  line-height: 1.1;
}

.room-card.selected .room-number {
  color: white;
}

.room-footer {
  display: flex;
  justify-content: center;
  align-items: center;
}

.status-indicators {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: center;
}

.status-text,
.clean-text {
  font-size: 9px;
  font-weight: 600;
  line-height: 1;
  transition: all 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.room-card.selected .status-text,
.room-card.selected .clean-text {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* 已选房间展示区域 */
.selected-rooms-section {
  flex-shrink: 0;
  margin-bottom: 6px;
  padding: 4px 6px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.selected-rooms-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.selected-title {
  font-size: 12px;
  font-weight: 600;
  color: #495057;
}

.selected-rooms-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.selected-room-chip {
  display: inline-flex;
  align-items: center;
  gap: 2px;
  padding: 2px 5px;
  background: #fff;
  border: 1px solid var(--primary-color);
  border-radius: 10px;
  font-size: 10px;
  font-weight: 500;
  color: var(--primary-color);
  cursor: pointer;
  transition: all 0.2s ease;
  height: 20px;
}

.selected-room-chip:hover {
  background: var(--primary-color);
  color: white;
}

.selected-room-chip:hover .chip-close {
  color: white;
}

.chip-room-number {
  font-weight: 600;
}

.chip-close {
  font-size: 14px;
  color: #6c757d;
  transition: color 0.2s ease;
}

.chip-close:hover {
  color: #dc3545;
}

.room-selector-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid rgba(var(--primary-color-rgb), 0.15);
}

/* 暗色模式支持 */
.dark .room-selector-header {
  background: rgba(var(--primary-color-rgb), 0.12);
}

.dark .filter-group label {
  color: #d1d5db;
}

.dark .filter-value {
  color: #f9fafb;
}

.dark .selection-summary {
  background: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
}

.dark .room-list-container {
  border-color: rgba(75, 85, 99, 0.6);
  background: rgba(31, 41, 55, 0.8);
}

.dark .category-name {
  color: var(--primary-color);
}

.dark .category-count {
  color: #9ca3af;
}

.dark .room-card {
  background: rgba(55, 65, 81, 0.9);
  border-color: rgba(75, 85, 99, 0.6);
  color: #f9fafb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.dark .room-card:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(var(--primary-color-rgb), 0.4);
}

.dark .room-card.selected {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-color-pressed) 100%);
  box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.6);
}

.dark .room-card.disabled {
  background: rgba(31, 41, 55, 0.6);
  color: #6b7280;
  border-color: rgba(75, 85, 99, 0.4);
  opacity: 0.5;
}

.dark .room-number {
  color: #f9fafb;
}

/* 暗色模式 - 已选房间区域 */
.dark .selected-rooms-section {
  background: rgba(55, 65, 81, 0.6);
  border-color: rgba(75, 85, 99, 0.6);
}

.dark .selected-title {
  color: #f9fafb;
}

.dark .selected-room-chip {
  background: rgba(55, 65, 81, 0.8);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.dark .selected-room-chip:hover {
  background: var(--primary-color);
  color: white;
}

.dark .chip-close {
  color: #9ca3af;
}

.dark .chip-close:hover {
  color: #f87171;
}

.dark .room-selector-footer {
  border-color: rgba(75, 85, 99, 0.6);
}

/* 高饱和度主题的房间选择器增强样式 */
:root[style*="--primary-color: #10B981"] .room-selector-header,
:root[style*="--primary-color: #8B5CF6"] .room-selector-header,
:root[style*="--primary-color: #EF4444"] .room-selector-header,
:root[style*="--primary-color: #F59E0B"] .room-selector-header {
  background: rgba(var(--primary-color-rgb), 0.2) !important;
  border: 1px solid rgba(var(--primary-color-rgb), 0.3) !important;
}

:root[style*="--primary-color: #10B981"] .time-value,
:root[style*="--primary-color: #8B5CF6"] .time-value,
:root[style*="--primary-color: #EF4444"] .time-value,
:root[style*="--primary-color: #F59E0B"] .time-value {
  background: rgba(var(--primary-color-rgb), 0.25) !important;
  border-color: rgba(var(--primary-color-rgb), 0.5) !important;
  font-weight: 600 !important;
}

:root[style*="--primary-color: #10B981"] .room-card:hover,
:root[style*="--primary-color: #8B5CF6"] .room-card:hover,
:root[style*="--primary-color: #EF4444"] .room-card:hover,
:root[style*="--primary-color: #F59E0B"] .room-card:hover {
  border-color: var(--primary-color) !important;
  box-shadow: 0 4px 16px rgba(var(--primary-color-rgb), 0.3) !important;
  transform: translateY(-2px) !important;
}

:root[style*="--primary-color: #10B981"] .room-card.selected,
:root[style*="--primary-color: #8B5CF6"] .room-card.selected,
:root[style*="--primary-color: #EF4444"] .room-card.selected,
:root[style*="--primary-color: #F59E0B"] .room-card.selected {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-color-pressed) 100%) !important;
  box-shadow: 0 6px 20px rgba(var(--primary-color-rgb), 0.5) !important;
  transform: translateY(-3px) !important;
}

:root[style*="--primary-color: #10B981"] .selection-summary,
:root[style*="--primary-color: #8B5CF6"] .selection-summary,
:root[style*="--primary-color: #EF4444"] .selection-summary,
:root[style*="--primary-color: #F59E0B"] .selection-summary {
  background: rgba(var(--primary-color-rgb), 0.2) !important;
  border: 1px solid rgba(var(--primary-color-rgb), 0.4) !important;
  font-weight: 600 !important;
}

:root[style*="--primary-color: #10B981"] .selected-room-chip,
:root[style*="--primary-color: #8B5CF6"] .selected-room-chip,
:root[style*="--primary-color: #EF4444"] .selected-room-chip,
:root[style*="--primary-color: #F59E0B"] .selected-room-chip {
  background: rgba(var(--primary-color-rgb), 0.15) !important;
  border-color: rgba(var(--primary-color-rgb), 0.5) !important;
  font-weight: 600 !important;
}

:root[style*="--primary-color: #10B981"] .selected-room-chip:hover,
:root[style*="--primary-color: #8B5CF6"] .selected-room-chip:hover,
:root[style*="--primary-color: #EF4444"] .selected-room-chip:hover,
:root[style*="--primary-color: #F59E0B"] .selected-room-chip:hover {
  background: var(--primary-color) !important;
  color: white !important;
  transform: scale(1.05) !important;
}

/* 暗夜黑主题的苹果风格房间选择器 */
:root[style*="--primary-color: #1F2937"] .room-selector-header {
  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), var(--apple-dark-light)) 0%, rgba(var(--primary-color-rgb), var(--apple-dark-subtle)) 100%) !important;
  border: 1px solid rgba(var(--primary-color-rgb), var(--apple-border-opacity)) !important;
  box-shadow:
    0 3px 12px rgba(var(--primary-color-rgb), 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.12) !important;
  color: #FFFFFF !important;
}

:root[style*="--primary-color: #1F2937"] .time-value {
  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), var(--apple-dark-medium)) 0%, rgba(var(--primary-color-rgb), var(--apple-dark-light)) 100%) !important;
  border-color: rgba(var(--primary-color-rgb), 0.5) !important;
  box-shadow:
    0 2px 8px rgba(var(--primary-color-rgb), 0.35),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
  color: #FFFFFF !important;
  font-weight: 600 !important;
}

:root[style*="--primary-color: #1F2937"] .room-card {
  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), var(--apple-dark-subtle)) 0%, rgba(var(--primary-color-rgb), 0.1) 100%) !important;
  border: 1px solid rgba(var(--primary-color-rgb), var(--apple-border-opacity)) !important;
  box-shadow:
    0 3px 10px rgba(var(--primary-color-rgb), 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.08) !important;
  color: #FFFFFF !important;
}

:root[style*="--primary-color: #1F2937"] .room-card:hover {
  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), var(--apple-dark-light)) 0%, rgba(var(--primary-color-rgb), var(--apple-dark-subtle)) 100%) !important;
  border-color: rgba(var(--primary-color-rgb), 0.5) !important;
  box-shadow:
    0 6px 20px rgba(var(--primary-color-rgb), 0.35),
    0 0 0 1px rgba(255, 255, 255, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.12) !important;
  transform: translateY(-3px) !important;
}

:root[style*="--primary-color: #1F2937"] .room-card.selected {
  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), var(--apple-dark-intense)) 0%, rgba(var(--primary-color-rgb), var(--apple-dark-medium)) 100%) !important;
  border-color: rgba(var(--primary-color-rgb), 0.7) !important;
  box-shadow:
    0 8px 24px rgba(var(--primary-color-rgb), 0.5),
    0 0 0 2px rgba(255, 255, 255, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 0 30px rgba(var(--primary-color-rgb), 0.3) !important;
  transform: translateY(-4px) !important;
  color: #FFFFFF !important;
  font-weight: 600 !important;
}

:root[style*="--primary-color: #1F2937"] .selection-summary {
  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), var(--apple-dark-light)) 0%, rgba(var(--primary-color-rgb), var(--apple-dark-subtle)) 100%) !important;
  border: 1px solid rgba(var(--primary-color-rgb), var(--apple-border-opacity)) !important;
  box-shadow:
    0 3px 12px rgba(var(--primary-color-rgb), 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.12) !important;
  color: #FFFFFF !important;
  font-weight: 600 !important;
}

:root[style*="--primary-color: #1F2937"] .selected-room-chip {
  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), var(--apple-dark-light)) 0%, rgba(var(--primary-color-rgb), var(--apple-dark-subtle)) 100%) !important;
  border-color: rgba(var(--primary-color-rgb), 0.5) !important;
  box-shadow:
    0 2px 6px rgba(var(--primary-color-rgb), 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  color: #FFFFFF !important;
  font-weight: 600 !important;
}

:root[style*="--primary-color: #1F2937"] .selected-room-chip:hover {
  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), var(--apple-dark-medium)) 0%, rgba(var(--primary-color-rgb), var(--apple-dark-light)) 100%) !important;
  border-color: rgba(var(--primary-color-rgb), 0.6) !important;
  box-shadow:
    0 4px 12px rgba(var(--primary-color-rgb), 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
  transform: scale(1.05) translateY(-1px) !important;
}

:root[style*="--primary-color: #1F2937"] .room-selector-footer {
  background: rgba(var(--primary-color-rgb), 0.03) !important;
  border-top: 1px solid rgba(var(--primary-color-rgb), 0.12) !important;
  box-shadow: 0 -1px 3px rgba(var(--primary-color-rgb), 0.1) !important;
}

/* 暗夜黑主题的房间状态徽章 */
:root[style*="--primary-color: #1F2937"] .room-status {
  background: rgba(var(--primary-color-rgb), 0.08) !important;
  border: 1px solid rgba(var(--primary-color-rgb), 0.15) !important;
  box-shadow: 0 1px 2px rgba(var(--primary-color-rgb), 0.1) !important;
}




.room-selector-modal .fixed-header {
  flex-shrink: 0 !important;
  position: relative !important;
  background: #fff !important;
  padding: 12px 16px !important;
  border-bottom: 1px solid #e0e0e6 !important;
}



.room-selector-modal .room-list-container {
  flex: 1 !important;
  min-height: 0 !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  padding: 0 !important;
  margin: 0 !important;
  border: none !important;
  background: transparent !important;
  /* 美化滚动条 */
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f3f4f6;
}

.room-selector-modal .room-list-container::-webkit-scrollbar {
  width: 6px;
}

.room-selector-modal .room-list-container::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 3px;
}

.room-selector-modal .room-list-container::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.room-selector-modal .room-list-container::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

:root[style*="--primary-color: #1F2937"] .room-features .feature-item {
  background: rgba(var(--primary-color-rgb), 0.06) !important;
  border: 1px solid rgba(var(--primary-color-rgb), 0.12) !important;
  box-shadow: 0 1px 2px rgba(var(--primary-color-rgb), 0.08) !important;
}

/* 骨架屏样式 */
.room-skeleton {
  padding: 0;
}

.skeleton-category {
  margin-bottom: 20px;
}

.skeleton-category-header {
  margin-bottom: 12px;
}

.skeleton-room-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(75px, 1fr));
  gap: 16px;
}

.skeleton-room-card {
  width: 75px;
  height: 42px;
  border-radius: 6px;
  overflow: hidden;
}

/* 房间网格高度限制 - 防止溢出 */
.room-selector-modal .room-grid {
  /* max-height: 300px !important; */ /* REMOVED: Let the main container scroll */
  /* overflow-y: auto !important; */ /* REMOVED: No nested scrolling */
  overflow-x: hidden !important;
}

.room-selector-modal .room-category {
  margin-bottom: 16px !important;
}

.room-selector-modal .selected-rooms-section {
  margin-bottom: 8px !important;
  padding: 6px 8px !important;
}
</style>
