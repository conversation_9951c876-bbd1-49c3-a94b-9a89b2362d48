/**********************************
 * @Author: <PERSON>
 * @LastEditor: <PERSON>
 * @LastEditTime: 2023/12/05 21:25:59
 * @Email: <EMAIL>
 * Copyright © 2023 <PERSON>(大脸怪) | https://isme.top
 **********************************/

import { defineStore } from 'pinia'
import { getLoginResponseData, getSystemConfig, getShopInfo, getWebConfig } from '@/utils/loginData'

export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: null,
    loginResponseData: null,
    systemConfig: null,
    shopInfo: null,
    webConfig: null,
  }),
  getters: {
    // 基础用户信息
    userId() {
      return this.userInfo?.id
    },
    username() {
      return this.userInfo?.username
    },
    nickName() {
      return this.userInfo?.nickName
    },
    avatar() {
      return this.userInfo?.avatar
    },
    currentRole() {
      return this.userInfo?.currentRole || {}
    },
    roles() {
      return this.userInfo?.roles || []
    },
    gender() {
      return this.userInfo?.gender
    },
    address() {
      return this.userInfo?.address
    },
    email() {
      return this.userInfo?.email
    },
    phone() {
      return this.userInfo?.phone
    },

    // 平台和应用信息
    platformList() {
      return this.userInfo?.platformList || []
    },
    baseUrl() {
      return this.userInfo?.baseUrl
    },
    miniprogramList() {
      return this.userInfo?.miniprogramList || []
    },

    // 酒店/商店信息
    shopName() {
      return this.userInfo?.shop_name || getShopInfo().shop_name
    },
    shopId() {
      return this.userInfo?.shop_id || getShopInfo().shop_id
    },
    coverPic() {
      return this.userInfo?.cover_pic || getShopInfo().cover_pic
    },
    shopLogo() {
      return this.userInfo?.shop_logo || getShopInfo().shop_logo
    },
    shopAddress() {
      return this.userInfo?.shop_address || getShopInfo().shop_address
    },
    shopPhone() {
      return this.userInfo?.shop_phone || getShopInfo().shop_phone
    },
    shopDescription() {
      return this.userInfo?.shop_description || getShopInfo().shop_description
    },

    // 网站配置信息
    webTitle() {
      return this.userInfo?.set_web_title || getWebConfig().set_web_title
    },
    webLogo() {
      return this.userInfo?.set_web_logo || getWebConfig().set_web_logo
    },
    webCopyright() {
      return this.userInfo?.set_web_copyright || getWebConfig().set_web_copyright
    },
    webCopyrightUrl() {
      return this.userInfo?.set_web_copyright_url || getWebConfig().set_web_copyright_url
    },

    // 其他信息
    department() {
      return this.userInfo?.department
    },
    position() {
      return this.userInfo?.position
    },
    company() {
      return this.userInfo?.company
    },
    lastLoginTime() {
      return this.userInfo?.lastLoginTime
    },

    // 获取完整的登录数据
    getLoginResponseData() {
      return getLoginResponseData()
    },

    // 获取系统配置
    getSystemConfig() {
      return getSystemConfig()
    },
  },
  actions: {
    setUser(user) {
      this.userInfo = user
    },
    resetUser() {
      this.$reset()
    },
    setLoginResponseData(data) {
      this.loginResponseData = data
    },
    setSystemConfig(data) {
      this.systemConfig = data
    },
    setShopInfo(data) {
      this.shopInfo = data
    },
    setWebConfig(data) {
      this.webConfig = data
    },
  },
})
