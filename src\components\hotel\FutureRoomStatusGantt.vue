<template>
  <n-modal
    v-model:show="show"
    :mask-closable="false"
    preset="card"
    title="远期房态甘特图"
    class="future-room-status-modal"
    style="width: 95vw; max-width: 1400px;"
  >
    <template #header-extra>
      <n-space>
        <n-date-picker
          v-model:value="dateRange"
          type="daterange"
          clearable
          format="yyyy-MM-dd"
          placeholder="选择日期范围"
          @update:value="handleDateRangeChange"
        />
        <n-button @click="refreshData" :loading="loading">
          <template #icon>
            <i class="i-carbon:refresh" />
          </template>
          刷新
        </n-button>
      </n-space>
    </template>

    <div class="gantt-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <n-spin size="large">
          <template #description>正在加载远期房态数据...</template>
        </n-spin>
      </div>

      <!-- 甘特图内容 -->
      <div v-else class="gantt-content">
        <!-- 时间轴头部 -->
        <div class="gantt-header">
          <div class="room-header">房间</div>
          <div class="timeline-header">
            <div
              v-for="date in dateList"
              :key="date"
              class="date-column"
              :class="{ 'today': isToday(date), 'weekend': isWeekend(date) }"
            >
              <div class="date-text">{{ formatDate(date) }}</div>
              <div class="weekday-text">{{ getWeekday(date) }}</div>
            </div>
          </div>
        </div>

        <!-- 房间行 -->
        <div class="gantt-body">
          <div
            v-for="room in roomList"
            :key="room.id"
            class="room-row"
          >
            <!-- 房间信息 -->
            <div class="room-info">
              <div class="room-number">{{ room.roomNumber }}</div>
              <div class="room-type">{{ room.roomTypeName }}</div>
            </div>

            <!-- 时间线 -->
            <div class="room-timeline">
              <div
                v-for="date in dateList"
                :key="date"
                class="date-cell"
                :class="getCellClass(room, date)"
                @click="handleCellClick(room, date)"
              >
                <div class="status-bar" :style="getStatusStyle(room, date)">
                  <span class="status-text">{{ getStatusText(room, date) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图例 -->
    <div class="gantt-legend">
      <div class="legend-title">状态说明：</div>
      <div class="legend-items">
        <div class="legend-item">
          <div class="legend-color available"></div>
          <span>空房</span>
        </div>
        <div class="legend-item">
          <div class="legend-color occupied"></div>
          <span>有客</span>
        </div>
        <div class="legend-item">
          <div class="legend-color reserved"></div>
          <span>预订</span>
        </div>
        <div class="legend-item">
          <div class="legend-color maintenance"></div>
          <span>维修</span>
        </div>
        <div class="legend-item">
          <div class="legend-color cleaning"></div>
          <span>清洁</span>
        </div>
        <div class="legend-item">
          <div class="legend-color out-of-order"></div>
          <span>停用</span>
        </div>
      </div>
    </div>
  </n-modal>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useMessage } from 'naive-ui'
import * as api from '@/api/hotel/roomStatus'

const message = useMessage()

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:show'])

// 响应式数据
const show = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const loading = ref(false)
const dateRange = ref([])
const roomList = ref([])
const roomStatusData = ref({})

// 计算属性
const dateList = computed(() => {
  if (!dateRange.value || dateRange.value.length !== 2) {
    // 默认显示未来30天
    const dates = []
    const today = new Date()
    for (let i = 0; i < 30; i++) {
      const date = new Date(today)
      date.setDate(today.getDate() + i)
      dates.push(date.toISOString().split('T')[0])
    }
    return dates
  }

  const dates = []
  const startDate = new Date(dateRange.value[0])
  const endDate = new Date(dateRange.value[1])

  for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
    dates.push(d.toISOString().split('T')[0])
  }

  return dates
})

// 方法
const initData = async () => {
  await Promise.all([
    fetchRoomList(),
    fetchRoomStatusData()
  ])
}

const fetchRoomList = async () => {
  try {
    const response = await api.getUsableRoom()
    roomList.value = response.data || []
  } catch (error) {
    console.error('获取房间列表失败:', error)
    message.error('获取房间列表失败')
  }
}

const fetchRoomStatusData = async () => {
  try {
    loading.value = true

    // 构建日期范围参数
    const startDate = dateList.value[0]
    const endDate = dateList.value[dateList.value.length - 1]

    // 调用远期房态API
    try {
      const response = await api.getFutureRoomStatus({
        startDate,
        endDate,
        roomIds: roomList.value.map(room => room.id)
      })

      if (response && response.data) {
        roomStatusData.value = response.data
      } else {
        throw new Error('API返回数据格式错误')
      }
    } catch (apiError) {
      console.warn('API调用失败，使用模拟数据:', apiError)

      // API失败时使用模拟数据
      const mockData = {}
      roomList.value.forEach(room => {
        mockData[room.id] = {}
        dateList.value.forEach(date => {
          // 模拟不同的房间状态
          const random = Math.random()
          let status = 'available'
          if (random < 0.3) status = 'occupied'
          else if (random < 0.5) status = 'reserved'
          else if (random < 0.6) status = 'maintenance'
          else if (random < 0.7) status = 'cleaning'
          else if (random < 0.75) status = 'out-of-order'

          mockData[room.id][date] = {
            status,
            guestName: status === 'occupied' ? '张三' : '',
            checkIn: status === 'occupied' ? '14:00' : '',
            checkOut: status === 'occupied' ? '12:00' : ''
          }
        })
      })

      roomStatusData.value = mockData
    }
  } catch (error) {
    console.error('获取房态数据失败:', error)
    message.error('获取房态数据失败')
  } finally {
    loading.value = false
  }
}

const handleDateRangeChange = () => {
  if (dateRange.value && dateRange.value.length === 2) {
    fetchRoomStatusData()
  }
}

const refreshData = () => {
  initData()
}

const formatDate = (dateStr) => {
  const date = new Date(dateStr)
  return `${date.getMonth() + 1}/${date.getDate()}`
}

const getWeekday = (dateStr) => {
  const date = new Date(dateStr)
  const weekdays = ['日', '一', '二', '三', '四', '五', '六']
  return weekdays[date.getDay()]
}

const isToday = (dateStr) => {
  const today = new Date().toISOString().split('T')[0]
  return dateStr === today
}

const isWeekend = (dateStr) => {
  const date = new Date(dateStr)
  const day = date.getDay()
  return day === 0 || day === 6
}

const getCellClass = (room, date) => {
  const status = roomStatusData.value[room.id]?.[date]?.status || 'available'
  return [
    'status-cell',
    status,
    {
      'today': isToday(date),
      'weekend': isWeekend(date)
    }
  ]
}

const getStatusStyle = (room, date) => {
  const status = roomStatusData.value[room.id]?.[date]?.status || 'available'
  const colorMap = {
    available: '#52c41a',
    occupied: '#ff4d4f',
    reserved: '#1890ff',
    maintenance: '#faad14',
    cleaning: '#722ed1',
    'out-of-order': '#8c8c8c'
  }

  return {
    backgroundColor: colorMap[status],
    opacity: 0.8
  }
}

const getStatusText = (room, date) => {
  const statusData = roomStatusData.value[room.id]?.[date]
  if (!statusData) return ''

  const textMap = {
    available: '空',
    occupied: statusData.guestName || '客',
    reserved: '订',
    maintenance: '修',
    cleaning: '洁',
    'out-of-order': '停'
  }

  return textMap[statusData.status] || ''
}

const handleCellClick = (room, date) => {
  const statusData = roomStatusData.value[room.id]?.[date]
  if (statusData) {
    message.info(`房间 ${room.roomNumber} - ${date} - 状态: ${statusData.status}`)
  }
}

// 监听弹窗显示状态
watch(() => props.show, (newVal) => {
  if (newVal) {
    initData()
  }
})
</script>

<style scoped>
.future-room-status-modal {
  height: 80vh;
}

.gantt-container {
  height: 60vh;
  overflow: hidden;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.gantt-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.gantt-header {
  display: flex;
  background: #fafafa;
  border-bottom: 2px solid #d9d9d9;
  position: sticky;
  top: 0;
  z-index: 10;
}

.room-header {
  width: 120px;
  padding: 12px 8px;
  font-weight: 600;
  text-align: center;
  border-right: 1px solid #d9d9d9;
  background: #fafafa;
}

.timeline-header {
  display: flex;
  flex: 1;
  overflow-x: auto;
}

.date-column {
  min-width: 80px;
  padding: 8px 4px;
  text-align: center;
  border-right: 1px solid #e8e8e8;
  background: #fafafa;
}

.date-column.today {
  background: #e6f7ff;
  color: #1890ff;
  font-weight: 600;
}

.date-column.weekend {
  background: #fff2e8;
  color: #fa8c16;
}

.date-text {
  font-size: 12px;
  font-weight: 500;
}

.weekday-text {
  font-size: 10px;
  color: #666;
  margin-top: 2px;
}

.gantt-body {
  flex: 1;
  overflow-y: auto;
}

.room-row {
  display: flex;
  border-bottom: 1px solid #f0f0f0;
  min-height: 50px;
}

.room-row:hover {
  background: #fafafa;
}

.room-info {
  width: 120px;
  padding: 8px;
  border-right: 1px solid #d9d9d9;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: #fff;
  position: sticky;
  left: 0;
  z-index: 5;
}

.room-number {
  font-weight: 600;
  font-size: 14px;
  color: #333;
}

.room-type {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.room-timeline {
  display: flex;
  flex: 1;
}

.date-cell {
  min-width: 80px;
  border-right: 1px solid #e8e8e8;
  position: relative;
  cursor: pointer;
}

.date-cell:hover {
  background: #f5f5f5;
}

.status-bar {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.status-text {
  color: white;
  font-size: 12px;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.gantt-legend {
  margin-top: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
}

.legend-title {
  font-weight: 600;
  margin-bottom: 8px;
}

.legend-items {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 3px;
}

.legend-color.available { background: #52c41a; }
.legend-color.occupied { background: #ff4d4f; }
.legend-color.reserved { background: #1890ff; }
.legend-color.maintenance { background: #faad14; }
.legend-color.cleaning { background: #722ed1; }
.legend-color.out-of-order { background: #8c8c8c; }
</style>
