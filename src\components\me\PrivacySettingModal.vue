<template>
  <n-modal
  v-model:show="show"
  class="modal-box"
  :style="{ width: modalOptions.width, ...modalOptions.modalStyle }"
  :preset="undefined"
  size="huge">
  <n-card class="card">
  <n-space vertical>
    <n-alert type="info" title="隐私协议提交">
      <template #icon>
        <n-icon>
          <i class="i-material-symbols:info-outline"></i>
        </n-icon>
      </template>
      系统已经预设好相关协议,请直接提交审核即可。提交成功后,请去提交代码审核！
      <n-button type="info" @click="handleConfirm" size="large" class="" :loading="loadingShow">
        立即提交
      </n-button>
    </n-alert>

  </n-space>
</n-card>
</n-modal>

</template>

<script setup>
import { ref, computed } from 'vue'
import api from './api'

const props = defineProps({
appid: {
  type: String,
  default: ''
}
})

const show = ref(false);
// 声明一个modalOptions变量，用于存储模态框的配置信息
const modalOptions = ref({width:'800px',modalStyle:{height:'400px'}})
async function open(options = {}) {
// 将props和options合并赋值给modalOptions
modalOptions.value = { ...props, ...options }
// 如果modalOptions中没有title属性，则将props中的title属性赋值给modalOptions的title属性
if (!modalOptions.value.title)
  modalOptions.value.title = props.title
// 将show的值设置为true
show.value = true
await nextTick()
initDrag(
  Array.prototype.at.call(document.querySelectorAll('.modal-header'), -1),
  Array.prototype.at.call(document.querySelectorAll('.modal-box'), -1),
)
}

const newAppid = ref('');
// 监听show变化，当弹窗显示时获取版本列表
// watch(() => props.show, (newVal) => {
// if (newVal) {
//   checkedRowKeys.value = []
//   getVersionList()
// }
// })
const loadingShow = ref(false);
async function handleConfirm() {
  loadingShow.value = true;
  const params = {appid:props.appid};
  const data = await api.getCodePrivacyInfo(params);
  if (data.code == 200) {
    $message.success('上传隐私协议成功！');
    loadingShow.value = false;
    return;
  }else{
    $message.error('上传隐私协议失败！');
    loadingShow.value = false;
  }

}

// 定义一个defineExpose函数，用于暴露open、close、handleOk、handleCancel函数
defineExpose({
open,
close
})
</script>
