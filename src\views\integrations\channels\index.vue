<template>
  <div class="channels-page">
    <div class="page-header">
      <h1 class="page-title">
        <i class="i-material-symbols:hub-outline"></i>
        渠道集成
      </h1>
      <p class="page-description">管理第三方渠道集成和配置</p>
    </div>

    <div class="page-content">
      <n-card>
        <n-empty description="渠道集成功能开发中...">
          <template #extra>
            <n-button type="primary">敬请期待</n-button>
          </template>
        </n-empty>
      </n-card>
    </div>
  </div>
</template>

<script setup>
// 渠道集成页面 - 占位页面
</script>

<style scoped>
.channels-page {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: var(--text-color-1);
}

.page-description {
  color: var(--text-color-3);
  margin: 0;
}
</style>