<!--------------------------------
 - @Author: 你的名字
 - @LastEditor: 你的名字
 - @LastEditTime: 2024/xx/xx xx:xx:xx
 - Copyright © 2024 你的项目 | https://你的域名.com
 --------------------------------->

 <template>
  <CommonPage>
   <n-space vertical :size="12">
   <n-data-table
     size="large"
     :columns="columns"
     :data="data"
     :pagination="pagination"
   />
 </n-space>
  </CommonPage>

</template>

<script setup>
import { onMounted, ref } from 'vue'
// 可选：根据业务需求导入其他组件（如 MeCrud、MeQueryItem 等）
import { NButton, NTag, useMessage } from "naive-ui";
import { defineComponent, h } from "vue";
import dayjs from 'dayjs';
import api from './api';
defineOptions({ name: 'CodeModel' })
// 可选：查询参数（根据业务需求定义）
const queryItems = ref({})
const $table = ref(null)
// 可选：表格列配置（根据业务需求定义）
// const columns = ref([
//   { title: '字段1', key: 'field1' },
//   { title: '字段2', key: 'field2' }
// ])
function createColumns({
 delCode
}) {
 return [
  {
     title: "模板ID",
     key: "template_id"
   },
   {
     title: "草稿ID",
     key: "draft_id"
   },
   {
     title: "版本号",
     key: "user_version"
   },
   {
     title: "上传时间",
     key: "create_time"
   },
   {
     title: "备注",
     key: "user_desc"
   },
   {
     title: "模板类型",
     key: "template_type"
   },
  //  {
  //    title: "审核状态",
  //    key: "audit_status"
  //  },
   {
     title: "驳回原因",
     key: "reason"
   },
   {
     title: "操作",
     key: "actions",
     render(row) {
       return h(
         NButton,
         {
           size: "small",
           type: "error",
           onClick: () => delCode(row)
         },
         { default: () => "删除模板" }
       );
     }
   }
 ];
}

onMounted(async () => {
 // 通过 .value 更新 ref 的值，且使用 await 等待异步函数完成
 data.value = await createData();
});

async function createData() {
 const datalist = await api.getModelList();
 const list = datalist.data.list;

 return list;
}

const data = ref([]); // 假设这是你的数据;
const columns = createColumns({
  delCode(rowData) {

    api.delModel({'template_id':rowData.template_id}).then(async (res) => {
      if(res.code == 200){
        $message.success('删除成功');
        // 重新获取数据并刷新表格
        data.value = await createData();
      }
    })
  }
});

const pagination = ref({
 pageSize: 10
});

</script>
