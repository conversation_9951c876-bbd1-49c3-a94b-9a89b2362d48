<template>
  <div class="room-card-medium">
    <!-- 顶部信息行：房间号 + 联房标识 -->
    <div class="room-header-medium">
      <div class="room-number-medium">{{ room.roomNumber || room.room_number }}</div>
      <!-- 联房标识 -->
      <div
        v-if="room.isConnectedRoom && room.connectCode"
        class="connect-room-badge-medium"
        :title="`联房组: ${room.connectCode}`"
      >
        <i class="i-mdi:link-variant"></i>
      </div>
    </div>

    <!-- 房型信息 -->
    <div class="room-type-medium">{{ room.roomType || room.room_type_name }}</div>

    <!-- 主要信息：客人信息或预订信息 -->
    <div v-if="hasGuestInfo(room)" class="guest-main-info">
      <div class="guest-name-medium">{{ room.guestName || getGuestName(room) }}</div>
      <!-- 会员和订单来源信息（放在名字下面） -->
      <div v-if="room.memberGrade || room.channelSource" class="member-channel-info-medium">
        <span v-if="room.memberGrade" class="member-grade-medium" :title="`会员等级: ${room.memberGrade}`">
          <i class="i-mdi:crown"></i>{{ getShortMemberGrade(room.memberGrade) }}
        </span>
        <span v-if="room.channelSource" class="channel-source-medium" :title="`订单来源: ${room.channelSource}`">
          <i class="i-mdi:source-branch"></i>{{ getShortChannelSource(room.channelSource) }}
        </span>
      </div>
      <!-- 优先显示手机号，如果空间不够则隐藏 -->
      <div v-if="(room.guestPhone || getGuestPhone(room)) && !hasMultipleInfo(room)" class="guest-phone-medium">
        {{ room.guestPhone || getGuestPhone(room) }}
      </div>
      <!-- 协议单位信息 -->
      <div v-if="getAgreementUnit(room)" class="agreement-info-medium">
        <span class="agreement-unit-medium" :title="`协议单位: ${getAgreementUnit(room)}`">
          <i class="i-mdi:office-building"></i>{{ getShortAgreementUnit(getAgreementUnit(room)) }}
        </span>
      </div>
      <!-- 售卖类型信息（放在最下面） -->
      <div v-if="getSellType(room)" class="sell-type-info-medium">
        <span class="sell-type-medium" :title="`售卖类型: ${getSellType(room)}`">
          <i class="i-mdi:tag"></i>{{ getShortSellType(getSellType(room)) }}
        </span>
      </div>
    </div>

    <!-- 预订信息 -->
    <div v-else-if="hasReservationInfo(room)" class="reservation-main-info">
      <div class="guest-name-medium">{{ room.guestName || '预订客人' }}</div>
      <div v-if="getArrivalTime(room) || getExpectedArrival(room)" class="arrival-info-medium">
        <span v-if="getArrivalTime(room)">预计: {{ formatTime(getArrivalTime(room)) }}</span>
        <span v-if="getExpectedArrival(room)">应到: {{ formatTime(getExpectedArrival(room)) }}</span>
      </div>
    </div>

    <!-- 维修信息（紧凑显示） -->
    <div v-if="room.maintenance && !hasMultipleInfo(room)" class="maintenance-info-medium">
      <div class="maintenance-issue-medium">{{ room.maintenance.issue }}</div>
    </div>

    <!-- 清洁信息（紧凑显示） -->
    <div v-if="room.housekeeping && (room.status === 'cleaning' || room.status === 'inspecting') && !hasMultipleInfo(room)" class="housekeeping-info-medium">
      <div v-if="room.housekeeping.assignedTo" class="hk-assignee-medium">
        {{ room.housekeeping.assignedTo }}
      </div>
    </div>



    <!-- 底部状态区域 -->
    <div class="bottom-status-area-medium">
      <!-- 左侧：状态标签 -->
      <div class="status-badges-medium">
        <!-- 优先显示业务状态，如果是空房或净房则显示清洁状态 -->
        <div
          v-if="room.roomStatusName && !shouldShowCleanStatus(room)"
          class="room-status-badge-medium"
          :style="getRoomStatusBadgeStyle(room)"
        >
          <i :class="getStatusIcon(room.status)"></i>
          <span>{{ room.roomStatusName }}</span>
        </div>
        <div
          v-else-if="room.cleanStatusName"
          class="room-clean-badge-medium"
          :class="getCleanStatusClass(room)"
          :style="getCleanStatusBadgeStyle(room)"
        >
          <i :class="getCleanStatusIcon(room)"></i>
          <span>{{ room.cleanStatusName }}</span>
        </div>

        <!-- 退房提醒 -->
        <div v-if="isCheckoutOverdue(room)" class="checkout-alert-medium overdue">
          <i class="i-mdi:alert-circle"></i>
        </div>
        <div v-else-if="isCheckoutSoon(room)" class="checkout-alert-medium soon">
          <i class="i-mdi:clock-alert"></i>
        </div>
      </div>

      <!-- 右侧：重要提醒和指示器 -->
      <div class="right-indicators-medium">
        <!-- 欠费提醒（最高优先级） -->
        <div v-if="room.debtInfo" class="debt-reminder-medium" :title="room.debtInfo.fullText">
          <i class="i-mdi:currency-cny"></i>
          <span>{{ room.debtInfo.debtText }}</span>
        </div>

        <!-- 离店提醒 -->
        <div v-else-if="room.checkoutReminderInfo" class="checkout-reminder-medium" :title="room.checkoutReminderInfo.fullText">
          <i class="i-mdi:clock-alert-outline"></i>
          <span>{{ room.checkoutReminderInfo.text }}</span>
        </div>

        <!-- 订单指示器 -->
        <div v-if="getTodayOrders(room.futureOrders).length > 0 || getFutureOrders(room.futureOrders).length > 0" class="orders-indicator-medium">
          <span v-if="getTodayOrders(room.futureOrders).length > 0" class="today-orders-dot" :title="`今日${getTodayOrders(room.futureOrders).length}单`">
            {{ getTodayOrders(room.futureOrders).length }}
          </span>
          <span v-if="getFutureOrders(room.futureOrders).length > 0" class="future-orders-dot" :title="`未来${getFutureOrders(room.futureOrders).length}单`">
            {{ getFutureOrders(room.futureOrders).length }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Props
const props = defineProps({
  room: {
    type: Object,
    required: true
  }
})

// 判断是否有客人信息
const hasGuestInfo = (room) => {
  if (!room) return false

  // 优先检查bill_info中的客人信息
  if (room.bill_info && room.bill_info.link_man) {
    return true
  }

  // 检查guest对象
  if (room.guest && room.guest.name) {
    return true
  }

  // 检查转换后的数据
  if (room.guestName) {
    return true
  }

  return false
}

// 获取客人姓名
const getGuestName = (room) => {
  if (!room) return ''

  if (room.bill_info && room.bill_info.link_man) {
    return room.bill_info.link_man
  }

  if (room.guest && room.guest.name) {
    return room.guest.name
  }

  return room.guest_name || ''
}

// 获取客人电话
const getGuestPhone = (room) => {
  if (!room) return ''

  if (room.bill_info && room.bill_info.link_phone) {
    return room.bill_info.link_phone
  }

  if (room.guest && room.guest.phone) {
    return room.guest.phone
  }

  return room.guest_phone || ''
}

// 获取入住时间
const getCheckInTime = (room) => {
  if (!room) return null

  if (room.bill_info && room.bill_info.enter_time) {
    return new Date(room.bill_info.enter_time * 1000)
  }

  if (room.guest && room.guest.checkInTime) {
    return new Date(room.guest.checkInTime)
  }

  return null
}

// 获取退房时间
const getCheckOutTime = (room) => {
  if (!room) return null

  if (room.bill_info && room.bill_info.leave_time) {
    return new Date(room.bill_info.leave_time * 1000)
  }

  if (room.guest && room.guest.checkOutTime) {
    return new Date(room.guest.checkOutTime)
  }

  return null
}

// 判断是否有预订信息
const hasReservationInfo = (room) => {
  if (!room) return false

  // 房间状态为预订相关
  const statusName = room.roomStatusName || room.room_status_name || ''
  if (statusName.includes('预订') || statusName.includes('预定')) {
    return true
  }

  // 或者有预订相关的状态标识
  const status = room.status || ''
  if (status === 'reserved' || status === 'noshow') {
    return true
  }

  return false
}

// 获取到店时间
const getArrivalTime = (room) => {
  if (!room) return null

  // 从bill_info获取
  if (room.bill_info && room.bill_info.arrival_time) {
    return room.bill_info.arrival_time
  }

  // 从guest对象获取
  if (room.guest && room.guest.arrivalTime) {
    return room.guest.arrivalTime
  }

  return null
}

// 获取预期到店时间
const getExpectedArrival = (room) => {
  if (!room) return null

  // 从bill_info获取
  if (room.bill_info && room.bill_info.expected_arrival) {
    return room.bill_info.expected_arrival
  }

  // 从guest对象获取
  if (room.guest && room.guest.expectedArrival) {
    return room.guest.expectedArrival
  }

  return null
}

// 获取今日订单
const getTodayOrders = (orders) => {
  if (!orders || !Array.isArray(orders)) return []

  const today = new Date()
  today.setHours(0, 0, 0, 0)
  const tomorrow = new Date(today)
  tomorrow.setDate(tomorrow.getDate() + 1)

  return orders.filter(order => {
    const timestamp = order.enter_time || order.enter_time_plan || order.start_time_plan
    if (!timestamp) return false

    const orderDate = new Date(timestamp * 1000)
    return orderDate >= today && orderDate < tomorrow
  })
}

// 获取未来订单（明日及以后）
const getFutureOrders = (orders) => {
  if (!orders || !Array.isArray(orders)) return []

  const tomorrow = new Date()
  tomorrow.setHours(0, 0, 0, 0)
  tomorrow.setDate(tomorrow.getDate() + 1)

  return orders.filter(order => {
    const timestamp = order.enter_time || order.enter_time_plan || order.start_time_plan
    if (!timestamp) return false

    const orderDate = new Date(timestamp * 1000)
    return orderDate >= tomorrow
  })
}

// 判断是否应该显示清洁状态而不是业务状态
const shouldShowCleanStatus = (room) => {
  // 如果没有清洁状态，不显示
  if (!room.cleanStatusName) return false

  // 如果是净房，不需要特别显示清洁状态
  if (room.cleanStatusName === '净') return false

  // 如果房间状态是空房，优先显示清洁状态
  if (room.roomStatusName === '空房') return true

  // 如果清洁状态是需要关注的状态（脏、清洁中、维修等），显示清洁状态
  const importantCleanStatus = ['脏', '清洁中', '维修', '维修中', '封锁', '查房', '查房中']
  return importantCleanStatus.includes(room.cleanStatusName)
}

// 其他辅助函数
const formatTime = (timeStr) => {
  if (!timeStr) return ''
  try {
    const date = new Date(timeStr)
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } catch {
    return timeStr
  }
}

const getStatusIcon = (status) => {
  const icons = {
    occupied: 'i-material-symbols:person',
    available: 'i-material-symbols:check-circle',
    checkout: 'i-material-symbols:logout',
    dirty: 'i-material-symbols:warning',
    cleaning: 'i-material-symbols:cleaning-services',
    inspecting: 'i-material-symbols:search',
    maintenance: 'i-material-symbols:build',
    blocked: 'i-material-symbols:block',
    reserved: 'i-material-symbols:event',
    noshow: 'i-material-symbols:person-off'
  }
  return icons[status] || 'i-material-symbols:help'
}

const getRoomStatusBadgeStyle = (room) => {
  const color = room.room_status_color || '#e5e7eb'
  return {
    'background': colorWithOpacity(color, 0.25),
    'color': color,
    'border': `1px solid ${colorWithOpacity(color, 0.5)}`
  }
}

const getCleanStatusBadgeStyle = (room) => {
  const color = room.clear_color || '#f3f4f6'
  return {
    'background': colorWithOpacity(color, 0.18),
    'color': color,
    'border': `1px solid ${colorWithOpacity(color, 0.4)}`
  }
}

const getCleanStatusClass = (room) => {
  const statusName = room.cleanStatusName || room.clear_status_name || ''
  const classes = []

  if (statusName.includes('脏')) {
    classes.push('status-dirty')
  } else if (statusName.includes('维修')) {
    classes.push('status-maintenance')
  } else if (statusName.includes('清洁')) {
    classes.push('status-cleaning')
  } else if (statusName.includes('查房')) {
    classes.push('status-inspecting')
  } else if (statusName.includes('封锁')) {
    classes.push('status-blocked')
  }

  return classes
}

const getCleanStatusIcon = (room) => {
  const statusName = room.cleanStatusName || room.clear_status_name || ''

  if (statusName.includes('脏')) {
    return 'i-mdi:delete-sweep'
  } else if (statusName.includes('维修')) {
    return 'i-mdi:tools'
  } else if (statusName.includes('清洁')) {
    return 'i-mdi:broom'
  } else if (statusName.includes('查房')) {
    return 'i-mdi:magnify'
  } else if (statusName.includes('封锁')) {
    return 'i-mdi:block-helper'
  } else {
    return 'i-mdi:check-circle'
  }
}

const isCheckoutSoon = (room) => {
  if (!room.checkoutTime && !room.checkout_time) return false

  const checkoutTime = new Date(room.checkoutTime || room.checkout_time)
  const now = new Date()
  const timeDiff = checkoutTime.getTime() - now.getTime()
  const hoursDiff = timeDiff / (1000 * 60 * 60)

  return hoursDiff > 0 && hoursDiff <= 2
}

const isCheckoutOverdue = (room) => {
  if (!room.checkoutTime && !room.checkout_time) return false

  const checkoutTime = new Date(room.checkoutTime || room.checkout_time)
  const now = new Date()

  return now > checkoutTime
}

const colorWithOpacity = (color, opacity) => {
  if (color.startsWith('#')) {
    const hex = color.replace('#', '')
    const r = parseInt(hex.substr(0, 2), 16)
    const g = parseInt(hex.substr(2, 2), 16)
    const b = parseInt(hex.substr(4, 2), 16)
    return `rgba(${r}, ${g}, ${b}, ${opacity})`
  }
  return color
}

// 缩短会员等级显示
const getShortMemberGrade = (grade) => {
  if (!grade) return ''

  // 常见会员等级的缩写映射
  const shortMap = {
    '超级黑钻会员': '超钻',
    '黑钻会员': '黑钻',
    '铂金会员': '铂金',
    '黄金会员': '黄金',
    '白银会员': '白银',
    '青铜会员': '青铜',
    '普通会员': '普通',
    '散客': '散客'
  }

  return shortMap[grade] || (grade.length > 4 ? grade.substring(0, 4) : grade)
}

// 缩短订单来源显示
const getShortChannelSource = (source) => {
  if (!source) return ''

  // 常见来源的缩写映射
  const shortMap = {
    '微信小程序': '微信',
    '支付宝小程序': '支付宝',
    '携程网': '携程',
    '去哪儿网': '去哪儿',
    '美团网': '美团',
    '飞猪网': '飞猪',
    '艺龙网': '艺龙',
    '同程网': '同程',
    '步入': '步入',
    '电话预订': '电话',
    '前台预订': '前台'
  }

  return shortMap[source] || (source.length > 3 ? source.substring(0, 3) : source)
}

// 判断是否有多个信息需要显示（用于决定是否隐藏某些次要信息）
const hasMultipleInfo = (room) => {
  let infoCount = 0

  // 计算信息项数量
  if (room.memberGrade) infoCount++
  if (room.channelSource) infoCount++
  if (room.debtInfo) infoCount++
  if (room.checkoutReminderInfo) infoCount++
  if (room.maintenance) infoCount++
  if (room.housekeeping && (room.status === 'cleaning' || room.status === 'inspecting')) infoCount++
  if (getTodayOrders(room.futureOrders).length > 0 || getFutureOrders(room.futureOrders).length > 0) infoCount++
  if (getAgreementUnit(room)) infoCount++
  if (getSellType(room)) infoCount++

  // 如果有2个或以上的额外信息，则认为信息较多
  return infoCount >= 2
}

// 获取协议单位信息
const getAgreementUnit = (room) => {
  if (room.bill_info?.company) return room.bill_info.company
  if (room.bill_info?.agreement_unit) return room.bill_info.agreement_unit
  if (room.bill_info?.unit_name) return room.bill_info.unit_name
  return ''
}

// 获取售卖类型信息
const getSellType = (room) => {
  if (room.bill_info?.sell_type_name) return room.bill_info.sell_type_name
  if (room.bill_info?.sale_type_name) return room.bill_info.sale_type_name
  if (room.sellType) return room.sellType
  return ''
}

// 缩短协议单位显示
const getShortAgreementUnit = (unit) => {
  if (!unit) return ''

  // 常见单位的缩写映射
  const shortMap = {
    '有限公司': '有限',
    '股份有限公司': '股份',
    '集团有限公司': '集团',
    '科技有限公司': '科技',
    '贸易有限公司': '贸易',
    '投资有限公司': '投资'
  }

  // 查找匹配的缩写
  for (const [full, short] of Object.entries(shortMap)) {
    if (unit.includes(full)) {
      return unit.replace(full, short)
    }
  }

  // 如果超过6个字符，截取前6个字符
  return unit.length > 6 ? unit.substring(0, 6) + '...' : unit
}

// 缩短售卖类型显示
const getShortSellType = (type) => {
  if (!type) return ''

  // 常见售卖类型的缩写映射
  const shortMap = {
    '标准售卖': '标准',
    '钟点房': '钟点',
    '长包房': '长包',
    '免费房': '免费',
    '自用房': '自用',
    '维修房': '维修',
    '封锁房': '封锁'
  }

  return shortMap[type] || (type.length > 4 ? type.substring(0, 4) : type)
}
</script>

<style scoped>
.room-card-medium {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.12rem;
  overflow: hidden;
}

/* 顶部信息行 */
.room-header-medium {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.05rem;
}

.room-number-medium {
  font-size: 1.1rem;
  font-weight: 800;
  color: #1f2937;
  line-height: 1;
  flex: 1;
}

/* 空房的房号更大 */
.room-card-medium:not(:has(.guest-main-info)):not(:has(.reservation-main-info)) .room-number-medium {
  font-size: 1.4rem;
  font-weight: 900;
}

.room-type-medium {
  font-size: 0.75rem;
  color: #4b5563;
  line-height: 1;
  margin-bottom: 0.1rem;
  font-weight: 500;
}

.connect-room-badge-medium {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  border-radius: 50%;
  font-size: 0.6rem;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.connect-room-badge-medium:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 10px rgba(139, 92, 246, 0.5);
}

/* 主要信息 */
.guest-main-info,
.reservation-main-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.08rem;
  margin: 0.03rem 0 0.08rem 0;
  min-height: 0;
  overflow: hidden;
}

.guest-name-medium {
  font-weight: 600;
  color: #374151;
  font-size: 0.75rem;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.guest-phone-medium {
  font-size: 0.65rem;
  color: #6b7280;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.time-info-medium {
  font-size: 0.55rem;
  color: #6b7280;
  display: flex;
  flex-direction: column;
  gap: 0.02rem;
  line-height: 1.2;
}

.arrival-info-medium {
  font-size: 0.55rem;
  color: #6b7280;
  display: flex;
  flex-direction: column;
  gap: 0.02rem;
  line-height: 1.2;
}

/* 维修和清洁信息 */
.maintenance-info-medium,
.housekeeping-info-medium {
  font-size: 0.55rem;
  color: #6b7280;
  background: rgba(0, 0, 0, 0.05);
  padding: 0.1rem 0.15rem;
  border-radius: 2px;
  margin: 0.05rem 0;
  flex-shrink: 0;
}

.maintenance-issue-medium,
.hk-assignee-medium {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 会员和订单来源信息 */
.member-channel-info-medium {
  display: flex;
  gap: 0.2rem;
  margin: 0.05rem 0;
  font-size: 0.55rem;
  flex-wrap: wrap;
  flex-shrink: 0;
}

.member-grade-medium {
  display: flex;
  align-items: center;
  gap: 0.05rem;
  color: #d97706;
  background: rgba(217, 119, 6, 0.1);
  padding: 0.05rem 0.15rem;
  border-radius: 2px;
  font-weight: 500;
  line-height: 1;
  white-space: nowrap;
}

.channel-source-medium {
  display: flex;
  align-items: center;
  gap: 0.05rem;
  color: #059669;
  background: rgba(5, 150, 105, 0.1);
  padding: 0.05rem 0.15rem;
  border-radius: 2px;
  font-weight: 500;
  line-height: 1;
  white-space: nowrap;
}

/* 协议单位和售卖类型信息 */
.business-info-medium {
  display: flex;
  gap: 0.2rem;
  margin: 0.05rem 0;
  font-size: 0.55rem;
  flex-wrap: wrap;
  flex-shrink: 0;
}

.agreement-unit-medium {
  display: flex;
  align-items: center;
  gap: 0.05rem;
  color: #7c3aed;
  background: rgba(124, 58, 237, 0.1);
  padding: 0.05rem 0.15rem;
  border-radius: 2px;
  font-weight: 500;
  line-height: 1;
  white-space: nowrap;
}

.sell-type-medium {
  display: flex;
  align-items: center;
  gap: 0.05rem;
  color: #dc2626;
  background: rgba(220, 38, 38, 0.1);
  padding: 0.05rem 0.15rem;
  border-radius: 2px;
  font-weight: 500;
  line-height: 1;
  white-space: nowrap;
}

/* 协议单位信息容器 */
.agreement-info-medium {
  display: flex;
  gap: 0.2rem;
  margin: 0.05rem 0;
  font-size: 0.55rem;
  flex-wrap: wrap;
  flex-shrink: 0;
}

/* 售卖类型信息容器 */
.sell-type-info-medium {
  display: flex;
  gap: 0.2rem;
  margin: 0.05rem 0;
  font-size: 0.55rem;
  flex-wrap: wrap;
  flex-shrink: 0;
}

/* 底部状态区域 */
.bottom-status-area-medium {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-top: auto;
  gap: 0.2rem;
}

.status-badges-medium {
  display: flex;
  gap: 0.15rem;
  flex-wrap: wrap;
  flex: 1;
}

.right-indicators-medium {
  display: flex;
  flex-direction: column;
  gap: 0.1rem;
  align-items: flex-end;
  flex-shrink: 0;
}

.room-status-badge-medium,
.room-clean-badge-medium {
  display: flex;
  align-items: center;
  gap: 0.15rem;
  padding: 0.1rem 0.25rem;
  border-radius: 3px;
  font-size: 0.6rem;
  font-weight: 500;
  line-height: 1;
}

.checkout-alert-medium {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  font-size: 0.6rem;
}

.checkout-alert-medium.overdue {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.checkout-alert-medium.soon {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.3);
}

/* 欠费提醒 */
.debt-reminder-medium {
  display: flex;
  align-items: center;
  gap: 0.15rem;
  padding: 0.1rem 0.25rem;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border-radius: 3px;
  font-size: 0.6rem;
  font-weight: 500;
  box-shadow: 0 1px 2px rgba(239, 68, 68, 0.3);
  animation: debtPulse 2s ease-in-out infinite;
}

/* 离店提醒 */
.checkout-reminder-medium {
  display: flex;
  align-items: center;
  gap: 0.15rem;
  padding: 0.1rem 0.25rem;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  border-radius: 3px;
  font-size: 0.6rem;
  font-weight: 500;
  box-shadow: 0 1px 2px rgba(245, 158, 11, 0.3);
}

/* 欠费脉冲动画 */
@keyframes debtPulse {
  0% {
    box-shadow: 0 1px 2px rgba(239, 68, 68, 0.3);
  }
  50% {
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.6), 0 0 0 2px rgba(239, 68, 68, 0.2);
  }
  100% {
    box-shadow: 0 1px 2px rgba(239, 68, 68, 0.3);
  }
}

/* 订单指示器 */
.orders-indicator-medium {
  display: flex;
  gap: 0.1rem;
  flex-shrink: 0;
}

.today-orders-dot,
.future-orders-dot {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  font-size: 0.6rem;
  font-weight: 600;
  color: white;
  cursor: pointer;
}

.today-orders-dot {
  background: linear-gradient(135deg, #10b981, #059669);
}

.future-orders-dot {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}
</style>
