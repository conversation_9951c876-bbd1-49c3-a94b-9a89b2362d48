/**
 * 营业日路由守卫
 * 确保在进入任何页面前都获取最新的营业日信息
 */
import { useBusinessDateStore } from '@/store'

export function createBusinessDateGuard(router) {
  router.beforeEach(async (to) => {

    // 跳过登录页面和其他不需要营业日的页面
    const skipRoutes = ['/login', '/404', '/403', '/500', '/']
    if (skipRoutes.includes(to.path)) {

      return true
    }

    try {
      // 检查是否有登录状态，如果没有则跳过营业日获取
      // 使用store中的token而不是直接从localStorage读取
      const { useAuthStore } = await import('@/store')
      const authStore = useAuthStore()
      const token = authStore.accessToken

      if (!token || token === 'null' || token === 'undefined') {

        return true
      }

      const businessDateStore = useBusinessDateStore()

      // 检查是否需要刷新营业日
      const shouldRefresh = !businessDateStore.currentDate ||
        !businessDateStore.lastUpdateTime ||
        (Date.now() - new Date(businessDateStore.lastUpdateTime).getTime()) > 5 * 60 * 1000 // 5分钟

      if (shouldRefresh) {

        try {
          await businessDateStore.fetchBusinessDate()

        } catch (apiError) {

          // 设置默认营业日为当前日期
          businessDateStore.setBusinessDate(new Date().toISOString().split('T')[0])
        }
      }

      return true
    } catch (error) {

      // 即使获取营业日失败，也继续导航，不阻塞用户操作
      return true
    }
  })
}
