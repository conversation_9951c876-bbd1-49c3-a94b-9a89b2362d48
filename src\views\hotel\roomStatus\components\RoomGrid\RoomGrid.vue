<template>
  <div class="room-grid-container">
    <!-- 骨架屏加载状态 -->
    <div v-if="loading" class="skeleton-container">
      <div class="skeleton-building" v-for="i in 2" :key="i">
        <div class="skeleton-building-header">
          <div class="skeleton-building-title"></div>
          <div class="skeleton-building-stats"></div>
        </div>
        <div class="skeleton-floor" v-for="j in 3" :key="j">
          <div class="skeleton-floor-header">
            <div class="skeleton-floor-title"></div>
            <div class="skeleton-floor-stats"></div>
          </div>
          <div :class="['skeleton-room-grid', `skeleton-grid-${cardSize}`]">
            <div
              v-for="k in (cardSize === 'large' ? 6 : cardSize === 'medium' ? 12 : 20)"
              :key="k"
              :class="['skeleton-room-card', `skeleton-card-${cardSize}`]"
            >
              <div class="skeleton-room-content">
                <div class="skeleton-room-number"></div>
                <div class="skeleton-room-type"></div>
                <div v-if="cardSize === 'large'" class="skeleton-guest-info">
                  <div class="skeleton-guest-name"></div>
                  <div class="skeleton-guest-phone"></div>
                </div>
                <div v-if="cardSize !== 'small'" class="skeleton-room-status"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <n-alert type="error" :title="error">
        <template #action>
          <n-button size="small" @click="$emit('retry')">
            {{ t('roomStatus.retryFetch') }}
          </n-button>
        </template>
      </n-alert>
    </div>

    <!-- 无数据状态 -->
    <div v-else-if="groupedRooms.length === 0" class="empty-container">
      <n-empty :description="t('roomStatus.noRoomData')">
        <template #extra>
          <n-button @click="$emit('refresh')">{{ t('roomStatus.refreshData') }}</n-button>
        </template>
      </n-empty>
    </div>

    <!-- 房间数据 -->
    <div
      v-else
      v-for="building in props.groupedRooms"
      :key="building.buildingName"
      class="building-section"
    >
        <div class="building-header">
          <h3 class="building-title">{{ building.buildingName }}</h3>
          <span class="building-stats">共{{ building.totalRooms }}间房</span>
        </div>

        <div
          v-for="floor in building.floors"
          :key="floor.floorNumber"
          class="floor-section"
        >
        <div class="floor-header">
          <h4 class="floor-title">{{ floor.floorNumber }}楼</h4>
          <div class="floor-stats">
            <span class="floor-room-count">{{ floor.rooms.length }}间</span>
            <div class="floor-status-summary">
              <span
                v-for="(count, status) in floor.statusSummary"
                :key="status"
                :class="['status-dot', `status-${status}`]"
                :title="`${statusTextMap[status] || '未知'}: ${count}间`"
              >
                {{ count }}
              </span>
            </div>
          </div>
        </div>

        <div :class="['room-grid', `grid-${cardSize}`]">
          <RoomCard
            v-for="room in floor.rooms"
            :key="`room-${room.roomNumber || room.room_number}-${room.id || room.room_id || 'empty'}-${forceRerenderKey}`"
            :room="room"
            :card-size="cardSize"
            :hovered-connect-code="hoveredConnectCode"
            :force-rerender-key="forceRerenderKey"
            @click="$emit('room-click', room)"
            @contextmenu="handleRoomContextMenu"
            @mouseenter="$emit('room-mouseenter', room)"
            @mouseleave="$emit('room-mouseleave', room)"
            @connect-room-hover="handleConnectRoomHover"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>

import { useI18n } from 'vue-i18n'
import RoomCard from './RoomCard.vue'

const { t } = useI18n()



// Props
const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: null
  },
  groupedRooms: {
    type: Array,
    default: () => []
  },
  cardSize: {
    type: String,
    default: 'medium',
    validator: (value) => ['large', 'medium', 'small'].includes(value)
  },
  hoveredConnectCode: {
    type: String,
    default: ''
  },
  forceRerenderKey: {
    type: Number,
    default: 0
  },
  statusTextMap: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits([
  'retry',
  'refresh',
  'room-click',
  'room-contextmenu',
  'room-mouseenter',
  'room-mouseleave',
  'connect-room-hover'
])

// 处理房间右键事件
const handleRoomContextMenu = (room, event) => {
  console.log('RoomGrid: 接收到右键事件', { room, event })
  emit('room-contextmenu', room, event)
}

// 处理联房悬浮事件
const handleConnectRoomHover = (connectCode, isHover) => {
  emit('connect-room-hover', connectCode, isHover)
}


</script>

<style scoped>
.room-grid-container {
  flex: 1;
  overflow-y: auto;
  padding: 0.8rem 1.5rem 3rem 1.5rem;
  min-height: 0;
  max-height: 100%;
  box-sizing: border-box;
}



/* 骨架屏样式 */
.skeleton-container {
  padding: 20px;
}

.skeleton-building {
  margin-bottom: 32px;
}

.skeleton-building-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.skeleton-building-title {
  width: 120px;
  height: 24px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.skeleton-building-stats {
  width: 80px;
  height: 20px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.skeleton-floor {
  margin-bottom: 24px;
}

.skeleton-floor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.skeleton-floor-title {
  width: 80px;
  height: 20px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.skeleton-floor-stats {
  width: 60px;
  height: 16px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.skeleton-room-grid {
  display: grid;
  gap: 12px;
}

.skeleton-grid-large {
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
}

.skeleton-grid-medium {
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}

.skeleton-grid-small {
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
}

.skeleton-room-card {
  border-radius: 8px;
  background: #fafafa;
  border: 1px solid #f0f0f0;
  overflow: hidden;
}

.skeleton-card-large {
  height: 160px;
  padding: 16px;
}

.skeleton-card-medium {
  height: 120px;
  padding: 12px;
}

.skeleton-card-small {
  height: 80px;
  padding: 8px;
}

.skeleton-room-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.skeleton-room-number {
  width: 60px;
  height: 18px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.skeleton-room-type {
  width: 80px;
  height: 14px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.skeleton-guest-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-top: 8px;
}

.skeleton-guest-name {
  width: 100px;
  height: 16px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.skeleton-guest-phone {
  width: 120px;
  height: 14px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.skeleton-room-status {
  width: 50px;
  height: 12px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
  margin-top: auto;
}

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 状态容器样式 */
.error-container {
  margin: 20px;
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  margin: 20px;
}

/* 楼栋楼层样式 */
.building-section {
  margin-bottom: 0.8rem;
}

.building-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 8px 8px 0 0;
  border-bottom: 2px solid var(--primary-color);
  margin-bottom: 0.5rem;
}

.building-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.building-stats {
  font-size: 0.75rem;
  color: #6b7280;
  background: rgba(107, 114, 128, 0.1);
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
}

.floor-section {
  margin-bottom: 1rem;
}

.floor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.4rem 0.8rem;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 6px;
  margin-bottom: 0.6rem;
  border-left: 3px solid var(--primary-color);
}

.floor-title {
  font-size: 0.95rem;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.floor-stats {
  display: flex;
  align-items: center;
  gap: 0.6rem;
}

.floor-room-count {
  font-size: 0.7rem;
  color: #6b7280;
}

.floor-status-summary {
  display: flex;
  gap: 0.3rem;
}

.status-dot {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 600;
  color: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.status-dot.status-occupied {
  background: #dc2626;
}

.status-dot.status-available {
  background: #059669;
}

.status-dot.status-checkout {
  background: #d97706;
}

.status-dot.status-dirty {
  background: #dc2626;
}

.status-dot.status-cleaning {
  background: #7c3aed;
}

.status-dot.status-inspecting {
  background: #0e7490;
}

.status-dot.status-maintenance {
  background: #d97706;
}

.status-dot.status-reserved {
  background: #059669;
}

/* 房态网格 */
.room-grid {
  display: grid;
  gap: 0.8rem;
  transition: all 0.3s ease;
}

.room-grid.grid-large {
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 0.8rem;
}

.room-grid.grid-medium {
  grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));
  gap: 0.5rem;
}

.room-grid.grid-small {
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 0.5rem;
}
</style>
