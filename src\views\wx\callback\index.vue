<template>
  <div>小程序授权</div>
</template>
<script setup>
  import { onMounted, ref } from 'vue';
  import { useRoute, useRouter } from 'vue-router'
  import { useTabStore ,useUserStore} from '@/store'
  import api from './api'

  const route = useRoute()
  const router = useRouter()
  const userStore = useUserStore()
  const queryParams = route.query

  onMounted(async () => { // 修改为异步函数
    try {
      // 等待授权请求完成
      await getCallback()
      // 授权完成查询插件状态写入数据库
      // 关闭当前标签页（假设使用 useTabStore 管理标签）
      const tabStore = useTabStore()
      const currentTabPath = route.fullPath
      tabStore.removeTab(currentTabPath) // 先关闭当前标签

      // 跳转回管理页
      router.replace('/wx/manager')
    } catch (error) {
      // 可选：跳转错误页或提示用户
      router.replace('/error')
    }
  })

  async function getCallback() {
    // 发送授权请求
    const { data } = await api.create({
      auth_code: queryParams.auth_code,
      pid: userStore.userInfo.platformList[0].id
    })
    return data
  }
</script>
