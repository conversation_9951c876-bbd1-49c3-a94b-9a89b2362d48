/**
 * 登录数据管理工具
 * 用于获取和管理登录后保存的所有信息
 */
import { lStorage } from '@/utils'

/**
 * 获取完整的登录响应数据
 * @returns {Object} 登录时服务器返回的完整数据
 */
export function getLoginResponseData() {
  return lStorage.get('loginResponseData') || {}
}

/**
 * 获取系统配置信息
 * @returns {Object} 系统配置数据
 */
export function getSystemConfig() {
  return lStorage.get('systemConfig') || {}
}

/**
 * 获取用户信息
 * @returns {Object} 用户信息数据
 */
export function getUserInfo() {
  return lStorage.get('userInfo') || {}
}

/**
 * 获取酒店/商店信息
 * @returns {Object} 酒店相关信息
 */
export function getShopInfo() {
  const loginData = getLoginResponseData()
  const systemConfig = getSystemConfig()

  return {
    shop_name: loginData.shop_name || systemConfig.shop_name || '',
    shop_id: loginData.shop_id || systemConfig.shop_id || '',
    cover_pic: loginData.cover_pic || systemConfig.cover_pic || '',
    shop_logo: loginData.shop_logo || systemConfig.shop_logo || '',
    shop_address: loginData.shop_address || systemConfig.shop_address || '',
    shop_phone: loginData.shop_phone || systemConfig.shop_phone || '',
    shop_description: loginData.shop_description || systemConfig.shop_description || ''
  }
}

/**
 * 获取网站配置信息
 * @returns {Object} 网站配置信息
 */
export function getWebConfig() {
  // 使用新的缓存管理工具
  const { getCachedWebConfig } = require('./loginCache')
  return getCachedWebConfig()
}

/**
 * 获取权限列表
 * @returns {Array} 权限列表
 */
export function getAuthList() {
  const loginData = getLoginResponseData()
  return loginData.auth_list || []
}

/**
 * 获取平台列表
 * @returns {Array} 平台列表
 */
export function getPlatformList() {
  const loginData = getLoginResponseData()
  return loginData.platformList || []
}

/**
 * 获取小程序列表
 * @returns {Array} 小程序列表
 */
export function getMiniprogramList() {
  const loginData = getLoginResponseData()
  return loginData.miniprogramList || []
}

/**
 * 获取指定字段的值
 * @param {string} fieldName 字段名
 * @param {any} defaultValue 默认值
 * @returns {any} 字段值
 */
export function getLoginField(fieldName, defaultValue = null) {
  const loginData = getLoginResponseData()
  const systemConfig = getSystemConfig()
  const userInfo = getUserInfo()

  // 按优先级查找字段值
  return loginData[fieldName] ||
         systemConfig[fieldName] ||
         userInfo[fieldName] ||
         defaultValue
}

/**
 * 获取所有保存的登录相关数据
 * @returns {Object} 包含所有登录相关数据的对象
 */
export function getAllLoginData() {
  return {
    loginResponseData: getLoginResponseData(),
    systemConfig: getSystemConfig(),
    userInfo: getUserInfo(),
    shopInfo: getShopInfo(),
    webConfig: getWebConfig(),
    authList: getAuthList(),
    platformList: getPlatformList(),
    miniprogramList: getMiniprogramList()
  }
}

/**
 * 清除所有登录相关数据
 */
export function clearAllLoginData() {
  lStorage.remove('loginResponseData')
  lStorage.remove('systemConfig')
  lStorage.remove('userInfo')
  lStorage.remove('token')
  lStorage.remove('refreshToken')
}

/**
 * 检查是否有登录数据
 * @returns {boolean} 是否有登录数据
 */
export function hasLoginData() {
  const token = lStorage.get('token')
  const loginData = getLoginResponseData()
  return !!(token && Object.keys(loginData).length > 0)
}

// 导出默认对象，包含所有方法
export default {
  getLoginResponseData,
  getSystemConfig,
  getUserInfo,
  getShopInfo,
  getWebConfig,
  getAuthList,
  getPlatformList,
  getMiniprogramList,
  getLoginField,
  getAllLoginData,
  clearAllLoginData,
  hasLoginData
}
