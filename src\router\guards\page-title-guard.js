
const baseTitle = import.meta.env.VITE_TITLE || '酒店管理系统'

// 获取自定义网站标题（简化版本，避免复杂依赖）
function getCustomTitle() {
  try {
    // 尝试从localStorage获取登录数据
    const loginData = localStorage.getItem('loginResponseData')
    if (loginData) {
      const data = JSON.parse(loginData)
      const webConfig = data.web_config
      const shopInfo = data.shop_info

      if (webConfig?.set_web_title && webConfig.set_web_title !== '自定义站点标题') {
        return webConfig.set_web_title
      }

      if (shopInfo?.shop_name) {
        return shopInfo.shop_name
      }
    }
  } catch (error) {
  }

  return baseTitle
}

export function createPageTitleGuard(router) {
  router.afterEach((to) => {
    const pageTitle = to.meta?.title
    const customBaseTitle = getCustomTitle()

    if (pageTitle) {
      document.title = `${pageTitle} | ${customBaseTitle}`
    } else {
      document.title = customBaseTitle
    }
  })
}
