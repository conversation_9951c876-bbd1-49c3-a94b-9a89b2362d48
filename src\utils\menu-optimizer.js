/**********************************
 * 菜单结构优化工具
 * 解决权限系统生成的菜单层级过深问题
 **********************************/

import { h } from 'vue'

// 菜单分组配置
const MENU_GROUPS = {
  'dashboard': {
    name: '工作台',
    icon: 'i-material-symbols:dashboard',
    priority: 1,
    keywords: ['dashboard', 'overview', 'today', 'alerts']
  },
  'front-desk': {
    name: '前台业务',
    icon: 'i-material-symbols:hotel',
    priority: 2,
    keywords: ['room', 'checkin', 'checkout', 'reservation']
  },
  'crm': {
    name: '客户关系',
    icon: 'i-material-symbols:people',
    priority: 3,
    keywords: ['guest', 'member', 'loyalty', 'customer']
  },
  'hotel': {
    name: '酒店管理',
    icon: 'i-material-symbols:apartment',
    priority: 4,
    keywords: ['hotel', 'room', 'card', 'member']
  },
  'system': {
    name: '系统管理',
    icon: 'i-material-symbols:settings',
    priority: 8,
    keywords: ['system', 'user', 'role', 'backup', 'audit']
  },
  'wx': {
    name: '微信管理',
    icon: 'i-material-symbols:chat',
    priority: 5,
    keywords: ['wx', 'wechat', 'manager', 'code']
  }
}

// 图标映射优化
const ICON_MAPPING = {
  // 系统管理
  '系统管理': 'i-material-symbols:settings-outline',
  '用户管理': 'i-material-symbols:person-outline',
  '角色管理': 'i-material-symbols:admin-panel-settings-outline',
  '权限管理': 'i-material-symbols:security-outline',
  '菜单管理': 'i-material-symbols:menu-outline',
  '操作日志': 'i-material-symbols:history-outline',
  '数据备份': 'i-material-symbols:backup-outline',
  '系统状态': 'i-material-symbols:monitor-heart-outline',
  
  // 前台业务
  '房态管理': 'i-material-symbols:grid-view-outline',
  '入住登记': 'i-material-symbols:person-add-outline',
  '退房结账': 'i-material-symbols:person-remove-outline',
  '预订管理': 'i-material-symbols:event-available-outline',
  
  // 客户关系
  '客人档案': 'i-material-symbols:person-outline',
  '会员管理': 'i-material-symbols:card-membership-outline',
  '积分管理': 'i-material-symbols:stars-outline',
  
  // 酒店管理
  '房型管理': 'i-material-symbols:bed-outline',
  '会员列表': 'i-material-symbols:people-outline',
  '会员卡管理': 'i-material-symbols:credit-card-outline',
  
  // 微信管理
  '微信管理': 'i-material-symbols:settings-outline',
  '代码模型': 'i-material-symbols:code-outline',
  '代码CGX': 'i-material-symbols:integration-instructions-outline',
  
  // 工作台
  '功能概览': 'i-material-symbols:analytics-outline',
  '今日营运': 'i-material-symbols:today-outline',
  '预警中心': 'i-material-symbols:notification-important-outline'
}

// 菜单优化器类
export class MenuOptimizer {
  constructor() {
    this.maxDepth = 2 // 最大层级限制
    this.orphanItems = [] // 孤立菜单项
  }

  /**
   * 优化菜单结构
   */
  optimize(menus) {
    if (!Array.isArray(menus)) return []
    
    // 第一步：扁平化处理
    const flatItems = this.flattenMenus(menus)
    
    // 第二步：智能分组
    const grouped = this.smartGroup(flatItems)
    
    // 第三步：层级优化
    const optimized = this.optimizeHierarchy(grouped)
    
    // 第四步：排序和清理
    return this.sortAndClean(optimized)
  }

  /**
   * 扁平化所有菜单项
   */
  flattenMenus(menus, depth = 0) {
    const items = []
    
    menus.forEach(menu => {
      const item = {
        key: menu.key,
        label: menu.label,
        path: menu.path,
        icon: menu.icon,
        order: menu.order || 999,
        depth: depth,
        original: menu
      }
      
      items.push(item)
      
      if (menu.children && menu.children.length > 0 && depth < this.maxDepth) {
        items.push(...this.flattenMenus(menu.children, depth + 1))
      } else if (menu.children && menu.children.length > 0) {
        // 超出层级的子菜单作为孤立项处理
        this.orphanItems.push(...this.flattenMenus(menu.children, depth + 1))
      }
    })
    
    return items
  }

  /**
   * 智能分组
   */
  smartGroup(items) {
    const groups = new Map()
    
    // 初始化分组
    Object.keys(MENU_GROUPS).forEach(key => {
      groups.set(key, {
        key: key,
        label: MENU_GROUPS[key].name,
        icon: MENU_GROUPS[key].icon,
        order: MENU_GROUPS[key].priority,
        children: []
      })
    })
    
    // 将菜单项分配到合适的分组
    items.forEach(item => {
      let matched = false
      
      // 根据关键词匹配
      for (const [groupKey, group] of Object.entries(MENU_GROUPS)) {
        const keywords = group.keywords
        const label = item.label.toLowerCase()
        const path = (item.path || '').toLowerCase()
        
        if (keywords.some(keyword => 
          label.includes(keyword) || 
          path.includes(keyword) ||
          item.key.toLowerCase().includes(keyword)
        )) {
          groups.get(groupKey).children.push(item)
          matched = true
          break
        }
      }
      
      // 未匹配的放入系统管理组
      if (!matched) {
        groups.get('system').children.push(item)
      }
    })
    
    // 处理孤立项
    this.orphanItems.forEach(item => {
      groups.get('system').children.push(item)
    })
    
    return Array.from(groups.values())
  }

  /**
   * 优化层级结构
   */
  optimizeHierarchy(groups) {
    return groups
      .filter(group => group.children.length > 0)
      .map(group => ({
        key: group.key,
        label: group.label,
        path: '', // 分组无路径
        icon: () => h('i', { class: `${group.icon} text-16` }),
        order: group.order,
        children: group.children
          .filter(child => child.path) // 确保有路径
          .map(child => ({
            key: child.key,
            label: child.label,
            path: child.path,
            icon: this.getOptimizedIcon(child.label, child.icon),
            order: child.order
          }))
          .sort((a, b) => a.order - b.order)
      }))
  }

  /**
   * 获取优化后的图标
   */
  getOptimizedIcon(label, originalIcon) {
    const icon = ICON_MAPPING[label] || 'i-material-symbols:folder-outline'
    return () => h('i', { class: `${icon} text-16` })
  }

  /**
   * 排序和清理
   */
  sortAndClean(menus) {
    return menus
      .sort((a, b) => a.order - b.order)
      .map(menu => {
        if (menu.children && menu.children.length === 0) {
          delete menu.children
        }
        return menu
      })
  }

  /**
   * 获取菜单统计信息
   */
  getStats(menus) {
    const flatten = (items) => {
      let count = 0
      items.forEach(item => {
        count++
        if (item.children) {
          count += flatten(item.children)
        }
      })
      return count
    }
    
    return {
      total: flatten(menus),
      groups: menus.length,
      maxDepth: this.calculateDepth(menus)
    }
  }

  /**
   * 计算菜单最大深度
   */
  calculateDepth(menus, depth = 1) {
    if (!menus || menus.length === 0) return depth
    
    let maxDepth = depth
    menus.forEach(menu => {
      if (menu.children && menu.children.length > 0) {
        maxDepth = Math.max(maxDepth, this.calculateDepth(menu.children, depth + 1))
      }
    })
    
    return maxDepth
  }
}

// 拼音搜索支持
export const pinyinSearch = {
  // 简化的拼音映射（实际项目中应使用完整拼音库）
  pinyinMap: {
    '工作台': 'gongzuotai',
    '房态管理': 'fangtaiguanli',
    '入住登记': 'ruzhudengji',
    '退房结账': 'tuifangjiezhang',
    '预订管理': 'yudingguanli',
    '客人档案': 'kerendangan',
    '会员管理': 'huiyuanguanli',
    '积分管理': 'jifenguanli',
    '系统管理': 'xitongguanli',
    '用户管理': 'yonghuguanli',
    '角色管理': 'jueseguanli',
    '操作日志': 'caozuorizhi'
  },

  search(items, keyword) {
    const lowerKeyword = keyword.toLowerCase()
    
    return items.filter(item => {
      const label = item.label.toLowerCase()
      const pinyin = this.pinyinMap[item.label] || ''
      
      return label.includes(lowerKeyword) || 
             pinyin.includes(lowerKeyword) ||
             (item.key && item.key.toLowerCase().includes(lowerKeyword))
    })
  }
}

// 菜单缓存工具
export const menuCache = {
  key: 'menu_state',
  
  save(state) {
    localStorage.setItem(this.key, JSON.stringify(state))
  },
  
  load() {
    try {
      const saved = localStorage.getItem(this.key)
      return saved ? JSON.parse(saved) : null
    } catch (error) {
      return null
    }
  },
  
  clear() {
    localStorage.removeItem(this.key)
  }
}