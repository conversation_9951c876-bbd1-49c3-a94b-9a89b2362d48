<template>
  <div
    :class="[
      'room-card',
      `card-${cardSize}`,
      { 'connect-room-hovered': hoveredConnectCode && room.connectCode && hoveredConnectCode === room.connectCode }
    ]"
    :style="getRoomCardStyle(room)"
    :data-connect-code="room.isConnectedRoom && room.connectCode ? room.connectCode : null"
    @click="handleClick"
    @contextmenu="handleContextMenu"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <!-- 大卡片模式 -->
    <RoomCardLarge
      v-if="cardSize === 'large'"
      :room="room"
    />

    <!-- 中卡片模式 -->
    <RoomCardMedium
      v-else-if="cardSize === 'medium'"
      :room="room"
    />

    <!-- 小方块模式 -->
    <RoomCardSmall
      v-else
      :room="room"
    />
  </div>
</template>

<script setup>
import RoomCardLarge from './RoomCardLarge.vue'
import RoomCardMedium from './RoomCardMedium.vue'
import RoomCardSmall from './RoomCardSmall.vue'

// Props
const props = defineProps({
  room: {
    type: Object,
    required: true
  },
  cardSize: {
    type: String,
    default: 'medium',
    validator: (value) => ['large', 'medium', 'small'].includes(value)
  },
  hoveredConnectCode: {
    type: String,
    default: ''
  },
  forceRerenderKey: {
    type: Number,
    default: 0
  }
})

// Emits
const emit = defineEmits(['click', 'contextmenu', 'mouseenter', 'mouseleave', 'connect-room-hover'])

// 处理点击事件
const handleClick = (event) => {
  emit('click', props.room)
}

// 处理右键菜单事件
const handleContextMenu = (event) => {
  console.log('RoomCard: 右键事件触发', {
    roomNumber: props.room?.roomNumber || props.room?.room_number,
    event: event
  })

  // 阻止默认的系统右键菜单
  event.preventDefault()
  event.stopPropagation()

  // 发送自定义右键事件
  emit('contextmenu', props.room, event)

  console.log('RoomCard: 右键事件已发送')
}

// 处理鼠标进入事件
const handleMouseEnter = (event) => {
  emit('mouseenter', props.room)

  // 如果是联房，触发联房悬浮效果
  if (props.room.isConnectedRoom && props.room.connectCode) {
    emit('connect-room-hover', props.room.connectCode, true)
  }
}

// 处理鼠标离开事件
const handleMouseLeave = (event) => {
  emit('mouseleave', props.room)

  // 如果是联房，取消联房悬浮效果
  if (props.room.isConnectedRoom && props.room.connectCode) {
    emit('connect-room-hover', props.room.connectCode, false)
  }
}

// 获取房间卡片样式
const getRoomCardStyle = (room) => {
  // 直接使用接口返回的颜色
  const primaryColor = room.room_status_color || '#e5e7eb'
  const secondaryColor = room.clear_color || '#f3f4f6'

  // 检查退房提醒
  let alertColor = ''
  if (isCheckoutOverdue(room)) {
    alertColor = '#ef4444' // 红色 - 已超时
  } else if (isCheckoutSoon(room)) {
    alertColor = '#f59e0b' // 橙色 - 即将到期
  }

  // 创建渐变背景，结合业务状态和清洁状态
  let gradientBackground = `linear-gradient(135deg, ${colorWithOpacity(primaryColor, 0.35)} 0%, ${colorWithOpacity(primaryColor, 0.15)} 50%, rgba(255, 255, 255, 0.85) 100%)`

  // 如果有退房提醒，添加警告色边框
  if (alertColor) {
    gradientBackground = `linear-gradient(135deg, ${colorWithOpacity(alertColor, 0.4)} 0%, ${colorWithOpacity(primaryColor, 0.35)} 30%, ${colorWithOpacity(primaryColor, 0.15)} 70%, rgba(255, 255, 255, 0.85) 100%)`
  }

  return {
    '--room-primary-color': primaryColor,
    '--room-secondary-color': secondaryColor,
    'border-color': colorWithOpacity(primaryColor, 0.6),
    'background': gradientBackground,
    'box-shadow': `0 2px 12px ${colorWithOpacity(primaryColor, 0.35)}`
  }
}

// 检查房间是否快到退房时间
const isCheckoutSoon = (room) => {
  if (!room.checkoutTime && !room.checkout_time) return false

  const checkoutTime = new Date(room.checkoutTime || room.checkout_time)
  const now = new Date()
  const timeDiff = checkoutTime.getTime() - now.getTime()
  const hoursDiff = timeDiff / (1000 * 60 * 60)

  // 如果在2小时内需要退房，返回true
  return hoursDiff > 0 && hoursDiff <= 2
}

// 检查房间是否已超时未退房
const isCheckoutOverdue = (room) => {
  if (!room.checkoutTime && !room.checkout_time) return false

  const checkoutTime = new Date(room.checkoutTime || room.checkout_time)
  const now = new Date()

  // 如果已过退房时间，返回true
  return now > checkoutTime
}

// 将颜色转换为带透明度的rgba格式
const colorWithOpacity = (color, opacity) => {
  // 如果是十六进制颜色
  if (color.startsWith('#')) {
    const hex = color.replace('#', '')
    const r = parseInt(hex.substr(0, 2), 16)
    const g = parseInt(hex.substr(2, 2), 16)
    const b = parseInt(hex.substr(4, 2), 16)
    return `rgba(${r}, ${g}, ${b}, ${opacity})`
  }

  // 如果是rgb格式
  if (color.startsWith('rgb(')) {
    const values = color.match(/\d+/g)
    if (values && values.length >= 3) {
      return `rgba(${values[0]}, ${values[1]}, ${values[2]}, ${opacity})`
    }
  }

  // 如果是rgba格式，替换透明度
  if (color.startsWith('rgba(')) {
    return color.replace(/,\s*[\d.]+\)$/, `, ${opacity})`)
  }

  // 其他情况，尝试直接添加透明度（兼容性处理）
  return `${color}${Math.round(opacity * 255).toString(16).padStart(2, '0')}`
}
</script>

<style scoped>
.room-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid;
  position: relative;
  display: flex;
  flex-direction: column;
}

.room-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

/* 联房悬浮效果 */
.room-card.connect-room-hovered {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 6px 20px rgba(139, 92, 246, 0.3);
  border-color: #8b5cf6 !important;
}

/* 大卡片样式 */
.room-card.card-large {
  padding: 0.6rem;
  min-height: 120px;
  max-height: 140px;
  gap: 0.25rem;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

/* 中卡片样式 */
.room-card.card-medium {
  padding: 0.5rem;
  min-height: 100px;
  max-height: 110px;
  gap: 0.2rem;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

/* 小卡片样式 */
.room-card.card-small {
  padding: 0.35rem;
  min-height: 70px;
  max-height: 75px;
  gap: 0.15rem;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
</style>
