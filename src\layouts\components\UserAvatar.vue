<template>
  <n-dropdown :options="options" @select="handleSelect">
    <div id="user-dropdown" class="user-dropdown-container">
      <!-- 用户头像 -->
      <div class="avatar-wrapper">
        <n-avatar
          round
          :size="32"
          :src="displayAvatar"
          :style="{ background: avatarBgColor }"
          class="user-avatar"
        >
          {{ avatarText }}
        </n-avatar>
        <div class="status-indicator"></div>
      </div>

      <!-- 用户信息 -->
      <div v-if="userStore.userInfo" class="user-info">
        <div class="user-primary">
          <span class="user-name">{{ displayAccountName }}</span>
          <span class="user-role">{{ displayRole }}</span>
        </div>
        <span class="user-account">{{ displayUsername }}</span>
      </div>

      <!-- 下拉箭头 -->
      <div class="dropdown-arrow" @click="debugUserInfo">
        <i class="i-material-symbols:expand-more"></i>
      </div>
    </div>
  </n-dropdown>

  <RoleSelect ref="roleSelectRef" />

  <!-- 账号切换弹窗 -->
  <AccountSwitchModal ref="accountSwitchModalRef" />
</template>

<script setup>
import { computed, h, ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useMessage, useDialog } from 'naive-ui'
import AccountSwitchModal from '@/components/AccountSwitchModal.vue'
import api from '@/api'
import { RoleSelect } from '@/layouts/components'
import { useAuthStore, usePermissionStore, useUserStore } from '@/store'
import { useLoginData } from '@/composables/useLoginData'
import { lStorage } from '@/utils'

const router = useRouter()
const userStore = useUserStore()
const authStore = useAuthStore()
const permissionStore = usePermissionStore()
const { t } = useI18n()
const message = useMessage()
const dialog = useDialog()

// 使用响应式登录数据
const {
  loginData,
  currentAccount,
  hasChildAccounts,
  forceUpdate
} = useLoginData()

// 强制获取数据
const initializeData = async () => {
  // 检查是否有数据
  const existingData = localStorage.getItem('loginResponseData')

  if (!existingData) {
    try {
      const response = await api.checkLoginStatus({ needTip: false })

      if (response && response.data) {
        // 直接使用原生 localStorage
        try {
          localStorage.setItem('loginResponseData', JSON.stringify(response.data))
        } catch (e) {
        }

        // 强制更新数据
        forceUpdate()
      }
    } catch (error) {
    }
  }
}

// 立即执行
initializeData()

// 用户信息显示 - 直接使用登录数据
const displayAccountName = computed(() => {
  // 直接使用登录数据中的昵称
  return loginData.value?.nickname ||
         loginData.value?.admin_name ||
         userStore.userInfo?.nickname ||
         '用户'
})

const displayUsername = computed(() => {
  // 直接使用登录数据中的店铺名
  return loginData.value?.shop_name ||
         userStore.userInfo?.shopName ||
         '当前店铺'
})

// 角色显示 - 显示账号名称（如"1045"）
const displayRole = computed(() => {
  // 直接使用登录数据中的账号名
  return loginData.value?.admin_name ||
         loginData.value?.name ||
         '默认角色'
})

const displayAvatar = computed(() => {
  return userStore.avatar ||
         userStore.userInfo?.avatar ||
         ''
})

// 头像背景色（当没有头像图片时）
const avatarBgColor = computed(() => {
  const colors = [
    'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
    'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
    'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
    'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)'
  ]
  const name = displayAccountName.value || '用户'
  const index = name.charCodeAt(0) % colors.length
  return colors[index]
})

// 头像文字（当没有头像图片时显示用户名首字母）
const avatarText = computed(() => {
  if (displayAvatar.value) return ''
  const name = displayAccountName.value || '用户'
  return name.charAt(0).toUpperCase()
})

// hasChildAccounts 已从 useLoginData 获取，无需重复定义

const options = computed(() => [
  {
    label: t('user.profile'),
    key: 'profile',
    icon: () => h('i', { class: 'i-material-symbols:person-outline text-14' }),
    show: computed(() => permissionStore.accessRoutes?.some(item => item.path === '/profile')),
  },
  {
    label: '切换账号',
    key: 'switchAccount',
    icon: () => h('i', { class: 'i-material-symbols:switch-account-outline text-14' }),
    show: computed(() => hasChildAccounts.value),
  },
  {
    label: t('user.logout'),
    key: 'logout',
    icon: () => h('i', { class: 'i-mdi:exit-to-app text-14' }),
  },
])

const roleSelectRef = ref(null)
const accountSwitchModalRef = ref(null)

// 调试函数 - 查看登录数据结构
const debugUserInfo = () => {
}

function handleSelect(key) {
  switch (key) {
    case 'profile':
      router.push('/profile')
      break
    case 'switchAccount':
      // 打开账号切换弹窗
      accountSwitchModalRef.value?.open()
      break
    case 'logout':
      dialog.warning({
        title: '提示',
        content: '确认退出？',
        positiveText: '确认',
        negativeText: '取消',
        onPositiveClick: async () => {
          try {
            await api.logout()
          }
          catch (error) {
          }
          authStore.logout()
          message.success(t('user.logoutSuccess'))
        },
      })
      break
  }
}
</script>

<style scoped>
.user-dropdown-container {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  height: 44px;
  border-radius: 22px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(226, 232, 240, 0.6);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  position: relative;
  overflow: hidden;
}

.user-dropdown-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(var(--primary-color), 0.02) 0%, transparent 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.user-dropdown-container:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(var(--primary-color), 0.2);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.user-dropdown-container:hover::before {
  opacity: 1;
}

.avatar-wrapper {
  position: relative;
  flex-shrink: 0;
}

.user-avatar {
  border: 2px solid rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.status-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 10px;
  height: 10px;
  background: #10b981;
  border: 2px solid white;
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.user-info {
  margin-left: 10px;
  flex: 1;
  min-width: 0;
  max-width: 140px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 2px;
}

.user-primary {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.user-name {
  font-size: 13px;
  font-weight: 600;
  color: var(--text-color-1);
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  letter-spacing: -0.01em;
}

.user-account {
  font-size: 10px;
  color: var(--text-color-3);
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  opacity: 0.7;
  font-weight: 400;
}

.user-role {
  font-size: 10px;
  color: var(--text-color-2);
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  opacity: 0.8;
  font-weight: 500;
  background: rgba(var(--primary-color), 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  align-self: flex-start;
}

.dropdown-arrow {
  margin-left: 8px;
  color: var(--text-color-3);
  font-size: 18px;
  transition: all 0.3s ease;
  flex-shrink: 0;
  opacity: 0.6;
}

.user-dropdown-container:hover .dropdown-arrow {
  opacity: 1;
  transform: rotate(180deg);
}

.user-dropdown-container:hover .user-avatar {
  transform: scale(1.05);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

/* 暗色主题适配 */
.dark .user-dropdown-container {
  background: rgba(31, 41, 55, 0.9);
  border-color: rgba(75, 85, 99, 0.6);
}

.dark .user-dropdown-container::before {
  background: linear-gradient(135deg, rgba(var(--primary-color), 0.05) 0%, transparent 100%);
}

.dark .user-dropdown-container:hover {
  background: rgba(31, 41, 55, 0.95);
  border-color: rgba(var(--primary-color), 0.3);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.dark .user-avatar {
  border-color: rgba(75, 85, 99, 0.8);
}

.dark .status-indicator {
  border-color: rgba(31, 41, 55, 1);
}
</style>
