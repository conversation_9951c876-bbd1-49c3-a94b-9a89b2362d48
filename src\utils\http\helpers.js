import { useAuthStore } from '@/store'
import { forceRelogin } from '@/utils/auth-check'

let isConfirming = false
let isHandling886 = false // 防止重复处理886错误

export function resolveResError(code, message, needTip = true) {
  switch (code) {

    case 886:
      // 登录过期处理 - 优先让路由守卫处理，这里作为兜底
      // 检查是否在路由守卫中（通过检查调用栈）
      const isFromRouteGuard = new Error().stack?.includes('permission-guard')

      if (isFromRouteGuard) {
        // 如果是从路由守卫调用的，不在这里处理跳转，让路由守卫处理
        return message || '登录已过期'
      }

      // 如果不是从路由守卫调用的，执行兜底跳转逻辑
      if (!isHandling886) {
        isHandling886 = true
        const errorMessage = message || '登录已过期，请重新登录'
        const currentHash = window.location.hash
        const isInLoginPage = currentHash.includes('/login')

        if (!isInLoginPage) {
          try {
            forceRelogin(errorMessage)
          } catch (error) {
          }
        }

        setTimeout(() => {
          isHandling886 = false
        }, 2000)
      }

      return message || '登录已过期'
    case 11007:
    case 11008:
      if (isConfirming || !needTip)
        return
      isConfirming = true
      $dialog.confirm({
        title: '提示',
        type: 'info',
        content: `${message}，是否重新登录？`,
        confirm() {
          useAuthStore().logout()
          window.$message?.success('已退出登录')
          isConfirming = false
        },
        cancel() {
          isConfirming = false
        },
      })
      return false
    case 403:
      message = '请求被拒绝'
      break
    case 404:
      message = '请求资源或接口不存在'
      break
    case 500:
      message =  message ?? `【${code}】: 未知异常!`
      break
    default:
      message = message ?? `【${code}】: 未知异常!`
      break
  }
  needTip && window.$message?.error(message)
  return message
}
