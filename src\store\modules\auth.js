/**********************************
  *hotel-jf-admin

 **********************************/

import { usePermissionStore, useRouterStore, useTabStore, useUserStore } from '@/store'
import { defineStore } from 'pinia'
import { lStorage } from '@/utils'
import { clearAccountsCache } from '@/utils/accountsCache'

/**
 * 认证状态管理 Store
 *
 * 用于管理用户认证相关的状态和操作，包括：
 * - 访问令牌(accessToken)的存储和管理
 * - 登录/登出相关操作
 * - 角色切换功能
 * - 登录状态重置
 *
 * @property {string|null} accessToken - 当前用户的访问令牌
 * @method setToken - 设置访问令牌
 * @method resetToken - 重置访问令牌
 * @method toLogin - 跳转到登录页
 * @method switchCurrentRole - 切换当前角色
 * @method resetLoginState - 重置所有登录相关状态
 * @method logout - 执行登出操作
 * @property {Object} persist - 持久化配置
 */
export const useAuthStore = defineStore('auth', {
  state: () => ({
    accessToken: null,
  }),
  actions: {
    setToken({ user_token }) {
      this.accessToken = user_token
    },
    resetToken() {
      this.$reset()
    },
    toLogin() {
      const { router, route } = useRouterStore()
      router.replace({
        path: '/login',
        query: route.query,
      })
    },
    async switchCurrentRole(data) {
      this.resetLoginState()
      await nextTick()
      this.setToken(data)
    },
    resetLoginState() {
      const { resetUser } = useUserStore()
      const { resetRouter } = useRouterStore()
      const { resetPermission, accessRoutes } = usePermissionStore()
      const { resetTabs } = useTabStore()
      // 重置路由
      resetRouter(accessRoutes)
      // 重置用户
      resetUser()
      // 重置权限
      resetPermission()
      // 重置Tabs
      resetTabs()
      // 清理本地存储的用户信息和权限信息
      lStorage.remove('userInfo')
      lStorage.remove('auth_list')
      // 清理登录响应数据和原始登录数据
      lStorage.remove('loginResponseData')
      lStorage.remove('originalLoginData')
      // 清理账号缓存
      clearAccountsCache()
      // 重置token
      this.resetToken()
    },
    async logout() {
      this.resetLoginState()
      this.toLogin()
    },
  },
  persist: {
    key: 'hotelhotel',
  },
})
