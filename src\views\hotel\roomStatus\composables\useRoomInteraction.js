import { ref, nextTick } from 'vue'

/**
 * 房间交互逻辑组合函数
 * 负责房间点击、右键菜单、悬浮效果等交互处理
 */
export function useRoomInteraction() {
  // 响应式状态
  const selectedRoom = ref(null)
  const hoveredRoom = ref(null)
  const hoveredConnectCode = ref('')
  const contextMenuVisible = ref(false)
  const contextMenuPosition = ref({ x: 0, y: 0 })
  const contextMenuRoom = ref(null)

  // 房间点击处理
  const handleRoomClick = (room, event) => {
    if (!room) return

    // 阻止事件冒泡
    if (event) {
      event.stopPropagation()
    }

    // 设置选中房间
    selectedRoom.value = room

    console.log('房间点击:', {
      roomNumber: room.roomNumber || room.room_number,
      roomId: room.id || room.room_id,
      status: room.status || room.room_status_sign
    })

    // 触发房间选择事件
    return {
      type: 'room-select',
      room: room,
      event: event
    }
  }

  // 房间双击处理
  const handleRoomDoubleClick = (room, event) => {
    if (!room) return

    // 阻止事件冒泡
    if (event) {
      event.stopPropagation()
    }

    console.log('房间双击:', {
      roomNumber: room.roomNumber || room.room_number,
      roomId: room.id || room.room_id
    })

    // 触发房间详情事件
    return {
      type: 'room-detail',
      room: room,
      event: event
    }
  }

  // 房间右键菜单处理
  const handleRoomContextMenu = (room, event) => {
    console.log('useRoomInteraction: 接收到右键事件', {
      room: room,
      event: event,
      roomNumber: room?.roomNumber || room?.room_number
    })

    if (!room || !event) {
      console.warn('useRoomInteraction: 房间或事件为空', { room, event })
      return
    }

    // 安全地阻止默认右键菜单
    try {
      if (typeof event.preventDefault === 'function') {
        event.preventDefault()
      }
      if (typeof event.stopPropagation === 'function') {
        event.stopPropagation()
      }
    } catch (error) {
      console.warn('事件处理警告:', error)
    }

    // 检查房间状态，如果是空房间直接弹出入住弹窗
    if (isRoomAvailableForCheckIn(room)) {
      console.log('空房间右键，直接弹出入住弹窗')
      // 直接触发入住操作
      return {
        type: 'room-checkin',
        room: room,
        event: event
      }
    }

    // 设置右键菜单相关状态
    contextMenuRoom.value = room
    contextMenuPosition.value = {
      x: event.clientX,
      y: event.clientY
    }
    contextMenuVisible.value = true

    console.log('房间右键:', {
      roomNumber: room.roomNumber || room.room_number,
      position: contextMenuPosition.value
    })

    // 触发右键菜单事件
    return {
      type: 'room-contextmenu',
      room: room,
      position: contextMenuPosition.value,
      event: event
    }
  }

  // 房间悬浮进入处理
  const handleRoomMouseEnter = (room, event) => {
    if (!room) return

    hoveredRoom.value = room

    // 处理联房悬浮效果
    if (room.isConnectedRoom && room.connectCode) {
      hoveredConnectCode.value = room.connectCode
    }

    // 触发悬浮进入事件
    return {
      type: 'room-hover-enter',
      room: room,
      event: event
    }
  }

  // 房间悬浮离开处理
  const handleRoomMouseLeave = (room, event) => {
    if (!room) return

    hoveredRoom.value = null

    // 清除联房悬浮效果
    if (room.isConnectedRoom && room.connectCode) {
      hoveredConnectCode.value = ''
    }

    // 触发悬浮离开事件
    return {
      type: 'room-hover-leave',
      room: room,
      event: event
    }
  }

  // 联房悬浮处理
  const handleConnectRoomHover = (connectCode, isHover) => {
    if (isHover) {
      hoveredConnectCode.value = connectCode
    } else {
      hoveredConnectCode.value = ''
    }
  }

  // 关闭右键菜单
  const closeContextMenu = () => {
    contextMenuVisible.value = false
    contextMenuRoom.value = null
    contextMenuPosition.value = { x: 0, y: 0 }
  }

  // 处理右键菜单项点击
  const handleContextMenuAction = (action, room = null) => {
    const targetRoom = room || contextMenuRoom.value
    if (!targetRoom) return

    console.log('右键菜单操作:', {
      action,
      roomNumber: targetRoom.roomNumber || targetRoom.room_number
    })

    // 关闭右键菜单
    closeContextMenu()

    // 根据操作类型处理
    switch (action) {
      case 'checkin':
        return handleCheckinAction(targetRoom)
      case 'checkout':
        return handleCheckoutAction(targetRoom)
      case 'clean':
        return handleCleanAction(targetRoom)
      case 'maintenance':
        return handleMaintenanceAction(targetRoom)
      case 'block':
        return handleBlockAction(targetRoom)
      case 'unblock':
        return handleUnblockAction(targetRoom)
      case 'detail':
        return handleRoomDetailAction(targetRoom)
      case 'history':
        return handleRoomHistoryAction(targetRoom)
      case 'room-service':
        return handleRoomServiceAction(targetRoom)
      default:
        console.warn('未知的右键菜单操作:', action)
        return null
    }
  }

  // 入住操作
  const handleCheckinAction = (room) => {
    return {
      type: 'room-checkin',
      room: room
    }
  }

  // 退房操作
  const handleCheckoutAction = (room) => {
    return {
      type: 'room-checkout',
      room: room
    }
  }

  // 清洁操作
  const handleCleanAction = (room) => {
    return {
      type: 'room-clean',
      room: room
    }
  }

  // 维修操作
  const handleMaintenanceAction = (room) => {
    return {
      type: 'room-maintenance',
      room: room
    }
  }

  // 封锁操作
  const handleBlockAction = (room) => {
    return {
      type: 'room-block',
      room: room
    }
  }

  // 解封操作
  const handleUnblockAction = (room) => {
    return {
      type: 'room-unblock',
      room: room
    }
  }

  // 房间详情操作
  const handleRoomDetailAction = (room) => {
    return {
      type: 'room-detail',
      room: room
    }
  }

  // 房间历史操作
  const handleRoomHistoryAction = (room) => {
    return {
      type: 'room-history',
      room: room
    }
  }

  // 客房服务操作
  const handleRoomServiceAction = (room) => {
    return {
      type: 'room-service',
      room: room
    }
  }

  // 批量选择处理
  const selectedRooms = ref([])
  const isMultiSelectMode = ref(false)

  const toggleMultiSelectMode = () => {
    isMultiSelectMode.value = !isMultiSelectMode.value
    if (!isMultiSelectMode.value) {
      selectedRooms.value = []
    }
  }

  const toggleRoomSelection = (room) => {
    if (!isMultiSelectMode.value) return

    const roomId = room.id || room.room_id
    const index = selectedRooms.value.findIndex(r =>
      (r.id || r.room_id) === roomId
    )

    if (index > -1) {
      selectedRooms.value.splice(index, 1)
    } else {
      selectedRooms.value.push(room)
    }
  }

  const selectAllRooms = (rooms) => {
    if (!isMultiSelectMode.value) return
    selectedRooms.value = [...rooms]
  }

  const clearRoomSelection = () => {
    selectedRooms.value = []
  }

  const isRoomSelected = (room) => {
    if (!isMultiSelectMode.value) return false
    const roomId = room.id || room.room_id
    return selectedRooms.value.some(r => (r.id || r.room_id) === roomId)
  }

  // 键盘快捷键处理
  const handleKeyboardShortcut = (event) => {
    if (!selectedRoom.value) return

    switch (event.key) {
      case 'Enter':
        // 回车键打开房间详情
        event.preventDefault()
        return handleRoomDetailAction(selectedRoom.value)

      case 'Delete':
        // 删除键触发退房（如果房间已入住）
        event.preventDefault()
        if (isRoomOccupied(selectedRoom.value)) {
          return handleCheckoutAction(selectedRoom.value)
        }
        break

      case 'F2':
        // F2键触发入住（如果房间可用）
        event.preventDefault()
        if (isRoomAvailable(selectedRoom.value)) {
          return handleCheckinAction(selectedRoom.value)
        }
        break

      case 'Escape':
        // ESC键清除选择
        event.preventDefault()
        selectedRoom.value = null
        closeContextMenu()
        break
    }

    return null
  }

  // 辅助函数
  const isRoomOccupied = (room) => {
    const status = room.status || room.room_status_sign
    return status === 'stay' || status === 'occupied'
  }

  const isRoomAvailable = (room) => {
    const status = room.status || room.room_status_sign
    return status === 'vacant' || status === 'available'
  }

  const isRoomDirty = (room) => {
    const cleanStatus = room.clear_status_name || room.cleanStatusName
    return cleanStatus === '脏' || cleanStatus === 'dirty'
  }

  const isRoomMaintenance = (room) => {
    const cleanStatus = room.clear_status_name || room.cleanStatusName
    return cleanStatus === '维修' || cleanStatus === '维修中' || cleanStatus === 'maintenance'
  }

  // 判断房间是否可以办理入住
  const isRoomAvailableForCheckIn = (room) => {
    if (!room) return false

    console.log('检查房间是否可入住:', {
      room: room,
      roomNumber: room.roomNumber || room.room_number,
      billId: room.billId,
      bill_id: room.bill_id,
      room_status_name: room.room_status_name,
      statusName: room.statusName,
      clear_status_name: room.clear_status_name,
      cleanStatusName: room.cleanStatusName
    })

    // 检查房间是否有现有订单
    const hasExistingOrder = !!(room.billId || room.bill_id)
    if (hasExistingOrder) {
      console.log('房间有现有订单，不能入住')
      return false
    }

    // 检查房间状态 - 空房状态可以入住
    // 尝试多种可能的状态字段名
    const roomStatus = room.room_status_name || room.statusName || room.status || room.roomStatus
    const cleanStatus = room.clear_status_name || room.cleanStatusName || room.cleanStatus

    // 判断是否为空房状态
    // 根据调试信息，available状态且没有订单的房间可以入住
    const isAvailableStatus = roomStatus === 'available' || roomStatus === '空' || roomStatus === '空房' || roomStatus === 'vacant'

    // 修改：允许脏房直接入住，不再限制清洁状态
    // 原来的限制：const isCleanStatus = cleanStatus === '净' || cleanStatus === '空' || cleanStatus === '空房' || cleanStatus === 'vacant'
    // 现在允许所有清洁状态，包括脏房
    const isCleanStatusOk = !cleanStatus || cleanStatus === '净' || cleanStatus === '空' || cleanStatus === '空房' || cleanStatus === 'vacant' || cleanStatus === '脏'

    // 房间可入住的条件：状态为可用（不再限制清洁状态）
    const isVacant = isAvailableStatus && isCleanStatusOk

    console.log('房间状态检查:', {
      roomStatus: roomStatus,
      cleanStatus: cleanStatus,
      isAvailableStatus: isAvailableStatus,
      isCleanStatusOk: isCleanStatusOk,
      isVacant: isVacant
    })

    return isVacant
  }

  // 清理函数
  const cleanup = () => {
    selectedRoom.value = null
    hoveredRoom.value = null
    hoveredConnectCode.value = ''
    closeContextMenu()
    selectedRooms.value = []
    isMultiSelectMode.value = false
  }

  return {
    // 状态
    selectedRoom,
    hoveredRoom,
    hoveredConnectCode,
    contextMenuVisible,
    contextMenuPosition,
    contextMenuRoom,
    selectedRooms,
    isMultiSelectMode,

    // 基础交互方法
    handleRoomClick,
    handleRoomDoubleClick,
    handleRoomContextMenu,
    handleRoomMouseEnter,
    handleRoomMouseLeave,
    handleConnectRoomHover,

    // 右键菜单方法
    closeContextMenu,
    handleContextMenuAction,

    // 批量选择方法
    toggleMultiSelectMode,
    toggleRoomSelection,
    selectAllRooms,
    clearRoomSelection,
    isRoomSelected,

    // 键盘快捷键
    handleKeyboardShortcut,

    // 辅助方法
    isRoomOccupied,
    isRoomAvailable,
    isRoomDirty,
    isRoomMaintenance,
    isRoomAvailableForCheckIn,

    // 清理方法
    cleanup
  }
}
