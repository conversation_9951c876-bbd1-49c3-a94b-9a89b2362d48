import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import * as Api from './api'
import dayjs from 'dayjs'

export const useData = () => {
  const hotelId = ref(1)
  const hotelName = ref('演示酒店')
  const tableData = ref([])
  const summaryData = ref({})
  const loading = ref(false)
  const today = dayjs()
  const startDate = ref(today.add(1, 'day'))
  const dateRange = ref(15)

  const dates = computed(() => {
    return Array.from({ length: dateRange.value }, (_, i) => {
      return startDate.value.add(i, 'day')
    })
  })

  const fetchRoomStatus = async () => {
    try {
      loading.value = true
      const params = {
        hotel_id: hotelId.value,
        start_date: startDate.value.format('YYYY-MM-DD'),
        end_date: startDate.value.add(dateRange.value - 1, 'day').format('YYYY-MM-DD')
      }
      const res = await Api.getRoomStatus(params)
      tableData.value = res.data.room_status
      summaryData.value = res.data.summary
    } catch (error) {
      ElMessage.error('加载失败')
    } finally {
      loading.value = false
    }
  }

  const handleDateChange = (days) => {
    startDate.value = startDate.value.add(days, 'day')
    fetchRoomStatus()
  }

  onMounted(() => {
    fetchRoomStatus()
  })

  return {
    hotelId,
    hotelName,
    tableData,
    summaryData,
    loading,
    startDate,
    dateRange,
    dates,
    fetchRoomStatus,
    handleDateChange
  }
}

