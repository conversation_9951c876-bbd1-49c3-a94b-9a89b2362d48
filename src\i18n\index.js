/**
 * 多语言配置文件
 * 支持中文和英文两种语言
 */

import { createI18n } from 'vue-i18n'
import zhCN from './locales/zh-CN'
import enUS from './locales/en-US'

// 支持的语言列表
export const SUPPORT_LOCALES = [
  {
    key: 'zh-CN',
    label: '简体中文',
    flag: '🇨🇳'
  },
  {
    key: 'en-US', 
    label: 'English',
    flag: '🇺🇸'
  }
]

// 获取默认语言
function getDefaultLocale() {
  // 优先从localStorage获取用户设置的语言
  const savedLocale = localStorage.getItem('app-locale')
  if (savedLocale && SUPPORT_LOCALES.find(locale => locale.key === savedLocale)) {
    return savedLocale
  }
  
  // 其次从浏览器语言获取
  const browserLocale = navigator.language || navigator.userLanguage
  if (browserLocale.startsWith('zh')) {
    return 'zh-CN'
  } else if (browserLocale.startsWith('en')) {
    return 'en-US'
  }
  
  // 默认返回中文
  return 'zh-CN'
}

// 创建i18n实例
const i18n = createI18n({
  legacy: false, // 使用Composition API模式
  locale: getDefaultLocale(),
  fallbackLocale: 'zh-CN',
  messages: {
    'zh-CN': zhCN,
    'en-US': enUS
  },
  globalInjection: true, // 全局注入$t方法
  silentTranslationWarn: true, // 静默翻译警告
  silentFallbackWarn: true // 静默回退警告
})

// 切换语言的工具函数
export function setLocale(locale) {
  if (SUPPORT_LOCALES.find(item => item.key === locale)) {
    i18n.global.locale.value = locale
    localStorage.setItem('app-locale', locale)
    document.documentElement.lang = locale
    return locale
  }
  return i18n.global.locale.value
}

// 获取当前语言
export function getLocale() {
  return i18n.global.locale.value
}

// 获取语言标签
export function getLocaleLabel(locale) {
  const localeItem = SUPPORT_LOCALES.find(item => item.key === locale)
  return localeItem ? localeItem.label : locale
}

export default i18n
