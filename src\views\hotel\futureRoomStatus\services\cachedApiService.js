import { roomStatusCache, basicDataCache, globalCache } from '../utils/cacheManager'
import * as api from '../api'

/**
 * 带缓存的API服务
 */
class CachedApiService {
  constructor() {
    this.roomStatusCache = roomStatusCache
    this.basicDataCache = basicDataCache
    this.globalCache = globalCache
  }

  /**
   * 获取房间列表（带缓存）
   */
  async getRoomList(params = {}) {
    const cacheKey = this.basicDataCache.generateKey('roomList', params)
    
    // 尝试从缓存获取
    const cached = this.basicDataCache.get(cacheKey)
    if (cached) {
      console.log('从缓存获取房间列表')
      return cached
    }

    // 缓存未命中，调用API
    try {
      const data = await api.getRoomList(params)
      this.basicDataCache.set(cacheKey, data, 30 * 60 * 1000) // 缓存30分钟
      console.log('API获取房间列表并缓存')
      return data
    } catch (error) {
      console.error('获取房间列表失败:', error)
      throw error
    }
  }

  /**
   * 获取楼栋列表（带缓存）
   */
  async getBuildingList(params = {}) {
    const cacheKey = this.basicDataCache.generateKey('buildingList', params)
    
    const cached = this.basicDataCache.get(cacheKey)
    if (cached) {
      console.log('从缓存获取楼栋列表')
      return cached
    }

    try {
      const data = await api.getBuildingList(params)
      this.basicDataCache.set(cacheKey, data, 30 * 60 * 1000) // 缓存30分钟
      console.log('API获取楼栋列表并缓存')
      return data
    } catch (error) {
      console.error('获取楼栋列表失败:', error)
      throw error
    }
  }

  /**
   * 获取楼层列表（带缓存）
   */
  async getFloorList(buildingId, params = {}) {
    const cacheKey = this.basicDataCache.generateKey('floorList', { buildingId, ...params })
    
    const cached = this.basicDataCache.get(cacheKey)
    if (cached) {
      console.log('从缓存获取楼层列表')
      return cached
    }

    try {
      const data = await api.getFloorList(buildingId, params)
      this.basicDataCache.set(cacheKey, data, 30 * 60 * 1000) // 缓存30分钟
      console.log('API获取楼层列表并缓存')
      return data
    } catch (error) {
      console.error('获取楼层列表失败:', error)
      throw error
    }
  }

  /**
   * 获取远期房态数据（带智能缓存）
   */
  async getFutureRoomStatusData(params) {
    const cacheKey = this.roomStatusCache.generateKey('roomStatus', params)
    
    // 尝试从缓存获取
    const cached = this.roomStatusCache.get(cacheKey)
    if (cached) {
      console.log('从缓存获取房态数据')
      return cached
    }

    // 检查是否有部分缓存可以复用
    const partialData = this.getPartialCachedData(params)
    if (partialData) {
      console.log('使用部分缓存数据')
      return partialData
    }

    try {
      const data = await api.getFutureRoomStatusData(params)
      
      // 缓存完整数据
      this.roomStatusCache.set(cacheKey, data, 5 * 60 * 1000) // 缓存5分钟
      
      // 同时缓存子集数据，便于后续复用
      this.cacheSubsetData(params, data)
      
      console.log('API获取房态数据并缓存')
      return data
    } catch (error) {
      console.error('获取房态数据失败:', error)
      throw error
    }
  }

  /**
   * 获取部分缓存数据
   */
  getPartialCachedData(params) {
    const { startDate, endDate, roomIds } = params
    
    // 查找是否有包含当前查询范围的缓存数据
    for (const [key, value] of this.roomStatusCache.cache.entries()) {
      if (key.includes('roomStatus')) {
        try {
          const cachedParams = JSON.parse(key.split(':')[1])
          
          // 检查日期范围是否被包含
          if (cachedParams.startDate <= startDate && 
              cachedParams.endDate >= endDate &&
              this.isRoomIdsSubset(roomIds, cachedParams.roomIds)) {
            
            // 提取需要的数据子集
            const cachedData = this.roomStatusCache.get(key)
            if (cachedData) {
              return this.extractSubsetData(cachedData, params)
            }
          }
        } catch (error) {
          // 忽略解析错误
        }
      }
    }
    
    return null
  }

  /**
   * 检查房间ID是否为子集
   */
  isRoomIdsSubset(subset, superset) {
    return subset.every(id => superset.includes(id))
  }

  /**
   * 提取数据子集
   */
  extractSubsetData(fullData, params) {
    const { startDate, endDate, roomIds } = params
    const result = {}
    
    // 生成需要的日期列表
    const dates = []
    const start = new Date(startDate)
    const end = new Date(endDate)
    
    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      dates.push(d.toISOString().split('T')[0])
    }
    
    // 提取指定房间和日期的数据
    roomIds.forEach(roomId => {
      if (fullData[roomId]) {
        result[roomId] = {}
        dates.forEach(date => {
          if (fullData[roomId][date]) {
            result[roomId][date] = fullData[roomId][date]
          }
        })
      }
    })
    
    return result
  }

  /**
   * 缓存子集数据
   */
  cacheSubsetData(params, data) {
    const { startDate, endDate, roomIds } = params
    
    // 为不同的日期范围和房间组合创建缓存
    const dateRanges = this.generateDateRanges(startDate, endDate)
    const roomGroups = this.generateRoomGroups(roomIds)
    
    dateRanges.forEach(dateRange => {
      roomGroups.forEach(roomGroup => {
        const subsetParams = {
          startDate: dateRange.start,
          endDate: dateRange.end,
          roomIds: roomGroup
        }
        
        const subsetData = this.extractSubsetData(data, subsetParams)
        const subsetKey = this.roomStatusCache.generateKey('roomStatus', subsetParams)
        
        this.roomStatusCache.set(subsetKey, subsetData, 5 * 60 * 1000)
      })
    })
  }

  /**
   * 生成日期范围组合
   */
  generateDateRanges(startDate, endDate) {
    const ranges = []
    const start = new Date(startDate)
    const end = new Date(endDate)
    const totalDays = Math.ceil((end - start) / (1000 * 60 * 60 * 24))
    
    // 生成不同长度的日期范围
    for (let length = 7; length <= totalDays; length += 7) {
      for (let offset = 0; offset <= totalDays - length; offset += 7) {
        const rangeStart = new Date(start)
        rangeStart.setDate(start.getDate() + offset)
        
        const rangeEnd = new Date(rangeStart)
        rangeEnd.setDate(rangeStart.getDate() + length - 1)
        
        ranges.push({
          start: rangeStart.toISOString().split('T')[0],
          end: rangeEnd.toISOString().split('T')[0]
        })
      }
    }
    
    return ranges
  }

  /**
   * 生成房间组合
   */
  generateRoomGroups(roomIds) {
    const groups = []
    const groupSize = Math.min(10, roomIds.length)
    
    for (let i = 0; i < roomIds.length; i += groupSize) {
      groups.push(roomIds.slice(i, i + groupSize))
    }
    
    return groups
  }

  /**
   * 清除指定类型的缓存
   */
  clearCache(type = 'all') {
    switch (type) {
      case 'roomStatus':
        this.roomStatusCache.clear()
        break
      case 'basicData':
        this.basicDataCache.clear()
        break
      case 'all':
        this.roomStatusCache.clear()
        this.basicDataCache.clear()
        this.globalCache.clear()
        break
    }
    console.log(`已清除 ${type} 缓存`)
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    return {
      roomStatus: this.roomStatusCache.getStats(),
      basicData: this.basicDataCache.getStats(),
      global: this.globalCache.getStats()
    }
  }

  /**
   * 预热缓存
   */
  async warmupCache() {
    try {
      // 预热基础数据
      await Promise.all([
        this.getRoomList(),
        this.getBuildingList()
      ])
      
      console.log('缓存预热完成')
    } catch (error) {
      console.error('缓存预热失败:', error)
    }
  }
}

// 创建单例实例
export const cachedApiService = new CachedApiService()

export default CachedApiService
