<template>
  <div class="today-dashboard">
    <div class="page-header">
      <h1 class="page-title">
        <i class="i-material-symbols:today-outline"></i>
        今日营运
      </h1>
      <p class="page-description">{{ currentDate }} 的营运数据概览</p>
    </div>

    <!-- 核心指标卡片 -->
    <div class="metrics-grid">
      <n-card class="metric-card occupancy">
        <div class="metric-content">
          <div class="metric-icon">
            <i class="i-material-symbols:hotel-outline"></i>
          </div>
          <div class="metric-info">
            <div class="metric-value">{{ todayMetrics.occupancyRate }}%</div>
            <div class="metric-label">入住率</div>
            <div class="metric-detail">{{ todayMetrics.occupiedRooms }}/{{ todayMetrics.totalRooms }} 间</div>
          </div>
        </div>
      </n-card>

      <n-card class="metric-card revenue">
        <div class="metric-content">
          <div class="metric-icon">
            <i class="i-material-symbols:monetization-on-outline"></i>
          </div>
          <div class="metric-info">
            <div class="metric-value">¥{{ todayMetrics.revenue.toLocaleString() }}</div>
            <div class="metric-label">今日收入</div>
            <div class="metric-detail">较昨日 +{{ todayMetrics.revenueGrowth }}%</div>
          </div>
        </div>
      </n-card>

      <n-card class="metric-card arrivals">
        <div class="metric-content">
          <div class="metric-icon">
            <i class="i-material-symbols:person-add-outline"></i>
          </div>
          <div class="metric-info">
            <div class="metric-value">{{ todayMetrics.arrivals }}</div>
            <div class="metric-label">今日到店</div>
            <div class="metric-detail">预计 {{ todayMetrics.expectedArrivals }} 人</div>
          </div>
        </div>
      </n-card>

      <n-card class="metric-card departures">
        <div class="metric-content">
          <div class="metric-icon">
            <i class="i-material-symbols:person-remove-outline"></i>
          </div>
          <div class="metric-info">
            <div class="metric-value">{{ todayMetrics.departures }}</div>
            <div class="metric-label">今日离店</div>
            <div class="metric-detail">预计 {{ todayMetrics.expectedDepartures }} 人</div>
          </div>
        </div>
      </n-card>
    </div>

    <!-- 详细信息区域 -->
    <n-grid :cols="2" :x-gap="24" class="detail-grid">
      <!-- 今日任务 -->
      <n-card title="今日任务" class="task-card">
        <div class="task-list">
          <div
            v-for="task in todayTasks"
            :key="task.id"
            :class="['task-item', task.priority]"
          >
            <div class="task-info">
              <div class="task-title">{{ task.title }}</div>
              <div class="task-time">{{ task.time }}</div>
            </div>
            <div class="task-status">
              <n-tag :type="getTaskStatusType(task.status)">
                {{ task.status }}
              </n-tag>
            </div>
          </div>
        </div>
      </n-card>

      <!-- 房间状态分布 -->
      <n-card title="房间状态" class="room-status-card">
        <div class="room-status-chart">
          <div
            v-for="status in roomStatusData"
            :key="status.name"
            class="status-item"
          >
            <div class="status-bar">
              <div
                class="status-fill"
                :style="{
                  width: `${(status.count / todayMetrics.totalRooms) * 100}%`,
                  backgroundColor: status.color
                }"
              ></div>
            </div>
            <div class="status-info">
              <span class="status-name">{{ status.name }}</span>
              <span class="status-count">{{ status.count }}</span>
            </div>
          </div>
        </div>
      </n-card>
    </n-grid>

    <!-- 实时动态 -->
    <n-card title="实时动态" class="activity-card">
      <div class="activity-list">
        <div
          v-for="activity in recentActivities"
          :key="activity.id"
          class="activity-item"
        >
          <div class="activity-icon">
            <i :class="activity.icon"></i>
          </div>
          <div class="activity-content">
            <div class="activity-text">{{ activity.text }}</div>
            <div class="activity-time">{{ activity.time }}</div>
          </div>
        </div>
      </div>
    </n-card>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const currentDate = computed(() => {
  return new Date().toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
})

// 今日核心指标
const todayMetrics = ref({
  occupancyRate: 85,
  totalRooms: 120,
  occupiedRooms: 102,
  revenue: 45600,
  revenueGrowth: 12.5,
  arrivals: 28,
  expectedArrivals: 32,
  departures: 25,
  expectedDepartures: 30
})

// 今日任务
const todayTasks = ref([
  {
    id: 1,
    title: '夜审作业',
    time: '23:30',
    status: '待处理',
    priority: 'high'
  },
  {
    id: 2,
    title: '房间清洁检查',
    time: '14:00',
    status: '进行中',
    priority: 'medium'
  },
  {
    id: 3,
    title: '客人投诉处理',
    time: '10:30',
    status: '已完成',
    priority: 'high'
  },
  {
    id: 4,
    title: '设备维护',
    time: '16:00',
    status: '待处理',
    priority: 'low'
  }
])

// 房间状态数据
const roomStatusData = ref([
  { name: '已入住', count: 102, color: '#18a058' },
  { name: '空房净', count: 12, color: '#2080f0' },
  { name: '空房脏', count: 4, color: '#f0a020' },
  { name: '维修中', count: 2, color: '#d03050' }
])

// 实时动态
const recentActivities = ref([
  {
    id: 1,
    text: '客人张三办理了入住手续，房间101',
    time: '2分钟前',
    icon: 'i-material-symbols:person-add-outline'
  },
  {
    id: 2,
    text: '房间205清洁完成，状态更新为空房净',
    time: '5分钟前',
    icon: 'i-material-symbols:cleaning-services-outline'
  },
  {
    id: 3,
    text: '客人李四完成退房结账，房间302',
    time: '8分钟前',
    icon: 'i-material-symbols:person-remove-outline'
  },
  {
    id: 4,
    text: '新预订：王五预订豪华间，入住日期明天',
    time: '12分钟前',
    icon: 'i-material-symbols:event-available-outline'
  }
])

function getTaskStatusType(status) {
  const statusMap = {
    '待处理': 'warning',
    '进行中': 'info',
    '已完成': 'success'
  }
  return statusMap[status] || 'default'
}
</script>

<style scoped>
.today-dashboard {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: var(--text-color-1);
}

.page-description {
  color: var(--text-color-3);
  margin: 0;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.metric-card {
  border-radius: 12px;
  overflow: hidden;
}

.metric-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px;
}

.metric-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.occupancy .metric-icon {
  background: linear-gradient(135deg, #18a058, #36ad6a);
}

.revenue .metric-icon {
  background: linear-gradient(135deg, #2080f0, #4098fc);
}

.arrivals .metric-icon {
  background: linear-gradient(135deg, #f0a020, #fcb040);
}

.departures .metric-icon {
  background: linear-gradient(135deg, #d03050, #e54666);
}

.metric-info {
  flex: 1;
}

.metric-value {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-color-1);
  line-height: 1;
}

.metric-label {
  font-size: 14px;
  color: var(--text-color-2);
  margin: 4px 0;
}

.metric-detail {
  font-size: 12px;
  color: var(--text-color-3);
}

.detail-grid {
  margin-bottom: 24px;
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  border-left: 4px solid transparent;
}

.task-item.high {
  background: rgba(208, 48, 80, 0.1);
  border-left-color: #d03050;
}

.task-item.medium {
  background: rgba(240, 160, 32, 0.1);
  border-left-color: #f0a020;
}

.task-item.low {
  background: rgba(32, 128, 240, 0.1);
  border-left-color: #2080f0;
}

.task-title {
  font-weight: 500;
  color: var(--text-color-1);
}

.task-time {
  font-size: 12px;
  color: var(--text-color-3);
  margin-top: 2px;
}

.room-status-chart {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-bar {
  flex: 1;
  height: 8px;
  background: var(--fill-color);
  border-radius: 4px;
  overflow: hidden;
}

.status-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.status-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-width: 80px;
}

.status-name {
  font-size: 14px;
  color: var(--text-color-2);
}

.status-count {
  font-weight: 600;
  color: var(--text-color-1);
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--primary-color-hover);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-size: 16px;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
}

.activity-text {
  color: var(--text-color-1);
  line-height: 1.4;
}

.activity-time {
  font-size: 12px;
  color: var(--text-color-3);
  margin-top: 4px;
}
</style>