/**********************************
 * @Author: <PERSON>
 * @LastEditor: <PERSON>
 * @LastEditTime: 2023/12/05 21:30:57
 * @Email: <EMAIL>
 * Copyright © 2023 <PERSON>(大脸怪) | https://isme.top
 **********************************/

import { FileSystemIconLoader } from '@iconify/utils/lib/loader/node-loaders'
import presetRemToPx from '@unocss/preset-rem-to-px'
import { defineConfig, presetAttributify, presetIcons, presetWind3 } from 'unocss'
import { getIcons } from './build/index.js'

const icons = getIcons()
export default defineConfig({
  presets: [
    presetWind3(),
    presetAttributify(),
    presetIcons({
      warn: false, // 关闭图标警告，避免控制台噪音
      prefix: ['i-'],
      extraProperties: {
        display: 'inline-block',
        width: '1em',
        height: '1em',
      },
      collections: {
        me: FileSystemIconLoader('./src/assets/icons/isme'),
        fe: FileSystemIconLoader('./src/assets/icons/feather'),
      },
    }),
    presetRemToPx({ baseFontSize: 4 })
  ],
  safelist: icons.map(icon => `${icon} ${icon}?mask`.split(' ')).flat(),
  // 添加内容扫描配置，避免误解析JavaScript代码
  content: {
    pipeline: {
      include: [
        // 只扫描模板和样式相关的内容
        /\.(vue|html|jsx|tsx)($|\?)/,
        // 排除JavaScript逻辑代码
        '!**/*.{js,ts}',
        '!**/node_modules/**'
      ]
    }
  },
  shortcuts: [
    ['wh-full', 'w-full h-full'],
    ['f-c-c', 'flex justify-center items-center'],
    ['flex-col', 'flex flex-col'],
    ['card-border', 'border border-solid border-light_border dark:border-dark_border'],
    ['auto-bg', 'bg-white dark:bg-dark'],
    ['auto-bg-hover', 'hover:bg-[rgba(var(--primary-color-rgb),0.15)] hover:dark:bg-[rgba(var(--primary-color-rgb),0.25)]'],
    ['auto-bg-highlight', 'bg-[rgba(var(--primary-color-rgb),0.12)] dark:bg-[rgba(var(--primary-color-rgb),0.20)]'],
    // 苹果风格暗夜黑主题专用工具类
    ['apple-dark-bg', 'bg-gradient-to-br from-[rgba(var(--primary-color-rgb),0.25)] to-[rgba(var(--primary-color-rgb),0.15)]'],
    ['apple-dark-card', 'bg-gradient-to-br from-[rgba(var(--primary-color-rgb),0.15)] to-[rgba(var(--primary-color-rgb),0.08)] border-[rgba(var(--primary-color-rgb),0.4)] shadow-[0_3px_12px_rgba(var(--primary-color-rgb),0.3),0_0_0_1px_rgba(255,255,255,0.08),inset_0_1px_0_rgba(255,255,255,0.12)]'],
    ['apple-dark-hover', 'hover:bg-gradient-to-br hover:from-[rgba(var(--primary-color-rgb),0.35)] hover:to-[rgba(var(--primary-color-rgb),0.25)] hover:border-[rgba(var(--primary-color-rgb),0.5)] hover:shadow-[0_6px_20px_rgba(var(--primary-color-rgb),0.4),0_0_0_1px_rgba(255,255,255,0.12),inset_0_1px_0_rgba(255,255,255,0.18)] hover:-translate-y-1'],
    ['apple-dark-active', 'bg-gradient-to-br from-[rgba(var(--primary-color-rgb),0.45)] to-[rgba(var(--primary-color-rgb),0.35)] border-[rgba(var(--primary-color-rgb),0.6)] shadow-[0_4px_16px_rgba(var(--primary-color-rgb),0.45),0_0_0_1px_rgba(255,255,255,0.12),inset_0_1px_0_rgba(255,255,255,0.18),inset_0_0_20px_rgba(var(--primary-color-rgb),0.2)] text-white font-semibold'],
    ['apple-dark-text', 'text-white'],
    ['apple-dark-border', 'border-[rgba(var(--primary-color-rgb),0.4)]'],
    ['text-highlight', 'rounded-4 px-8 py-2 auto-bg-highlight'],
  ],
  rules: [
    [
      'card-shadow',
      { 'box-shadow': '0 1px 2px -2px #00000029, 0 3px 6px #0000001f, 0 5px 12px 4px #00000017' },
    ],
  ],
  theme: {
    colors: {
      primary: 'rgba(var(--primary-color))',
      dark: '#18181c',
      light_border: '#efeff5',
      dark_border: '#2d2d30',
    },
  },
})
