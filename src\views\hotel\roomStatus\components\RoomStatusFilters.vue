<template>
  <div class="room-status-filters" :class="`layout-${layoutMode}`">
    <!-- 顶部布局筛选器 -->
    <div v-if="layoutMode === 'top'" class="filters-top">
      <div class="filter-left">
        <n-select
          v-model:value="localFilters.building"
          :placeholder="t('roomStatus.filters.building')"
          :options="buildingOptions"
          style="width: 100px"
          @update:value="handleBuildingChange"
          clearable
          size="small"
        />
        <n-select
          v-model:value="localFilters.floor"
          :placeholder="$t('roomStatus.filters.floor')"
          :options="floorOptions"
          style="width: 100px"
          @update:value="handleFloorChange"
          clearable
          size="small"
        />
        <n-select
          v-model:value="localFilters.roomType"
          :placeholder="$t('roomStatus.filters.roomType')"
          :options="roomTypeOptions"
          style="width: 100px"
          @update:value="handleRoomTypeChange"
          clearable
          size="small"
        />
        <n-select
          v-model:value="localFilters.status"
          :placeholder="$t('roomStatus.filters.status')"
          :options="statusOptions"
          style="width: 100px"
          @update:value="handleStatusChange"
          clearable
          size="small"
        />
        <n-select
          v-model:value="localFilters.cleanStatus"
          :placeholder="$t('roomStatus.filters.cleanStatus')"
          :options="cleanStatusOptions"
          style="width: 100px"
          @update:value="handleCleanStatusChange"
          clearable
          size="small"
        />
      </div>

      <div class="filter-right">
        <n-input
          v-model:value="localFilters.keyword"
          :placeholder="t('roomStatus.searchPlaceholder')"
          clearable
          style="width: 160px"
          size="small"
          @input="handleSearch"
        >
          <template #prefix>
            <i class="i-material-symbols:search"></i>
          </template>
        </n-input>
      </div>
    </div>

    <!-- 左侧布局筛选器 -->
    <div v-else-if="layoutMode === 'left'" class="filters-left">
      <!-- 搜索框 -->
      <div class="filter-group search-group">
        <label>搜索</label>
        <n-input
          v-model:value="searchKeyword"
          placeholder="搜索房间号或客人姓名"
          clearable
          size="small"
          @input="handleSearchInput"
          @clear="handleSearchClear"
        >
          <template #prefix>
            <i class="i-material-symbols:search"></i>
          </template>
        </n-input>
      </div>

      <div class="filter-group">
        <label>楼栋</label>
        <n-select
          v-model:value="localFilters.building"
          :placeholder="t('roomStatus.filters.building')"
          :options="buildingOptions"
          @update:value="handleBuildingChange"
          clearable
          size="small"
        />
      </div>
      <div class="filter-group">
        <label>楼层</label>
        <n-select
          v-model:value="localFilters.floor"
          :placeholder="$t('roomStatus.filters.floor')"
          :options="floorOptions"
          @update:value="handleFloorChange"
          clearable
          size="small"
        />
      </div>
      <div class="filter-group">
        <label>房型</label>
        <n-select
          v-model:value="localFilters.roomType"
          :placeholder="$t('roomStatus.filters.roomType')"
          :options="roomTypeOptions"
          @update:value="handleRoomTypeChange"
          clearable
          size="small"
        />
      </div>
      <div class="filter-group">
        <label>状态</label>
        <n-select
          v-model:value="localFilters.status"
          :placeholder="$t('roomStatus.filters.status')"
          :options="statusOptions"
          @update:value="handleStatusChange"
          clearable
          size="small"
        />
      </div>
      <div class="filter-group">
        <label>清洁</label>
        <n-select
          v-model:value="localFilters.cleanStatus"
          :placeholder="$t('roomStatus.filters.cleanStatus')"
          :options="cleanStatusOptions"
          @update:value="handleCleanStatusChange"
          clearable
          size="small"
        />
      </div>
    </div>

    <!-- 底部布局筛选器 -->
    <div v-else-if="layoutMode === 'bottom'" class="filters-bottom">
      <n-select
        v-model:value="localFilters.building"
        :placeholder="t('roomStatus.filters.building')"
        :options="buildingOptions"
        style="width: 100px"
        @update:value="handleBuildingChange"
        clearable
        size="small"
      />
      <n-select
        v-model:value="localFilters.floor"
        :placeholder="$t('roomStatus.filters.floor')"
        :options="floorOptions"
        style="width: 100px"
        @update:value="handleFloorChange"
        clearable
        size="small"
      />
      <n-select
        v-model:value="localFilters.roomType"
        :placeholder="$t('roomStatus.filters.roomType')"
        :options="roomTypeOptions"
        style="width: 100px"
        @update:value="handleRoomTypeChange"
        clearable
        size="small"
      />
      <n-select
        v-model:value="localFilters.status"
        :placeholder="$t('roomStatus.filters.status')"
        :options="statusOptions"
        style="width: 100px"
        @update:value="handleStatusChange"
        clearable
        size="small"
      />
      <n-select
        v-model:value="localFilters.cleanStatus"
        :placeholder="$t('roomStatus.filters.cleanStatus')"
        :options="cleanStatusOptions"
        style="width: 100px"
        @update:value="handleCleanStatusChange"
        clearable
        size="small"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

// Props
const props = defineProps({
  layoutMode: {
    type: String,
    default: 'top',
    validator: (value) => ['top', 'left', 'bottom'].includes(value)
  },
  filters: {
    type: Object,
    default: () => ({})
  },
  buildingOptions: {
    type: Array,
    default: () => []
  },
  floorOptions: {
    type: Array,
    default: () => []
  },
  roomTypeOptions: {
    type: Array,
    default: () => []
  },
  statusOptions: {
    type: Array,
    default: () => []
  },
  cleanStatusOptions: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits([
  'update:filters',
  'building-change',
  'floor-change',
  'room-type-change',
  'status-change',
  'clean-status-change',
  'search'
])

// 搜索关键词
const searchKeyword = ref('')

// 本地筛选状态
const localFilters = ref({
  building: null,
  floor: null,
  roomType: null,
  status: null,
  cleanStatus: null,
  keyword: ''
})

// 监听外部筛选器变化
watch(() => props.filters, (newFilters) => {
  Object.assign(localFilters.value, newFilters)
}, { immediate: true, deep: true })

// 事件处理函数
const handleBuildingChange = (value) => {
  localFilters.value.building = value
  localFilters.value.floor = null // 重置楼层选择
  emit('building-change', value)
  emitFiltersUpdate()
}

const handleFloorChange = (value) => {
  localFilters.value.floor = value
  emit('floor-change', value)
  emitFiltersUpdate()
}

const handleRoomTypeChange = (value) => {
  localFilters.value.roomType = value
  emit('room-type-change', value)
  emitFiltersUpdate()
}

const handleStatusChange = (value) => {
  localFilters.value.status = value
  emit('status-change', value)
  emitFiltersUpdate()
}

const handleCleanStatusChange = (value) => {
  localFilters.value.cleanStatus = value
  emit('clean-status-change', value)
  emitFiltersUpdate()
}

const handleSearch = () => {
  emit('search', localFilters.value.keyword)
  emitFiltersUpdate()
}

// 搜索输入处理
const handleSearchInput = (value) => {
  searchKeyword.value = value
  localFilters.value.keyword = value
  emit('search', value)
  emitFiltersUpdate()
}

// 清除搜索
const handleSearchClear = () => {
  searchKeyword.value = ''
  localFilters.value.keyword = ''
  emit('search', '')
  emitFiltersUpdate()
}

const emitFiltersUpdate = () => {
  emit('update:filters', { ...localFilters.value })
}
</script>

<style scoped>
.room-status-filters {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
}

.filters-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid rgba(var(--primary-color-rgb), 0.15);
}

.filter-left {
  display: flex;
  gap: 0.6rem;
}

.filter-right {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.filters-left {
  padding: 1rem;
}

.filter-group {
  margin-bottom: 1rem;
}

.filter-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.search-group {
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  padding-bottom: 1rem;
  margin-bottom: 1rem;
}

.search-group :deep(.n-input__prefix) {
  color: #999;
}

.filters-bottom {
  display: flex;
  gap: 0.6rem;
  padding: 0.75rem 1rem;
  border-top: 1px solid rgba(var(--primary-color-rgb), 0.15);
}
</style>
