<template>
  <div class="status-legend" :class="`layout-${layoutMode}`">
    <!-- 顶部布局状态图例 -->
    <div v-if="layoutMode === 'top'" class="legend-top">
      <!-- 清洁状态 -->
      <div class="status-group">
        <span class="group-label">清洁:</span>
        <div
          v-for="statusConfig in cleanStatusConfig"
          :key="'clean-' + statusConfig.id"
          :class="['status-dot', { active: selectedCleanStatus === statusConfig.id }]"
          @click="handleCleanStatusClick(statusConfig.id)"
          :title="`${statusConfig.name || '未知'}: ${statusConfig.count || 0}间`"
          :style="{ backgroundColor: statusConfig.color || '#999' }"
        >
          <span class="status-char">{{ (statusConfig.name || '未知').charAt(0) }}</span>
          <span class="status-count">{{ statusConfig.count || 0 }}</span>
        </div>
      </div>

      <!-- 分隔符 -->
      <div class="separator">|</div>

      <!-- 业务状态 -->
      <div class="status-group">
        <span class="group-label">业务:</span>
        <div
          v-for="statusConfig in roomStatusConfig"
          :key="'business-' + statusConfig.id"
          :class="['status-dot', { active: selectedStatus === statusConfig.sign }]"
          @click="handleBusinessStatusClick(statusConfig.sign)"
          :title="`${statusConfig.name || '未知'}: ${statusConfig.count || 0}间`"
          :style="{ backgroundColor: statusConfig.color || '#999' }"
        >
          <span class="status-char">{{ (statusConfig.name || '未知').charAt(0) }}</span>
          <span class="status-count">{{ statusConfig.count || 0 }}</span>
        </div>
      </div>
    </div>

    <!-- 左侧布局状态图例 -->
    <div v-else-if="layoutMode === 'left'" class="legend-left">
      <h4 class="legend-title">状态图例</h4>

      <!-- 清洁状态图例 -->
      <div class="legend-section">
        <span class="legend-section-title">清洁:</span>
        <div class="legend-dots">
          <div
            v-for="statusConfig in cleanStatusConfig"
            :key="'clean-' + statusConfig.id"
            :class="['legend-dot', { active: selectedCleanStatus === statusConfig.id }]"
            @click="handleCleanStatusClick(statusConfig.id)"
            :title="`${statusConfig.name || '未知'}: ${statusConfig.count || 0}间`"
            :style="{ backgroundColor: statusConfig.color || '#999' }"
          >
            <span class="dot-char">{{ (statusConfig.name || '未知').charAt(0) }}</span>
            <span class="dot-count">{{ statusConfig.count || 0 }}</span>
          </div>
        </div>
      </div>

      <!-- 业务状态图例 -->
      <div class="legend-section">
        <span class="legend-section-title">业务:</span>
        <div class="legend-dots">
          <div
            v-for="statusConfig in roomStatusConfig"
            :key="'business-' + statusConfig.id"
            :class="['legend-dot', { active: selectedStatus === statusConfig.sign }]"
            @click="handleBusinessStatusClick(statusConfig.sign)"
            :title="`${statusConfig.name || '未知'}: ${statusConfig.count || 0}间`"
            :style="{ backgroundColor: statusConfig.color || '#999' }"
          >
            <span class="dot-char">{{ (statusConfig.name || '未知').charAt(0) }}</span>
            <span class="dot-count">{{ statusConfig.count || 0 }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部布局状态图例 -->
    <div v-else-if="layoutMode === 'bottom'" class="legend-bottom">
      <!-- 清洁状态图例 -->
      <div class="bottom-legend-group">
        <span class="bottom-legend-title">清洁:</span>
        <div class="bottom-legend-dots">
          <div
            v-for="statusConfig in cleanStatusConfig"
            :key="'clean-' + statusConfig.id"
            :class="['bottom-legend-dot', { active: selectedCleanStatus === statusConfig.id }]"
            @click="handleCleanStatusClick(statusConfig.id)"
            :title="`${statusConfig.name || '未知'}: ${statusConfig.count || 0}间`"
            :style="{ backgroundColor: statusConfig.color || '#999' }"
          >
            <span class="bottom-dot-char">{{ (statusConfig.name || '未知').charAt(0) }}</span>
            <span class="bottom-dot-count">{{ statusConfig.count || 0 }}</span>
          </div>
        </div>
      </div>

      <!-- 业务状态图例 -->
      <div class="bottom-legend-group">
        <span class="bottom-legend-title">业务:</span>
        <div class="bottom-legend-dots">
          <div
            v-for="statusConfig in roomStatusConfig"
            :key="'business-' + statusConfig.id"
            :class="['bottom-legend-dot', { active: selectedStatus === statusConfig.sign }]"
            @click="handleBusinessStatusClick(statusConfig.sign)"
            :title="`${statusConfig.name || '未知'}: ${statusConfig.count || 0}间`"
            :style="{ backgroundColor: statusConfig.color || '#999' }"
          >
            <span class="bottom-dot-char">{{ (statusConfig.name || '未知').charAt(0) }}</span>
            <span class="bottom-dot-count">{{ statusConfig.count || 0 }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

// Props
const props = defineProps({
  layoutMode: {
    type: String,
    default: 'top',
    validator: (value) => ['top', 'left', 'bottom'].includes(value)
  },
  cleanStatusConfig: {
    type: Array,
    default: () => []
  },
  roomStatusConfig: {
    type: Array,
    default: () => []
  },
  selectedCleanStatus: {
    type: [Number, String, null],
    default: null
  },
  selectedStatus: {
    type: [String, null],
    default: null
  }
})

// Emits
const emit = defineEmits([
  'clean-status-click',
  'business-status-click'
])

// 事件处理函数
const handleCleanStatusClick = (statusId) => {
  emit('clean-status-click', statusId)
}

const handleBusinessStatusClick = (statusSign) => {
  emit('business-status-click', statusSign)
}
</script>

<style scoped>
.status-legend {
  user-select: none;
}

/* 顶部布局样式 */
.legend-top {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem 1rem;
}

.status-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.group-label {
  font-size: 0.8rem;
  font-weight: 600;
  color: #374151;
  margin-right: 0.25rem;
}

.status-dot {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  position: relative;
}

.status-dot:hover {
  transform: scale(1.1);
  border-color: rgba(255, 255, 255, 0.8);
}

.status-dot.active {
  transform: scale(1.15);
  border-color: rgba(255, 255, 255, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.status-char {
  font-size: 0.7rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.status-count {
  position: absolute;
  top: -8px;
  right: -8px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  font-size: 0.6rem;
  font-weight: 600;
  padding: 0.1rem 0.3rem;
  border-radius: 8px;
  min-width: 16px;
  text-align: center;
  line-height: 1;
}

.separator {
  color: #d1d5db;
  font-weight: 300;
  font-size: 1.2rem;
}

/* 左侧布局样式 */
.legend-left {
  padding: 1rem;
}

.legend-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.legend-section {
  margin-bottom: 1rem;
}

.legend-section-title {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.legend-dots {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.legend-dot {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  position: relative;
}

.legend-dot:hover {
  transform: scale(1.1);
  border-color: rgba(255, 255, 255, 0.8);
}

.legend-dot.active {
  transform: scale(1.15);
  border-color: rgba(255, 255, 255, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.dot-char {
  font-size: 0.8rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.dot-count {
  position: absolute;
  top: -6px;
  right: -6px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  font-size: 0.6rem;
  font-weight: 600;
  padding: 0.1rem 0.3rem;
  border-radius: 8px;
  min-width: 16px;
  text-align: center;
  line-height: 1;
}

/* 底部布局样式 */
.legend-bottom {
  display: flex;
  gap: 2rem;
  padding: 0.75rem 1rem;
}

.bottom-legend-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.bottom-legend-title {
  font-size: 0.8rem;
  font-weight: 600;
  color: #374151;
  margin-right: 0.25rem;
}

.bottom-legend-dots {
  display: flex;
  gap: 0.3rem;
}

.bottom-legend-dot {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  position: relative;
}

.bottom-legend-dot:hover {
  transform: scale(1.1);
  border-color: rgba(255, 255, 255, 0.8);
}

.bottom-legend-dot.active {
  transform: scale(1.15);
  border-color: rgba(255, 255, 255, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.bottom-dot-char {
  font-size: 0.7rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.bottom-dot-count {
  position: absolute;
  top: -8px;
  right: -8px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  font-size: 0.6rem;
  font-weight: 600;
  padding: 0.1rem 0.3rem;
  border-radius: 8px;
  min-width: 16px;
  text-align: center;
  line-height: 1;
}
</style>
