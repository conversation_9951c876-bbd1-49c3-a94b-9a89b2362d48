<!--------------------------------
 - @Author: <PERSON>
 - @LastEditor: <PERSON>
 - @LastEditTime: 2023/12/16 18:52:48
 - @Email: <EMAIL>
 - Copyright © 2023 <PERSON>(大脸怪) | https://isme.top
 --------------------------------->

<template>
  <AppCard class="flex items-center px-12 header-gradient" border-b="1px solid light_border dark:dark_border">
    <MenuCollapse />

    <!-- 概览页导航文字按钮 -->
    <n-button
      text
      type="primary"
      @click="navigateToOverview"
      class="overview-text-btn ml-16"
    >
      <template #icon>
        <i class="i-material-symbols:dashboard text-16"></i>
      </template>
      概览页
    </n-button>

    <!-- 远期房态按钮 (仅在房态页显示) -->
    <n-button
      v-if="isRoomStatusPage"
      text
      type="primary"
      @click="navigateToFutureRoomStatus"
      class="future-room-status-btn ml-12"
    >
      <template #icon>
        <i class="i-material-symbols:calendar-view-month text-16"></i>
      </template>
      远期房态
    </n-button>

    <div class="flex flex-shrink-0 items-center px-12 text-18 ml-auto">
      <BusinessDateHeader class="mr-16" />

      <!-- 手动刷新房态按钮 -->
      <!-- <n-button
        size="small"
        quaternary
        circle
        :loading="refreshing"
        @click="handleRefreshRoomStatus"
        title="刷新房态"
        class="mr-8"
      >
        <template #icon>
          <i class="i-material-symbols:refresh text-18"></i>
        </template>
      </n-button> -->

      <ThemeSelector class="mr-8" />

      <LanguageSelector class="mr-8" />

      <Fullscreen />

      <UserAvatar />
    </div>
  </AppCard>


</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useMessage } from 'naive-ui'
import { Fullscreen, LanguageSelector, MenuCollapse, ThemeSelector, UserAvatar } from '@/layouts/components'
import BusinessDateHeader from '@/components/hotel/BusinessDateHeader.vue'


const router = useRouter()
const route = useRoute()
const message = useMessage()

const refreshing = ref(false)

// 判断是否在房态页
const isRoomStatusPage = computed(() => {
  return route.name === 'RoomStatus'
})

// 导航到概览页
function navigateToOverview() {
  try {
    router.push({ name: 'DashboardOverview' })
    message.success('正在前往概览页...')
  } catch (error) {
    message.error('导航失败，请重试')
  }
}

// 导航到远期房态页面
function navigateToFutureRoomStatus() {
  try {
    router.push({ name: 'FutureRoomStatus' })
    message.success('正在前往远期房态页面...')
  } catch (error) {
    message.error('导航失败，请重试')
  }
}

// 手动刷新房态
async function handleRefreshRoomStatus() {
  // 只在房态页面显示刷新功能
  if (route.name !== 'RoomStatus') {
    message.info('请在房态页面使用此功能')
    return
  }

  refreshing.value = true
  try {
    // 触发房态页面的刷新事件
    window.dispatchEvent(new CustomEvent('refresh-room-status'))
    message.success('房态刷新中...')
  } catch (error) {
    message.error('刷新失败，请重试')
  } finally {
    setTimeout(() => {
      refreshing.value = false
    }, 1000)
  }
}

function handleLinkClick(link) {
  window.open(link)
}
</script>

<style scoped>
/* 漂亮的渐变背景 - 跟随主题色 */
.header-gradient {
  background: linear-gradient(135deg,
    rgba(var(--primary-color-rgb), 0.45) 0%,
    rgba(var(--primary-color-rgb), 0.4) 15%,
    rgba(var(--primary-color-rgb), 0.35) 30%,
    rgba(255, 255, 255, 0.8) 50%,
    rgba(var(--primary-color-rgb), 0.35) 70%,
    rgba(var(--primary-color-rgb), 0.4) 85%,
    rgba(var(--primary-color-rgb), 0.45) 100%
  ) !important;
  backdrop-filter: blur(15px);
  border-bottom: 1px solid rgba(var(--primary-color-rgb), 0.6) !important;
  box-shadow: 0 4px 20px rgba(var(--primary-color-rgb), 0.35), 0 2px 10px rgba(var(--primary-color-rgb), 0.25);
  position: relative;
}

.header-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(var(--primary-color-rgb), 0.15) 30%,
    rgba(var(--primary-color-rgb), 0.12) 50%,
    rgba(var(--primary-color-rgb), 0.15) 70%,
    transparent 100%
  );
  pointer-events: none;
}

/* 暗色主题适配 - 跟随主题色 */
.dark .header-gradient {
  background: linear-gradient(135deg,
    rgba(var(--primary-color-rgb), 0.5) 0%,
    rgba(var(--primary-color-rgb), 0.45) 15%,
    rgba(var(--primary-color-rgb), 0.4) 30%,
    rgba(31, 41, 55, 0.85) 50%,
    rgba(var(--primary-color-rgb), 0.4) 70%,
    rgba(var(--primary-color-rgb), 0.45) 85%,
    rgba(var(--primary-color-rgb), 0.5) 100%
  ) !important;
  border-bottom: 1px solid rgba(var(--primary-color-rgb), 0.7) !important;
  box-shadow: 0 4px 20px rgba(var(--primary-color-rgb), 0.4), 0 2px 10px rgba(var(--primary-color-rgb), 0.3);
}

.dark .header-gradient::before {
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(var(--primary-color-rgb), 0.2) 30%,
    rgba(var(--primary-color-rgb), 0.15) 50%,
    rgba(var(--primary-color-rgb), 0.2) 70%,
    transparent 100%
  );
}

/* 刷新按钮样式优化 */
.header-gradient .n-button {
  transition: all 0.3s ease;
}

.header-gradient .n-button:hover {
  background: rgba(var(--primary-color-rgb), 0.1) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(var(--primary-color-rgb), 0.2);
}

.dark .header-gradient .n-button:hover {
  background: rgba(var(--primary-color-rgb), 0.15) !important;
}

/* 概览页文字按钮样式（提升对比度与可辨识度） */
.overview-text-btn {
  font-weight: 700 !important;
  font-size: 14px !important;
  padding: 8px 16px !important;
  border-radius: 10px !important;
  transition: all 0.25s ease !important;
  background: #ffffff !important;
  color: var(--primary-color) !important;
  border: 1px solid rgba(var(--primary-color-rgb), 0.35) !important;
  box-shadow: 0 4px 10px rgba(var(--primary-color-rgb), 0.18);
  backdrop-filter: blur(6px);
  position: relative;
  overflow: hidden;
}

.overview-text-btn::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(var(--primary-color-rgb), 0.08) 50%, transparent 100%);
  left: -100%;
  transition: left 0.5s ease;
}

.overview-text-btn:hover::before {
  left: 100%;
}

.overview-text-btn:hover {
  background: var(--primary-color) !important;
  color: #ffffff !important;
  border-color: var(--primary-color-pressed) !important;
  transform: translateY(-1px);
  box-shadow: 0 6px 14px rgba(var(--primary-color-rgb), 0.28);
}

.overview-text-btn .n-button__content {
  position: relative;
  z-index: 1;
}

/* 暗色主题适配 */
.dark .overview-text-btn {
  background: rgba(255, 255, 255, 0.12) !important;
  color: #ffffff !important;
  border-color: rgba(255, 255, 255, 0.26) !important;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.25);
}

.dark .overview-text-btn:hover {
  background: var(--primary-color) !important;
  color: #ffffff !important;
  border-color: var(--primary-color-pressed) !important;
}
</style>

<style scoped>
/* 小屏幕优化 - 减少头部内边距 */
@media (max-width: 1440px) and (max-height: 900px) {
  :deep(.px-12) {
    padding-left: 8px !important;
    padding-right: 8px !important;
  }

  :deep(.text-18) {
    font-size: 14px !important;
  }

  :deep(.mr-16) {
    margin-right: 8px !important;
  }

  :deep(.mr-8) {
    margin-right: 6px !important;
  }

  :deep(.mx-6) {
    margin-left: 4px !important;
    margin-right: 4px !important;
  }
}

/* 移动端进一步优化 */
@media (max-width: 768px) {
  :deep(.px-12) {
    padding-left: 6px !important;
    padding-right: 6px !important;
  }

  :deep(.text-18) {
    font-size: 12px !important;
  }

  :deep(.mr-16) {
    margin-right: 6px !important;
  }

  :deep(.mr-8) {
    margin-right: 4px !important;
  }
}
</style>
