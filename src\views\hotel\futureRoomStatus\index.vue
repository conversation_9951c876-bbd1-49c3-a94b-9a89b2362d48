<template>
  <div class="future-room-status-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <i class="i-material-symbols:calendar-view-month"></i>
          远期房态
        </h1>
        <p class="page-description">查看和管理未来房间状态安排</p>
      </div>
      <div class="header-right">
        <n-space>
          <n-date-picker
            v-model:value="dateRange"
            type="daterange"
            clearable
            format="yyyy-MM-dd"
            placeholder="选择日期范围"
            :default-value="defaultDateRange"
            :shortcuts="dateShortcuts"
            @update:value="handleDateRangeChange"
          />
          <n-button @click="refreshData" :loading="loading" type="primary">
            <template #icon>
              <i class="i-material-symbols:refresh" />
            </template>
            刷新数据
          </n-button>
          <n-dropdown trigger="hover" :options="exportOptions" @select="handleExportSelect">
            <n-button>
              <template #icon>
                <i class="i-material-symbols:download" />
              </template>
              导出
            </n-button>
          </n-dropdown>
          <n-button @click="handlePrint">
            <template #icon>
              <i class="i-material-symbols:print" />
            </template>
            打印
          </n-button>
        </n-space>
      </div>
    </div>

    <!-- 统计信息卡片 -->
    <n-card class="stats-card" style="margin-bottom: 16px;">
      <template #header>
        <div style="display: flex; align-items: center; gap: 8px;">
          <i class="i-material-symbols:analytics" style="font-size: 20px; color: #1890ff;"></i>
          <span>统计概览</span>
        </div>
      </template>
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-label">总房间</div>
          <div class="stat-value primary">{{ totalStats.totalRooms }}</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">长住</div>
          <div class="stat-value warning">{{ totalStats.longTerm }}</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">维修</div>
          <div class="stat-value error">{{ totalStats.maintenance }}</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">可售</div>
          <div class="stat-value success">{{ totalStats.sellable }}</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">平均入住率</div>
          <div class="stat-value" :class="{
            'success': totalStats.averageOccupancy >= 80,
            'warning': totalStats.averageOccupancy >= 60 && totalStats.averageOccupancy < 80,
            'error': totalStats.averageOccupancy < 60
          }">{{ totalStats.averageOccupancy }}%</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">查询范围</div>
          <div class="stat-value info">{{ dateList.length }}天</div>
        </div>
      </div>
    </n-card>

    <!-- Main Data Table -->
    <n-card class="data-table-card" style="min-height: 500px;">
      <template #header>
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <div style="display: flex; align-items: center; gap: 8px;">
            <i class="i-material-symbols:table-view" style="font-size: 20px; color: #1890ff;"></i>
            <span>远期房态明细表</span>
          </div>
          <div style="font-size: 12px; color: #666;">
            查询开始:{{ formatDateFull(dateList[0] || '') }} 查询结束:{{ formatDateFull(dateList[dateList.length - 1] || '') }}
          </div>
        </div>
      </template>

      <div class="table-container">
        <n-data-table
          :columns="tableColumns"
          :data="roomTypeSummary"
          :loading="loading"
          :bordered="true"
          :single-line="false"
          size="small"
          :max-height="450"
          :scroll-x="Math.max(1200, (dateList.length * 180) + 600)"
          :row-key="(row) => row.id"
          virtual-scroll
          striped
        />
      </div>
    </n-card>

    <!-- 底部汇总信息 -->
    <n-card class="summary-card" style="margin-top: 16px;">
      <template #header>
        <div style="display: flex; align-items: center; gap: 8px;">
          <i class="i-material-symbols:summarize" style="font-size: 20px; color: #52c41a;"></i>
          <span>当前日期</span>
        </div>
      </template>
      <div class="summary-content">
        <div class="summary-info">
          <span class="summary-label">中软节假日开始:</span>
          <span class="summary-value">2025/09/30</span>
          <span class="summary-label">查询:</span>
          <span class="summary-value">2025/10/08</span>
        </div>
        <div class="summary-stats">
          <div class="summary-item">
            <span class="summary-title">总计:135间房</span>
          </div>
        </div>
        <div class="occupancy-rates">
          <div class="rate-item" v-for="date in dateList.slice(0, 6)" :key="date">
            <div class="rate-date">{{ formatDate(date) }}</div>
            <div class="rate-value">{{ getDateOccupancyRate(date) }}%</div>
          </div>
        </div>
      </div>
    </n-card>


  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, h } from 'vue'
import { useMessage } from 'naive-ui'
import { getFutureRoomStatusData, getRoomList, getBuildingList, getFloorList } from './api'

const message = useMessage()

// 响应式数据
const loading = ref(false)
const dateRange = ref(null)
const roomList = ref([])
const roomStatusData = ref({})
const buildingList = ref([])
const floorList = ref([])

// 计算属性
const defaultDateRange = computed(() => {
  const today = new Date()
  const endDate = new Date(today)
  endDate.setDate(today.getDate() + 30) // 默认显示未来30天
  return [today.getTime(), endDate.getTime()]
})

// 统计信息计算
const totalStats = computed(() => {
  const stats = {
    totalRooms: 0,
    longTerm: 0,
    maintenance: 0,
    sellable: 0,
    occupancyRateTotal: 0,
    averageOccupancy: 0
  }

  roomTypeSummary.value.forEach(roomType => {
    stats.totalRooms += roomType.totalRooms
    stats.longTerm += roomType.occupiedLongStay || 0
    stats.maintenance += roomType.occupiedMaintenance || 0
  })

  stats.sellable = stats.totalRooms - stats.longTerm - stats.maintenance

  // 计算平均入住率
  if (dateList.value.length > 0) {
    let totalOccupancy = 0
    let dayCount = 0

    roomTypeSummary.value.forEach(roomType => {
      dateList.value.forEach(date => {
        const stat = roomType.dailyStats[date]
        if (stat && !stat.closed) {
          totalOccupancy += stat.occupancyRate || 0
          dayCount++
        }
      })
    })

    stats.averageOccupancy = dayCount > 0 ? Math.round(totalOccupancy / dayCount) : 0
  }

  return stats
})

const dateShortcuts = {
  '未来7天': () => {
    const today = new Date()
    const endDate = new Date(today)
    endDate.setDate(today.getDate() + 7)
    return [today.getTime(), endDate.getTime()]
  },
  '未来15天': () => {
    const today = new Date()
    const endDate = new Date(today)
    endDate.setDate(today.getDate() + 15)
    return [today.getTime(), endDate.getTime()]
  },
  '未来30天': () => {
    const today = new Date()
    const endDate = new Date(today)
    endDate.setDate(today.getDate() + 30)
    return [today.getTime(), endDate.getTime()]
  },
  '未来60天': () => {
    const today = new Date()
    const endDate = new Date(today)
    endDate.setDate(today.getDate() + 60)
    return [today.getTime(), endDate.getTime()]
  },
  '未来90天': () => {
    const today = new Date()
    const endDate = new Date(today)
    endDate.setDate(today.getDate() + 90)
    return [today.getTime(), endDate.getTime()]
  }
}
const dateList = computed(() => {
  let startDate, endDate

  if (dateRange.value && Array.isArray(dateRange.value) && dateRange.value.length === 2) {
    startDate = new Date(dateRange.value[0])
    endDate = new Date(dateRange.value[1])

    // 验证日期是否有效
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      console.warn('日期范围无效，使用默认范围')
      startDate = new Date()
      endDate = new Date()
      endDate.setDate(startDate.getDate() + 30)
    }

    // 限制最大查询范围为180天
    const maxDays = 180
    const diffTime = Math.abs(endDate - startDate)
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays > maxDays) {
      message.warning(`日期范围过大，已限制为${maxDays}天`)
      endDate = new Date(startDate)
      endDate.setDate(startDate.getDate() + maxDays)
    }
  } else {
    // 使用默认日期范围（未来30天）
    startDate = new Date()
    endDate = new Date()
    endDate.setDate(startDate.getDate() + 30)
  }

  const dates = []
  for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
    dates.push(d.toISOString().split('T')[0])
  }

  return dates
})

// 导出选项
const exportOptions = computed(() => [
  {
    label: '导出为Excel',
    key: 'excel',
    icon: () => h('i', { class: 'i-material-symbols:table-view' })
  },
  {
    label: '导出为CSV',
    key: 'csv',
    icon: () => h('i', { class: 'i-material-symbols:description' })
  },
  {
    label: '导出为PDF',
    key: 'pdf',
    icon: () => h('i', { class: 'i-material-symbols:picture-as-pdf' })
  }
])




// --- START: New computed properties for room type summary ---

const roomTypeSummary = computed(() => {
  if (!roomList.value.length || !dateList.value.length) return [];

  const summary = {};

  // 1. Group rooms by roomTypeName and initialize stats
  roomList.value.forEach(room => {
    if (!summary[room.roomTypeName]) {
      summary[room.roomTypeName] = {
        id: room.roomTypeId || room.id,
        roomTypeName: room.roomTypeName,
        roomTypeId: room.roomTypeId,
        totalRooms: 0,
        rooms: [],
        dailyStats: {},
        // 渠道统计
        longTerm: 0,      // 长住
        allotment: 0,     // 协议
        maintenance: 0,   // 维修
        closed: 0,        // 关房
        // 占用统计
        occupiedMaintenance: 0, // 占用(维修)
        occupiedLongStay: 0,    // 占用(长住)
      };
      dateList.value.forEach(date => {
        summary[room.roomTypeName].dailyStats[date] = {
          reserved: 0,        // 预订
          occupied: 0,        // 在住
          maintenance: 0,     // 维修
          longStay: 0,        // 长住
          soldOut: false,     // 售完
          closed: false,      // 关房
          total: 0,           // 总数
          sellable: 0,        // 可售
          available: 0,       // 剩余
          occupancyRate: 0,   // 入住率
          // 渠道分布
          ota: 0,            // OTA渠道
          direct: 0,         // 直订
          corporate: 0,      // 协议
          walkIn: 0,         // 散客
        };
      });
    }
    summary[room.roomTypeName].totalRooms++;
    summary[room.roomTypeName].rooms.push(room);
  });

  // 2. Calculate stats for each day
  for (const roomTypeName in summary) {
    const roomType = summary[roomTypeName];

    // 计算固定占用数量
    roomType.occupiedMaintenance = Math.floor(roomType.totalRooms * 0.1); // 假设10%维修
    roomType.occupiedLongStay = Math.floor(roomType.totalRooms * 0.05);   // 假设5%长住

    dateList.value.forEach(date => {
      const dailyStat = roomType.dailyStats[date];
      let reservedCount = 0;
      let occupiedCount = 0;
      let maintenanceCount = 0;
      let longStayCount = 0;
      let closedCount = 0;

      roomType.rooms.forEach(room => {
        const statusData = roomStatusData.value[room.id]?.[date];
        const status = statusData?.status || 'available';

        switch (status) {
          case 'reserved':
            reservedCount++;
            // 模拟渠道分布
            if (Math.random() < 0.4) dailyStat.ota++;
            else if (Math.random() < 0.3) dailyStat.direct++;
            else if (Math.random() < 0.2) dailyStat.corporate++;
            else dailyStat.walkIn++;
            break;
          case 'occupied':
            occupiedCount++;
            // 模拟渠道分布
            if (Math.random() < 0.4) dailyStat.ota++;
            else if (Math.random() < 0.3) dailyStat.direct++;
            else if (Math.random() < 0.2) dailyStat.corporate++;
            else dailyStat.walkIn++;
            break;
          case 'maintenance':
            maintenanceCount++;
            break;
          case 'long-stay':
            longStayCount++;
            break;
          case 'out-of-order':
            closedCount++;
            break;
        }
      });

      // 设置基本统计
      dailyStat.reserved = reservedCount;
      dailyStat.occupied = occupiedCount;
      dailyStat.maintenance = maintenanceCount;
      dailyStat.longStay = longStayCount;
      dailyStat.closed = closedCount > 0;
      dailyStat.total = roomType.totalRooms;

      // 计算可售和剩余
      const nonSellable = maintenanceCount + closedCount;
      const sellable = roomType.totalRooms - nonSellable;
      const available = sellable - reservedCount - occupiedCount - longStayCount;

      dailyStat.sellable = sellable;
      dailyStat.available = Math.max(0, available);

      // 计算入住率
      if (sellable > 0) {
        dailyStat.occupancyRate = Math.round(((reservedCount + occupiedCount) / sellable) * 100);
      }

      // 判断是否售完
      if (sellable > 0 && available <= 0) {
        dailyStat.soldOut = true;
      }
    });
  }

  return Object.values(summary);
});

// --- END: New computed properties for room type summary ---


const tableColumns = computed(() => {
  const fixedColumns = [
    {
      title: '序号',
      key: 'id',
      fixed: 'left',
      width: 60,
      align: 'center',
      render(row, index) {
        return h('span', {}, index + 1);
      }
    },
    {
      title: '房间类型',
      key: 'roomTypeName',
      fixed: 'left',
      width: 120,
      className: 'room-type-name-cell',
    },
    {
      title: '房间总数',
      key: 'totalRooms',
      fixed: 'left',
      width: 80,
      align: 'center',
    },
    {
      title: '长住',
      key: 'longTerm',
      width: 60,
      align: 'center',
      render(row) {
        return h('span', {}, row.occupiedLongStay || 0);
      }
    },
    {
      title: '协议',
      key: 'allotment',
      width: 60,
      align: 'center',
      render(row) {
        return h('span', {}, row.allotment || 0);
      }
    },
    {
      title: '当前(维修)',
      key: 'maintenance',
      width: 80,
      align: 'center',
      render(row) {
        return h('span', {}, row.occupiedMaintenance || 0);
      }
    },
    {
      title: '当前(关房)',
      key: 'closed',
      width: 80,
      align: 'center',
      render(row) {
        return h('span', {}, row.closed || 0);
      }
    },
    {
      title: '可售',
      key: 'sellable',
      width: 60,
      align: 'center',
      render(row) {
        const sellable = row.totalRooms - (row.occupiedMaintenance || 0) - (row.closed || 0);
        return h('span', { style: { fontWeight: 'bold', color: '#1890ff' } }, sellable);
      }
    }
  ];

  const dateColumns = dateList.value.map(date => ({
    title: () => {
        return h('div', {
          class: { 'date-header': true, 'weekend': isWeekend(date), 'today': isToday(date) }
        }, [
          h('div', { class: 'date-header-date' }, formatDate(date)),
          h('div', { class: 'date-header-weekday' }, `周${getWeekday(date)}`)
        ]);
    },
    key: date,
    width: 180,
    align: 'center',
    render(row) {
      const stat = row.dailyStats[date];
      if (!stat) return null;

      if (stat.closed) {
        return h('div', {
          class: 'cell-content status-closed',
          style: { backgroundColor: '#8c8c8c', color: 'white', padding: '8px', borderRadius: '4px' }
        }, '关房');
      }

      if (stat.soldOut) {
        return h('div', {
          class: 'cell-content status-sold-out',
          style: { backgroundColor: '#ff4d4f', color: 'white', padding: '8px', borderRadius: '4px' }
        }, `售完`);
      }

      // 构建详细内容
      const lines = [];

      // 第一行：预订信息
      if (stat.reserved > 0) {
        lines.push(h('div', {
          style: { color: '#1890ff', fontSize: '12px', marginBottom: '2px' }
        }, `总:${stat.reserved}单预订:${Math.round(stat.reserved * 1.2)}房`));
      }

      // 第二行：在住信息
      if (stat.occupied > 0) {
        lines.push(h('div', {
          style: { color: '#52c41a', fontSize: '12px', marginBottom: '2px' }
        }, `总:${stat.occupied}单在住`));
      }

      // 第三行：入住率和剩余
      const occupancyColor = stat.occupancyRate >= 80 ? '#ff4d4f' :
                           stat.occupancyRate >= 60 ? '#faad14' : '#52c41a';

      lines.push(h('div', {
        style: { fontSize: '12px', fontWeight: 'bold', marginBottom: '2px' }
      }, [
        h('span', { style: { color: occupancyColor } }, `入住率:${stat.occupancyRate}%`),
        h('span', { style: { marginLeft: '8px', color: '#666' } }, `剩:${stat.available}`)
      ]));

      // 第四行：渠道分布（如果有数据）
      if (stat.ota + stat.direct + stat.corporate + stat.walkIn > 0) {
        lines.push(h('div', {
          style: { fontSize: '10px', color: '#999', marginTop: '4px' }
        }, `OTA:${stat.ota} 直订:${stat.direct} 协议:${stat.corporate}`));
      }

      const backgroundColor = stat.occupancyRate >= 90 ? '#fff2f0' :
                            stat.occupancyRate >= 80 ? '#fffbe6' :
                            stat.occupancyRate >= 60 ? '#f6ffed' : '#f0f5ff';

      const borderColor = stat.occupancyRate >= 90 ? '#ffa39e' :
                        stat.occupancyRate >= 80 ? '#ffe58f' :
                        stat.occupancyRate >= 60 ? '#b7eb8f' : '#91d5ff';

      return h('div', {
        class: 'cell-content',
        style: {
          backgroundColor,
          border: `1px solid ${borderColor}`,
          padding: '6px',
          borderRadius: '4px',
          minHeight: '80px',
          lineHeight: '1.2'
        }
      }, lines);
    }
  }));

  return [...fixedColumns, ...dateColumns];
});

// 方法
const initData = async () => {
  loading.value = true
  try {
    await Promise.all([
      fetchBuildingList(),
      fetchRoomList(),
      fetchRoomStatusData()
    ])
  } finally {
    loading.value = false
  }
}

const fetchBuildingList = async () => {
  try {
    const data = await getBuildingList()
    buildingList.value = data
  } catch (error) {
    console.error('获取楼栋列表失败:', error)
    message.error('获取楼栋列表失败')
  }
}

const fetchRoomList = async () => {
  try {
    const data = await getRoomList()
    roomList.value = data
  } catch (error) {
    console.error('获取房间列表失败:', error)
    message.error('获取房间列表失败')
  }
}

const fetchRoomStatusData = async () => {
  try {
    const startDate = dateList.value[0]
    const endDate = dateList.value[dateList.value.length - 1]

    const data = await getFutureRoomStatusData({
      startDate,
      endDate,
      roomIds: roomList.value.map(room => room.id),
      buildingId: selectedBuilding.value,
      floorId: selectedFloor.value
    })

    roomStatusData.value = data
  } catch (error) {
    console.error('获取房态数据失败:', error)
    message.error('获取房态数据失败')
  }
}

const handleDateRangeChange = (value) => {
  if (!value || !Array.isArray(value) || value.length !== 2) {
    return
  }

  const startDate = new Date(value[0])
  const endDate = new Date(value[1])

  // 验证日期有效性
  if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
    message.error('选择的日期无效')
    return
  }

  // 验证日期范围
  if (startDate >= endDate) {
    message.error('开始日期必须早于结束日期')
    return
  }

  // 验证日期不能早于今天
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  if (startDate < today) {
    message.warning('开始日期不能早于今天，已自动调整')
    dateRange.value = [today.getTime(), value[1]]
    return
  }

  // 验证日期范围不超过180天
  const maxDays = 180
  const diffTime = Math.abs(endDate - startDate)
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays > maxDays) {
    message.warning(`日期范围不能超过${maxDays}天`)
    const newEndDate = new Date(startDate)
    newEndDate.setDate(startDate.getDate() + maxDays)
    dateRange.value = [value[0], newEndDate.getTime()]
    return
  }

  // 延迟加载数据，避免频繁请求
  setTimeout(() => {
    fetchRoomStatusData()
  }, 300)
}



const handleExportSelect = (key) => {
  switch (key) {
    case 'excel':
      exportToExcel()
      break
    case 'csv':
      exportToCSV()
      break
    case 'pdf':
      exportToPDF()
      break
  }
}

const exportToCSV = () => {
  try {
    loading.value = true

    const headers = ['房间号', '房型', '楼栋', '楼层', ...dateList.value]
    const csvContent = []

    // 添加表头
    csvContent.push(headers.join(','))

    // 添加数据行
    filteredRoomList.value.forEach(room => {
      const row = [
        room.roomNumber,
        room.roomTypeName,
        room.buildingName,
        room.floorName
      ]

      // 添加每个日期的状态
      dateList.value.forEach(date => {
        const status = roomStatusData.value[room.id]?.[date]?.status || 'available'
        const statusText = {
          available: '空房',
          occupied: '有客',
          reserved: '预订',
          maintenance: '维修',
          cleaning: '清洁',
          'out-of-order': '停用'
        }[status] || status
        row.push(statusText)
      })

      csvContent.push(row.join(','))
    })

    // 创建并下载文件
    const blob = new Blob([csvContent.join('\\n')], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `远期房态_${formatDateForFilename(new Date())}.csv`
    link.click()
    URL.revokeObjectURL(url)

    message.success('CSV导出成功')
  } catch (error) {
    message.error('CSV导出失败')
  } finally {
    loading.value = false
  }
}

const exportToExcel = () => {
  message.info('Excel导出功能需要额外的库支持，当前使用CSV格式导出')
  exportToCSV()
}

const exportToPDF = () => {
  message.info('PDF导出功能需要额外的库支持，当前使用打印功能')
  handlePrint()
}

const handlePrint = () => {
  const printWindow = window.open('', '_blank')
  const printContent = generatePrintContent()

  printWindow.document.write(printContent)
  printWindow.document.close()
  printWindow.print()
}

const generatePrintContent = () => {
  const title = '远期房态报表'
  const dateRangeText = dateList.value.length > 0 ?
    `${formatDateFull(dateList.value[0])} - ${formatDateFull(dateList.value[dateList.value.length - 1])}` :
    '未选择日期'

  let content = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>${title}</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { margin: 0; color: #333; }
        .header p { margin: 10px 0; color: #666; }
        .stats { display: flex; justify-content: space-around; margin-bottom: 30px; }
        .stat-item { text-align: center; }
        .stat-number { font-size: 24px; font-weight: bold; color: #333; }
        .stat-label { font-size: 14px; color: #666; margin-top: 5px; }
        .gantt-table { width: 100%; border-collapse: collapse; font-size: 12px; }
        .gantt-table th, .gantt-table td { border: 1px solid #ddd; padding: 8px; text-align: center; }
        .gantt-table th { background: #f5f5f5; font-weight: 600; }
        .room-cell { background: #fafafa; font-weight: 500; }
        .status-available { background: #f6ffed; color: #52c41a; }
        .status-occupied { background: #fff2f0; color: #ff4d4f; }
        .status-reserved { background: #e6f7ff; color: #1890ff; }
        .status-maintenance { background: #fffbe6; color: #faad14; }
        .status-cleaning { background: #f9f0ff; color: #722ed1; }
        .status-out-of-order { background: #f5f5f5; color: #8c8c8c; }
        .legend { margin-top: 20px; display: flex; flex-wrap: wrap; gap: 15px; }
        .legend-item { display: flex; align-items: center; gap: 5px; }
        .legend-color { width: 16px; height: 16px; border-radius: 3px; }
        .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #999; }
        @media print {
          body { margin: 0; }
          .gantt-table { font-size: 10px; }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>${title}</h1>
        <p>日期范围: ${dateRangeText}</p>
        <p>生成时间: ${formatDateFull(new Date().toISOString().split('T')[0])} ${new Date().toTimeString().split(' ')[0]}</p>
      </div>

      <div class="stats">
        <div class="stat-item">
          <div class="stat-number">${roomStats.value.available}</div>
          <div class="stat-label">空房</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">${roomStats.value.occupied}</div>
          <div class="stat-label">有客</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">${roomStats.value.reserved}</div>
          <div class="stat-label">预订</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">${roomStats.value.maintenance}</div>
          <div class="stat-label">维修</div>
        </div>
      </div>

      <table class="gantt-table">
        <thead>
          <tr>
            <th>房间号</th>
            <th>房型</th>
            <th>位置</th>
            ${dateList.value.map(date => `<th>${formatDate(date)}<br/>${getWeekday(date)}</th>`).join('')}
          </tr>
        </thead>
        <tbody>
          ${filteredRoomList.value.map(room => `
            <tr>
              <td class="room-cell">${room.roomNumber}</td>
              <td class="room-cell">${room.roomTypeName}</td>
              <td class="room-cell">${room.buildingName}-${room.floorName}</td>
              ${dateList.value.map(date => {
                const status = roomStatusData.value[room.id]?.[date]?.status || 'available'
                const statusText = getStatusText(room, date)
                return `<td class="status-${status}">${statusText}</td>`
              }).join('')}
            </tr>
          `).join('')}
        </tbody>
      </table>

      <div class="legend">
        <div class="legend-item">
          <div class="legend-color" style="background: #52c41a;"></div>
          <span>空房</span>
        </div>
        <div class="legend-item">
          <div class="legend-color" style="background: #ff4d4f;"></div>
          <span>有客</span>
        </div>
        <div class="legend-item">
          <div class="legend-color" style="background: #1890ff;"></div>
          <span>预订</span>
        </div>
        <div class="legend-item">
          <div class="legend-color" style="background: #faad14;"></div>
          <span>维修</span>
        </div>
        <div class="legend-item">
          <div class="legend-color" style="background: #722ed1;"></div>
          <span>清洁</span>
        </div>
        <div class="legend-item">
          <div class="legend-color" style="background: #8c8c8c;"></div>
          <span>停用</span>
        </div>
      </div>

      <div class="footer">
        <p>此报表由PMS系统自动生成</p>
      </div>
    </body>
    </html>
  `

  return content
}

const formatDateForFilename = (date) => {
  return date.toISOString().split('T')[0].replace(/-/g, '')
}

const formatDateFull = (dateStr) => {
  const date = new Date(dateStr)
  return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`
}

// 添加缺失的工具函数
const formatDate = (dateStr) => {
  const date = new Date(dateStr)
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  return `${month}-${day}`
}

const isToday = (dateStr) => {
  const today = new Date().toISOString().split('T')[0]
  return dateStr === today
}

const isWeekend = (dateStr) => {
  const date = new Date(dateStr)
  const day = date.getDay()
  return day === 0 || day === 6
}

const getWeekday = (dateStr) => {
  const date = new Date(dateStr)
  const weekdays = ['日', '一', '二', '三', '四', '五', '六']
  return weekdays[date.getDay()]
}

const getStatusText = (room, date) => {
  const statusData = roomStatusData.value[room.id]?.[date]
  if (!statusData) return '空'

  const textMap = {
    available: '空',
    occupied: statusData.guestName ? statusData.guestName.charAt(0) : '客',
    reserved: '订',
    maintenance: '修',
    cleaning: '洁',
    'out-of-order': '停'
  }

  return textMap[statusData.status] || '空'
}

// 获取指定日期的整体入住率
const getDateOccupancyRate = (date) => {
  let totalRooms = 0
  let occupiedRooms = 0

  roomTypeSummary.value.forEach(roomType => {
    const stat = roomType.dailyStats[date]
    if (stat && !stat.closed) {
      totalRooms += stat.sellable || 0
      occupiedRooms += (stat.reserved || 0) + (stat.occupied || 0)
    }
  })

  return totalRooms > 0 ? Math.round((occupiedRooms / totalRooms) * 100) : 0
}

const refreshData = async () => {
  await initData()
  message.success('数据刷新成功')
}

// 添加缺失的计算属性
const selectedBuilding = ref(null)
const selectedFloor = ref(null)

const filteredRoomList = computed(() => {
  return roomList.value
})

const roomStats = computed(() => {
  const stats = {
    available: 0,
    occupied: 0,
    reserved: 0,
    maintenance: 0
  }

  roomList.value.forEach(room => {
    const today = new Date().toISOString().split('T')[0]
    const status = roomStatusData.value[room.id]?.[today]?.status || 'available'
    if (stats[status] !== undefined) {
      stats[status]++
    }
  })

  return stats
})

// 生命周期
onMounted(() => {
  initData()
})
</script>

<style scoped>
.future-room-status-page {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.page-title i {
  font-size: 32px;
  color: #1890ff;
}

.page-description {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.header-right {
  flex-shrink: 0;
}

.stats-cards {
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-card.available .stat-icon {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.stat-card.occupied .stat-icon {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
}

.stat-card.reserved .stat-icon {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.stat-card.maintenance .stat-icon {
  background: linear-gradient(135deg, #faad14, #ffc53d);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

.gantt-card {
  margin-bottom: 24px;
  border-radius: 12px;
  overflow: hidden;
}

.gantt-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.gantt-container {
  height: 60vh;
  overflow: hidden;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.gantt-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.gantt-header {
  display: flex;
  background: #fafafa;
  border-bottom: 2px solid #d9d9d9;
  position: sticky;
  top: 0;
  z-index: 10;
}

.room-header {
  width: 140px;
  padding: 12px 8px;
  font-weight: 600;
  text-align: center;
  border-right: 1px solid #d9d9d9;
  background: #fafafa;
}

.timeline-header {
  display: flex;
  flex: 1;
  overflow-x: auto;
}

.date-column {
  min-width: 80px;
  padding: 8px 4px;
  text-align: center;
  border-right: 1px solid #e8e8e8;
  background: #fafafa;
}

.date-column.today {
  background: #e6f7ff;
  color: #1890ff;
  font-weight: 600;
}

.date-column.weekend {
  background: #fff2e8;
  color: #fa8c16;
}

.date-text {
  font-size: 12px;
  font-weight: 500;
}

.weekday-text {
  font-size: 10px;
  color: #666;
  margin-top: 2px;
}

.gantt-body {
  flex: 1;
  overflow: auto;
  position: relative;
}

.virtual-list {
  position: relative;
}

.virtual-item {
  position: absolute;
  width: 100%;
  left: 0;
}

.room-row {
  display: flex;
  border-bottom: 1px solid #f0f0f0;
  min-height: 50px;
}

.room-row:hover {
  background: #fafafa;
}

.room-info {
  width: 140px;
  padding: 8px;
  border-right: 1px solid #d9d9d9;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: #fff;
  position: sticky;
  left: 0;
  z-index: 5;
}

.room-number {
  font-weight: 600;
  font-size: 14px;
  color: #333;
}

.room-type {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.room-building {
  font-size: 11px;
  color: #999;
  margin-top: 2px;
}

.room-timeline {
  display: flex;
  flex: 1;
}

.date-cell {
  min-width: 80px;
  border-right: 1px solid #e8e8e8;
  position: relative;
  cursor: pointer;
}

.date-cell:hover {
  background: #f5f5f5;
}

.status-bar {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.status-text {
  color: white;
  font-size: 12px;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.legend-card {
  border-radius: 12px;
}

.legend-content {
  padding: 8px 0;
}

.legend-items {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
}

.legend-color.available { background: #52c41a; }
.legend-color.occupied { background: #ff4d4f; }
.legend-color.reserved { background: #1890ff; }
.legend-color.maintenance { background: #faad14; }
.legend-color.cleaning { background: #722ed1; }
.legend-color.out-of-order { background: #8c8c8c; }


/* --- New Styles for Data Table --- */
.data-table-card {
  border-radius: 12px;
  overflow: hidden;
}

.table-container {
  overflow: auto;
  max-width: 100%;
}

:deep(.n-data-table) {
  overflow-x: auto !important;
}

:deep(.n-data-table-base-table) {
  min-width: 100%;
}

:deep(.n-data-table-th) {
  background-color: #f0f5ff !important;
  font-weight: 600;
  font-size: 12px;
  white-space: nowrap;
  position: sticky;
  top: 0;
  z-index: 10;
}

:deep(.n-data-table-td) {
  border-right: 1px solid #f0f0f0;
  vertical-align: top;
}

:deep(.room-type-name-cell) {
  font-weight: 500;
  background-color: #fafafa !important;
  position: sticky;
  left: 0;
  z-index: 5;
}

/* 固定列样式 */
:deep(.n-data-table-th[data-col-key="id"]),
:deep(.n-data-table-td[data-col-key="id"]),
:deep(.n-data-table-th[data-col-key="roomTypeName"]),
:deep(.n-data-table-td[data-col-key="roomTypeName"]),
:deep(.n-data-table-th[data-col-key="totalRooms"]),
:deep(.n-data-table-td[data-col-key="totalRooms"]) {
  position: sticky;
  background-color: #fafafa !important;
  z-index: 3;
}

:deep(.n-data-table-th[data-col-key="id"]),
:deep(.n-data-table-td[data-col-key="id"]) {
  left: 0;
}

:deep(.n-data-table-th[data-col-key="roomTypeName"]),
:deep(.n-data-table-td[data-col-key="roomTypeName"]) {
  left: 60px;
}

:deep(.n-data-table-th[data-col-key="totalRooms"]),
:deep(.n-data-table-td[data-col-key="totalRooms"]) {
  left: 180px;
}

.date-header {
  line-height: 1.2;
  text-align: center;
}

.date-header-date {
  font-size: 13px;
  font-weight: 600;
}

.date-header-weekday {
  font-size: 11px;
  font-weight: 400;
  margin-top: 2px;
}

.date-header.weekend {
  color: #fa541c;
}

.date-header.today {
  color: #1890ff;
  font-weight: 700;
}

.cell-content {
  white-space: pre-wrap;
  line-height: 1.3;
  font-size: 12px;
  padding: 6px;
  border-radius: 4px;
  color: #333;
  min-height: 60px;
  text-align: left;
}

.cell-content.status-closed {
  background-color: #bfbfbf;
  color: white;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cell-content.status-sold-out {
  background-color: #ff4d4f;
  color: white;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 统计卡片样式 */
.stats-card {
  border-radius: 12px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
  padding: 16px 0;
}

.stat-item {
  text-align: center;
  padding: 12px;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  line-height: 1;
}

.stat-value.primary {
  color: #1890ff;
}

.stat-value.success {
  color: #52c41a;
}

.stat-value.warning {
  color: #faad14;
}

.stat-value.error {
  color: #ff4d4f;
}

.stat-value.info {
  color: #722ed1;
}

/* 汇总卡片样式 */
.summary-card {
  border-radius: 12px;
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f5ff 100%);
}

.summary-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.summary-info {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.summary-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.summary-value {
  font-size: 14px;
  color: #1890ff;
  font-weight: 600;
}

.summary-stats {
  display: flex;
  align-items: center;
}

.summary-item {
  padding: 8px 16px;
  background: rgba(24, 144, 255, 0.1);
  border-radius: 20px;
  border: 1px solid #91d5ff;
}

.summary-title {
  font-size: 14px;
  font-weight: 600;
  color: #1890ff;
}

.occupancy-rates {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.rate-item {
  text-align: center;
  padding: 8px 12px;
  background: white;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  min-width: 80px;
}

.rate-date {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.rate-value {
  font-size: 14px;
  font-weight: 600;
  color: #1890ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .summary-content {
    flex-direction: column;
    align-items: flex-start;
  }

  .occupancy-rates {
    width: 100%;
    justify-content: center;
  }

  .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  :deep(.n-data-table-th),
  :deep(.n-data-table-td) {
    min-width: 120px;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .rate-item {
    min-width: 60px;
  }

  .data-table-card {
    margin: 0 -12px;
  }

  .table-container {
    padding: 0;
  }

  :deep(.n-data-table-th),
  :deep(.n-data-table-td) {
    min-width: 100px;
    font-size: 11px;
    padding: 4px;
  }
}

/* 滚动条样式 */
.table-container::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

:deep(.n-data-table)::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

:deep(.n-data-table)::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

:deep(.n-data-table)::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

:deep(.n-data-table)::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 暗色主题适配 */
.dark .future-room-status-page {
  background: #1a1a1a;
}

.dark .page-header {
  background: #2a2a2a;
  color: #fff;
}

.dark .page-title {
  color: #fff;
}

.dark .page-description {
  color: #ccc;
}

.dark .stat-number {
  color: #fff;
}

.dark .stat-label {
  color: #ccc;
}

.dark .gantt-container {
  border-color: #404040;
}

.dark .gantt-header {
  background: #2a2a2a;
  border-bottom-color: #404040;
}

.dark .room-header {
  background: #2a2a2a;
  color: #fff;
  border-right-color: #404040;
}

.dark .date-column {
  background: #2a2a2a;
  color: #fff;
  border-right-color: #404040;
}

.dark .room-info {
  background: #2a2a2a;
  color: #fff;
  border-right-color: #404040;
}

.dark .room-row {
  border-bottom-color: #404040;
}

.dark .room-row:hover {
  background: #333;
}

.dark .date-cell {
  border-right-color: #404040;
}

.dark .date-cell:hover {
  background: #333;
}
</style>
