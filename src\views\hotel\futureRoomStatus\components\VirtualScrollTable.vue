<template>
  <div class="virtual-scroll-table" ref="containerRef">
    <!-- 表格头部 -->
    <div class="table-header" :style="{ width: totalWidth + 'px' }">
      <div
        v-for="column in columns"
        :key="column.key"
        class="header-cell"
        :style="{
          width: column.width + 'px',
          minWidth: column.minWidth + 'px'
        }"
      >
        <component
          v-if="typeof column.title === 'function'"
          :is="column.title"
        />
        <span v-else>{{ column.title }}</span>
      </div>
    </div>

    <!-- 虚拟滚动容器 -->
    <div
      class="table-body"
      :style="{ height: containerHeight + 'px' }"
      @scroll="handleScroll"
    >
      <!-- 占位空间 -->
      <div :style="{ height: totalHeight + 'px', position: 'relative' }">
        <!-- 可见行 -->
        <div
          v-for="(item, index) in visibleItems"
          :key="getRowKey(item, visibleStartIndex + index)"
          class="table-row"
          :style="{
            position: 'absolute',
            top: (visibleStartIndex + index) * itemHeight + 'px',
            width: totalWidth + 'px',
            height: itemHeight + 'px'
          }"
        >
          <div
            v-for="column in columns"
            :key="column.key"
            class="table-cell"
            :style="{
              width: column.width + 'px',
              minWidth: column.minWidth + 'px'
            }"
          >
            <component
              v-if="column.render"
              :is="column.render"
              :row="item"
              :index="visibleStartIndex + index"
            />
            <span v-else>{{ item[column.key] }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <n-spin size="large">
        <template #description>正在加载数据...</template>
      </n-spin>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { useVirtualScroll } from '../composables/useOptimization'

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  columns: {
    type: Array,
    default: () => []
  },
  itemHeight: {
    type: Number,
    default: 50
  },
  containerHeight: {
    type: Number,
    default: 600
  },
  loading: {
    type: Boolean,
    default: false
  },
  rowKey: {
    type: [String, Function],
    default: 'id'
  }
})

const emit = defineEmits(['scroll', 'load-more'])

const containerRef = ref(null)

// 使用虚拟滚动 composable
const {
  scrollTop,
  visibleRange,
  handleScroll: onScroll
} = useVirtualScroll(props.itemHeight, props.containerHeight)

// 计算总宽度
const totalWidth = computed(() => {
  return props.columns.reduce((total, column) => {
    return total + (column.width || 150)
  }, 0)
})

// 计算总高度
const totalHeight = computed(() => {
  return props.data.length * props.itemHeight
})

// 可见范围
const visibleStartIndex = computed(() => visibleRange.value.start)
const visibleEndIndex = computed(() => Math.min(visibleRange.value.end, props.data.length))

// 可见项目
const visibleItems = computed(() => {
  return props.data.slice(visibleStartIndex.value, visibleEndIndex.value)
})

// 获取行键
const getRowKey = (item, index) => {
  if (typeof props.rowKey === 'function') {
    return props.rowKey(item, index)
  }
  return item[props.rowKey] || index
}

// 处理滚动
const handleScroll = (event) => {
  onScroll(event)
  emit('scroll', {
    scrollTop: event.target.scrollTop,
    scrollLeft: event.target.scrollLeft
  })

  // 检查是否需要加载更多数据
  const { scrollTop, scrollHeight, clientHeight } = event.target
  if (scrollTop + clientHeight >= scrollHeight - 100) {
    emit('load-more')
  }
}

// 监听数据变化，重置滚动位置
watch(() => props.data.length, () => {
  if (containerRef.value) {
    const scrollContainer = containerRef.value.querySelector('.table-body')
    if (scrollContainer) {
      scrollContainer.scrollTop = 0
    }
  }
})

// 响应式调整
const resizeObserver = ref(null)

onMounted(() => {
  if (containerRef.value) {
    resizeObserver.value = new ResizeObserver(() => {
      // 容器大小变化时重新计算
      nextTick(() => {
        // 触发重新计算
      })
    })
    resizeObserver.value.observe(containerRef.value)
  }
})

onUnmounted(() => {
  if (resizeObserver.value) {
    resizeObserver.value.disconnect()
  }
})
</script>

<style scoped>
.virtual-scroll-table {
  position: relative;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  overflow: hidden;
}

.table-header {
  display: flex;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
  position: sticky;
  top: 0;
  z-index: 10;
}

.header-cell {
  padding: 12px 8px;
  border-right: 1px solid #e8e8e8;
  font-weight: 600;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-cell:last-child {
  border-right: none;
}

.table-body {
  overflow: auto;
  position: relative;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
}

.table-row:hover {
  background-color: #f5f5f5;
}

.table-cell {
  padding: 8px;
  border-right: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.table-cell:last-child {
  border-right: none;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
}

/* 滚动条样式 */
.table-body::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.table-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.table-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
