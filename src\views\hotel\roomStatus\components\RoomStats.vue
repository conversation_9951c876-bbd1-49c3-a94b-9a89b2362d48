<template>
  <div class="room-stats" :class="`layout-${layoutMode}`">
    <!-- 左侧布局统计面板 -->
    <div v-if="layoutMode === 'left'" class="stats-left">
      <h4 class="stats-title">房态统计</h4>
      <div class="grid-stats">
        <!-- 业务状态 -->
        <div
          class="stat-item-grid clickable"
          :class="{ active: isStatusActive('stay') }"
          :style="getStatItemStyle('occupied')"
          @click="handleStatClick('stay')"
        >
          <span class="stat-number">{{ roomStats.occupied }}</span>
          <span class="stat-label">已住</span>
        </div>
        <div
          class="stat-item-grid clickable"
          :class="{ active: isStatusActive('vacant') }"
          :style="getStatItemStyle('available')"
          @click="handleStatClick('vacant')"
        >
          <span class="stat-number">{{ roomStats.available }}</span>
          <span class="stat-label">空房</span>
        </div>
        <div
          class="stat-item-grid clickable"
          :class="{ active: isStatusActive('reserved') }"
          :style="getStatItemStyle('reserved')"
          @click="handleStatClick('reserved')"
        >
          <span class="stat-number">{{ roomStats.reserved }}</span>
          <span class="stat-label">预订</span>
        </div>

        <!-- 清洁状态 -->
        <div
          class="stat-item-grid clickable clean-status"
          :class="{ active: selectedCleanStatus === 2 }"
          :style="getStatItemStyle('dirty')"
          @click="handleCleanStatClick(2)"
        >
          <span class="stat-number">{{ roomStats.dirty }}</span>
          <span class="stat-label">脏房</span>
        </div>
        <div
          v-show="roomStats.cleaning > 0"
          class="stat-item-grid clickable clean-status"
          :class="{ active: selectedCleanStatus === 3 }"
          :style="getStatItemStyle('cleaning')"
          @click="handleCleanStatClick(3)"
        >
          <span class="stat-number">{{ roomStats.cleaning }}</span>
          <span class="stat-label">清洁中</span>
        </div>
        <div
          v-show="roomStats.maintenance > 0"
          class="stat-item-grid clickable clean-status"
          :class="{ active: selectedCleanStatus === 4 }"
          :style="getStatItemStyle('maintenance')"
          @click="handleCleanStatClick(4)"
        >
          <span class="stat-number">{{ roomStats.maintenance }}</span>
          <span class="stat-label">维修</span>
        </div>
      </div>
    </div>

    <!-- 底部布局统计面板 -->
    <div v-else-if="layoutMode === 'bottom'" class="stats-bottom">
      <div
        class="stat-compact clickable"
        :class="{ active: isStatusActive('stay') }"
        :style="getStatItemStyle('occupied')"
        @click="handleStatClick('stay')"
      >
        <span class="stat-num">{{ roomStats.occupied }}</span>
        <span class="stat-text">住</span>
      </div>
      <div
        class="stat-compact clickable"
        :class="{ active: isStatusActive('vacant') }"
        :style="getStatItemStyle('available')"
        @click="handleStatClick('vacant')"
      >
        <span class="stat-num">{{ roomStats.available }}</span>
        <span class="stat-text">空</span>
      </div>
      <div
        class="stat-compact clickable"
        :class="{ active: isStatusActive('reserved') }"
        :style="getStatItemStyle('reserved')"
        @click="handleStatClick('reserved')"
      >
        <span class="stat-num">{{ roomStats.reserved }}</span>
        <span class="stat-text">订</span>
      </div>
    </div>

    <!-- 顶部布局统计（如果需要的话） -->
    <div v-else-if="layoutMode === 'top'" class="stats-top">
      <div class="quick-stats">
        <div
          class="stat-item clickable"
          :class="{ active: isStatusActive('stay') }"
          :style="getStatItemStyle('occupied')"
          @click="handleStatClick('stay')"
        >
          <span class="stat-number">{{ roomStats.occupied }}</span>
          <span class="stat-label">已住</span>
        </div>
        <div
          class="stat-item clickable"
          :class="{ active: isStatusActive('vacant') }"
          :style="getStatItemStyle('available')"
          @click="handleStatClick('vacant')"
        >
          <span class="stat-number">{{ roomStats.available }}</span>
          <span class="stat-label">空房</span>
        </div>
        <div
          class="stat-item clickable"
          :class="{ active: isStatusActive('reserved') }"
          :style="getStatItemStyle('reserved')"
          @click="handleStatClick('reserved')"
        >
          <span class="stat-number">{{ roomStats.reserved }}</span>
          <span class="stat-label">预订</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Props
const props = defineProps({
  layoutMode: {
    type: String,
    default: 'top',
    validator: (value) => ['top', 'left', 'bottom'].includes(value)
  },
  roomStats: {
    type: Object,
    default: () => ({
      occupied: 0,
      available: 0,
      reserved: 0,
      dirty: 0,
      cleaning: 0,
      maintenance: 0
    })
  },
  selectedStatus: {
    type: [String, null],
    default: null
  },
  selectedCleanStatus: {
    type: [Number, String, null],
    default: null
  },
  roomStatusConfig: {
    type: Array,
    default: () => []
  },
  cleanStatusConfig: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits([
  'stat-click',
  'clean-stat-click'
])

// 判断统计卡片状态是否激活
const isStatusActive = (statusKey) => {
  if (!props.selectedStatus || !Array.isArray(props.roomStatusConfig)) return false

  // 直接查找匹配的状态配置
  let targetSign = null

  if (statusKey === 'vacant') {
    const config = props.roomStatusConfig.find(s =>
      s.name === '空房' || s.name === '可用' || s.name === '空闲' ||
      s.sign === 'vacant' || s.sign === 'available' || s.sign === 'empty'
    )
    targetSign = config ? config.sign : null
  } else if (statusKey === 'stay') {
    const config = props.roomStatusConfig.find(s =>
      s.name === '已住' || s.name === '入住' || s.name === '在住' ||
      s.sign === 'stay' || s.sign === 'occupied' || s.sign === 'checkin'
    )
    targetSign = config ? config.sign : null
  } else if (statusKey === 'reserved') {
    const config = props.roomStatusConfig.find(s =>
      s.name === '预订' || s.name === '已预订' || s.name === '预定' ||
      s.sign === 'reserved' || s.sign === 'booking'
    )
    targetSign = config ? config.sign : null
  }

  return props.selectedStatus === targetSign
}

// 获取统计项样式
const getStatItemStyle = (statusType) => {
  let color = '#6b7280' // 默认颜色

  // 根据状态类型查找对应的颜色
  if (['occupied', 'available', 'reserved', 'checkout', 'maintenance', 'blocked'].includes(statusType)) {
    // 从业务状态配置中查找颜色
    if (Array.isArray(props.roomStatusConfig)) {
      const businessStatus = props.roomStatusConfig.find(status => {
        const mappedStatus = mapRoomStatusBySign(status.sign)
        return mappedStatus === statusType
      })
      if (businessStatus) {
        color = businessStatus.color
      }
    }
  } else if (['dirty', 'cleaning'].includes(statusType)) {
    // 从清洁状态配置中查找颜色
    if (Array.isArray(props.cleanStatusConfig)) {
      const cleanStatus = props.cleanStatusConfig.find(status => {
        const cleanKey = getCleanStatusKey(status.name)
        return cleanKey === statusType
      })
      if (cleanStatus) {
        color = cleanStatus.color
      }
    }
  }

  return {
    'background': `linear-gradient(135deg, ${color}20, rgba(255, 255, 255, 0.95))`,
    'color': color,
    'border': `2px solid ${color}40`,
    'box-shadow': `0 2px 8px ${color}20`
  }
}

// 辅助函数
const mapRoomStatusBySign = (sign) => {
  const signMap = {
    'stay': 'occupied',
    'vacant': 'available',
    'reserved': 'reserved',
    'checkout': 'checkout',
    'dirty': 'dirty',
    'cleaning': 'cleaning',
    'maintenance': 'maintenance',
    'blocked': 'blocked',
    'inspecting': 'inspecting',
    'noshow': 'noshow',
    'available': 'available',
    'occupied': 'occupied',
    'ooo': 'maintenance',
    'oos': 'blocked'
  }
  return signMap[sign] || 'available'
}

const getCleanStatusKey = (statusName) => {
  const cleanStatusMap = {
    '净': null,
    '脏': 'dirty',
    '清洁中': 'cleaning',
    '维修': 'maintenance',
    '维修中': 'maintenance',
    '封锁': 'blocked',
    '查房': 'inspecting',
    '查房中': 'inspecting'
  }
  return cleanStatusMap[statusName] || null
}

// 事件处理函数
const handleStatClick = (statusKey) => {
  emit('stat-click', statusKey)
}

const handleCleanStatClick = (cleanStatusId) => {
  emit('clean-stat-click', cleanStatusId)
}
</script>

<style scoped>
.room-stats {
  user-select: none;
}

/* 左侧布局样式 */
.stats-left {
  padding: 1rem;
}

.stats-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.grid-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.stat-item-grid {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.75rem 0.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.stat-item-grid:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-item-grid.active {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.8);
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.75rem;
  font-weight: 500;
  opacity: 0.9;
}

/* 底部布局样式 */
.stats-bottom {
  display: flex;
  gap: 1rem;
  padding: 0.75rem 1rem;
}

.stat-compact {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 50px;
}

.stat-compact:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-compact.active {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-num {
  font-size: 1.25rem;
  font-weight: 700;
  line-height: 1;
}

.stat-text {
  font-size: 0.7rem;
  font-weight: 500;
  margin-top: 0.2rem;
  opacity: 0.9;
}

/* 顶部布局样式 */
.stats-top {
  padding: 0.5rem 1rem;
}

.quick-stats {
  display: flex;
  gap: 0.75rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  min-width: 50px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-item.active {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.8);
}
</style>
