<template>
  <div class="operation-grid">
    <div class="grid-container">
      <div
        v-for="item in operations"
        :key="item.key"
        class="operation-item"
        @click="handleItemClick(item)"
      >
        <div class="operation-icon">
          <i :class="item.icon" v-if="item.icon"></i>
          <span v-else class="icon-text">{{ item.iconText }}</span>
        </div>
        <div class="operation-label">{{ item.label }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
  operations: {
    type: Array,
    default: () => []
  },
  columns: {
    type: Number,
    default: 10
  }
})

const emit = defineEmits(['item-click'])

function handleItemClick(item) {
  if (item.disabled) return
  emit('item-click', item)
}
</script>

<style scoped>
.operation-grid {
  padding: 2rem;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(v-bind(columns), 1fr);
  gap: 1.5rem;
  max-width: 100%;
}

.operation-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem 1rem;
  border-radius: 12px;
  background: #fafbfc;
  border: 1px solid #e5e7eb;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.operation-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.05) 0%, rgba(var(--primary-color-rgb), 0.08) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.operation-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #d1d5db;
}

.operation-item:hover::before {
  opacity: 1;
}

.operation-item:active {
  transform: translateY(0);
}

.operation-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.75rem;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 24px;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.operation-item:hover .operation-icon {
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.icon-text {
  font-size: 14px;
  font-weight: 600;
}

.operation-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  text-align: center;
  line-height: 1.4;
  position: relative;
  z-index: 1;
  transition: color 0.3s ease;
}

.operation-item:hover .operation-label {
  color: #1f2937;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .grid-container {
    grid-template-columns: repeat(8, 1fr);
  }
}

@media (max-width: 992px) {
  .grid-container {
    grid-template-columns: repeat(6, 1fr);
    gap: 1rem;
  }

  .operation-item {
    padding: 1rem 0.75rem;
  }

  .operation-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }
}

@media (max-width: 768px) {
  .grid-container {
    grid-template-columns: repeat(4, 1fr);
  }

  .operation-grid {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .grid-container {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
  }

  .operation-item {
    padding: 0.75rem 0.5rem;
  }

  .operation-icon {
    width: 36px;
    height: 36px;
    font-size: 18px;
    margin-bottom: 0.5rem;
  }

  .operation-label {
    font-size: 12px;
  }
}

/* 暗色主题 */
.dark .operation-grid {
  background: #1f2937;
}

.dark .operation-item {
  background: #374151;
  border-color: #4b5563;
}

.dark .operation-item:hover {
  border-color: #6b7280;
}

.dark .operation-label {
  color: #d1d5db;
}

.dark .operation-item:hover .operation-label {
  color: #f9fafb;
}
</style>
