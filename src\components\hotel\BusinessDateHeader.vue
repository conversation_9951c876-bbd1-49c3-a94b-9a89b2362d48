<template>
  <div class="business-date-header">
    <!-- 营业日显示 -->
    <div class="date-container" :class="{ 'loading': businessDateStore.loading }">
      <div class="date-icon">
        <i class="i-mdi:calendar-today"></i>
      </div>
      <div class="date-content">
        <div class="date-label">营业日</div>
        <div class="date-value">
          {{ businessDateStore.formattedDate }}
        </div>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="businessDateStore.error" class="error-tooltip">
      {{ businessDateStore.error }}
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useBusinessDateStore } from '@/store'

const businessDateStore = useBusinessDateStore()

// 组件挂载时获取营业日
onMounted(() => {
  // 如果没有营业日或者距离上次更新超过5分钟，则重新获取
  const shouldRefresh = !businessDateStore.currentDate ||
    !businessDateStore.lastUpdateTime ||
    (Date.now() - new Date(businessDateStore.lastUpdateTime).getTime()) > 5 * 60 * 1000

  if (shouldRefresh) {
    businessDateStore.fetchBusinessDate()
  }
})
</script>

<style scoped>
.business-date-header {
  position: relative;
  display: flex;
  align-items: center;
}

.date-container {
  display: flex;
  align-items: center;
  padding: 6px 10px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  min-width: 140px;
}

.date-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(var(--primary-color), 0.02) 0%, transparent 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.date-container:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(var(--primary-color), 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

.date-container:hover::before {
  opacity: 1;
}

.date-container.loading {
  animation: pulse 1.5s ease-in-out infinite;
}

.date-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, rgba(var(--primary-color), 0.1) 0%, rgba(var(--primary-color), 0.05) 100%);
  border-radius: 6px;
  margin-right: 8px;
  flex-shrink: 0;
}

.date-icon i {
  font-size: 12px;
  color: rgb(var(--primary-color));
}

.date-content {
  flex: 1;
  min-width: 0;
}

.date-label {
  font-size: 10px;
  color: #64748b;
  font-weight: 500;
  margin-bottom: 1px;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  line-height: 1;
}

.date-value {
  font-size: 11px;
  font-weight: 600;
  color: #1e293b;
  line-height: 1.2;
  transition: all 0.3s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.date-actions {
  margin-left: 6px;
  flex-shrink: 0;
}

.error-tooltip {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #fee2e2;
  color: #dc2626;
  font-size: 10px;
  padding: 4px 8px;
  border-radius: 4px;
  margin-top: 4px;
  z-index: 10;
  border: 1px solid #fecaca;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 暗色主题适配 */
.dark .date-container {
  background: rgba(30, 41, 59, 0.9);
  border-color: rgba(71, 85, 105, 0.6);
}

.dark .date-container:hover {
  background: rgba(30, 41, 59, 0.95);
  border-color: rgba(var(--primary-color), 0.3);
}

.dark .date-label {
  color: #94a3b8;
}

.dark .date-value {
  color: #f1f5f9;
}

.dark .error-tooltip {
  background: rgba(239, 68, 68, 0.1);
  color: #fca5a5;
  border-color: rgba(239, 68, 68, 0.2);
}

/* 脉冲动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .date-container {
    padding: 4px 8px;
    min-width: 120px;
  }

  .date-icon {
    width: 20px;
    height: 20px;
    margin-right: 6px;
  }

  .date-icon i {
    font-size: 10px;
  }

  .date-label {
    font-size: 9px;
  }

  .date-value {
    font-size: 10px;
  }
}

/* 紧凑模式 */
@media (max-width: 1024px) {
  .date-label {
    display: none;
  }

  .date-value {
    font-size: 12px;
    margin-top: 0;
  }

  .date-container {
    min-width: 110px;
  }
}
</style>
