<template>
  <div class="room-status-layout" :class="`layout-${layoutMode}`">
    <!-- 顶部布局 -->
    <div v-if="layoutMode === 'top'" class="layout-top">
      <!-- 顶部工具栏 -->
      <div class="top-toolbar">
        <!-- 左侧：筛选器和状态图例 -->
        <div class="toolbar-left">
          <div class="filters-section">
            <slot name="filters"></slot>
          </div>
          <div class="legend-section">
            <slot name="legend"></slot>
          </div>
        </div>

        <!-- 右侧：控制按钮 -->
        <div class="toolbar-right">
          <slot name="controls"></slot>
        </div>
      </div>

      <!-- 主内容区域 -->
      <div class="top-content">
        <slot name="content"></slot>
      </div>
    </div>

    <!-- 左侧布局 -->
    <div v-else-if="layoutMode === 'left'" class="layout-left">
      <!-- 左侧边栏 -->
      <div class="left-sidebar" :style="{ width: '250px' }">
        <!-- 控制按钮（放在最上面） -->
        <div class="sidebar-section">
          <slot name="controls"></slot>
        </div>

        <!-- 筛选器 -->
        <div class="sidebar-section">
          <slot name="filters"></slot>
        </div>

        <!-- 状态图例 -->
        <div class="sidebar-section">
          <slot name="legend"></slot>
        </div>

        <!-- 统计信息（已删除） -->
        <div class="sidebar-section">
          <slot name="stats"></slot>
        </div>
      </div>



      <!-- 主内容区域 -->
      <div class="left-content">
        <slot name="content"></slot>
      </div>
    </div>

    <!-- 底部布局 -->
    <div v-else-if="layoutMode === 'bottom'" class="layout-bottom">
      <!-- 主内容区域 -->
      <div class="bottom-content">
        <slot name="content"></slot>
      </div>

      <!-- 底部工具栏 -->
      <div class="bottom-toolbar">
        <!-- 左侧：筛选器和状态图例 -->
        <div class="bottom-toolbar-left">
          <div class="filters-section">
            <slot name="filters"></slot>
          </div>
          <div class="legend-section">
            <slot name="legend"></slot>
          </div>
        </div>

        <!-- 右侧：控制按钮 -->
        <div class="bottom-toolbar-right">
          <slot name="controls"></slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

// Props
const props = defineProps({
  layoutMode: {
    type: String,
    default: 'top',
    validator: (value) => ['top', 'left', 'bottom'].includes(value)
  },
  initialSidebarWidth: {
    type: Number,
    default: 180
  }
})

// Emits
const emit = defineEmits(['sidebar-width-change'])

// 侧边栏宽度管理
const sidebarWidth = ref(props.initialSidebarWidth)
const isResizing = ref(false)
const minSidebarWidth = 160
const maxSidebarWidth = 220

// 开始调整侧边栏宽度
const startResize = (e) => {
  if (props.layoutMode !== 'left') return

  isResizing.value = true
  document.addEventListener('mousemove', handleResize)
  document.addEventListener('mouseup', stopResize)
  document.body.style.cursor = 'col-resize'
  document.body.style.userSelect = 'none'

  e.preventDefault()
}

// 处理调整过程
const handleResize = (e) => {
  if (!isResizing.value) return

  const newWidth = Math.max(minSidebarWidth, Math.min(maxSidebarWidth, e.clientX))
  sidebarWidth.value = newWidth
  emit('sidebar-width-change', newWidth)
}

// 停止调整
const stopResize = () => {
  isResizing.value = false
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
  document.body.style.cursor = ''
  document.body.style.userSelect = ''
}

// 响应式布局处理
const handleWindowResize = () => {
  // 在小屏幕上自动调整侧边栏宽度
  if (window.innerWidth < 1024 && sidebarWidth.value > 180) {
    sidebarWidth.value = 180
    emit('sidebar-width-change', 180)
  }
}

onMounted(() => {
  window.addEventListener('resize', handleWindowResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleWindowResize)
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
})
</script>

<style scoped>
.room-status-layout {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f8fafc;
}

/* 顶部布局样式 */
.layout-top {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.top-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* padding: 0.25rem 0.75rem; */
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  z-index: 10;
  min-height: auto;
  height: auto;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.filters-section {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.legend-section {
  display: flex;
  align-items: center;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.top-legend {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  z-index: 9;
}

.top-content {
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

/* 左侧布局样式 */
.layout-left {
  height: 100%;
  display: flex;
}

.left-sidebar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  z-index: 10;
  width: 180px;
  flex-shrink: 0;
  overflow-y: auto;
  overflow-x: hidden;
}

.sidebar-section {
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  padding: 0.5rem;
}

.sidebar-section:last-child {
  border-bottom: none;
  margin-top: auto;
}

/* 搜索框在左侧布局中的样式优化 */
.layout-left .search-container {
  margin-bottom: 0.5rem;
  padding: 0;
}

.layout-left .search-input {
  width: 100%;
  max-width: none;
}



.left-content {
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

/* 底部布局样式 */
.layout-bottom {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.bottom-content {
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

.bottom-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.bottom-toolbar-left {
  display: flex;
  align-items: center;
  gap: 2rem;
  flex: 1;
}

.bottom-toolbar-right {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* 通用筛选器和状态图例样式 */
.filters-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.legend-section {
  display: flex;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .layout-left .left-sidebar {
    min-width: 180px;
  }

  .top-toolbar {
    padding: 0.5rem 0.75rem;
  }

  .toolbar-left {
    gap: 0.75rem;
  }

  .toolbar-right {
    gap: 0.5rem;
  }
}

@media (max-width: 768px) {
  .layout-top .top-toolbar {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }

  .bottom-toolbar {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .bottom-toolbar-left,
  .bottom-toolbar-center,
  .bottom-toolbar-right {
    justify-content: center;
  }
}

/* 动画效果 */
.layout-top,
.layout-left,
.layout-bottom {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.top-toolbar,
.top-legend,
.left-sidebar,
.bottom-toolbar {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .room-status-layout {
    background: #0f172a;
  }

  .top-toolbar,
  .top-legend,
  .left-sidebar,
  .bottom-toolbar {
    background: rgba(15, 23, 42, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .sidebar-resizer:hover {
    background: rgba(59, 130, 246, 0.4);
  }
}
</style>
