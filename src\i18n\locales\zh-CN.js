/**
 * 中文语言包
 */

export default {
  // 通用
  common: {
    confirm: '确认',
    cancel: '取消',
    save: '保存',
    delete: '删除',
    edit: '编辑',
    add: '添加',
    search: '搜索',
    reset: '重置',
    submit: '提交',
    back: '返回',
    close: '关闭',
    refresh: '刷新',
    loading: '加载中...',
    noData: '暂无数据',
    success: '成功',
    error: '错误',
    warning: '警告',
    info: '信息',
    yes: '是',
    no: '否',
    all: '全部',
    select: '选择',
    clear: '清空',
    export: '导出',
    import: '导入',
    copy: '复制',
    view: '查看',
    detail: '详情',
    operation: '操作',
    status: '状态',
    remark: '备注',
    createTime: '创建时间',
    updateTime: '更新时间',
    action: '操作'
  },

  // 登录页面
  login: {
    title: '酒店管理系统',
    subtitle: '智能化酒店管理系统',
    welcomeBack: '欢迎回来',
    loginPrompt: '请登录您的账户以继续使用系统',
    username: '用户名',
    password: '密码',
    usernamePlaceholder: '请输入账户名',
    passwordPlaceholder: '请输入密码',
    captchaPlaceholder: '请输入验证码',
    loginBtn: '登录',
    loginButton: '立即登录',
    loggingIn: '登录中...',
    loginSuccess: '登录成功',
    loginFailed: '登录失败',
    usernameRequired: '请输入用户名',
    passwordRequired: '请输入密码',
    captchaRequired: '请输入验证码',
    usernameRule: '用户名长度应为3-20位',
    usernameInvalid: '用户名只能包含字母、数字、下划线和中文',
    passwordRule: '密码长度应为6-20位',
    passwordWeak: '密码强度较弱，建议包含大小写字母、数字和符号',
    captchaError: '验证码错误',
    switchLanguage: '切换语言',
    rememberMe: '记住我',
    forgotPassword: '忘记密码？',
    forgotPasswordTip: '请联系管理员重置密码',
    securityTip: '您的信息将被安全加密传输',
    learnMore: '了解更多',
    loginExpired: '登录已过期，请重新登录',
    redirecting: '正在跳转...',
    clickToRefresh: '点击刷新',
    keyboardHint: '按 Enter 登录，按 Esc 清空',
    readyToLogin: '准备登录',
    invalidCredentials: '用户名或密码错误',
    tooManyAttempts: '登录尝试过于频繁，请稍后再试',
    serverError: '服务器错误，请稍后重试',
    networkError: '网络连接失败，请检查网络设置',
    captchaRequired: '登录失败次数过多，请输入验证码',
    features: {
      roomManagement: {
        title: '房间管理',
        description: '实时房态，智能分配'
      },
      customerService: {
        title: '客户服务',
        description: '全程跟踪，贴心服务'
      },
      dataAnalysis: {
        title: '数据分析',
        description: '经营洞察，决策支持'
      }
    }
  },

  // 导航菜单
  menu: {
    dashboard: '仪表盘',
    overview: '功能概览',
    hotel: '酒店管理',
    roomStatus: '房态管理',
    member: '会员管理',
    memberDetail: '会员详情',
    card: '会员卡管理',
    system: '系统管理',
    systemStatus: '系统状态',
    searchPlaceholder: '搜索功能...',
    quickFunctions: '快速功能',
    checkIn: '办理入住',
    reservation: '办理预订',
    selectOpenMethod: '请选择打开方式',
    openExternal: '外链打开',
    openInternal: '在本站内嵌打开',
    functionalModules: '功能模块',
    searchResults: '搜索结果'
  },

  // 应用中心页面
  overview: {
    title: '功能中心',
    welcome: '集中管理，快速导航 - 一站式访问所有系统功能模块',
    todayDate: '今日日期',
    currentTime: '当前时间',
    roomStatusEntry: '房态页',
    frontDeskCore: '前台核心',

    // 功能模块
    modules: {
      title: '核心功能模块',
      roomManagement: {
        title: '房态管理',
        description: '实时房间状态监控、房间清洁、维修管理',
        features: ['房态监控', '清洁管理', '维修跟踪']
      },
      billing: {
        title: '账单管理',
        description: '客房账单、消费记录、结算管理',
        features: ['账单查询', '消费统计', '结算处理']
      },
      member: {
        title: '会员管理',
        description: '会员信息管理、积分系统、等级管理',
        features: ['会员档案', '积分管理', '等级升级']
      },
      reservation: {
        title: '预订管理',
        description: '房间预订、预订查询、预订变更',
        features: ['在线预订', '预订查询', '变更处理']
      }
    },

    // 数据概览
    dataOverview: {
      title: '经营数据概览',
      occupancyTrend: '入住率趋势',
      roomTypeDistribution: '房型分布',
      revenueStats: '收入统计',
      roomStatus: '客房状态',
      period: {
        last7Days: '近7天',
        currentStatus: '当前状态',
        thisMonth: '本月',
        realTime: '实时'
      }
    },

    // 快速操作
    quickActions: {
      title: '快速操作',
      checkIn: {
        title: '快速入住',
        description: '为客人办理入住手续'
      },
      checkOut: {
        title: '快速退房',
        description: '为客人办理退房手续'
      },
      reservation: {
        title: '新增预订',
        description: '创建新的房间预订'
      },
      maintenance: {
        title: '维修报告',
        description: '提交房间维修申请'
      }
    },

    // 系统信息
    systemInfo: {
      title: '系统信息',
      description: '当前系统运行状态和版本信息',
      version: '系统版本',
      lastUpdate: '最后更新',
      techStack: '技术栈',
      uptime: '运行时间',
      uptimeFormat: {
        daysHours: '{days}天{hours}小时',
        hours: '{hours}小时'
      }
    },

    // 酒店切换
    hotelSwitcher: {
      title: '选择酒店',
      mainStore: '总店',
      branch: '分店',
      online: '在线',
      offline: '离线',
      switchSuccess: '已切换到{name}'
    },

    // 图表相关
    charts: {
      occupancyTrend: '入住率趋势',
      last7Days: '近7天',
      roomTypeDistribution: '房型分布',
      currentStatus: '当前状态',
      revenueStats: '收入统计',
      thisMonth: '本月',
      roomStatus: '客房状态',
      realTime: '实时',
      weekdays: {
        monday: '周一',
        tuesday: '周二',
        wednesday: '周三',
        thursday: '周四',
        friday: '周五',
        saturday: '周六',
        sunday: '周日'
      },
      roomTypes: {
        standard: '标准间',
        deluxe: '豪华间',
        suite: '套房',
        presidential: '总统套'
      },
      revenue: {
        room: '客房收入',
        dining: '餐饮收入',
        service: '服务收入'
      },
      roomStatusLabels: {
        occupied: '已入住',
        available: '可入住',
        maintenance: '维修中',
        cleaning: '清洁中'
      }
    },

    // 消息提示
    messages: {
      moduleInDevelopment: '{title} 功能开发中...',
      quickCheckinInDev: '快速入住功能开发中...',
      quickCheckoutInDev: '快速退房功能开发中...',
      roomCleaningInDev: '房间清洁功能开发中...',
      nightAuditInDev: '夜审操作功能开发中...',
      featureInDev: '功能开发中...'
    },

    // 入住办理
    checkin: {
      title: '办理入住',
      roomNumber: '房间',
      salesInfo: '销售信息',
      customerInfo: '客户信息',
      checkinInfo: '入住信息',
      roomList: '入住房间列表',

      // 销售信息字段
      sellType: '销售类型',
      saleRule: '销售规则',
      checkinType: '入住类型',
      isSecret: '保密房',
      priceScheme: '价格方案',
      intermediary: '单位/中介',
      orderSource: '订单来源',

      // 客户信息字段
      contactName: '联系人名字',
      contactPhone: '联系人手机号',
      externalOrderNo: '外部订单号',

      // 入住信息字段
      stayDuration: '入住时长',
      checkoutTime: '离店时间',
      roomPrice: '房价',
      remark: '备注',

      // 房间列表字段
      roomNumber: '房号',
      roomType: '房型',
      price: '房价(元)',
      deposit: '押金(元)',
      package: '可选套餐',
      actions: '操作',

      // 按钮
      addRoom: '添加房间',
      changeRoom: '换房',
      removeRoom: '删除',
      readCard: '读卡',
      confirm: '确认入住',

      // 占位符
      selectSellType: '请选择销售类型',
      selectSaleRule: '请选择销售规则',
      selectCheckinType: '请选择入住类型',
      selectPriceScheme: '请选择价格方案',
      selectIntermediary: '请选择单位/中介',
      selectOrderSource: '请选择订单来源',
      inputContactName: '请输入联系人名字',
      inputContactPhone: '请输入手机号',
      inputExternalOrderNo: '请输入外部订单号',
      inputStayDuration: '请输入入住{unit}',
      selectPackage: '选择套餐',

      // 验证消息
      validation: {
        sellTypeRequired: '请选择销售类型',
        saleRuleRequired: '请选择销售规则',
        checkinTypeRequired: '请选择入住类型',
        priceSchemeRequired: '请选择价格方案',
        orderSourceRequired: '请选择订单来源',
        contactNameRequired: '请输入联系人名字',
        contactPhoneRequired: '请输入联系人手机号',
        stayDurationRequired: '请输入入住时长',
        checkoutTimeRequired: '请选择离店时间',
        roomRequired: '请至少选择一间房间'
      },

      // 成功消息
      success: {
        checkinSuccess: '办理入住成功！共 {count} 间房间',
        roomAdded: '已添加 {count} 间房间',
        roomChanged: '已将房间更换为 {roomNumber}',
        roomRemoved: '已移除房间',
        cardReadSuccess: '身份证读取成功'
      },

      // 错误消息
      error: {
        cardReadFailed: '读取身份证失败，请检查设备连接',
        noCardInfo: '未读取到身份证信息',
        changeRoomSingleOnly: '换房时只能选择一个房间',
        minOneRoom: '至少需要保留一间房间'
      },

      // 时间单位
      timeUnit: {
        day: '天',
        hour: '小时',
        month: '月'
      }
    }
  },
    // 酒店管理
    hotel: {
      checkin: {
        validation: {
          sellTypeRequired: '请选择销售类型',
          saleRuleRequired: '请选择销售规则',
          checkinTypeRequired: '请选择入住类型',
          priceSchemeRequired: '请选择价格方案',
          orderSourceRequired: '请选择订单来源',
          contactNameRequired: '请输入联系人姓名',
          contactPhoneRequired: '请输入联系人电话',
          stayDurationRequired: '请输入住宿天数',
          checkoutTimeRequired: '请选择退房时间'
        },
        success: {
          checkinSuccess: '成功办理入住，共 {count} 间房'
        }
      }
    },

  // 房态管理页面
  roomStatus: {
    title: '房态管理',
    refresh: '刷新',
    refreshing: '正在更新房态数据...',
    autoRefresh0: '自动刷新',
    autoRefreshOn: '自动刷新',
    autoRefreshOff: '手动刷新',
    manualRefresh: '手动刷新',
    quickCheckin: '快速入住',
    quickReservation: '快速预订',
    updating: '更新中',
    refreshStatus: '刷新房态',
    searchPlaceholder: '搜索房间号或客人姓名',
    loadingRoomData: '正在获取房间数据...',
    retryFetch: '重新获取',
    noRoomData: '暂无房间数据',
    refreshData: '刷新数据',
    cleanStatusLegend: '清洁状态',
    businessStatusLegend: '业务状态',
    roomUnit: '间',
    lastUpdate: '最后更新',
    justNow: '刚刚',
    minutesAgo: '{minutes}分钟前',
    filters: {
      building: '楼栋筛选',
      floor: '选择楼层',
      roomType: '房型筛选',
      status: '状态筛选',
      cleanStatus: '清洁状态',
      search: '搜索房间号',
      all: '全部'
    },
    cardSize: {
      large: '大卡片',
      medium: '中卡片',
      small: '小方块'
    },
    cardSizeLarge: '大卡片',
    cardSizeMedium: '中卡片',
    cardSizeSmall: '小卡片',

    // 布局模式
    layoutTop: '顶部布局',
    layoutLeft: '左侧布局',
    layoutBottom: '底部布局',

    // 房间状态
    status: {
      occupied: '已入住',
      available: '可入住',
      checkout: '待退房',
      reserved: '已预订'
    },

    // 清洁状态
    cleanStatus: {
      dirty: '脏房',
      cleaning: '清洁中',
      maintenance: '维修中',
      inspecting: '查房中',
      blocked: '封锁',
      noshow: '未到店'
    },

    // 操作
    actions: {
      checkIn: '入住',
      checkOut: '退房',
      clean: '清洁',
      maintenance: '维修',
      block: '封锁',
      unblock: '解封',
      detail: '详情'
    },

    // 右键菜单
    contextMenu: {
      roomDetail: '房间详情',
      checkIn: '办理入住',
      checkOut: '办理退房',
      startCleaning: '开始清洁',
      completeCleaning: '完成清洁',
      startMaintenance: '开始维修',
      completeMaintenance: '完成维修',
      blockRoom: '封锁房间',
      unblockRoom: '解封房间'
    },

    // 自动刷新
    autoRefresh: {
      enabled: '自动刷新',
      interval: '刷新间隔',
      seconds: '秒',
      refreshing: '刷新中...',
      lastRefresh: '最后刷新'
    },

    // 卡片大小
    cardSize: {
      large: '大卡片',
      medium: '中卡片',
      small: '小卡片'
    },
    // 办理入住
    // 入住办理
    checkin: {
      title: '办理入住',
      roomNumber: '房间',
      salesInfo: '销售信息',
      customerInfo: '客户信息',
      checkinInfo: '入住信息',
      roomList: '入住房间列表',

      // 销售信息字段
      sellType: '销售类型',
      saleRule: '销售规则',
      checkinType: '入住类型',
      isSecret: '保密房',
      priceScheme: '价格方案',
      intermediary: '单位/中介',
      orderSource: '订单来源',

      // 客户信息字段
      contactName: '联系人名字',
      contactPhone: '联系人手机号',
      externalOrderNo: '外部订单号',

      // 入住信息字段
      stayDuration: '入住时长',
      checkoutTime: '离店时间',
      roomPrice: '房价',
      remark: '备注',

      // 房间列表字段
      roomNumber: '房号',
      roomType: '房型',
      price: '房价(元)',
      deposit: '押金(元)',
      package: '可选套餐',
      actions: '操作',

      // 按钮
      addRoom: '添加房间',
      changeRoom: '换房',
      removeRoom: '删除',
      readCard: '读卡',
      confirm: '确认入住',

      // 占位符
      selectSellType: '请选择销售类型',
      selectSaleRule: '请选择销售规则',
      selectCheckinType: '请选择入住类型',
      selectPriceScheme: '请选择价格方案',
      selectIntermediary: '请选择单位/中介',
      selectOrderSource: '请选择订单来源',
      inputContactName: '请输入联系人名字',
      inputContactPhone: '请输入手机号',
      inputExternalOrderNo: '请输入外部订单号',
      inputStayDuration: '请输入入住{unit}',
      selectPackage: '选择套餐',

      // 验证消息
      validation: {
        sellTypeRequired: '请选择销售类型',
        saleRuleRequired: '请选择销售规则',
        checkinTypeRequired: '请选择入住类型',
        priceSchemeRequired: '请选择价格方案',
        orderSourceRequired: '请选择订单来源',
        contactNameRequired: '请输入联系人名字',
        contactPhoneRequired: '请输入联系人手机号',
        stayDurationRequired: '请输入入住时长',
        checkoutTimeRequired: '请选择离店时间',
        roomRequired: '请至少选择一间房间'
      },

      // 成功消息
      success: {
        checkinSuccess: '办理入住成功！共 {count} 间房间',
        roomAdded: '已添加 {count} 间房间',
        roomChanged: '已将房间更换为 {roomNumber}',
        roomRemoved: '已移除房间',
        cardReadSuccess: '身份证读取成功'
      },

      // 错误消息
      error: {
        cardReadFailed: '读取身份证失败，请检查设备连接',
        noCardInfo: '未读取到身份证信息',
        changeRoomSingleOnly: '换房时只能选择一个房间',
        minOneRoom: '至少需要保留一间房间'
      },

      // 时间单位
      timeUnit: {
        day: '天',
        hour: '小时',
        month: '月'
      }
    }
  },

  // 会员管理
  member: {
    title: '会员管理',
    list: '会员列表',
    add: '新增会员',
    edit: '编辑会员',
    detail: '会员详情',
    search: '搜索会员',
    name: '姓名',
    phone: '手机号',
    email: '邮箱',
    level: '会员等级',
    points: '积分',
    balance: '余额',
    status: '状态',
    registerTime: '注册时间',
    lastLoginTime: '最后登录时间'
  },

  // 系统管理
  system: {
    title: '系统管理',
    status: '系统状态',
    settings: '系统设置',
    logs: '系统日志',
    backup: '数据备份',
    restore: '数据恢复'
  },

  // 主题设置
  theme: {
    title: '主题设置',
    light: '浅色主题',
    dark: '深色主题',
    auto: '跟随系统',
    blue: '蓝色主题',
    green: '绿色主题',
    purple: '紫色主题',
    orange: '橙色主题',
    red: '红色主题',
    switchSuccess: '已切换到{theme}主题'
  },

  // 语言设置
  language: {
    title: '语言设置',
    chinese: '简体中文',
    english: 'English',
    switchSuccess: '语言已切换为{language}'
  },

  // 用户相关
  user: {
    profile: '个人资料',
    switchRole: '切换角色',
    logout: '退出登录',
    logoutSuccess: '已退出登录'
  },

  // 错误页面
  error: {
    404: {
      title: '页面不存在',
      description: '抱歉，您访问的页面不存在',
      backHome: '返回首页'
    },
    403: {
      title: '访问被拒绝',
      description: '抱歉，您没有权限访问此页面',
      backHome: '返回首页'
    },
    500: {
      title: '服务器错误',
      description: '抱歉，服务器出现错误',
      backHome: '返回首页'
    }
  },

  // 消息提示
  message: {
    saveSuccess: '保存成功',
    saveFailed: '保存失败',
    deleteSuccess: '删除成功',
    deleteFailed: '删除失败',
    updateSuccess: '更新成功',
    updateFailed: '更新失败',
    addSuccess: '添加成功',
    addFailed: '添加失败',
    operationSuccess: '操作成功',
    operationFailed: '操作失败',
    networkError: '网络错误，请稍后重试',
    permissionDenied: '权限不足',
    dataNotFound: '数据不存在',
    parameterError: '参数错误'
  }
}
