import { defineStore } from 'pinia'

/**
 * 房间选择缓存 Store
 * 作用：
 * - 记住最近一次点击/右键的房间基础信息（含 bill_id）
 * - 为订单详情弹窗提供快速打开所需的最小数据，避免等待房间详情弹窗加载
 */
export const useRoomSelectionStore = defineStore('roomSelection', {
  state: () => ({
    lastClickedRoom: null,
    lastClickedAt: 0,
  }),
  getters: {
    hasBillId: (state) => {
      const r = state.lastClickedRoom
      return !!(r && (r.bill_id || r.billId))
    },
    quickOrderPayload: (state) => {
      const r = state.lastClickedRoom || {}
      const billId = r.bill_id || r.billId || null
      if (!billId) return null
      // 订单详情弹窗所需的最小数据
      return {
        orderData: {
          id: billId,
          bill_id: billId,
          room_number: r.room_number || r.roomNumber,
          room_type_name: r.room_type_name || r.roomType,
          ...(r.bill_info || r.billInfo || {})
        },
        billId
      }
    }
  },
  actions: {
    setFromRoom(room) {
      if (!room) return
      // 仅保存必要字段，避免响应式大对象带来负担
      this.lastClickedRoom = {
        id: room.id || room.room_id,
        room_number: room.room_number || room.roomNumber,
        room_type_name: room.room_type_name || room.roomType,
        bill_id: room.bill_id || room.billId || null,
        bill_info: room.bill_info || room.billInfo || null,
        shop_id: room.shop_id || room.shopId,
        floor_id: room.floor_id || room.floorId,
        building_id: room.building_id || room.buildingId,
      }
      this.lastClickedAt = Date.now()
    },
    reset() {
      this.$reset()
    }
  },
  persist: {
    storage: sessionStorage,
    pick: ['lastClickedRoom', 'lastClickedAt']
  }
})

