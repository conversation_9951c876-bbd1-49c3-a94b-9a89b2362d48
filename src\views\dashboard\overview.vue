<template>
  <div class="dashboard-modern">
    <!-- 顶部欢迎区域 -->
    <div class="hero-modern reveal">
      <div class="hero-content-modern">
        <div class="brand-section">
          <div class="brand-logo">
            <div class="logo-gradient"></div>
            <i class="i-material-symbols:wb-sunny"></i>
          </div>
          <div class="brand-text">
            <h1 class="brand-title">
              <span class="hotel-name">{{ hotelName }}</span>
              <span class="page-title">概览</span>
            </h1>
            <p class="brand-subtitle">实时洞察 · 智能管理</p>
          </div>
        </div>
        
        <div class="hero-stats">
          <div class="stat-pill time-pill">
            <i class="i-material-symbols:schedule"></i>
            <span>{{ currentTime }}</span>
          </div>
          <div class="stat-pill date-pill">
            <i class="i-material-symbols:today"></i>
            <span>{{ todayDate.split(' ')[0] }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 核心指标看板 -->
    <div class="metrics-panel reveal">
      <div class="metrics-grid">
        <div class="metric-card primary-metric" @click="goToRoomStatus">
          <div class="metric-icon-wrapper">
            <div class="metric-icon">
              <i class="i-material-symbols:hotel"></i>
            </div>
            <div class="metric-trend up">
              <i class="i-material-symbols:trending-up"></i>
            </div>
          </div>
          <div class="metric-content">
            <div class="metric-value">78%</div>
            <div class="metric-label">入住率</div>
            <div class="metric-detail">156/200 间</div>
          </div>
          <div class="metric-chart">
            <div class="mini-bars">
              <div class="bar" style="--height: 60%"></div>
              <div class="bar" style="--height: 75%"></div>
              <div class="bar" style="--height: 68%"></div>
              <div class="bar" style="--height: 85%"></div>
              <div class="bar" style="--height: 78%"></div>
            </div>
          </div>
        </div>

        <div class="metric-card revenue-metric">
          <div class="metric-icon-wrapper">
            <div class="metric-icon">
              <i class="i-material-symbols:monetization-on"></i>
            </div>
            <div class="metric-trend up">
              <i class="i-material-symbols:trending-up"></i>
            </div>
          </div>
          <div class="metric-content">
            <div class="metric-value">¥28.5K</div>
            <div class="metric-label">今日收入</div>
            <div class="metric-detail">+12.3% vs 昨日</div>
          </div>
          <div class="metric-progress">
            <div class="progress-ring">
              <svg width="60" height="60">
                <circle cx="30" cy="30" r="25" stroke="#f0f0f0" stroke-width="4" fill="none"/>
                <circle cx="30" cy="30" r="25" stroke="url(#gradient)" stroke-width="4" fill="none"
                  stroke-dasharray="157" stroke-dashoffset="47" transform="rotate(-90 30 30)"/>
              </svg>
              <div class="progress-text">70%</div>
            </div>
          </div>
        </div>

        <div class="metric-card service-metric">
          <div class="metric-icon-wrapper">
            <div class="metric-icon">
              <i class="i-material-symbols:room-service"></i>
            </div>
            <div class="metric-trend neutral">
              <i class="i-material-symbols:trending-flat"></i>
            </div>
          </div>
          <div class="metric-content">
            <div class="metric-value">12</div>
            <div class="metric-label">服务请求</div>
            <div class="metric-detail">8 进行中</div>
          </div>
          <div class="metric-status">
            <div class="status-dot processing"></div>
            <div class="status-dot processing"></div>
            <div class="status-dot completed"></div>
            <div class="status-dot completed"></div>
          </div>
        </div>

        <div class="metric-card satisfaction-metric">
          <div class="metric-icon-wrapper">
            <div class="metric-icon">
              <i class="i-material-symbols:sentiment-satisfied"></i>
            </div>
            <div class="metric-trend up">
              <i class="i-material-symbols:trending-up"></i>
            </div>
          </div>
          <div class="metric-content">
            <div class="metric-value">4.8</div>
            <div class="metric-label">满意度</div>
            <div class="metric-detail">基于 156 评价</div>
          </div>
          <div class="metric-stars">
            <i class="i-material-symbols:star" v-for="n in 5" :key="n" 
              :class="{ active: n <= 5 }"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要功能区域 -->
    <div class="main-content reveal">
      <!-- 左侧功能模块 -->
      <div class="modules-section-modern">
        <div class="section-header-elegant">
          <h2 class="section-title-elegant">
            <span class="title-accent"></span>
            核心功能
          </h2>
          <p class="section-desc">快速访问常用管理功能</p>
        </div>

        <div class="modules-elegant-grid">
          <div
            v-for="(module, index) in systemModules.slice(0, 6)"
            :key="module.id"
            class="module-elegant-card"
            @click="handleModuleClick(module)"
            :style="{ '--delay': index * 0.1 + 's' }"
          >
            <div class="module-bg-pattern"></div>
            <div class="module-icon-elegant" :style="{ background: module.gradient }">
              <i :class="module.icon"></i>
            </div>
            <div class="module-info">
              <h3 class="module-name">{{ module.title }}</h3>
              <p class="module-desc">{{ module.description.slice(0, 20) }}...</p>
            </div>
            <div class="module-arrow-elegant">
              <i class="i-material-symbols:arrow-forward"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧数据面板 -->
      <div class="analytics-panel">
        <!-- 趋势图表 -->
        <div class="chart-elegant-card">
          <div class="chart-header-elegant">
            <div class="chart-title-group">
              <h3>入住趋势</h3>
              <span class="chart-period">最近7天</span>
            </div>
            <div class="chart-actions">
              <button class="chart-btn"><i class="i-material-symbols:more-horiz"></i></button>
            </div>
          </div>
          <div class="chart-area">
            <div class="trend-chart">
              <div class="trend-line">
                <svg width="100%" height="120" preserveAspectRatio="none">
                  <defs>
                    <linearGradient id="trendGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                      <stop offset="0%" stop-color="#667eea" stop-opacity="0.8"/>
                      <stop offset="100%" stop-color="#667eea" stop-opacity="0.1"/>
                    </linearGradient>
                  </defs>
                  <path d="M 0 80 Q 50 60 100 45 T 200 40 T 300 35 T 400 30" 
                    stroke="#667eea" stroke-width="3" fill="none" 
                    stroke-linecap="round"/>
                  <path d="M 0 80 Q 50 60 100 45 T 200 40 T 300 35 T 400 30 L 400 120 L 0 120 Z" 
                    fill="url(#trendGradient)"/>
                </svg>
              </div>
              <div class="trend-points">
                <div class="point" style="left: 10%; bottom: 33%">65%</div>
                <div class="point" style="left: 30%; bottom: 50%">72%</div>
                <div class="point" style="left: 50%; bottom: 55%">81%</div>
                <div class="point" style="left: 70%; bottom: 58%">89%</div>
                <div class="point" style="left: 90%; bottom: 62%">92%</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 房间状态概览 -->
        <div class="status-elegant-card">
          <div class="status-header">
            <h3>房间状态</h3>
            <div class="status-live">
              <div class="live-dot"></div>
              <span>实时</span>
            </div>
          </div>
          <div class="status-grid-elegant">
            <div class="status-item-elegant occupied">
              <div class="status-icon-elegant">
                <i class="i-material-symbols:bed"></i>
              </div>
              <div class="status-info-elegant">
                <div class="status-count">156</div>
                <div class="status-name">已入住</div>
              </div>
              <div class="status-bar">
                <div class="bar-fill" style="--width: 78%"></div>
              </div>
            </div>

            <div class="status-item-elegant available">
              <div class="status-icon-elegant">
                <i class="i-material-symbols:door-open"></i>
              </div>
              <div class="status-info-elegant">
                <div class="status-count">44</div>
                <div class="status-name">可入住</div>
              </div>
              <div class="status-bar">
                <div class="bar-fill" style="--width: 22%"></div>
              </div>
            </div>

            <div class="status-item-elegant maintenance">
              <div class="status-icon-elegant">
                <i class="i-material-symbols:build"></i>
              </div>
              <div class="status-info-elegant">
                <div class="status-count">8</div>
                <div class="status-name">维修中</div>
              </div>
              <div class="status-bar">
                <div class="bar-fill" style="--width: 4%"></div>
              </div>
            </div>

            <div class="status-item-elegant cleaning">
              <div class="status-icon-elegant">
                <i class="i-material-symbols:cleaning-services"></i>
              </div>
              <div class="status-info-elegant">
                <div class="status-count">12</div>
                <div class="status-name">清洁中</div>
              </div>
              <div class="status-bar">
                <div class="bar-fill" style="--width: 6%"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部快速操作 -->
    <div class="quick-panel reveal">
      <div class="quick-header">
        <h3>快速操作</h3>
        <span class="quick-subtitle">一键访问常用功能</span>
      </div>
      <div class="quick-actions-elegant">
        <div
          v-for="(action, index) in quickActions.slice(0, 6)"
          :key="action.id"
          class="quick-action-elegant"
          @click="handleActionClick(action)"
          :style="{ '--delay': index * 0.05 + 's' }"
        >
          <div class="quick-icon" :style="{ background: action.color }">
            <i :class="action.icon"></i>
          </div>
          <span class="quick-label">{{ action.title }}</span>
        </div>
      </div>
    </div>

    <!-- SVG渐变定义 -->
    <svg width="0" height="0">
      <defs>
        <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stop-color="#667eea"/>
          <stop offset="100%" stop-color="#764ba2"/>
        </linearGradient>
      </defs>
    </svg>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import { useI18n } from 'vue-i18n'
import { useUserStore, useAppStore, usePermissionStore } from '@/store'
import { getLoginField } from '@/utils/loginData'

const router = useRouter()
const message = useMessage()
const { t, locale } = useI18n()
const userStore = useUserStore()
const appStore = useAppStore()

// 响应式数据
const currentTime = ref('')
const timeInterval = ref(null)

// 计算属性
const hotelName = computed(() => {
  return getLoginField('shop_name') ||
         userStore.shopName ||
         '酒店管理系统'
})

const todayDate = computed(() => {
  return new Date().toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
})

const systemUptime = computed(() => {
  // 模拟系统启动时间（可以从实际API获取）
  const startTime = new Date('2024-01-15T09:00:00')
  const now = new Date()
  const diffMs = now - startTime
  const days = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))

  if (days > 0) {
    return t('overview.systemInfo.uptimeFormat.daysHours', { days, hours })
  } else {
    return t('overview.systemInfo.uptimeFormat.hours', { hours })
  }
})

// 系统模块数据 - 使用计算属性支持多语言
const systemModules = computed(() => {
  // 根据当前语言返回对应的功能特性数组
  const getFeatures = (moduleKey) => {
    if (locale.value === 'zh-CN') {
      const featuresMap = {
        reservation: ['在线预订', '预订查询', '变更处理'],
        billing: ['账单查询', '消费统计', '结算处理'],
        member: ['会员档案', '积分管理', '等级升级'],
        roomManagement: ['房态监控', '清洁管理', '维修跟踪']
      }
      return featuresMap[moduleKey] || []
    } else {
      const featuresMap = {
        reservation: ['Online Booking', 'Booking Query', 'Change Processing'],
        billing: ['Bill Query', 'Consumption Statistics', 'Settlement Processing'],
        member: ['Member Profile', 'Points Management', 'Level Upgrade'],
        roomManagement: ['Room Monitoring', 'Cleaning Management', 'Maintenance Tracking']
      }
      return featuresMap[moduleKey] || []
    }
  }

  return [
    {
      id: 'reservation',
      title: t('overview.modules.reservation.title'),
      description: t('overview.modules.reservation.description'),
      icon: 'i-material-symbols:hotel',
      gradient: 'linear-gradient(135deg, #2A5DAA 0%, #1E4A8C 100%)',
      features: getFeatures('reservation'),
      status: 'active',
      path: '/reservation',
      isNew: false
    },
    {
      id: 'billing',
      title: t('overview.modules.billing.title'),
      description: t('overview.modules.billing.description'),
      icon: 'i-material-symbols:receipt-long',
      gradient: 'linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%)',
      features: getFeatures('billing'),
      status: 'active',
      path: '/billing'
    },
    {
      id: 'member',
      title: t('overview.modules.member.title'),
      description: t('overview.modules.member.description'),
      icon: 'i-material-symbols:person',
      gradient: 'linear-gradient(135deg, #EF4444 0%, #DC2626 100%)',
      features: getFeatures('member'),
      status: 'active',
      path: '/member'
    },
    {
      id: 'roomstatus',
      title: t('overview.modules.roomManagement.title'),
      description: t('overview.modules.roomManagement.description'),
      icon: 'i-material-symbols:grid-view',
      gradient: 'linear-gradient(135deg, var(--primary-color) 0%, var(--primary-color-hover) 100%)',
      features: getFeatures('roomManagement'),
      status: 'active',
      path: '/hotel/roomstatus',
      isNew: false
    }
  ]
})

// 快速操作数据 - 使用计算属性支持多语言
const quickActions = computed(() => [
  {
    id: 'quick-checkin',
    title: t('overview.quickActions.checkIn.title'),
    description: t('overview.quickActions.checkIn.description'),
    icon: 'i-material-symbols:login',
    color: 'linear-gradient(135deg, #10B981 0%, #059669 100%)',
    action: 'checkin'
  },
  {
    id: 'quick-checkout',
    title: t('overview.quickActions.checkOut.title'),
    description: t('overview.quickActions.checkOut.description'),
    icon: 'i-material-symbols:logout',
    color: 'linear-gradient(135deg, #F59E0B 0%, #D97706 100%)',
    action: 'checkout'
  },
  {
    id: 'new-reservation',
    title: t('overview.quickActions.reservation.title'),
    description: t('overview.quickActions.reservation.description'),
    icon: 'i-material-symbols:hotel',
    color: 'linear-gradient(135deg, #2A5DAA 0%, #1E4A8C 100%)',
    action: 'reservation'
  },
  {
    id: 'maintenance-report',
    title: t('overview.quickActions.maintenance.title'),
    description: t('overview.quickActions.maintenance.description'),
    icon: 'i-material-symbols:build',
    color: 'linear-gradient(135deg, #6366F1 0%, #4F46E5 100%)',
    action: 'maintenance'
  }
])

// 方法
function updateTime() {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

function getStatusText(status) {
  const statusMap = {
    active: t('common.status'),
    maintenance: t('roomStatus.cleanStatus.maintenance'),
    disabled: t('roomStatus.cleanStatus.blocked'),
    beta: 'Beta'
  }
  return statusMap[status] || t('common.status')
}

function handleModuleClick(module) {
  if (module.path) {
    router.push(module.path)
  } else {
    message.info(t('overview.messages.moduleInDevelopment', { title: module.title }))
  }
}

function handleActionClick(action) {
  switch (action.action) {
    case 'checkin':
      message.info(t('overview.messages.quickCheckinInDev'))
      break
    case 'checkout':
      message.info(t('overview.messages.quickCheckoutInDev'))
      break
    case 'clean':
      message.info(t('overview.messages.roomCleaningInDev'))
      break
    case 'audit':
      message.info(t('overview.messages.nightAuditInDev'))
      break
    default:
      message.info(t('overview.messages.featureInDev'))
  }
}

function goToRoomStatus() {
  router.push('/hotel/roomstatus')
}


// 监听主题变化并更新颜色
watch(() => appStore.currentTheme, () => {
  updateThemeColors()
}, { immediate: false })

// 生命周期
// 动效：滚动显隐 + 3D倾斜 + 鼠标跟踪
const observer = ref(null)
const tiltElements = ref([])

function setupScrollReveal() {
  const elements = document.querySelectorAll('.reveal')
  const io = new IntersectionObserver(
    entries => {
      entries.forEach((entry, index) => {
        if (entry.isIntersecting) {
          setTimeout(() => {
            entry.target.classList.add('is-visible')
          }, index * 100) // 错开动画时间
          io.unobserve(entry.target)
        }
      })
    },
    { rootMargin: '0px 0px -10% 0px', threshold: 0.1 }
  )
  elements.forEach(el => io.observe(el))
  observer.value = io
}

function setup3DTilt() {
  const tiltCards = document.querySelectorAll('[data-tilt]')
  
  tiltCards.forEach(card => {
    const onMove = (e) => {
      const rect = card.getBoundingClientRect()
      const centerX = rect.left + rect.width / 2
      const centerY = rect.top + rect.height / 2
      
      const mouseX = e.clientX - centerX
      const mouseY = e.clientY - centerY
      
      const rotateX = (mouseY / rect.height) * -10
      const rotateY = (mouseX / rect.width) * 10
      
      const px = (e.clientX - rect.left) / rect.width
      const py = (e.clientY - rect.top) / rect.height
      
      card.style.setProperty('--tilt-x', `${rotateX}deg`)
      card.style.setProperty('--tilt-y', `${rotateY}deg`)
      card.style.setProperty('--x', `${px * 100}%`)
      card.style.setProperty('--y', `${py * 100}%`)
      
      // 更新卡片发光效果
      const glow = card.querySelector('.card-glow')
      if (glow) {
        glow.style.setProperty('--x', `${px * 100}%`)
        glow.style.setProperty('--y', `${py * 100}%`)
      }
    }
    
    const onLeave = () => {
      card.style.setProperty('--tilt-x', '0deg')
      card.style.setProperty('--tilt-y', '0deg')
      card.style.removeProperty('--x')
      card.style.removeProperty('--y')
    }
    
    card.addEventListener('mousemove', onMove)
    card.addEventListener('mouseleave', onLeave)
    
    tiltElements.value.push({ card, onMove, onLeave })
  })
}

// 性能优化的滚动监听
function setupPerformanceOptimizations() {
  // 防抖处理滚动事件
  let ticking = false
  
  function updateAnimations() {
    // 这里可以添加基于滚动位置的动画
    ticking = false
  }
  
  function onScroll() {
    if (!ticking) {
      requestAnimationFrame(updateAnimations)
      ticking = true
    }
  }
  
  window.addEventListener('scroll', onScroll, { passive: true })
  
  return () => {
    window.removeEventListener('scroll', onScroll)
  }
}

// 主题颜色动态更新
function updateThemeColors() {
  const themes = [
    { key: 'default', primary: '#667eea', success: '#48bb78', warning: '#ed8936', error: '#f56565', info: '#4299e1' },
    { key: 'green', primary: '#10B981', success: '#059669', warning: '#d97706', error: '#dc2626', info: '#3b82f6' },
    { key: 'purple', primary: '#8B5CF6', success: '#10b981', warning: '#f59e0b', error: '#ef4444', info: '#6366f1' },
    { key: 'orange', primary: '#F59E0B', success: '#10b981', warning: '#d97706', error: '#ef4444', info: '#3b82f6' },
    { key: 'red', primary: '#EF4444', success: '#10b981', warning: '#f59e0b', error: '#dc2626', info: '#6366f1' },
    { key: 'indigo', primary: '#6366F1', success: '#10b981', warning: '#f59e0b', error: '#ef4444', info: '#4299e1' }
  ]
  
  const currentTheme = themes.find(theme => theme.key === appStore.currentTheme) || themes[0]
  const root = document.documentElement
  
  // 更新CSS变量
  Object.entries(currentTheme).forEach(([key, value]) => {
    if (key !== 'key') {
      root.style.setProperty(`--${key.replace(/([A-Z])/g, '-$1').toLowerCase()}-color`, value)
    }
  })
  
  // 计算hover和pressed状态的颜色
  root.style.setProperty('--primary-color-hover', adjustColor(currentTheme.primary, -10))
  root.style.setProperty('--primary-color-pressed', adjustColor(currentTheme.primary, -20))
  root.style.setProperty('--primary-color-suppl', adjustColor(currentTheme.primary, 20))
}

// 颜色调整辅助函数
function adjustColor(color, percent) {
  const hex = color.replace('#', '')
  const r = parseInt(hex.substr(0, 2), 16)
  const g = parseInt(hex.substr(2, 2), 16)
  const b = parseInt(hex.substr(4, 2), 16)
  
  const adjust = (c) => {
    c = Math.round(c + (c * percent / 100))
    return Math.max(0, Math.min(255, c))
  }
  
  const newR = adjust(r).toString(16).padStart(2, '0')
  const newG = adjust(g).toString(16).padStart(2, '0')
  const newB = adjust(b).toString(16).padStart(2, '0')
  
  return `#${newR}${newG}${newB}`
}

onMounted(() => {
  updateTime()
  timeInterval.value = setInterval(updateTime, 1000)

  // 初始化主题变量
  updateThemeColors()

  // 等待DOM渲染完成后初始化动效
  nextTick(() => {
    setupScrollReveal()
    setup3DTilt()
    setupPerformanceOptimizations()
  })
})

onUnmounted(() => {
  if (timeInterval.value) {
    clearInterval(timeInterval.value)
  }
  if (observer.value) {
    observer.value.disconnect()
  }
  
  // 清理3D倾斜事件监听器
  tiltElements.value.forEach(({ card, onMove, onLeave }) => {
    card.removeEventListener('mousemove', onMove)
    card.removeEventListener('mouseleave', onLeave)
  })
  tiltElements.value = []
})
</script>

<style scoped>
/* ========== 高端现代化设计系统 ========== */
:root {
  /* 主色调优化 - 更加精致的蓝紫色系 */
  --primary: #6366f1;
  --primary-light: #8b9eff;
  --primary-dark: #4f46e5;
  --secondary: #8b5cf6;
  --accent: #ec4899;
  
  /* 功能色彩 */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;
  
  /* 中性色调 - 更温暖的灰色系 */
  --neutral-50: #fefefe;
  --neutral-100: #f8fafc;
  --neutral-200: #f1f5f9;
  --neutral-300: #e2e8f0;
  --neutral-400: #94a3b8;
  --neutral-500: #64748b;
  --neutral-600: #475569;
  --neutral-700: #334155;
  --neutral-800: #1e293b;
  --neutral-900: #0f172a;
  
  /* 表面和背景色 */
  --surface: rgba(255, 255, 255, 0.85);
  --surface-hover: rgba(255, 255, 255, 0.95);
  --surface-glass: rgba(255, 255, 255, 0.1);
  --surface-elevated: rgba(255, 255, 255, 0.9);
  --surface-soft: rgba(248, 250, 252, 0.8);
  
  /* 阴影系统 */
  --shadow-xs: 0 1px 2px rgba(15, 23, 42, 0.04);
  --shadow-sm: 0 2px 8px rgba(15, 23, 42, 0.06);
  --shadow-md: 0 4px 20px rgba(15, 23, 42, 0.08);
  --shadow-lg: 0 8px 32px rgba(15, 23, 42, 0.10);
  --shadow-xl: 0 12px 48px rgba(15, 23, 42, 0.12);
  
  /* 边框半径 */
  --border-radius: 16px;
  --border-radius-sm: 8px;
  --border-radius-lg: 24px;
  --border-radius-xl: 32px;
  
  /* 过渡动画 */
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ========== 全局布局 ========== */
.dashboard-modern {
  min-height: 100vh;
  background: 
    linear-gradient(135deg, 
      var(--neutral-50) 0%, 
      var(--neutral-100) 25%, 
      var(--neutral-200) 50%, 
      var(--neutral-100) 75%, 
      var(--neutral-50) 100%
    );
  position: relative;
  overflow-x: hidden;
  padding: 2rem;
}

.dashboard-modern::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.06) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(236, 72, 153, 0.04) 0%, transparent 50%),
    linear-gradient(135deg, rgba(248, 250, 252, 0.5), rgba(241, 245, 249, 0.3));
  pointer-events: none;
  z-index: -1;
}

.dashboard-modern::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    repeating-linear-gradient(
      90deg,
      transparent,
      transparent 98px,
      rgba(99, 102, 241, 0.02) 100px
    ),
    repeating-linear-gradient(
      180deg,
      transparent,
      transparent 98px,
      rgba(139, 92, 246, 0.02) 100px
    );
  pointer-events: none;
  z-index: -1;
}

/* ========== 顶部英雄区域 ========== */
.hero-modern {
  margin-bottom: 3rem;
  position: relative;
}

.hero-content-modern {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2.5rem 3rem;
  background: var(--surface);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-md);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  transition: var(--transition);
}

.hero-content-modern:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.hero-content-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -50%;
  width: 200%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 4s infinite;
}

.hero-content-modern::after {
  content: '';
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at var(--x, 50%) var(--y, 50%), rgba(99, 102, 241, 0.025), transparent 70%);
  opacity: 0;
  transition: opacity var(--transition);
  pointer-events: none;
}

.hero-content-modern:hover::after {
  opacity: 1;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.brand-section {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex: 1;
}

.brand-logo {
  position: relative;
  width: 72px;
  height: 72px;
  border-radius: var(--border-radius-lg);
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2.25rem;
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  transition: var(--transition);
}

.brand-logo:hover {
  transform: scale(1.05) rotate(5deg);
  box-shadow: var(--shadow-xl);
}

.logo-gradient {
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, var(--accent), transparent, var(--primary-light));
  opacity: 0.6;
  animation: rotate 6s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.brand-text {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  flex: 1;
}

.brand-title {
  font-size: 2.25rem;
  font-weight: 800;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 1rem;
  line-height: 1.2;
}

.hotel-name {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.hotel-name::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  transform: scaleX(0);
  transition: transform var(--transition);
  transform-origin: left;
}

.brand-title:hover .hotel-name::after {
  transform: scaleX(1);
}

.page-title {
  color: var(--neutral-700);
  font-weight: 300;
  opacity: 0.8;
}

.brand-subtitle {
  color: var(--neutral-500);
  font-size: 1.125rem;
  font-weight: 500;
  margin: 0;
  letter-spacing: 0.5px;
  opacity: 0.9;
}

.hero-stats {
  display: flex;
  gap: 1rem;
  flex-shrink: 0;
}

.stat-pill {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50px;
  backdrop-filter: blur(10px);
  font-weight: 600;
  color: var(--neutral-700);
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.stat-pill::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
  opacity: 0;
  transition: opacity var(--transition);
}

.stat-pill:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: var(--shadow-md);
  background: var(--surface-hover);
}

.stat-pill:hover::before {
  opacity: 1;
}

.stat-pill i {
  font-size: 1.25rem;
  transition: var(--transition);
}

.stat-pill:hover i {
  transform: scale(1.1);
}

.time-pill {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.12), rgba(79, 70, 229, 0.08));
  border-color: rgba(99, 102, 241, 0.25);
  color: var(--primary);
}

.time-pill:hover {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.18), rgba(79, 70, 229, 0.12));
  border-color: rgba(99, 102, 241, 0.35);
}

.date-pill {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.12), rgba(5, 150, 105, 0.08));
  border-color: rgba(16, 185, 129, 0.25);
  color: var(--success);
}

.date-pill:hover {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.18), rgba(5, 150, 105, 0.12));
  border-color: rgba(16, 185, 129, 0.35);
}

/* ========== 顶部区域特殊效果 ========== */
.hero-modern::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(from 0deg, transparent, rgba(99, 102, 241, 0.02), transparent, rgba(139, 92, 246, 0.015), transparent);
  animation: rotate 20s linear infinite;
  z-index: -1;
}

/* 品牌标题文字特效 */
.brand-title {
  position: relative;
}

.brand-title::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(99, 102, 241, 0.08), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.brand-title:hover::before {
  transform: translateX(100%);
}

/* 时间日期胶囊增强效果 */
.stat-pill::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.6), transparent);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
}

.stat-pill:hover::after {
  width: 100%;
  height: 100%;
}

/* Logo 脉冲效果 */
.brand-logo::after {
  content: '';
  position: absolute;
  inset: -4px;
  border-radius: var(--border-radius-lg);
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  opacity: 0;
  animation: logoPulse 3s ease-in-out infinite;
  z-index: -1;
}

@keyframes logoPulse {
  0%, 100% {
    opacity: 0;
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(1.1);
  }
}

/* 微交互优化 */
.hero-content-modern {
  transform-style: preserve-3d;
}

.brand-section:hover .brand-logo {
  animation: bounce 0.6s ease;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0) scale(1);
  }
  40% {
    transform: translateY(-8px) scale(1.05);
  }
  60% {
    transform: translateY(-4px) scale(1.02);
  }
}

/* 实时状态指示器 */
.hero-stats::before {
  content: '';
  position: absolute;
  top: -10px;
  right: -10px;
  width: 12px;
  height: 12px;
  background: var(--success);
  border-radius: 50%;
  box-shadow: 0 0 0 3px rgba(74, 222, 128, 0.3);
  animation: pulse 2s infinite;
}

/* 焦点可访问性增强 */
.brand-logo:focus-visible,
.stat-pill:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 3px;
}

/* 深色模式适配预备 */
@media (prefers-color-scheme: dark) {
  .hero-content-modern {
    background: rgba(30, 41, 59, 0.85);
    border-color: rgba(71, 85, 105, 0.3);
  }
  
  .stat-pill {
    background: rgba(30, 41, 59, 0.6);
    border-color: rgba(71, 85, 105, 0.4);
    color: var(--neutral-200);
  }
  
  .time-pill {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.2), rgba(79, 70, 229, 0.15));
    border-color: rgba(99, 102, 241, 0.4);
    color: var(--primary-light);
  }
  
  .date-pill {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(5, 150, 105, 0.15));
    border-color: rgba(16, 185, 129, 0.4);
    color: #34d399;
  }
}

/* 减少动画偏好设置 */
@media (prefers-reduced-motion: reduce) {
  .logo-gradient,
  .hero-content-modern::before,
  .brand-logo::after {
    animation: none;
  }
  
  .hero-content-modern:hover,
  .brand-logo:hover,
  .stat-pill:hover {
    transform: none;
  }
}

/* ========== 核心指标看板 ========== */
.metrics-panel {
  margin-bottom: 3rem;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  max-width: 1400px;
  margin: 0 auto;
}

.metric-card {
  background: var(--surface);
  border-radius: var(--border-radius-lg);
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: var(--shadow-md);
  backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  min-height: 180px;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  transform: scaleX(0);
  transition: var(--transition);
  transform-origin: left;
}

.metric-card::after {
  content: '';
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at var(--x, 50%) var(--y, 50%), rgba(255, 255, 255, 0.1), transparent 60%);
  opacity: 0;
  transition: opacity var(--transition);
  pointer-events: none;
}

.metric-card:hover {
  transform: translateY(-8px) rotateX(2deg);
  box-shadow: 
    var(--shadow-xl),
    0 0 40px rgba(102, 126, 234, 0.15);
  background: var(--surface-hover);
}

.metric-card:hover::before {
  transform: scaleX(1);
}

.metric-card:hover::after {
  opacity: 1;
}

.primary-metric {
  border-left: 4px solid var(--primary);
}

.revenue-metric {
  border-left: 4px solid var(--success);
}

.service-metric {
  border-left: 4px solid var(--warning);
}

.satisfaction-metric {
  border-left: 4px solid var(--accent);
}

.metric-icon-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.metric-icon {
  width: 56px;
  height: 56px;
  border-radius: var(--border-radius);
  background: linear-gradient(135deg, var(--primary), var(--primary-light));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.75rem;
  box-shadow: var(--shadow-sm);
}

.revenue-metric .metric-icon {
  background: linear-gradient(135deg, var(--success), #22c55e);
}

.service-metric .metric-icon {
  background: linear-gradient(135deg, var(--warning), #f59e0b);
}

.satisfaction-metric .metric-icon {
  background: linear-gradient(135deg, var(--accent), #ec4899);
}

.metric-trend {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.125rem;
}

.metric-trend.up {
  background: rgba(74, 222, 128, 0.1);
  color: var(--success);
}

.metric-trend.neutral {
  background: rgba(251, 191, 36, 0.1);
  color: var(--warning);
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--neutral-800);
  line-height: 1;
  margin-bottom: 0.5rem;
  font-family: 'SF Mono', 'Monaco', monospace;
}

.metric-label {
  font-size: 1rem;
  font-weight: 600;
  color: var(--neutral-600);
  margin-bottom: 0.25rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metric-detail {
  font-size: 0.875rem;
  color: var(--neutral-500);
  font-weight: 500;
}

/* 迷你图表组件 */
.metric-chart {
  height: 40px;
  display: flex;
  align-items: end;
  justify-content: center;
}

.mini-bars {
  display: flex;
  gap: 4px;
  height: 100%;
  align-items: end;
}

.mini-bars .bar {
  width: 6px;
  background: linear-gradient(to top, var(--primary), var(--primary-light));
  border-radius: 3px;
  height: var(--height);
  transition: var(--transition);
}

.metric-card:hover .mini-bars .bar {
  background: linear-gradient(to top, var(--secondary), var(--accent));
}

.metric-progress {
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-ring {
  position: relative;
  width: 60px;
  height: 60px;
}

.progress-ring svg {
  transform: rotate(-90deg);
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.875rem;
  font-weight: 700;
  color: var(--primary);
}

.metric-status {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-dot.processing {
  background: var(--warning);
}

.status-dot.completed {
  background: var(--success);
}

.metric-stars {
  display: flex;
  gap: 4px;
  justify-content: center;
  font-size: 1.25rem;
}

.metric-stars i {
  color: var(--neutral-300);
  transition: var(--transition-fast);
}

.metric-stars i.active {
  color: #fbbf24;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* ========== 主要内容区域 ========== */
.main-content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto 3rem;
}

/* ========== 功能模块区域 ========== */
.modules-section-modern {
  background: var(--surface);
  border-radius: var(--border-radius-lg);
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: var(--shadow-md);
  backdrop-filter: blur(20px);
}

.section-header-elegant {
  margin-bottom: 2rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding-bottom: 1rem;
}

.section-title-elegant {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--neutral-800);
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  position: relative;
}

.title-accent {
  width: 4px;
  height: 24px;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border-radius: 2px;
}

.section-desc {
  color: var(--neutral-500);
  font-size: 0.875rem;
  margin: 0;
  font-weight: 500;
}

.modules-elegant-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.25rem;
}

.module-elegant-card {
  position: relative;
  padding: 1.75rem;
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  overflow: hidden;
  backdrop-filter: blur(10px);
  animation: slideInUp var(--transition-slow) ease-out;
  animation-delay: var(--delay);
  animation-fill-mode: both;
  min-height: 140px;
  display: flex;
  flex-direction: column;
}

.module-elegant-card::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
  opacity: 0;
  transition: opacity var(--transition);
}

.module-elegant-card:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow: 
    var(--shadow-lg),
    0 0 30px rgba(99, 102, 241, 0.08);
  background: rgba(255, 255, 255, 0.8);
  border-color: rgba(99, 102, 241, 0.15);
}

.module-elegant-card:hover::before {
  opacity: 1;
}


.module-bg-pattern {
  position: absolute;
  top: 0;
  right: 0;
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, var(--primary), var(--secondary));
  opacity: 0.1;
  border-radius: 0 var(--border-radius) 0 50px;
}

.module-icon-elegant {
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  margin-bottom: 1rem;
  box-shadow: var(--shadow-sm);
}

.module-info {
  flex: 1;
  margin-bottom: 1rem;
}

.module-name {
  font-size: 1rem;
  font-weight: 700;
  color: var(--neutral-800);
  margin: 0 0 0.25rem 0;
  line-height: 1.3;
}

.module-desc {
  font-size: 0.75rem;
  color: var(--neutral-500);
  margin: 0;
  line-height: 1.4;
}

.module-arrow-elegant {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  color: var(--neutral-400);
  transition: var(--transition);
}

.module-elegant-card:hover .module-arrow-elegant {
  background: var(--primary);
  color: white;
  transform: translate(2px, -2px);
}

/* ========== 分析面板 ========== */
.analytics-panel {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.chart-elegant-card,
.status-elegant-card {
  background: var(--surface);
  border-radius: var(--border-radius-lg);
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: var(--shadow-md);
  backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
  transition: var(--transition);
}

.chart-elegant-card::before,
.status-elegant-card::before {
  content: '';
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at var(--x, 50%) var(--y, 50%), rgba(102, 126, 234, 0.05), transparent 70%);
  opacity: 0;
  transition: opacity var(--transition);
  pointer-events: none;
}

.chart-elegant-card:hover,
.status-elegant-card:hover {
  transform: translateY(-4px);
  box-shadow: 
    var(--shadow-lg),
    0 0 25px rgba(99, 102, 241, 0.08);
}

.chart-elegant-card:hover::before,
.status-elegant-card:hover::before {
  opacity: 1;
}

.chart-header-elegant {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
}

.chart-title-group h3 {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--neutral-800);
  margin: 0 0 0.25rem 0;
}

.chart-period {
  font-size: 0.75rem;
  color: var(--neutral-500);
  background: rgba(0, 0, 0, 0.05);
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-weight: 500;
}

.chart-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 50%;
  cursor: pointer;
  color: var(--neutral-400);
  transition: var(--transition);
}

.chart-btn:hover {
  background: var(--primary);
  color: white;
}

.chart-area {
  height: 120px;
  position: relative;
}

.trend-chart {
  width: 100%;
  height: 100%;
  position: relative;
}

.trend-points {
  position: absolute;
  inset: 0;
}

.point {
  position: absolute;
  background: var(--primary);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  transform: translateX(-50%);
  box-shadow: var(--shadow-sm);
  opacity: 0;
  transition: var(--transition);
}

.chart-elegant-card:hover .point {
  opacity: 1;
}

/* ========== 状态面板 ========== */
.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.status-header h3 {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--neutral-800);
  margin: 0;
}

.status-live {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: var(--success);
  font-weight: 600;
}

.live-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--success);
  animation: pulse 1.5s infinite;
}

.status-grid-elegant {
  display: grid;
  gap: 1rem;
}

.status-item-elegant {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.5);
  border-radius: var(--border-radius-sm);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: var(--transition);
}

.status-item-elegant:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateX(4px);
}

.status-icon-elegant {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: white;
}

.status-item-elegant.occupied .status-icon-elegant {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.status-item-elegant.available .status-icon-elegant {
  background: linear-gradient(135deg, var(--success), #22c55e);
}

.status-item-elegant.maintenance .status-icon-elegant {
  background: linear-gradient(135deg, var(--info), #3b82f6);
}

.status-item-elegant.cleaning .status-icon-elegant {
  background: linear-gradient(135deg, var(--warning), #f59e0b);
}

.status-info-elegant {
  flex: 1;
}

.status-count {
  font-size: 1.5rem;
  font-weight: 800;
  color: var(--neutral-800);
  line-height: 1;
  margin-bottom: 0.25rem;
}

.status-name {
  font-size: 0.875rem;
  color: var(--neutral-600);
  font-weight: 500;
}

.status-bar {
  width: 60px;
  height: 4px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  width: var(--width);
  border-radius: 2px;
  transition: var(--transition-slow);
}

/* 数据概览紧凑版 */
.data-overview-section {
  padding: 0 1rem;
  margin-bottom: 2rem;
}

.charts-grid-compact {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1rem;
  max-width: 1200px;
  margin: 0 auto;
}

.chart-card-compact {
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  height: 100%;
  min-height: 220px;
  display: flex;
  flex-direction: column;
}

.chart-card-compact.wide {
  grid-column: 1 / -1;
  min-height: 280px;
}

.chart-card-compact:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.chart-header-compact {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.chart-header-compact h3 {
  font-size: 1rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.chart-period-compact {
  background: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  border: 1px solid rgba(var(--primary-color-rgb), 0.2);
}

.chart-period-compact.live {
  background: rgba(72, 187, 120, 0.1);
  color: #059669;
  border-color: rgba(72, 187, 120, 0.2);
  animation: pulse 2s ease-in-out infinite;
}

.chart-content-compact {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  min-height: 120px;
}

/* 紧凑版柱状图 */
.occupancy-chart-compact {
  width: 100%;
  height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.chart-bars-compact {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  height: 80px;
  gap: 0.5rem;
}

.bar-compact {
  flex: 1;
  background: linear-gradient(135deg, var(--color), var(--color));
  border-radius: 3px 3px 0 0;
  height: var(--height);
  transition: all 0.3s ease;
  cursor: pointer;
  min-height: 8px;
}

.bar-compact:hover {
  transform: translateY(-2px);
  filter: brightness(1.1);
}

.chart-labels-compact {
  display: flex;
  justify-content: space-between;
  margin-top: 0.5rem;
  font-size: 0.7rem;
  color: var(--text-secondary);
  font-weight: 500;
}

/* 紧凑版房间状态 */
.room-status-grid-compact {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
  width: 100%;
}

.status-item-compact {
  text-align: center;
  padding: 1rem 0.75rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  border: 1px solid;
}

.status-item-compact:hover {
  transform: translateY(-2px);
}

.status-item-compact.occupied {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.1));
  border-color: #ef4444;
  color: #991b1b;
}

.status-item-compact.available {
  background: linear-gradient(135deg, rgba(72, 187, 120, 0.1), rgba(5, 150, 105, 0.1));
  border-color: #10b981;
  color: #065f46;
}

.status-item-compact.maintenance {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(79, 70, 229, 0.1));
  border-color: #6366f1;
  color: #3730a3;
}

.status-item-compact.cleaning {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(217, 119, 6, 0.1));
  border-color: #f59e0b;
  color: #92400e;
}

.status-number {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.status-label {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  opacity: 0.8;
}

/* 收入统计紧凑版 */
.revenue-total-compact {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.total-amount-compact {
  font-size: 1.25rem;
  font-weight: 800;
  color: var(--text-primary);
  font-family: 'SF Mono', monospace;
}

.growth-rate {
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 600;
}

.growth-rate.positive {
  background: rgba(72, 187, 120, 0.1);
  color: #059669;
  border: 1px solid rgba(72, 187, 120, 0.2);
}

.revenue-stats-compact {
  display: flex;
  gap: 1rem;
  width: 100%;
  justify-content: space-between;
}

.revenue-item-compact {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 0.5rem;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.revenue-item-compact:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateY(-2px);
}

.revenue-icon-compact {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-color-hover));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
}

.revenue-details-compact {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.revenue-label {
  font-size: 0.7rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.revenue-amount {
  font-size: 1rem;
  font-weight: 700;
  color: var(--text-primary);
  font-family: 'SF Mono', monospace;
}

.revenue-change {
  font-size: 0.7rem;
  font-weight: 600;
  padding: 0.125rem 0.25rem;
  border-radius: 3px;
}

.revenue-change.positive {
  background: rgba(72, 187, 120, 0.1);
  color: #059669;
}

.revenue-change.negative {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
}

.revenue-progress-compact {
  width: 100%;
  height: 3px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  overflow: hidden;
  margin-top: 0.25rem;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-color-hover));
  border-radius: 2px;
  width: var(--progress);
  transition: all 0.5s ease;
}

/* ========== 功能模块现代化样式 ========== */
.modules-section {
  padding: 0 2rem;
  margin-bottom: 4rem;
}

.modules-grid-modern {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  align-items: stretch; /* 确保所有卡片高度相等 */
}

.module-card-modern {
  position: relative;
  border-radius: var(--border-radius-xl);
  padding: 2.5rem;
  cursor: pointer;
  transition: all var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  height: 100%; /* 填满网格高度 */
  min-height: 320px; /* 统一最小高度 */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.module-card-modern:hover {
  transform: translateY(-12px) rotateX(2deg);
  box-shadow: 0 25px 50px rgba(31, 38, 135, 0.25);
}

.module-background {
  position: absolute;
  inset: 0;
  opacity: 0.1;
  transition: opacity var(--transition-normal);
}

.module-card-modern:hover .module-background {
  opacity: 0.15;
}

.bg-pattern {
  position: absolute;
  inset: 0;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 25%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 25%);
}

.bg-gradient {
  position: absolute;
  inset: 0;
}

.module-header-modern {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
}

.module-icon-modern {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: var(--border-radius-lg);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(var(--blur-sm));
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  color: var(--primary-color);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: all var(--transition-normal);
}

.module-card-modern:hover .module-icon-modern {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.icon-orbit {
  position: absolute;
  inset: -15px;
  border: 2px solid rgba(102, 126, 234, 0.2);
  border-radius: var(--border-radius-lg);
  border-top-color: var(--primary-color);
  animation: rotate 3s linear infinite;
}

@keyframes rotate {
  to { transform: rotate(360deg); }
}

.module-badge-modern {
  position: relative;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  border-radius: 50px;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.badge-pulse {
  position: absolute;
  inset: 0;
  border-radius: 50px;
  background: rgba(255, 107, 107, 0.6);
  animation: pulse 1.5s ease-in-out infinite;
}

/* 新增：卡片内容对齐优化 */
.module-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  justify-content: space-between; /* 均匀分布内容 */
  min-height: 150px; /* 保证最小高度 */
}

.module-title-modern {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  line-height: 1.3;
}

.module-desc-modern {
  font-size: 1rem;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.6;
  flex: 1; /* 自动填满剩余空间 */
}

.module-features-modern {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-top: auto; /* 推到底部 */
}

.action-content-modern {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  justify-content: center; /* 垂直居中 */
  min-height: 120px;
}

.action-title-modern {
  font-size: 1.375rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.action-desc-modern {
  font-size: 1rem;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.5;
  flex: 1;
}

/* 保证图表标题高度一致 */
.chart-header-modern {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
  min-height: 60px; /* 统一标题区域高度 */
}

.feature-pill {
  position: relative;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(var(--blur-sm));
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: 50px;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  transition: all var(--transition-normal);
  overflow: hidden;
}

.feature-pill:hover {
  transform: translateY(-2px);
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.3);
  color: var(--primary-color);
}

.pill-glow {
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, transparent, rgba(102, 126, 234, 0.2), transparent);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.feature-pill:hover .pill-glow {
  opacity: 1;
}

.module-footer-modern {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

.status-indicator.active .status-dot {
  background: var(--success-color);
  box-shadow: 0 0 10px rgba(72, 187, 120, 0.6);
}

.action-arrow {
  position: relative;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: var(--primary-color);
  transition: all var(--transition-normal);
}

.module-card-modern:hover .action-arrow {
  transform: translateX(5px);
  background: var(--primary-color);
  color: white;
}

.arrow-trail {
  position: absolute;
  inset: -5px;
  border-radius: 50%;
  background: conic-gradient(from 0deg, transparent, var(--primary-color), transparent);
  opacity: 0;
  animation: trail 1s ease-in-out infinite;
}

.module-card-modern:hover .arrow-trail {
  opacity: 0.6;
}

@keyframes trail {
  to { transform: rotate(360deg); }
}

.card-glow {
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at var(--x, 50%) var(--y, 50%), rgba(102, 126, 234, 0.15), transparent 50%);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.module-card-modern:hover .card-glow {
  opacity: 1;
}

.hover-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.module-card-modern:hover .hover-overlay {
  opacity: 1;
}

/* ========== 数据概览现代化样式 ========== */
.data-overview-section {
  padding: 0 2rem;
  margin-bottom: 4rem;
}

.charts-grid-modern {
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* 固定2列布局 */
  grid-auto-rows: minmax(400px, auto); /* 统一最小高度 */
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  align-items: stretch;
}

.chart-card-modern {
  border-radius: var(--border-radius-xl);
  padding: 2.5rem;
  transition: all var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  height: 100%; /* 填满网格高度 */
  display: flex;
  flex-direction: column;
}

.chart-card-modern.wide {
  grid-column: 1 / -1; /* 占满宽度 */
  min-height: 450px;
}

.chart-card-modern:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(31, 38, 135, 0.25);
}

.chart-header-modern {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.chart-title-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.chart-title-group h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.chart-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

.indicator-dot.success {
  background: var(--success-color);
  box-shadow: 0 0 10px rgba(72, 187, 120, 0.6);
}

.indicator-dot.info {
  background: var(--info-color);
  box-shadow: 0 0 10px rgba(66, 153, 225, 0.6);
}

.indicator-dot.pulse {
  animation: pulse 1s ease-in-out infinite;
}

.chart-period-modern {
  padding: 0.5rem 1rem;
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 50px;
  color: var(--primary-color);
  font-size: 0.875rem;
  font-weight: 600;
  backdrop-filter: blur(var(--blur-sm));
}

.revenue-total {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.total-amount {
  font-size: 2rem;
  font-weight: 800;
  color: var(--text-primary);
  font-family: 'SF Mono', 'Monaco', monospace;
}

.growth-rate {
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
  font-size: 0.875rem;
  font-weight: 600;
}

.growth-rate.positive {
  background: rgba(72, 187, 120, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(72, 187, 120, 0.2);
}

.growth-rate.negative {
  background: rgba(245, 101, 101, 0.1);
  color: var(--error-color);
  border: 1px solid rgba(245, 101, 101, 0.2);
}

.chart-content-modern {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  min-height: 250px; /* 统一内容区域高度 */
}

/* 入住率趋势图现代化 */
.occupancy-chart-modern {
  width: 100%;
  height: 250px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.chart-bars-modern {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  height: 200px;
  gap: 1rem;
  padding: 0 1rem;
}

.bar-modern {
  flex: 1;
  background: linear-gradient(135deg, var(--color), var(--color));
  border-radius: 8px 8px 0 0;
  position: relative;
  height: var(--height);
  transition: all var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
}

.bar-modern:hover {
  transform: translateY(-5px) scale(1.05);
  filter: brightness(1.1);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.bar-glow {
  position: absolute;
  inset: -2px;
  background: linear-gradient(135deg, var(--color), transparent);
  border-radius: 10px 10px 0 0;
  filter: blur(8px);
  opacity: 0.6;
  z-index: -1;
}

.bar-tooltip {
  position: absolute;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: var(--border-radius-sm);
  font-size: 0.875rem;
  font-weight: 600;
  opacity: 0;
  transition: all var(--transition-normal);
  pointer-events: none;
}

.bar-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.8);
}

.bar-modern:hover .bar-tooltip {
  opacity: 1;
  transform: translateX(-50%) translateY(-5px);
}

.chart-labels-modern {
  display: flex;
  justify-content: space-between;
  margin-top: 1rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
  padding: 0 1rem;
}

/* 饼图现代化 */
.pie-chart-modern {
  width: 100%;
  height: 250px;
}

.pie-container-modern {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  gap: 3rem;
}

.pie-visual-modern {
  position: relative;
  width: 200px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pie-svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.pie-background {
  fill: none;
  stroke: rgba(226, 232, 240, 0.3);
  stroke-width: 8;
}

.pie-slice {
  fill: none;
  stroke-width: 8;
  transition: all var(--transition-normal);
  cursor: pointer;
}

.pie-slice:hover {
  stroke-width: 12;
  filter: brightness(1.1);
}

.pie-slice.slice-1 {
  stroke: var(--primary-color);
}

.pie-slice.slice-2 {
  stroke: var(--success-color);
}

.pie-slice.slice-3 {
  stroke: var(--warning-color);
}

.pie-slice.slice-4 {
  stroke: var(--error-color);
}

.pie-center {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.total-rooms {
  font-size: 2rem;
  font-weight: 800;
  color: var(--text-primary);
}

.total-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.pie-legend-modern {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-width: 250px;
}

.legend-item-modern {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.5);
  border-radius: var(--border-radius-md);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all var(--transition-normal);
  font-size: 0.875rem;
  color: var(--text-secondary);
  cursor: pointer;
}

.legend-item-modern:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateX(5px);
}

.color-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.color-dot.color-1 { background: var(--primary-color); }
.color-dot.color-2 { background: var(--success-color); }
.color-dot.color-3 { background: var(--warning-color); }
.color-dot.color-4 { background: var(--error-color); }

/* 收入统计现代化 */
.revenue-stats-modern {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  width: 100%;
}

.revenue-item {
  position: relative;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(var(--blur-sm));
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: var(--border-radius-lg);
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: all var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  overflow: hidden;
}

.revenue-item:hover {
  transform: translateY(-4px) translateX(5px);
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.1);
}

.revenue-icon {
  position: relative;
  width: 60px;
  height: 60px;
  border-radius: var(--border-radius-md);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-color-hover));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.75rem;
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.icon-pulse {
  position: absolute;
  inset: -10px;
  border: 2px solid rgba(102, 126, 234, 0.3);
  border-radius: var(--border-radius-md);
  animation: pulse 2s ease-in-out infinite;
}

.revenue-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.revenue-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.revenue-amount {
  font-size: 1.75rem;
  font-weight: 800;
  color: var(--text-primary);
  font-family: 'SF Mono', 'Monaco', monospace;
}

.revenue-progress {
  width: 100%;
  height: 6px;
  background: rgba(226, 232, 240, 0.4);
  border-radius: 3px;
  overflow: hidden;
  position: relative;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--color), var(--color));
  border-radius: 3px;
  width: var(--progress);
  position: relative;
  transition: all var(--transition-slow) ease-out;
}

.progress-bar::after {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.revenue-change {
  font-size: 0.875rem;
  font-weight: 700;
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius-sm);
}

.revenue-change.positive {
  background: rgba(72, 187, 120, 0.1);
  color: var(--success-color);
}

.revenue-change.negative {
  background: rgba(245, 101, 101, 0.1);
  color: var(--error-color);
}

/* 房间状态现代化 */
.room-status-grid-modern {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  width: 100%;
}

.status-card {
  position: relative;
  padding: 2rem;
  border-radius: var(--border-radius-lg);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  text-align: center;
  transition: all var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  overflow: hidden;
  border: 2px solid;
}

.status-card:hover {
  transform: translateY(-6px) scale(1.03);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.status-card.occupied {
  background: linear-gradient(135deg, rgba(245, 101, 101, 0.1), rgba(220, 38, 38, 0.1));
  border-color: var(--error-color);
  color: #991b1b;
}

.status-card.available {
  background: linear-gradient(135deg, rgba(72, 187, 120, 0.1), rgba(34, 197, 94, 0.1));
  border-color: var(--success-color);
  color: #065f46;
}

.status-card.cleaning {
  background: linear-gradient(135deg, rgba(237, 137, 54, 0.1), rgba(217, 119, 6, 0.1));
  border-color: var(--warning-color);
  color: #92400e;
}

.status-card.maintenance {
  background: linear-gradient(135deg, rgba(66, 153, 225, 0.1), rgba(59, 130, 246, 0.1));
  border-color: var(--info-color);
  color: #1e40af;
}

.status-icon {
  position: relative;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
}

.status-pulse {
  position: absolute;
  inset: -10px;
  border: 2px solid currentColor;
  border-radius: 50%;
  opacity: 0.3;
  animation: pulse 2s ease-in-out infinite;
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  align-items: center;
}

.status-number {
  font-size: 2.5rem;
  font-weight: 800;
  line-height: 1;
}

.status-label {
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  opacity: 0.8;
}

.status-percentage {
  font-size: 0.75rem;
  font-weight: 700;
  opacity: 0.6;
}

.status-decoration {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: currentColor;
  opacity: 0.2;
  animation: float 3s ease-in-out infinite;
}

/* ========== 快速操作面板 ========== */
.quick-panel {
  max-width: 1400px;
  margin: 0 auto 3rem;
}

.quick-header {
  text-align: center;
  margin-bottom: 2rem;
}

.quick-header h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--neutral-800);
  margin: 0 0 0.5rem 0;
}

.quick-subtitle {
  color: var(--neutral-500);
  font-size: 1rem;
  font-weight: 500;
}

.quick-actions-elegant {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 1rem;
  padding: 0 1rem;
}

.quick-action-elegant {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  padding: 1.5rem;
  background: var(--surface);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  backdrop-filter: blur(20px);
  text-align: center;
  animation: slideInUp var(--transition-slow) ease-out;
  animation-delay: var(--delay);
  animation-fill-mode: both;
  position: relative;
  overflow: hidden;
  min-height: 120px;
}

.quick-action-elegant::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.04), transparent);
  opacity: 0;
  transition: opacity var(--transition);
}

.quick-action-elegant:hover {
  transform: translateY(-6px) scale(1.05);
  box-shadow: 
    var(--shadow-lg),
    0 0 25px rgba(99, 102, 241, 0.12);
  background: var(--surface-hover);
  border-color: rgba(99, 102, 241, 0.25);
}

.quick-action-elegant:hover::before {
  opacity: 1;
}

.quick-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  box-shadow: var(--shadow-sm);
  position: relative;
  z-index: 1;
  transition: var(--transition);
}

.quick-action-elegant:hover .quick-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: var(--shadow-md);
}

.quick-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--neutral-700);
  margin: 0;
}

/* ========== 响应式设计 ========== */
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .analytics-panel {
    order: -1;
  }
  
  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .modules-elegant-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .quick-actions-elegant {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .hero-content-modern {
    padding: 2rem;
  }
  
  .brand-title {
    font-size: 2rem;
  }
  
  .brand-logo {
    width: 64px;
    height: 64px;
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .dashboard-modern {
    padding: 1rem;
  }
  
  .hero-content-modern {
    flex-direction: column;
    gap: 2rem;
    padding: 2rem 1.5rem;
    text-align: center;
  }
  
  .brand-section {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .brand-title {
    font-size: 1.75rem;
    justify-content: center;
  }
  
  .brand-subtitle {
    font-size: 1rem;
    text-align: center;
  }
  
  .hero-stats {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .stat-pill {
    padding: 0.875rem 1.25rem;
    font-size: 0.875rem;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .modules-elegant-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-actions-elegant {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .status-grid-elegant {
    gap: 0.75rem;
  }
}

@media (max-width: 480px) {
  .dashboard-modern {
    padding: 0.75rem;
  }
  
  .hero-content-modern {
    padding: 1.5rem 1rem;
    gap: 1.5rem;
  }
  
  .brand-title {
    font-size: 1.5rem;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .brand-logo {
    width: 56px;
    height: 56px;
    font-size: 1.75rem;
  }
  
  .brand-subtitle {
    font-size: 0.875rem;
  }
  
  .hero-stats {
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
  }
  
  .stat-pill {
    padding: 0.75rem 1rem;
    font-size: 0.8rem;
    min-width: 140px;
    justify-content: center;
  }
  
  .stat-pill i {
    font-size: 1rem;
  }
  
  .metric-card {
    padding: 1.5rem;
    min-height: 160px;
  }
  
  .metric-value {
    font-size: 2rem;
  }
  
  .modules-section-modern,
  .chart-elegant-card,
  .status-elegant-card {
    padding: 1.5rem;
  }
  
  .quick-actions-elegant {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .quick-action-elegant {
    padding: 1.25rem;
    min-height: 100px;
  }
}



/* ========== 滚动动画和交互效果 ========== */
.reveal {
  opacity: 0;
  transform: translateY(30px) scale(0.95);
  transition: all 0.8s cubic-bezier(0.22, 1, 0.36, 1);
}

.reveal.is-visible {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* ========== 高级动效和交互细节 ========== */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 3D 倾斜效果增强 */
.metric-card,
.module-elegant-card,
.chart-elegant-card,
.status-elegant-card,
.quick-action-elegant {
  transform-style: preserve-3d;
  will-change: transform;
}

/* 鼠标跟踪光效 */
.metric-card:hover::after,
.module-elegant-card:hover::before,
.chart-elegant-card:hover::before,
.status-elegant-card:hover::before,
.quick-action-elegant:hover::before {
  background: radial-gradient(
    circle at var(--x, 50%) var(--y, 50%), 
    rgba(99, 102, 241, 0.06), 
    transparent 60%
  );
}

/* 加载状态微动画 */
.metric-value,
.status-count {
  animation: countUp 0.8s ease-out;
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 脉冲效果优化 */
.live-dot,
.status-dot {
  position: relative;
}

.live-dot::before,
.status-dot::before {
  content: '';
  position: absolute;
  inset: -4px;
  border-radius: 50%;
  background: inherit;
  opacity: 0.4;
  animation: rippleEffect 2s infinite;
}

@keyframes rippleEffect {
  0% {
    transform: scale(1);
    opacity: 0.4;
  }
  50% {
    transform: scale(1.8);
    opacity: 0.1;
  }
  100% {
    transform: scale(2.2);
    opacity: 0;
  }
}

/* 悬浮状态优化 */
.metric-card:hover,
.module-elegant-card:hover,
.chart-elegant-card:hover,
.status-elegant-card:hover,
.quick-action-elegant:hover {
  filter: brightness(1.02) saturate(1.1);
}

/* 焦点状态 */
.metric-card:focus-visible,
.module-elegant-card:focus-visible,
.quick-action-elegant:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}


</style>
