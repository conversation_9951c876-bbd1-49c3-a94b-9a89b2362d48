/**
 * 响应式登录数据管理
 * 提供响应式的登录数据访问，自动监听 localStorage 变化
 */
import { ref, computed, onUnmounted } from 'vue'

// 全局的登录数据状态
const globalLoginData = ref({})
let storageCheckTimer = null
let isInitialized = false

// 更新登录数据的函数
const updateLoginData = () => {
  const newData = JSON.parse(localStorage.getItem('loginResponseData') || '{}')
  globalLoginData.value = newData
}

// 初始化监听器
const initializeWatcher = () => {
  if (isInitialized) return

  // 初始化数据
  updateLoginData()

  // 启动定时器监听 localStorage 变化
  if (storageCheckTimer) {
    clearInterval(storageCheckTimer)
  }

  storageCheckTimer = setInterval(() => {
    const currentData = JSON.parse(localStorage.getItem('loginResponseData') || '{}')
    if (JSON.stringify(currentData) !== JSON.stringify(globalLoginData.value)) {
      updateLoginData()
    }
  }, 500) // 每500ms检查一次，提高响应速度

  isInitialized = true
}

// 清理监听器
const cleanupWatcher = () => {
  if (storageCheckTimer) {
    clearInterval(storageCheckTimer)
    storageCheckTimer = null
  }
  isInitialized = false
}

// 手动触发更新（用于账号切换后立即更新）
const forceUpdate = () => {
  updateLoginData()
}

/**
 * 使用响应式登录数据的组合式函数
 * @returns {Object} 包含响应式登录数据和相关方法
 */
export function useLoginData() {
  // 确保监听器已启动
  initializeWatcher()

  // 组件卸载时不清理全局监听器，因为其他组件可能还在使用
  // 只有在没有组件使用时才清理

  // 响应式的登录数据
  const loginData = computed(() => globalLoginData.value)

  // 当前账号信息 - 现在登录时已经修正了数据，直接使用登录数据即可
  const currentAccount = computed(() => {
    const childrenAdminList = loginData.value.children_admin_list

    // 检查是否已经切换到子账号（通过检查 originalLoginData）
    const originalLoginData = JSON.parse(localStorage.getItem('originalLoginData') || '{}')

    // 如果有原始登录数据，说明用户已经切换过账号，使用当前登录数据
    if (originalLoginData.admin_id) {
      return {
        id: loginData.value.admin_id,
        name: loginData.value.admin_name || loginData.value.name || loginData.value.username,
        nickname: loginData.value.nickname || loginData.value.nickName,
        shop_name: loginData.value.shop_name,
        admin_name: loginData.value.admin_name,
        auth_list: loginData.value.auth_list || []
      }
    }

    // 否则直接使用登录数据（已经在登录时修正过）
    return {
      id: loginData.value.admin_id,
      name: loginData.value.admin_name || loginData.value.name || loginData.value.username,
      nickname: loginData.value.nickname || loginData.value.nickName,
      shop_name: loginData.value.shop_name,
      admin_name: loginData.value.admin_name,
      auth_list: loginData.value.auth_list || []
    }
  })

  // 酒店信息 - 根据当前登录账号ID匹配正确的店铺信息
  const hotelInfo = computed(() => {
    const currentId = loginData.value.admin_id  // 修复：使用 admin_id
    const childrenAdminList = loginData.value.children_admin_list

    // 如果有 children_admin_list，从中查找匹配当前ID的店铺信息
    if (childrenAdminList && childrenAdminList.length > 0) {
      // 先检查主账号（children_admin_list[0]）
      const mainAccount = childrenAdminList[0]
      if (mainAccount.id === currentId || String(mainAccount.id) === String(currentId)) {
        return {
          name: mainAccount.shop_name || '智慧酒店管理系统',
          logo: mainAccount.cover_pic || mainAccount.shop_logo || '',
          id: mainAccount.shop_id
        }
      }

      // 再检查子账号
      for (const admin of childrenAdminList) {
        if (admin.children && admin.children.length > 0) {
          for (const child of admin.children) {
            if (child.id === currentId || String(child.id) === String(currentId)) {
              return {
                name: child.shop_name || '智慧酒店管理系统',
                logo: child.cover_pic || child.shop_logo || '',
                id: child.shop_id
              }
            }
          }
        }
      }
    }

    // 如果没有找到匹配的，使用原始登录数据
    return {
      name: loginData.value.shop_name || '智慧酒店管理系统',
      logo: loginData.value.cover_pic || loginData.value.shop_logo || '',
      id: loginData.value.shop_id
    }
  })

  // 权限列表 - 现在登录时已经修正了数据，直接使用登录数据即可
  const authList = computed(() => {
    return loginData.value.auth_list || []
  })

  // 子账号列表
  const childrenAdminList = computed(() => loginData.value.children_admin_list || [])

  // 是否有子账号 - 检查 children_admin_list[0].children 是否存在
  const hasChildAccounts = computed(() => {
    const adminList = childrenAdminList.value
    if (adminList.length > 0) {
      const mainAccount = adminList[0] // 总店账号
      return mainAccount.children && mainAccount.children.length > 0
    }
    return false
  })

  // 是否为子账号
  const isChildAccount = computed(() => {
    const originalLoginData = JSON.parse(localStorage.getItem('originalLoginData') || '{}')

    // 如果有原始登录数据且当前账号ID不同，说明是子账号
    if (originalLoginData.id && originalLoginData.id !== loginData.value.id) {
      return true
    }

    // 或者检查当前账号是否在children中
    return childrenAdminList.value.some(admin => {
      if (admin.children && admin.children.length > 0) {
        return admin.children.some(child => child.id === loginData.value.id)
      }
      return false
    })
  })

  // 获取所有可切换的子账号
  const availableChildAccounts = computed(() => {
    const result = []
    childrenAdminList.value.forEach(admin => {
      if (admin.children && admin.children.length > 0) {
        admin.children.forEach(child => {
          result.push(child)
          // 递归处理多层级
          if (child.children && child.children.length > 0) {
            result.push(...child.children)
          }
        })
      }
    })
    return result
  })

  return {
    // 响应式数据
    loginData,
    currentAccount,
    hotelInfo,
    authList,
    childrenAdminList,
    hasChildAccounts,
    isChildAccount,
    availableChildAccounts,

    // 方法
    forceUpdate,

    // 原始数据访问（兼容性）
    getLoginResponseData: () => globalLoginData.value
  }
}

// 全局清理函数（在应用卸载时调用）
export function cleanupLoginDataWatcher() {
  cleanupWatcher()
}

// 手动更新函数（在账号切换后调用）
export function updateLoginDataNow() {
  forceUpdate()
}

export default useLoginData
