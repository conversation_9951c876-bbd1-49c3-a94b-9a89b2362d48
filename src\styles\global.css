/**********************************
 * @Author: <PERSON>
 * @LastEditor: <PERSON>
 * @LastEditTime: 2023/12/05 21:26:28
 * @Email: <EMAIL>
 * Copyright © 2023 <PERSON>(大脸怪) | https://isme.top
 **********************************/

:root {
  /* 默认主题色 - 会被ThemeSelector动态更新 */
  --primary-color: #8A9CFF;
  --primary-color-hover: #6E8AF8;
  --primary-color-pressed: #4C73E6;
  --primary-color-suppl: #A8B5FF;
  --primary-color-rgb: 138, 156, 255;

  /* 暗黑中性色 */
  --text-1: #f8fafc;
  --text-2: #9ca3af;
  --border-1: rgba(255,255,255,0.1);
  --bg-1: #0a0a0a;
  --bg-2: #1a1a1a;
}

:root[data-theme="dark"] {
  /* 暗色主题下的主题色 - 会被ThemeSelector动态更新 */
  --primary-color: #8A9CFF;
  --primary-color-hover: #6E8AF8;
  --primary-color-pressed: #4C73E6;
  --primary-color-suppl: #A8B5FF;
  --primary-color-rgb: 138, 156, 255;

  --text-1: #f8fafc;
  --text-2: #9ca3af;
  --border-1: rgba(255,255,255,0.1);
  --bg-1: #0a0a0a;
  --bg-2: #1a1a1a;
}

html,
body {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: #ffffff;
}

.dark html,
.dark body {
  background: #000000;
}

#app {
  width: 100%;
  height: 100%;
}

/* transition fade-slide */
.fade-slide-leave-active,
.fade-slide-enter-active {
  transition: all 0.3s;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(-2%);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(2%);
}

/* 自定义滚动条样式 */
.cus-scroll {
  overflow: auto;
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
}
.cus-scroll-x {
  overflow-x: auto;
  &::-webkit-scrollbar {
    width: 0;
    height: 8px;
  }
}
.cus-scroll-y {
  overflow-y: auto;
  &::-webkit-scrollbar {
    width: 8px;
    height: 0;
  }
}
.cus-scroll,
.cus-scroll-x,
.cus-scroll-y {
  &::-webkit-scrollbar-thumb {
    background-color: transparent;
    border-radius: 4px;
  }
  &:hover {
    &::-webkit-scrollbar-thumb {
      background: #bfbfbf;
    }
    &::-webkit-scrollbar-thumb:hover {
      background: rgb(var(--primary-color));
    }
  }
}

/* 切换主题的动画效果 */
::view-transition-old(root),
::view-transition-new(root) {
  animation: none;
  mix-blend-mode: normal;
}

::view-transition-old(root),
.dark::view-transition-new(root) {
  z-index: 1;
}

::view-transition-new(root),
.dark::view-transition-old(root) {
  z-index: 9999;
}


/* Global Naive UI theming with primary color */
:root {
  /* Pagination */
  --pagination-active-bg: rgba(var(--primary-color-rgb), 0.18);
  --pagination-active-border: var(--primary-color);
  --pagination-hover-bg: rgba(var(--primary-color-rgb), 0.12);
}

/***** Pagination *****/
:root :is(.n-pagination) .n-pagination-item.n-pagination-item--active {
  background-color: var(--pagination-active-bg) !important;
  border-color: var(--pagination-active-border) !important;
  color: var(--primary-color) !important;
}
:root :is(.n-pagination) .n-pagination-item:hover {
  background-color: var(--pagination-hover-bg);
  color: var(--primary-color);
}

/***** Date & Time Picker *****/
:root :is(.n-date-panel) .n-date-panel-date__day--selected,
:root :is(.n-date-panel) .n-date-panel-date__day--selected-start,
:root :is(.n-date-panel) .n-date-panel-date__day--selected-end,
:root :is(.n-date-panel) .n-date-panel-date__day--start,
:root :is(.n-date-panel) .n-date-panel-date__day--end {
  background-color: rgba(var(--primary-color-rgb), 0.15) !important;
  color: var(--primary-color) !important;
}
:root :is(.n-date-panel) .n-date-panel-date__day--today {
  border-color: var(--primary-color) !important;
}

/***** Modal & Card headers *****/
:root :is(.n-card > .n-card-header),
:root :is(.n-modal .n-card > .n-card-header) {
  border-bottom: 1px solid rgba(var(--primary-color-rgb), 0.12) !important;
}

/***** Statistic emphasize *****/
:root :is(.n-statistic .n-statistic-value) {
  color: var(--primary-color);
}

/***** Empty emphasize *****/
:root :is(.n-empty .n-empty__description) {
  color: rgba(var(--primary-color-rgb), 0.8);
}

/***** Data Table row hover/checked *****/
:root :is(.n-data-table .n-data-table-td.__n_data_table_selected),
:root :is(.n-data-table .n-data-table-tr.__n_data_table_selected) {
  background-color: rgba(var(--primary-color-rgb), 0.15) !important;
}
:root :is(.n-data-table .n-data-table-tr:hover) {
  background-color: rgba(var(--primary-color-rgb), 0.12);
}

/* 高饱和度主题特殊处理 */
/* 清新绿色主题 #10B981 */
:root[style*="--primary-color: #10B981"] {
  --enhanced-opacity-light: 0.25;
  --enhanced-opacity-medium: 0.35;
  --enhanced-opacity-strong: 0.45;
  --enhanced-border-opacity: 0.7;
}

/* 优雅紫色主题 #8B5CF6 */
:root[style*="--primary-color: #8B5CF6"] {
  --enhanced-opacity-light: 0.25;
  --enhanced-opacity-medium: 0.35;
  --enhanced-opacity-strong: 0.45;
  --enhanced-border-opacity: 0.7;
}

/* 热情红色主题 #EF4444 */
:root[style*="--primary-color: #EF4444"] {
  --enhanced-opacity-light: 0.25;
  --enhanced-opacity-medium: 0.35;
  --enhanced-opacity-strong: 0.45;
  --enhanced-border-opacity: 0.7;
}

/* 活力橙色主题 #F59E0B */
:root[style*="--primary-color: #F59E0B"] {
  --enhanced-opacity-light: 0.25;
  --enhanced-opacity-medium: 0.35;
  --enhanced-opacity-strong: 0.45;
  --enhanced-border-opacity: 0.7;
}

/* 暗夜黑主题 #1F2937 - 超深黑暗模式 */
:root[style*="--primary-color: #1F2937"] {
  --ultra-dark-subtle: 0.35;
  --ultra-dark-light: 0.50;
  --ultra-dark-medium: 0.65;
  --ultra-dark-strong: 0.80;
  --ultra-dark-intense: 0.95;
  --ultra-border-opacity: 0.7;
  --ultra-shadow-opacity: 0.8;
  --ultra-glow-opacity: 0.6;

  /* 超深黑色调色板 */
  --ultra-black-50: rgba(0, 0, 0, 0.15);
  --ultra-black-100: rgba(0, 0, 0, 0.25);
  --ultra-black-200: rgba(0, 0, 0, 0.35);
  --ultra-black-300: rgba(0, 0, 0, 0.45);
  --ultra-black-400: rgba(0, 0, 0, 0.55);

  /* 白色高亮调色板 */
  --ultra-white-50: rgba(255, 255, 255, 0.08);
  --ultra-white-100: rgba(255, 255, 255, 0.12);
  --ultra-white-200: rgba(255, 255, 255, 0.18);
  --ultra-white-300: rgba(255, 255, 255, 0.25);
  --ultra-white-400: rgba(255, 255, 255, 0.35);
}

/* 高饱和度主题的增强样式 */
:root[style*="--primary-color: #10B981"] :is(.n-pagination) .n-pagination-item.n-pagination-item--active,
:root[style*="--primary-color: #8B5CF6"] :is(.n-pagination) .n-pagination-item.n-pagination-item--active,
:root[style*="--primary-color: #EF4444"] :is(.n-pagination) .n-pagination-item.n-pagination-item--active,
:root[style*="--primary-color: #F59E0B"] :is(.n-pagination) .n-pagination-item.n-pagination-item--active {
  background-color: rgba(var(--primary-color-rgb), var(--enhanced-opacity-medium)) !important;
  border-color: var(--primary-color) !important;
  color: var(--primary-color) !important;
  font-weight: 600 !important;
}

:root[style*="--primary-color: #10B981"] :is(.n-pagination) .n-pagination-item:hover,
:root[style*="--primary-color: #8B5CF6"] :is(.n-pagination) .n-pagination-item:hover,
:root[style*="--primary-color: #EF4444"] :is(.n-pagination) .n-pagination-item:hover,
:root[style*="--primary-color: #F59E0B"] :is(.n-pagination) .n-pagination-item:hover {
  background-color: rgba(var(--primary-color-rgb), var(--enhanced-opacity-light));
  color: var(--primary-color);
  font-weight: 500;
}

/* 数据表格增强 */
:root[style*="--primary-color: #10B981"] :is(.n-data-table .n-data-table-td.__n_data_table_selected),
:root[style*="--primary-color: #8B5CF6"] :is(.n-data-table .n-data-table-td.__n_data_table_selected),
:root[style*="--primary-color: #EF4444"] :is(.n-data-table .n-data-table-td.__n_data_table_selected),
:root[style*="--primary-color: #F59E0B"] :is(.n-data-table .n-data-table-td.__n_data_table_selected),
:root[style*="--primary-color: #10B981"] :is(.n-data-table .n-data-table-tr.__n_data_table_selected),
:root[style*="--primary-color: #8B5CF6"] :is(.n-data-table .n-data-table-tr.__n_data_table_selected),
:root[style*="--primary-color: #EF4444"] :is(.n-data-table .n-data-table-tr.__n_data_table_selected),
:root[style*="--primary-color: #F59E0B"] :is(.n-data-table .n-data-table-tr.__n_data_table_selected) {
  background-color: rgba(var(--primary-color-rgb), var(--enhanced-opacity-light)) !important;
}

:root[style*="--primary-color: #10B981"] :is(.n-data-table .n-data-table-tr:hover),
:root[style*="--primary-color: #8B5CF6"] :is(.n-data-table .n-data-table-tr:hover),
:root[style*="--primary-color: #EF4444"] :is(.n-data-table .n-data-table-tr:hover),
:root[style*="--primary-color: #F59E0B"] :is(.n-data-table .n-data-table-tr:hover) {
  background-color: rgba(var(--primary-color-rgb), 0.18);
}

/* 暗夜黑主题的超深黑全局组件增强 */
:root[style*="--primary-color: #1F2937"] :is(.n-pagination) .n-pagination-item.n-pagination-item--active {
  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), var(--ultra-dark-medium)) 0%, rgba(var(--primary-color-rgb), var(--ultra-dark-light)) 100%) !important;
  border-color: rgba(var(--primary-color-rgb), var(--ultra-border-opacity)) !important;
  color: #FFFFFF !important;
  font-weight: 600 !important;
  box-shadow:
    0 3px 12px rgba(var(--primary-color-rgb), var(--ultra-shadow-opacity)),
    0 0 0 1px rgba(255, 255, 255, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.18) !important;
}

:root[style*="--primary-color: #1F2937"] :is(.n-pagination) .n-pagination-item:hover {
  background: rgba(var(--primary-color-rgb), var(--ultra-dark-light)) !important;
  color: #FFFFFF !important;
  border-color: rgba(var(--primary-color-rgb), 0.6) !important;
  box-shadow:
    0 4px 12px rgba(var(--primary-color-rgb), 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.12) !important;
}

:root[style*="--primary-color: #1F2937"] :is(.n-data-table .n-data-table-td.__n_data_table_selected),
:root[style*="--primary-color: #1F2937"] :is(.n-data-table .n-data-table-tr.__n_data_table_selected) {
  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), var(--ultra-dark-light)) 0%, rgba(var(--primary-color-rgb), var(--ultra-dark-subtle)) 100%) !important;
  border-left: 3px solid rgba(var(--primary-color-rgb), var(--ultra-border-opacity)) !important;
  box-shadow:
    inset 0 0 0 1px rgba(var(--primary-color-rgb), 0.35),
    0 1px 3px rgba(var(--primary-color-rgb), 0.4) !important;
}

:root[style*="--primary-color: #1F2937"] :is(.n-data-table .n-data-table-tr:hover) {
  background: rgba(var(--primary-color-rgb), var(--ultra-dark-subtle)) !important;
  box-shadow: 0 2px 6px rgba(var(--primary-color-rgb), 0.35) !important;
}

/* 暗夜黑主题的超深黑卡片和容器增强 */
:root[style*="--primary-color: #1F2937"] .n-card {
  background: linear-gradient(135deg, var(--ultra-black-200) 0%, var(--ultra-black-100) 100%) !important;
  border: 1px solid rgba(var(--primary-color-rgb), var(--ultra-border-opacity)) !important;
  box-shadow:
    0 6px 20px rgba(var(--primary-color-rgb), 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
}

:root[style*="--primary-color: #1F2937"] .n-card:hover {
  background: linear-gradient(135deg, var(--ultra-black-300) 0%, var(--ultra-black-200) 100%) !important;
  border-color: rgba(var(--primary-color-rgb), 0.7) !important;
  box-shadow:
    0 8px 24px rgba(var(--primary-color-rgb), 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  transform: translateY(-1px) !important;
}

:root[style*="--primary-color: #1F2937"] .n-modal .n-card {
  background: linear-gradient(135deg, var(--ultra-black-400) 0%, var(--ultra-black-300) 100%) !important;
  box-shadow:
    0 16px 36px rgba(var(--primary-color-rgb), 0.6),
    0 0 0 1px rgba(255, 255, 255, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.25) !important;
}
