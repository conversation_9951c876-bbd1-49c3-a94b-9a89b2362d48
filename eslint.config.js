import antfu from '@antfu/eslint-config'

export default antfu({
  ignores: ['**/*'], // 忽略所有文件
  unocss: false,
  formatters: false,
  stylistic: false,
  typescript: false, // 禁用 TypeScript 支持
  rules: {
    'no-console': 'off', // 完全关闭规则
    'n/prefer-global/process': 'off',
    'no-undef': 'error',
    'no-fallthrough': 'off',
    'vue/block-order': 'off',
    'prefer-promise-reject-errors': 'off',
  },
  languageOptions: {
    globals: {
      h: 'readonly',
      unref: 'readonly',
      provide: 'readonly',
      inject: 'readonly',
      markRaw: 'readonly',
      defineAsyncComponent: 'readonly',
      nextTick: 'readonly',
      useRoute: 'readonly',
      useRouter: 'readonly',
      Message: 'readonly',
      $loadingBar: 'readonly',
      $message: 'readonly',
      $dialog: 'readonly',
      $notification: 'readonly',
      $modal: 'readonly',
    },
  },
})
