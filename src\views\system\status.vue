<template>
  <div class="system-status-page">
    <!-- 页面标题 -->
    <n-page-header @back="$router.back()" title="系统状态" subtitle="当前系统功能恢复状态">
      <template #extra>
        <n-button @click="$router.push('/')">
          <template #icon>
            <n-icon><i class="i-mdi:home"></i></n-icon>
          </template>
          返回首页
        </n-button>
      </template>
    </n-page-header>

    <!-- 系统概览 -->
    <n-card title="系统概览" class="overview-card">
      <n-grid :cols="4" :x-gap="16">
        <n-grid-item>
          <n-statistic label="已恢复功能" :value="recoveredFeatures">
            <template #prefix>
              <n-icon color="var(--primary-color)">
                <i class="i-mdi:check-circle"></i>
              </n-icon>
            </template>
          </n-statistic>
        </n-grid-item>

        <n-grid-item>
          <n-statistic label="总功能数" :value="totalFeatures">
            <template #prefix>
              <n-icon color="var(--primary-color)">
                <i class="i-mdi:apps"></i>
              </n-icon>
            </template>
          </n-statistic>
        </n-grid-item>

        <n-grid-item>
          <n-statistic label="恢复进度" :value="recoveryProgress" suffix="%">
            <template #prefix>
              <n-icon color="var(--primary-color)">
                <i class="i-mdi:progress-check"></i>
              </n-icon>
            </template>
          </n-statistic>
        </n-grid-item>

        <n-grid-item>
          <n-statistic label="运行时间" :value="uptime">
            <template #prefix>
              <n-icon color="#8B5CF6">
                <i class="i-mdi:clock-outline"></i>
              </n-icon>
            </template>
          </n-statistic>
        </n-grid-item>
      </n-grid>
    </n-card>

    <!-- 恢复完成提示 -->
    <n-alert type="success" title="系统恢复完成" style="margin-bottom: 1rem;">
      <template #icon>
        <n-icon><i class="i-mdi:check-circle"></i></n-icon>
      </template>
      酒店管理系统已成功恢复，所有核心功能正常运行。权限系统、路由守卫、登录功能均已启用。
      <br>
      <n-text depth="3">
        恢复进度: 100% | 已恢复功能: {{ activeFeatures }}/{{ totalFeatures }} |
        <n-button text type="primary" @click="$router.push('/test/login')">测试登录功能</n-button>
      </n-text>
    </n-alert>

    <!-- 功能状态列表 -->
    <n-card title="功能状态详情" class="features-status-card">
      <n-list>
        <n-list-item v-for="feature in featuresList" :key="feature.name">
          <n-thing>
            <template #avatar>
              <n-avatar :style="{ backgroundColor: feature.status === 'active' ? '#10B981' : '#EF4444' }">
                <n-icon>
                  <i :class="feature.status === 'active' ? 'i-mdi:check' : 'i-mdi:close'"></i>
                </n-icon>
              </n-avatar>
            </template>

            <template #header>
              <n-space align="center">
                <span>{{ feature.name }}</span>
                <n-tag :type="feature.status === 'active' ? 'success' : 'error'" size="small">
                  {{ feature.status === 'active' ? '已恢复' : '未恢复' }}
                </n-tag>
              </n-space>
            </template>

            <template #description>
              {{ feature.description }}
            </template>

            <template #footer>
              <n-space>
                <n-text depth="3">最后更新: {{ feature.lastUpdate }}</n-text>
                <n-button
                  v-if="feature.testUrl"
                  size="small"
                  type="primary"
                  text
                  @click="$router.push(feature.testUrl)"
                >
                  测试功能
                </n-button>
              </n-space>
            </template>
          </n-thing>
        </n-list-item>
      </n-list>
    </n-card>

    <!-- 技术栈信息 -->
    <n-card title="技术栈信息" class="tech-stack-card">
      <n-descriptions :column="2" bordered>
        <n-descriptions-item label="前端框架">
          <n-tag type="success">Vue 3</n-tag>
        </n-descriptions-item>
        <n-descriptions-item label="构建工具">
          <n-tag type="info">Vite</n-tag>
        </n-descriptions-item>
        <n-descriptions-item label="UI组件库">
          <n-tag type="warning">Naive UI</n-tag>
        </n-descriptions-item>
        <n-descriptions-item label="状态管理">
          <n-tag type="error">Pinia</n-tag>
        </n-descriptions-item>
        <n-descriptions-item label="路由管理">
          <n-tag type="success">Vue Router</n-tag>
        </n-descriptions-item>
        <n-descriptions-item label="样式框架">
          <n-tag type="info">UnoCSS</n-tag>
        </n-descriptions-item>
        <n-descriptions-item label="HTTP客户端">
          <n-tag type="warning">Axios</n-tag>
        </n-descriptions-item>
        <n-descriptions-item label="开发环境">
          <n-tag type="error">Node.js</n-tag>
        </n-descriptions-item>
      </n-descriptions>
    </n-card>

    <!-- 性能指标 -->
    <n-card title="性能指标" class="performance-card">
      <n-grid :cols="3" :x-gap="16">
        <n-grid-item>
          <div class="performance-item">
            <n-progress
              type="circle"
              :percentage="95"
              status="success"
              :stroke-width="8"
            >
              <div class="performance-label">
                <div class="performance-value">95%</div>
                <div class="performance-name">页面加载</div>
              </div>
            </n-progress>
          </div>
        </n-grid-item>

        <n-grid-item>
          <div class="performance-item">
            <n-progress
              type="circle"
              :percentage="88"
              status="info"
              :stroke-width="8"
            >
              <div class="performance-label">
                <div class="performance-value">88%</div>
                <div class="performance-name">响应速度</div>
              </div>
            </n-progress>
          </div>
        </n-grid-item>

        <n-grid-item>
          <div class="performance-item">
            <n-progress
              type="circle"
              :percentage="92"
              status="warning"
              :stroke-width="8"
            >
              <div class="performance-label">
                <div class="performance-value">92%</div>
                <div class="performance-name">稳定性</div>
              </div>
            </n-progress>
          </div>
        </n-grid-item>
      </n-grid>
    </n-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

const startTime = ref(Date.now())

// 功能列表
const featuresList = ref([
  {
    name: 'Vue 3 应用框架',
    description: 'Vue 3 Composition API，响应式数据管理',
    status: 'active',
    lastUpdate: '2024-12-19 16:30',
    testUrl: '/'
  },
  {
    name: 'Pinia 状态管理',
    description: '全局状态管理，数据持久化',
    status: 'active',
    lastUpdate: '2024-12-19 16:25',
    testUrl: null
  },
  {
    name: 'Vue Router 路由系统',
    description: '页面路由，导航守卫',
    status: 'active',
    lastUpdate: '2024-12-19 16:28',
    testUrl: '/dashboard/features'
  },
  {
    name: 'Naive UI 组件库',
    description: '现代化UI组件，主题系统',
    status: 'active',
    lastUpdate: '2024-12-19 16:30',
    testUrl: null
  },
  {
    name: '房态管理',
    description: '房间状态监控，统计分析，支持筛选和实时操作',
    status: 'active',
    lastUpdate: '2024-12-19 16:45',
    testUrl: '/hotel/roomstatus'
  },
  {
    name: '会员管理',
    description: '客户会员信息管理，会员卡系统',
    status: 'active',
    lastUpdate: '2024-12-19 16:45',
    testUrl: '/hotel/member'
  },
  {
    name: '营业日期管理',
    description: '酒店营业日期获取和显示',
    status: 'active',
    lastUpdate: '2024-12-19 16:35',
    testUrl: null
  },
  {
    name: 'HTTP请求拦截器',
    description: 'API请求处理，错误拦截',
    status: 'active',
    lastUpdate: '2024-12-19 16:36',
    testUrl: null
  },
  {
    name: '登录认证系统',
    description: '用户登录认证，Token管理，权限验证',
    status: 'active',
    lastUpdate: '2024-12-19 16:45',
    testUrl: '/test/login'
  },
  {
    name: '权限管理系统',
    description: '用户权限控制，角色管理，路由守卫',
    status: 'active',
    lastUpdate: '2024-12-19 16:45',
    testUrl: '/pms/role'
  },
  {
    name: '财务报表',
    description: '收入统计，财务分析',
    status: 'inactive',
    lastUpdate: '待恢复',
    testUrl: null
  }
])

// 计算属性
const recoveredFeatures = computed(() => {
  return featuresList.value.filter(f => f.status === 'active').length
})

const activeFeatures = recoveredFeatures

const totalFeatures = computed(() => {
  return featuresList.value.length
})

const recoveryProgress = computed(() => {
  return Math.round((recoveredFeatures.value / totalFeatures.value) * 100)
})

const uptime = computed(() => {
  const diff = Date.now() - startTime.value
  const minutes = Math.floor(diff / 60000)
  const seconds = Math.floor((diff % 60000) / 1000)
  return `${minutes}分${seconds}秒`
})

onMounted(() => {

  // 每秒更新运行时间
  setInterval(() => {
    // 触发响应式更新
    startTime.value = startTime.value
  }, 1000)
})
</script>

<style scoped>
.system-status-page {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.overview-card,
.features-status-card,
.tech-stack-card,
.performance-card {
  background: white;
}

.performance-item {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem;
}

.performance-label {
  text-align: center;
}

.performance-value {
  font-size: 1.2rem;
  font-weight: bold;
  color: var(--primary-color);
}

.performance-name {
  font-size: 0.875rem;
  color: #6B7280;
  margin-top: 0.25rem;
}

@media (max-width: 768px) {
  .system-status-page {
    padding: 0.5rem;
  }
}
</style>
