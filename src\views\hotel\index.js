import { request } from '@/utils'

// 会员管理相关接口
export const getMemberList = (params) => {
  return request.get('/hotel/member/list', { params })
}

export const createMember = (data) => {
  return request.post('/hotel/member', data)
}

export const updateMember = (data) => {
  return request.put(`/hotel/member/${data.id}`, data)
}

export const deleteMember = (id) => {
  return request.delete(`/hotel/member/${id}`)
}

export const getMemberDetail = (id) => {
  return request.get(`/hotel/member/${id}`)
}
