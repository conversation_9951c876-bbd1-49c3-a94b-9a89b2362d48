/**
 * 登录信息缓存管理
 * 用于在登录前从缓存读取配置，登录后更新缓存
 */
import { lStorage } from '@/utils'

// 缓存键名
const CACHE_KEYS = {
  LOGIN_RESPONSE: 'loginResponseData',
  SYSTEM_CONFIG: 'systemConfig',
  USER_INFO: 'userInfo',
  WEB_CONFIG: 'webConfig'
}

/**
 * 获取缓存的网站配置
 * 优先从专门的webConfig缓存读取，其次从loginResponseData读取
 */
export function getCachedWebConfig() {
  // 先尝试从专门的webConfig缓存读取
  const cachedWebConfig = lStorage.get(CACHE_KEYS.WEB_CONFIG)
  if (cachedWebConfig && Object.keys(cachedWebConfig).length > 0) {
    return cachedWebConfig
  }

  // 如果没有专门的缓存，从登录响应数据中提取
  const loginData = lStorage.get(CACHE_KEYS.LOGIN_RESPONSE) || {}
  const systemConfig = lStorage.get(CACHE_KEYS.SYSTEM_CONFIG) || {}

  return {
    set_web_title: loginData.set_web_title || systemConfig.set_web_title || '',
    set_web_logo: loginData.set_web_logo || systemConfig.set_web_logo || '',
    set_web_copyright: loginData.set_web_copyright || systemConfig.set_web_copyright || '',
    set_web_copyright_url: loginData.set_web_copyright_url || systemConfig.set_web_copyright_url || '',
    bg_img_login: loginData.bg_img_login || systemConfig.bg_img_login || ''
  }
}

/**
 * 缓存网站配置
 * 将网站配置单独缓存，便于快速读取
 */
export function cacheWebConfig(webConfig) {
  if (webConfig && typeof webConfig === 'object') {
    lStorage.set(CACHE_KEYS.WEB_CONFIG, webConfig)
  }
}

/**
 * 从登录响应数据中提取并缓存网站配置
 */
export function extractAndCacheWebConfig(loginData) {
  if (!loginData || typeof loginData !== 'object') {
    return
  }

  const webConfig = {
    set_web_title: loginData.set_web_title || '',
    set_web_logo: loginData.set_web_logo || '',
    set_web_copyright: loginData.set_web_copyright || '',
    set_web_copyright_url: loginData.set_web_copyright_url || '',
    bg_img_login: loginData.bg_img_login || ''
  }

  // 只有当配置不为空时才缓存
  const hasValidConfig = Object.values(webConfig).some(value => value && value.trim())
  if (hasValidConfig) {
    cacheWebConfig(webConfig)
  }

  return webConfig
}

/**
 * 获取缓存的用户信息
 */
export function getCachedUserInfo() {
  return lStorage.get(CACHE_KEYS.USER_INFO) || {}
}

/**
 * 缓存用户信息
 */
export function cacheUserInfo(userInfo) {
  if (userInfo && typeof userInfo === 'object') {
    lStorage.set(CACHE_KEYS.USER_INFO, userInfo)
  }
}

/**
 * 获取完整的登录响应数据
 */
export function getCachedLoginData() {
  return lStorage.get(CACHE_KEYS.LOGIN_RESPONSE) || {}
}

/**
 * 缓存完整的登录响应数据
 */
export function cacheLoginData(loginData) {
  if (loginData && typeof loginData === 'object') {
    lStorage.set(CACHE_KEYS.LOGIN_RESPONSE, loginData)
    
    // 同时提取并缓存网站配置
    extractAndCacheWebConfig(loginData)
  }
}

/**
 * 检查是否有有效的登录缓存
 */
export function hasValidLoginCache() {
  const loginData = getCachedLoginData()
  const webConfig = getCachedWebConfig()
  
  return Object.keys(loginData).length > 0 || Object.keys(webConfig).length > 0
}

/**
 * 清除所有登录相关缓存
 */
export function clearLoginCache() {
  lStorage.remove(CACHE_KEYS.LOGIN_RESPONSE)
  lStorage.remove(CACHE_KEYS.SYSTEM_CONFIG)
  lStorage.remove(CACHE_KEYS.USER_INFO)
  lStorage.remove(CACHE_KEYS.WEB_CONFIG)
  lStorage.remove('token')
  lStorage.remove('refreshToken')
}

/**
 * 预加载登录配置
 * 在应用启动时调用，确保配置可用
 */
export function preloadLoginConfig() {
  const webConfig = getCachedWebConfig()
  
  // 如果有背景图片，预加载它
  if (webConfig.bg_img_login) {
    const img = new Image()
    img.src = webConfig.bg_img_login
  }
  
  return webConfig
}

/**
 * 更新登录缓存
 * 登录成功后调用，更新所有相关缓存
 */
export function updateLoginCache(loginData, userInfo = null) {
  // 缓存完整的登录数据
  cacheLoginData(loginData)
  
  // 如果提供了用户信息，也缓存它
  if (userInfo) {
    cacheUserInfo(userInfo)
  }
  
  // 返回提取的网站配置
  return extractAndCacheWebConfig(loginData)
}

export default {
  getCachedWebConfig,
  cacheWebConfig,
  extractAndCacheWebConfig,
  getCachedUserInfo,
  cacheUserInfo,
  getCachedLoginData,
  cacheLoginData,
  hasValidLoginCache,
  clearLoginCache,
  preloadLoginConfig,
  updateLoginCache
}
