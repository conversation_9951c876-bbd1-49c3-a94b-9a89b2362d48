/**
 * 权限指令
 * 用法：
 * v-permission="'auth_mark'" - 检查单个权限
 * v-permission="['auth_mark1', 'auth_mark2']" - 检查多个权限（任一）
 * v-permission:all="['auth_mark1', 'auth_mark2']" - 检查多个权限（全部）
 */
import { usePermissionStore } from '@/store'

function checkPermission(el, binding) {
  const permissionStore = usePermissionStore()
  const { value, arg } = binding

  if (!value) {
    return true
  }

  let hasPermission = false

  if (Array.isArray(value)) {
    if (arg === 'all') {
      // 需要全部权限
      hasPermission = value.every(authMark => 
        permissionStore.hasPermission(authMark)
      )
    } else {
      // 只需要任一权限
      hasPermission = value.some(authMark => 
        permissionStore.hasPermission(authMark)
      )
    }
  } else {
    // 单个权限
    hasPermission = permissionStore.hasPermission(value)
  }

  if (!hasPermission) {
    // 没有权限，隐藏元素
    el.style.display = 'none'
    // 或者移除元素
    // el.parentNode && el.parentNode.removeChild(el)
  } else {
    // 有权限，显示元素
    el.style.display = ''
  }
}

export default {
  mounted(el, binding) {
    checkPermission(el, binding)
  },
  updated(el, binding) {
    checkPermission(el, binding)
  }
}
