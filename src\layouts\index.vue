<!--------------------------------
 - @Author: <PERSON>
 - @LastEditor: <PERSON>
 - @LastEditTime: 2023/12/05 21:24:09
 - @Email: <EMAIL>
 - Copyright © 2023 <PERSON>(大脸怪) | https://isme.top
 --------------------------------->

<template>
  <component :is="layoutComponent" />
</template>

<script setup>
import { useAppStore } from '@/store'
import EmptyLayout from './empty/index.vue'
import FullLayout from './full/index.vue'
import NormalLayout from './normal/index.vue'
import SimpleLayout from './simple/index.vue'

const appStore = useAppStore()

const layoutMap = {
  empty: EmptyLayout,
  full: FullLayout,
  normal: NormalLayout,
  simple: SimpleLayout,
}

const layoutComponent = computed(() => {
  return layoutMap[appStore.layout] || NormalLayout
})
</script>
