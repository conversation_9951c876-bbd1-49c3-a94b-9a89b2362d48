<template>
  <div class="apis-page">
    <div class="page-header">
      <h1 class="page-title">
        <i class="i-material-symbols:api-outline"></i>
        API管理
      </h1>
      <p class="page-description">管理系统API接口和文档</p>
    </div>

    <div class="page-content">
      <n-card>
        <n-empty description="API管理功能开发中...">
          <template #extra>
            <n-button type="primary">敬请期待</n-button>
          </template>
        </n-empty>
      </n-card>
    </div>
  </div>
</template>

<script setup>
// API管理页面 - 占位页面
</script>

<style scoped>
.apis-page {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: var(--text-color-1);
}

.page-description {
  color: var(--text-color-3);
  margin: 0;
}
</style>