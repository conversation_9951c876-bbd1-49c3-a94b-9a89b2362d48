<template>
  <div>
    <n-page-header
      title="会员详情"
      @back="handleBack"
    />

    <n-card title="基本信息" class="mt-4">
      <n-descriptions bordered>
        <n-descriptions-item label="会员名称">
          {{ memberInfo.name }}
        </n-descriptions-item>
        <n-descriptions-item label="手机号码">
          {{ memberInfo.phone }}
        </n-descriptions-item>
        <n-descriptions-item label="会员等级">
          {{ getLevelLabel(memberInfo.level) }}
        </n-descriptions-item>
        <n-descriptions-item label="积分">
          {{ memberInfo.points }}
        </n-descriptions-item>
        <n-descriptions-item label="注册时间">
          {{ memberInfo.createTime }}
        </n-descriptions-item>
        <n-descriptions-item label="备注">
          {{ memberInfo.remark || '无' }}
        </n-descriptions-item>
      </n-descriptions>
    </n-card>

    <n-card title="消费记录" class="mt-4">
      <n-data-table
        :columns="consumeColumns"
        :data="consumeData"
        :loading="consumeLoading"
        :pagination="consumePagination"
        @update:page="handleConsumePage"
      />
    </n-card>

    <n-card title="积分变动记录" class="mt-4">
      <n-data-table
        :columns="pointsColumns"
        :data="pointsData"
        :loading="pointsLoading"
        :pagination="pointsPagination"
        @update:page="handlePointsPage"
      />
    </n-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { createDiscreteApi } from 'naive-ui'
import { getMemberDetail } from '@/api/hotel/member'

const { message } = createDiscreteApi(['message'])

defineOptions({ name: 'MemberDetail' })
const route = useRoute()
const router = useRouter()
// 会员ID
const memberId = route.params.id

// 会员基本信息
const memberInfo = ref({
  name: '',
  phone: '',
  level: null,
  points: 0,
  createTime: '',
  remark: ''
})

// 会员等级选项
const levelOptions = [
  {
    label: '普通会员',
    value: 1,
  },
  {
    label: '银卡会员',
    value: 2,
  },
  {
    label: '金卡会员',
    value: 3,
  },
  {
    label: '钻石会员',
    value: 4,
  },
]

// 获取会员等级标签
const getLevelLabel = (level) => {
  const option = levelOptions.find(item => item.value === level)
  return option ? option.label : '未知'
}

// 获取会员详情
const loadMemberInfo = async () => {
  try {
    const { data } = await getMemberDetail(memberId)
    memberInfo.value = data
  } catch (error) {
    message.error('获取会员详情失败')
  }
}

// 返回上一页
const handleBack = () => {
  router.back()
}

// 消费记录相关
const consumeColumns = [
  {
    title: '订单号',
    key: 'orderNo',
  },
  {
    title: '消费金额',
    key: 'amount',
  },
  {
    title: '消费项目',
    key: 'item',
  },
  {
    title: '消费时间',
    key: 'createTime',
  },
  {
    title: '备注',
    key: 'remark',
  },
]

const consumeData = ref([])
const consumeLoading = ref(false)
const consumePagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 30, 40],
})

// 模拟获取消费记录
const loadConsumeData = () => {
  consumeLoading.value = true
  // 这里应该调用API获取消费记录
  setTimeout(() => {
    consumeData.value = [
      {
        id: 1,
        orderNo: 'ORD20230501001',
        amount: 1280,
        item: '豪华套房',
        createTime: '2023-05-01 14:30:00',
        remark: '住宿2晚'
      },
      {
        id: 2,
        orderNo: 'ORD20230502001',
        amount: 368,
        item: '餐饮',
        createTime: '2023-05-02 19:20:00',
        remark: '晚餐'
      }
    ]
    consumePagination.itemCount = 2
    consumeLoading.value = false
  }, 500)
}

// 消费记录分页
const handleConsumePage = (page) => {
  consumePagination.page = page
  loadConsumeData()
}

// 积分变动记录相关
const pointsColumns = [
  {
    title: '变动类型',
    key: 'type',
    render(row) {
      return row.type === 1 ? '增加' : '减少'
    }
  },
  {
    title: '积分数量',
    key: 'points',
  },
  {
    title: '变动原因',
    key: 'reason',
  },
  {
    title: '变动时间',
    key: 'createTime',
  },
]

const pointsData = ref([])
const pointsLoading = ref(false)
const pointsPagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 30, 40],
})

// 模拟获取积分变动记录
const loadPointsData = () => {
  pointsLoading.value = true
  // 这里应该调用API获取积分变动记录
  setTimeout(() => {
    pointsData.value = [
      {
        id: 1,
        type: 1,
        points: 128,
        reason: '消费赠送',
        createTime: '2023-05-01 14:30:00',
      },
      {
        id: 2,
        type: 1,
        points: 36,
        reason: '消费赠送',
        createTime: '2023-05-02 19:20:00',
      },
      {
        id: 3,
        type: 2,
        points: 50,
        reason: '积分兑换',
        createTime: '2023-05-03 10:15:00',
      }
    ]
    pointsPagination.itemCount = 3
    pointsLoading.value = false
  }, 500)
}

// 积分记录分页
const handlePointsPage = (page) => {
  pointsPagination.page = page
  loadPointsData()
}

// 初始化
onMounted(() => {
  loadMemberInfo()
  loadConsumeData()
  loadPointsData()
})
</script>

<style scoped>
.mt-4 {
  margin-top: 16px;
}
</style>
