<!--------------------------------
 - @Author: <PERSON>
 - @LastEditor: <PERSON>
 - @LastEditTime: 2023/12/16 18:50:35
 - @Email: <EMAIL>
 - Copyright © 2023 <PERSON>(大脸怪) | https://isme.top
 --------------------------------->

<template>
  <div class="h-full menu-container">
    <!-- 菜单搜索框 -->
    <div v-if="!appStore.collapsed" class="menu-search">
      <n-input
        v-model:value="searchKeyword"
        :placeholder="t('menu.searchPlaceholder')"
        size="small"
        clearable
        @input="handleSearch"
      >
        <template #prefix>
          <i class="i-fe:search text-14"></i>
        </template>
      </n-input>
    </div>

    <!-- 快速功能按钮 -->
    <div v-if="!appStore.collapsed && quickMenus.length > 0" class="quick-menus">
      <div class="quick-menu-title">
        <i class="i-fe:zap"></i>
        <span>{{ t('menu.quickFunctions') }}</span>
      </div>
      <div class="quick-menu-grid">
        <div
          v-for="menu in quickMenus"
          :key="menu.key"
          class="quick-menu-item"
          @click="handleQuickMenuClick(menu)"
        >
          <div class="quick-icon">
            <component :is="menu.icon" v-if="menu.icon" />
            <i v-else class="i-fe:folder"></i>
          </div>
          <span class="quick-label">{{ menu.label }}</span>
        </div>
      </div>
    </div>

    <!-- 主菜单 -->
    <div class="main-menu">
      <n-menu
        ref="menu"
        class="side-menu"
        accordion
        :indent="24"
        :collapsed-icon-size="22"
        :collapsed-width="64"
        :collapsed="appStore.collapsed"
        :options="filteredMenus"
        :value="activeKey"
        @update:value="handleMenuSelect"
      />
    </div>

    <!-- 菜单统计信息 -->
    <div v-if="!appStore.collapsed" class="menu-stats">
      <div class="stats-item">
        <span class="stats-label">{{ t('menu.functionalModules') }}</span>
        <span class="stats-value">{{ totalMenuCount }}</span>
      </div>
      <div v-if="searchKeyword" class="stats-item">
        <span class="stats-label">{{ t('menu.searchResults') }}</span>
        <span class="stats-value">{{ filteredMenus.length }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, h, ref, watch, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useAppStore, usePermissionStore } from '@/store'
import { isExternal } from '@/utils'

const router = useRouter()
const route = useRoute()
const appStore = useAppStore()
const permissionStore = usePermissionStore()
const { t } = useI18n()

const menu = ref(null)
const searchKeyword = ref('')

// 菜单状态持久化
const MENU_STATE_KEY = 'sidebar_menu_state'

const activeKey = computed(() => route.meta?.parentKey || route.name)

// 拼音映射表（简化版本）
const PINYIN_MAP = {
  '工作台': 'gongzuotai',
  '房态管理': 'fangtaiguanli',
  '入住登记': 'ruzhudengji',
  '退房结账': 'tuifangjiezhang',
  '预订管理': 'yudingguanli',
  '客人档案': 'kerendangan',
  '会员管理': 'huiyuanguanli',
  '积分管理': 'jifenguanli',
  '系统管理': 'xitongguanli',
  '用户管理': 'yonghuguanli',
  '角色管理': 'jueseguanli',
  '权限管理': 'quanguanli',
  '操作日志': 'caozuorizhi',
  '数据备份': 'shujubeifen',
  '系统状态': 'xitongzhuangtai',
  '功能概览': 'gongnenggailan',
  '今日营运': 'jinriyunyin',
  '预警中心': 'yujingzhongxin'
}

// 扁平化菜单
function flattenMenus(menus) {
  const result = []
  menus.forEach(menu => {
    result.push(menu)
    if (menu.children && menu.children.length > 0) {
      result.push(...flattenMenus(menu.children))
    }
  })
  return result
}

// 智能搜索匹配
function smartSearch(text, keyword) {
  const lowerText = text.toLowerCase()
  const lowerKeyword = keyword.toLowerCase()

  // 完全匹配
  if (lowerText === lowerKeyword) return 100

  // 开头匹配
  if (lowerText.startsWith(lowerKeyword)) return 90

  // 包含匹配
  if (lowerText.includes(lowerKeyword)) return 80

  // 拼音匹配
  const pinyin = PINYIN_MAP[text] || ''
  if (pinyin && pinyin.includes(lowerKeyword)) return 70

  // 模糊匹配
  let score = 0
  let keywordIndex = 0
  for (let i = 0; i < lowerText.length && keywordIndex < lowerKeyword.length; i++) {
    if (lowerText[i] === lowerKeyword[keywordIndex]) {
      score += 10
      keywordIndex++
    }
  }

  return keywordIndex === lowerKeyword.length ? score : 0
}

// 根据关键词过滤菜单（增强版）
function filterMenusByKeyword(menus, keyword) {
  if (!keyword || !keyword.trim()) return menus

  const searchKeyword = keyword.trim().toLowerCase()
  const searchResults = []

  // 搜索所有菜单项并打分
  const allItems = []
  function collectItems(menus, path = []) {
    menus.forEach(menu => {
      const itemPath = [...path, menu]
      const score = Math.max(
        smartSearch(menu.label, searchKeyword),
        menu.key ? smartSearch(menu.key, searchKeyword) : 0,
        menu.path ? smartSearch(menu.path, searchKeyword) : 0
      )

      if (score > 0) {
        allItems.push({ menu, path: itemPath, score })
      }

      if (menu.children && menu.children.length > 0) {
        collectItems(menu.children, itemPath)
      }
    })
  }

  collectItems(menus)

  // 按分数排序
  allItems.sort((a, b) => b.score - a.score)

  // 重构菜单结构
  const addedItems = new Set()

  allItems.forEach(({ menu, path }) => {
    if (addedItems.has(menu.key)) return

    let currentLevel = searchResults

    path.forEach((item, index) => {
      if (addedItems.has(item.key)) {
        // 已存在，找到对应项
        const existing = currentLevel.find(m => m.key === item.key)
        if (existing && existing.children) {
          currentLevel = existing.children
        }
      } else {
        // 新项
        const newItem = { ...item }
        if (index < path.length - 1) {
          newItem.children = []
        }

        currentLevel.push(newItem)
        addedItems.add(item.key)

        if (newItem.children) {
          currentLevel = newItem.children
        }
      }
    })
  })

  return searchResults
}

// 搜索过滤菜单
const filteredMenus = computed(() => {
  if (!searchKeyword.value) {
    return permissionStore.menus
  }

  return filterMenusByKeyword(permissionStore.menus, searchKeyword.value.toLowerCase())
})

// 快速功能菜单（核心前台业务操作）
const quickMenus = computed(() => [
  {
    key: 'room-status',
    label: '房态管理',
    path: '/front-desk/room-status',
    icon: () => h('i', { class: 'i-fe:grid text-16' }),
    priority: 1
  },
  {
    key: 'checkin',
    label: '入住登记',
    path: '/front-desk/checkin',
    icon: () => h('i', { class: 'i-fe:user-plus text-16' }),
    priority: 2
  },
  {
    key: 'checkout',
    label: '退房结账',
    path: '/front-desk/checkout',
    icon: () => h('i', { class: 'i-fe:user-minus text-16' }),
    priority: 3
  },
  {
    key: 'reservation',
    label: '预订管理',
    path: '/front-desk/reservation',
    icon: () => h('i', { class: 'i-fe:calendar text-16' }),
    priority: 4
  }
])

// 菜单总数
const totalMenuCount = computed(() => {
  return flattenMenus(permissionStore.menus).length
})

// 保存菜单状态
function saveMenuState() {
  try {
    const state = {
      collapsed: appStore.collapsed,
      searchKeyword: searchKeyword.value,
      expandedKeys: menu.value?.getExpandedKeys() || []
    }
    localStorage.setItem(MENU_STATE_KEY, JSON.stringify(state))
  } catch (error) {
  }
}

// 加载菜单状态
function loadMenuState() {
  try {
    const saved = localStorage.getItem(MENU_STATE_KEY)
    if (saved) {
      const state = JSON.parse(saved)
      if (typeof state.collapsed === 'boolean') {
        appStore.collapsed = state.collapsed
      }
      if (state.searchKeyword) {
        searchKeyword.value = state.searchKeyword
      }
      // 展开状态将在菜单渲染后恢复
      setTimeout(() => {
        if (menu.value && state.expandedKeys && state.expandedKeys.length > 0) {
          menu.value?.showOption()
        }
      }, 100)
    }
  } catch (error) {
  }
}

// 清除菜单状态
function clearMenuState() {
  try {
    localStorage.removeItem(MENU_STATE_KEY)
  } catch (error) {
  }
}

// 监听菜单状态变化
watch(() => appStore.collapsed, saveMenuState)
watch(searchKeyword, saveMenuState)

// 初始化时加载状态
loadMenuState()

watch(route, async () => {
  await nextTick()
  menu.value?.showOption()
  saveMenuState()
})

function handleMenuSelect(key, item) {
  if (isExternal(item.originPath)) {
    $dialog.confirm({
      type: 'info',
      title: t('menu.selectOpenMethod'),
      positiveText: t('menu.openExternal'),
      negativeText: t('menu.openInternal'),
      confirm() {
        window.open(item.originPath)
      },
      cancel: () => {
        router.push(item.path)
      },
    })
  }
  else {
    if(!item.path) return
    router.push(item.path)
  }
}

// 处理搜索
function handleSearch() {
  // 搜索逻辑已在computed中处理
}

// 处理快速菜单点击
function handleQuickMenuClick(menu) {
  if (menu.path) {
    router.push(menu.path)
  }
}
</script>

<style scoped lang="less">
/* 菜单容器 - 跟随主题色 */
.menu-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: linear-gradient(180deg,
    rgba(var(--primary-color-rgb), 0.08) 0%,
    rgba(255, 255, 255, 0.95) 30%,
    rgba(255, 255, 255, 0.9) 70%,
    rgba(var(--primary-color-rgb), 0.05) 100%
  );
  backdrop-filter: blur(12px);
  border-right: 1px solid rgba(var(--primary-color-rgb), 0.2);
}

/* 菜单搜索 */
.menu-search {
  padding: 12px;
  border-bottom: 1px solid rgba(var(--primary-color-rgb), 0.15);
}

/* 快速功能 */
.quick-menus {
  padding: 12px;
  border-bottom: 1px solid rgba(var(--primary-color-rgb), 0.15);
}

.quick-menu-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 600;
  color: #64748b;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.quick-menu-title i {
  font-size: 14px;
  color: #2A5DAA;
}

.quick-menu-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.quick-menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px 4px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.quick-menu-item:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.quick-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #2A5DAA 0%, #1E4A8C 100%);
  border-radius: 6px;
  color: white;
  font-size: 12px;
}

.quick-label {
  font-size: 10px;
  color: #374151;
  text-align: center;
  line-height: 1.2;
  font-weight: 500;
}

/* 主菜单区域 */
.main-menu {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

/* 菜单统计 */
.menu-stats {
  padding: 12px;
  border-top: 1px solid rgba(var(--primary-color-rgb), 0.15);
  background: rgba(var(--primary-color-rgb), 0.05);
}

.stats-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.stats-item:last-child {
  margin-bottom: 0;
}

.stats-label {
  font-size: 11px;
  color: #64748b;
  font-weight: 500;
}

.stats-value {
  font-size: 11px;
  color: #2A5DAA;
  font-weight: 600;
  background: rgba(42, 93, 170, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
}

/* 菜单深度美观优化 - 现代化设计 */
.side-menu {
  background: transparent;
  border-right: 1px solid rgba(var(--primary-color-rgb), 0.2);
  position: relative;
  padding: 8px 0;
  width: 100%; /* 确保菜单占满侧边栏宽度 */

  /* 重置所有菜单项的基础样式 */
  :deep(.n-menu-item),
  :deep(.n-submenu) {
    margin: 0;
    border-radius: 0;
    padding: 0;
    width: 100%;
  }

  /* 统一所有菜单项的样式 */
  :deep(.n-menu-item-content),
  :deep(.n-submenu-header) {
    height: 46px !important;
    padding: 0 12px 0 16px !important; /* 减小右侧内边距 */
    margin: 2px 0 !important; /* 移除水平边距，使其占满宽度 */
    border-radius: 0; /* 移除圆角 */
    display: flex;
    align-items: center;
    position: relative;
    transition: all 0.3s ease;
    background: transparent;
    border: none;
    box-shadow: none;
    width: 100%; /* 确保内容占满菜单项宽度 */

    /* 悬停效果 */
    &:hover {
      background: rgba(var(--primary-color), 0.08) !important;
      color: rgb(var(--primary-color)) !important;
    }

    /* 选中状态 */
    &.n-menu-item-content--selected {
      background: rgba(var(--primary-color), 0.1) !important;
      color: rgb(var(--primary-color)) !important;
      font-weight: 600;

      /* 选中状态的左边装饰 */
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        width: 3px;
        height: 24px;
        background: rgb(var(--primary-color));
        transform: translateY(-50%);
      }

      /* 选中状态的图标 */
      .n-menu-item-content__icon,
      .n-submenu-header__icon {
        color: rgb(var(--primary-color)) !important;
      }
    }
  }

  /* 统一图标样式 */
  :deep(.n-menu-item-content__icon),
  :deep(.n-submenu-header__icon) {
    width: 18px !important;
    height: 18px !important;
    min-width: 18px !important;
    margin-right: 10px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 16px !important;
    transition: all 0.3s ease !important;
    flex-shrink: 0;
    color: var(--text-color-2);
  }

  /* 确保图标内的i元素正确显示 */
  :deep(.n-menu-item-content__icon i),
  :deep(.n-submenu-header__icon i) {
    font-size: 16px !important;
    width: 16px !important;
    height: 16px !important;
    display: inline-block !important;
  }

  /* 统一文字样式 */
  :deep(.n-menu-item-content-header),
  :deep(.n-submenu-header-main) {
    font-size: 14px !important;
    font-weight: 500 !important;
    line-height: 1.4 !important;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--text-color-1);
    letter-spacing: -0.01em;
  }

  /* 子菜单箭头图标 */
  :deep(.n-submenu-header__arrow) {
    margin-left: auto !important;
    transition: all 0.3s ease !important;
    font-size: 14px !important;
    color: var(--text-color-3);
    opacity: 0.7;
  }

  /* 展开状态的箭头 */
  :deep(.n-submenu--expanded .n-submenu-header__arrow) {
    transform: rotate(90deg) !important;
    color: rgb(var(--primary-color));
    opacity: 1;
  }

  /* 子菜单内容区域 */
  :deep(.n-submenu-children) {
    background: transparent;
    margin: 0 0 0 20px !important; /* 子菜单缩进 */
    padding: 0;
    border: none;
    box-shadow: none;
    width: calc(100% - 20px); /* 调整子菜单宽度 */

    /* 子菜单项样式 */
    .n-menu-item {
      .n-menu-item-content {
        margin: 2px 0 !important; /* 移除水平边距 */
        padding: 0 12px 0 16px !important;
        height: 40px !important;
        position: relative;
        background: transparent;
        border: none;
        width: 100%;

        /* 子菜单左侧连接线 */
        &::before {
          content: '';
          position: absolute;
          left: -10px;
          top: 20px;
          width: 10px;
          height: 1px;
          background: var(--text-color-3);
          opacity: 0.3;
        }

        /* 选中状态的子菜单 */
        &.n-menu-item-content--selected {
          background: rgba(var(--primary-color), 0.1) !important;

          &::before {
            background: rgb(var(--primary-color));
            opacity: 0.6;
          }
        }
      }
    }
  }

  /* 折叠状态优化 */
  &.n-menu--collapsed {
    padding: 8px 0;

    :deep(.n-menu-item-content),
    :deep(.n-submenu-header) {
      margin: 2px auto !important;
      padding: 0 !important;
      justify-content: center !important;
      width: 48px !important;
      height: 44px !important;

      .n-menu-item-content__icon,
      .n-submenu-header__icon {
        margin-right: 0 !important;
        margin-left: 0 !important;
        font-size: 20px !important;
      }

      .n-menu-item-content-header,
      .n-submenu-header-main,
      .n-submenu-header__arrow {
        display: none !important;
      }

      /* 选中状态的左边装饰 */
      &.n-menu-item-content--selected::before {
        height: 16px;
      }
    }
  }

  /* 菜单项间距优化 */
  :deep(.n-menu-item + .n-menu-item),
  :deep(.n-menu-item + .n-submenu),
  :deep(.n-submenu + .n-menu-item),
  :deep(.n-submenu + .n-submenu) {
    margin-top: 1px;
  }
}

/* 暗色主题适配 */
.dark .menu-container {
  background: linear-gradient(180deg,
    rgba(var(--primary-color-rgb), 0.15) 0%,
    rgba(31, 41, 55, 0.95) 30%,
    rgba(31, 41, 55, 0.9) 70%,
    rgba(var(--primary-color-rgb), 0.1) 100%
  );
  border-right-color: rgba(var(--primary-color-rgb), 0.3);
}

.dark .menu-search {
  border-bottom-color: rgba(var(--primary-color-rgb), 0.25);
}

.dark .quick-menus {
  border-bottom-color: rgba(var(--primary-color-rgb), 0.25);
}

.dark .menu-stats {
  border-top-color: rgba(var(--primary-color-rgb), 0.25);
  background: rgba(var(--primary-color-rgb), 0.1);
}

.dark .side-menu {
  border-right-color: rgba(var(--primary-color-rgb), 0.3);

  :deep(.n-menu-item-content:hover),
  :deep(.n-submenu-header:hover) {
    background: rgba(var(--primary-color), 0.15) !important;
  }

  :deep(.n-menu-item-content--selected) {
    background: rgba(var(--primary-color), 0.2) !important;
  }

  :deep(.n-submenu-children) {
    .n-menu-item .n-menu-item-content::before {
      background: var(--text-color-3);
      opacity: 0.2;
    }
  }
}

/* 菜单动画优化 */
:deep(.n-submenu-children) {
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 确保菜单项文字不换行 */
:deep(.n-menu-item-content),
:deep(.n-submenu-header) {
  overflow: hidden;
}

:deep(.n-menu-item-content-header),
:deep(.n-submenu-header-main) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 菜单滚动条美化 */
.side-menu {
  &::-webkit-scrollbar {
    width: 3px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(var(--primary-color), 0.2);
    border-radius: 1.5px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(var(--primary-color), 0.3);
  }
}
</style>
