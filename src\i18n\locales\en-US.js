/**
 * English Language Pack
 */

export default {
  // Common
  common: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    add: 'Add',
    search: 'Search',
    reset: 'Reset',
    submit: 'Submit',
    back: 'Back',
    close: 'Close',
    refresh: 'Refresh',
    loading: 'Loading...',
    noData: 'No Data',
    success: 'Success',
    error: 'Error',
    warning: 'Warning',
    info: 'Info',
    yes: 'Yes',
    no: 'No',
    all: 'All',
    select: 'Select',
    clear: 'Clear',
    export: 'Export',
    import: 'Import',
    copy: 'Copy',
    view: 'View',
    detail: 'Detail',
    operation: 'Operation',
    status: 'Status',
    remark: 'Remark',
    createTime: 'Create Time',
    updateTime: 'Update Time',
    action: 'Action'
  },

  // Login Page
  login: {
    title: 'Hotel Management System',
    subtitle: 'Intelligent Hotel Management System',
    welcomeBack: 'Welcome Back',
    loginPrompt: 'Please login to your account to continue using the system',
    username: '<PERSON><PERSON><PERSON>',
    password: 'Password',
    usernamePlaceholder: 'Please enter username',
    passwordPlaceholder: 'Please enter password',
    captchaPlaceholder: 'Please enter captcha',
    loginBtn: 'Login',
    loginButton: 'Login Now',
    loggingIn: 'Logging in...',
    loginSuccess: 'Login Successful',
    loginFailed: 'Login Failed',
    usernameRequired: 'Please enter username',
    passwordRequired: 'Please enter password',
    captchaRequired: 'Please enter captcha',
    usernameRule: 'Username should be 3-20 characters',
    usernameInvalid: 'Username can only contain letters, numbers, underscores and Chinese characters',
    passwordRule: 'Password should be 6-20 characters',
    passwordWeak: 'Password is weak, recommend including uppercase, lowercase, numbers and symbols',
    captchaError: 'Captcha is incorrect',
    switchLanguage: 'Switch Language',
    rememberMe: 'Remember Me',
    forgotPassword: 'Forgot Password?',
    forgotPasswordTip: 'Please contact administrator to reset password',
    clickToRefresh: 'Click to refresh',
    keyboardHint: 'Press Enter to login, Esc to clear',
    readyToLogin: 'Ready to login',
    invalidCredentials: 'Invalid username or password',
    tooManyAttempts: 'Too many login attempts, please try again later',
    serverError: 'Server error, please try again later',
    networkError: 'Network connection failed, please check network settings',
    captchaRequired: 'Too many failed attempts, please enter captcha',
    securityTip: 'Your information will be securely encrypted during transmission',
    learnMore: 'Learn More',
    loginExpired: 'Login expired, please login again',
    redirecting: 'Redirecting...',
    features: {
      roomManagement: {
        title: 'Room Management',
        description: 'Real-time room status, intelligent allocation'
      },
      customerService: {
        title: 'Customer Service',
        description: 'Full tracking, attentive service'
      },
      dataAnalysis: {
        title: 'Data Analysis',
        description: 'Business insights, decision support'
      }
    }
  },

  // Navigation Menu
  menu: {
    dashboard: 'Dashboard',
    overview: 'Overview',
    hotel: 'Hotel Management',
    roomStatus: 'Room Status',
    member: 'Member Management',
    memberDetail: 'Member Detail',
    card: 'Member Card Management',
    system: 'System Management',
    systemStatus: 'System Status',
    searchPlaceholder: 'Search functions...',
    quickFunctions: 'Quick Functions',
    checkIn: 'Check-in',
    reservation: 'Reservation',
    selectOpenMethod: 'Please select opening method',
    openExternal: 'Open in External Link',
    openInternal: 'Open Embedded in Site',
    functionalModules: 'Functional Modules',
    searchResults: 'Search Results'
  },

  // Overview Page
  overview: {
    title: 'Function Center',
    welcome: 'Centralized management, quick navigation - one-stop access to all system function modules',
    todayDate: 'Today\'s Date',
    currentTime: 'Current Time',
    roomStatusEntry: 'Room Status',
    frontDeskCore: 'Front Desk Core',

    // Function Modules
    modules: {
      title: 'Core Function Modules',
      roomManagement: {
        title: 'Room Management',
        description: 'Real-time room status monitoring, room cleaning, maintenance management',
        features: ['Room Monitoring', 'Cleaning Management', 'Maintenance Tracking']
      },
      billing: {
        title: 'Billing Management',
        description: 'Room billing, consumption records, settlement management',
        features: ['Bill Inquiry', 'Consumption Statistics', 'Settlement Processing']
      },
      member: {
        title: 'Member Management',
        description: 'Member information management, points system, level management',
        features: ['Member Profile', 'Points Management', 'Level Upgrade']
      },
      reservation: {
        title: 'Reservation Management',
        description: 'Room reservation, reservation inquiry, reservation changes',
        features: ['Online Booking', 'Reservation Inquiry', 'Change Processing']
      }
    },

    // Data Overview
    dataOverview: {
      title: 'Business Data Overview',
      occupancyTrend: 'Occupancy Trend',
      roomTypeDistribution: 'Room Type Distribution',
      revenueStats: 'Revenue Statistics',
      roomStatus: 'Room Status',
      period: {
        last7Days: 'Last 7 Days',
        currentStatus: 'Current Status',
        thisMonth: 'This Month',
        realTime: 'Real Time'
      }
    },

    // Quick Actions
    quickActions: {
      title: 'Quick Actions',
      checkIn: {
        title: 'Quick Check-in',
        description: 'Process guest check-in'
      },
      checkOut: {
        title: 'Quick Check-out',
        description: 'Process guest check-out'
      },
      reservation: {
        title: 'New Reservation',
        description: 'Create new room reservation'
      },
      maintenance: {
        title: 'Maintenance Report',
        description: 'Submit room maintenance request'
      }
    },

    // System Info
    systemInfo: {
      title: 'System Information',
      description: 'Current system status and version information',
      version: 'System Version',
      lastUpdate: 'Last Update',
      techStack: 'Tech Stack',
      uptime: 'Uptime',
      uptimeFormat: {
        daysHours: '{days} days {hours} hours',
        hours: '{hours} hours'
      }
    },

    // Hotel Switcher
    hotelSwitcher: {
      title: 'Select Hotel',
      mainStore: 'Main Store',
      branch: 'Branch',
      online: 'Online',
      offline: 'Offline',
      switchSuccess: 'Switched to {name}'
    },

    // Charts
    charts: {
      occupancyTrend: 'Occupancy Trend',
      last7Days: 'Last 7 Days',
      roomTypeDistribution: 'Room Type Distribution',
      currentStatus: 'Current Status',
      revenueStats: 'Revenue Statistics',
      thisMonth: 'This Month',
      roomStatus: 'Room Status',
      realTime: 'Real Time',
      weekdays: {
        monday: 'Mon',
        tuesday: 'Tue',
        wednesday: 'Wed',
        thursday: 'Thu',
        friday: 'Fri',
        saturday: 'Sat',
        sunday: 'Sun'
      },
      roomTypes: {
        standard: 'Standard',
        deluxe: 'Deluxe',
        suite: 'Suite',
        presidential: 'Presidential'
      },
      revenue: {
        room: 'Room Revenue',
        dining: 'Dining Revenue',
        service: 'Service Revenue'
      },
      roomStatusLabels: {
        occupied: 'Occupied',
        available: 'Available',
        maintenance: 'Maintenance',
        cleaning: 'Cleaning'
      }
    },

    // Check-in Management
    checkin: {
      title: 'Check-in',
      roomNumber: 'Room',
      salesInfo: 'Sales Information',
      customerInfo: 'Customer Information',
      checkinInfo: 'Check-in Information',
      roomList: 'Room List for Check-in',

      // Sales information fields
      sellType: 'Sales Type',
      saleRule: 'Sales Rule',
      checkinType: 'Check-in Type',
      isSecret: 'Secret Room',
      priceScheme: 'Price Scheme',
      intermediary: 'Unit/Intermediary',
      orderSource: 'Order Source',

      // Customer information fields
      contactName: 'Contact Name',
      contactPhone: 'Contact Phone',
      externalOrderNo: 'External Order No.',

      // Check-in information fields
      stayDuration: 'Stay Duration',
      checkoutTime: 'Check-out Time',
      roomPrice: 'Room Price',
      remark: 'Remark',

      // Room list fields
      roomNumber: 'Room No.',
      roomType: 'Room Type',
      price: 'Price (¥)',
      deposit: 'Deposit (¥)',
      package: 'Package',
      actions: 'Actions',

      // Buttons
      addRoom: 'Add Room',
      changeRoom: 'Change Room',
      removeRoom: 'Remove',
      readCard: 'Read Card',
      confirm: 'Confirm Check-in',

      // Placeholders
      selectSellType: 'Please select sales type',
      selectSaleRule: 'Please select sales rule',
      selectCheckinType: 'Please select check-in type',
      selectPriceScheme: 'Please select price scheme',
      selectIntermediary: 'Please select unit/intermediary',
      selectOrderSource: 'Please select order source',
      inputContactName: 'Please enter contact name',
      inputContactPhone: 'Please enter phone number',
      inputExternalOrderNo: 'Please enter external order number',
      inputStayDuration: 'Please enter stay {unit}',
      selectPackage: 'Select package',

      // Validation messages
      validation: {
        sellTypeRequired: 'Please select sales type',
        saleRuleRequired: 'Please select sales rule',
        checkinTypeRequired: 'Please select check-in type',
        priceSchemeRequired: 'Please select price scheme',
        orderSourceRequired: 'Please select order source',
        contactNameRequired: 'Please enter contact name',
        contactPhoneRequired: 'Please enter contact phone',
        stayDurationRequired: 'Please enter stay duration',
        checkoutTimeRequired: 'Please select check-out time',
        roomRequired: 'Please select at least one room'
      },

      // Success messages
      success: {
        checkinSuccess: 'Check-in successful! Total {count} rooms',
        roomAdded: 'Added {count} rooms',
        roomChanged: 'Room changed to {roomNumber}',
        roomRemoved: 'Room removed',
        cardReadSuccess: 'ID card read successfully'
      },

      // Error messages
      error: {
        cardReadFailed: 'Failed to read ID card, please check device connection',
        noCardInfo: 'No ID card information read',
        changeRoomSingleOnly: 'Only one room can be selected when changing room',
        minOneRoom: 'At least one room must be kept'
      },

      // Time units
      timeUnit: {
        day: 'day',
        hour: 'hour',
        month: 'month'
      }
    }
  },

  // Room Status Page
  roomStatus: {
    title: 'Room Status Management',
    refresh: 'Refresh',
    refreshing: 'Updating room status data...',
    autoRefresh0: 'Auto Refresh',
    autoRefreshOn: 'Auto Refresh',
    autoRefreshOff: 'Manual Refresh',
    manualRefresh: 'Manual Refresh',
    quickCheckin: 'Quick Check-in',
    lastUpdate: 'Last Update',
    justNow: 'Just Now',
    minutesAgo: '{minutes} minutes ago',

    // Card Size
    cardSizeLarge: 'Large Cards',
    cardSizeMedium: 'Medium Cards',
    cardSizeSmall: 'Small Cards',

    // Layout Mode
    layoutTop: 'Top Layout',
    layoutLeft: 'Left Layout',
    layoutBottom: 'Bottom Layout',
    quickReservation: 'Quick Reservation',
    updating: 'Updating',
    refreshStatus: 'Refresh Status',
    searchPlaceholder: 'Search room number or guest name',
    loadingRoomData: 'Loading room data...',
    retryFetch: 'Retry Fetch',
    noRoomData: 'No room data available',
    refreshData: 'Refresh Data',
    cleanStatusLegend: 'Clean Status',
    businessStatusLegend: 'Business Status',
    roomUnit: ' rooms',
    filters: {
      building: 'Filter by Building',
      floor: 'Select Floor',
      roomType: 'Filter by Room Type',
      status: 'Filter by Status',
      cleanStatus: 'Clean Status',
      search: 'Search Room Number',
      all: 'All'
    },
    cardSize: {
      large: 'Large Card',
      medium: 'Medium Card',
      small: 'Small Block'
    },

    // Room Status
    status: {
      occupied: 'Occupied',
      available: 'Available',
      checkout: 'Check-out',
      reserved: 'Reserved'
    },

    // Clean Status
    cleanStatus: {
      dirty: 'Dirty',
      cleaning: 'Cleaning',
      maintenance: 'Maintenance',
      inspecting: 'Inspecting',
      blocked: 'Blocked',
      noshow: 'No Show'
    },

    // Actions
    actions: {
      checkIn: 'Check-in',
      checkOut: 'Check-out',
      clean: 'Clean',
      maintenance: 'Maintenance',
      block: 'Block',
      unblock: 'Unblock',
      detail: 'Detail'
    },

    // Context Menu
    contextMenu: {
      roomDetail: 'Room Detail',
      checkIn: 'Process Check-in',
      checkOut: 'Process Check-out',
      startCleaning: 'Start Cleaning',
      completeCleaning: 'Complete Cleaning',
      startMaintenance: 'Start Maintenance',
      completeMaintenance: 'Complete Maintenance',
      blockRoom: 'Block Room',
      unblockRoom: 'Unblock Room'
    },

    // Auto Refresh
    autoRefresh: {
      enabled: 'Auto Refresh',
      interval: 'Refresh Interval',
      seconds: 'Seconds',
      refreshing: 'Refreshing...',
      lastRefresh: 'Last Refresh'
    },

    // Card Size
    cardSize: {
      large: 'Large Card',
      medium: 'Medium Card',
      small: 'Small Card'
    },

  },

  // Member Management
  member: {
    title: 'Member Management',
    list: 'Member List',
    add: 'Add Member',
    edit: 'Edit Member',
    detail: 'Member Detail',
    search: 'Search Member',
    name: 'Name',
    phone: 'Phone',
    email: 'Email',
    level: 'Member Level',
    points: 'Points',
    balance: 'Balance',
    status: 'Status',
    registerTime: 'Register Time',
    lastLoginTime: 'Last Login Time'
  },

  // System Management
  system: {
    title: 'System Management',
    status: 'System Status',
    settings: 'System Settings',
    logs: 'System Logs',
    backup: 'Data Backup',
    restore: 'Data Restore'
  },

  // Theme Settings
  theme: {
    title: 'Theme Settings',
    light: 'Light Theme',
    dark: 'Dark Theme',
    auto: 'Follow System',
    blue: 'Blue Theme',
    green: 'Green Theme',
    purple: 'Purple Theme',
    orange: 'Orange Theme',
    red: 'Red Theme',
    switchSuccess: 'Switched to {theme} theme'
  },

  // Language Settings
  language: {
    title: 'Language Settings',
    chinese: '简体中文',
    english: 'English',
    switchSuccess: 'Language switched to {language}'
  },

  // User Related
  user: {
    profile: 'Profile',
    switchRole: 'Switch Role',
    logout: 'Logout',
    logoutSuccess: 'Successfully logged out'
  },

  // Error Pages
  error: {
    404: {
      title: 'Page Not Found',
      description: 'Sorry, the page you are looking for does not exist',
      backHome: 'Back to Home'
    },
    403: {
      title: 'Access Denied',
      description: 'Sorry, you do not have permission to access this page',
      backHome: 'Back to Home'
    },
    500: {
      title: 'Server Error',
      description: 'Sorry, there was a server error',
      backHome: 'Back to Home'
    }
  },
      // Hotel Management
    hotel: {
      checkin: {
        validation: {
          sellTypeRequired: 'Please select sales type',
          saleRuleRequired: 'Please select sales rule',
          checkinTypeRequired: 'Please select check-in type',
          priceSchemeRequired: 'Please select price scheme',
          orderSourceRequired: 'Please select order source',
          contactNameRequired: 'Please enter contact name',
          contactPhoneRequired: 'Please enter contact phone',
          stayDurationRequired: 'Please enter stay duration',
          checkoutTimeRequired: 'Please select checkout time'
        },
        success: {
          checkinSuccess: 'Successfully checked in {count} room(s)'
        }
      }
    },

  // Message Tips
  message: {
    saveSuccess: 'Save successful',
    saveFailed: 'Save failed',
    deleteSuccess: 'Delete successful',
    deleteFailed: 'Delete failed',
    updateSuccess: 'Update successful',
    updateFailed: 'Update failed',
    addSuccess: 'Add successful',
    addFailed: 'Add failed',
    operationSuccess: 'Operation successful',
    operationFailed: 'Operation failed',
    networkError: 'Network error, please try again later',
    permissionDenied: 'Permission denied',
    dataNotFound: 'Data not found',
    parameterError: 'Parameter error'
  }
}
