import { request } from '@/utils'

/**
 * 切换管理员账号
 * @param {Object} params - 切换参数
 * @param {number} params.admin_id - 当前管理员ID
 * @param {number} params.to_admin_id - 目标管理员ID
 * @returns {Promise} 切换结果
 */
export function toggleAdmin(params) {
  return request({
    url: '/admin/Admin/toggleAdmin',
    method: 'post',
    data: params
  })
}

/**
 * 获取管理员信息
 * @param {number} adminId - 管理员ID
 * @returns {Promise} 管理员信息
 */
export function getAdminInfo(adminId) {
  return request({
    url: `/admin/info/${adminId}`,
    method: 'get'
  })
}
