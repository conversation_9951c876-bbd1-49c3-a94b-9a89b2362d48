<template>
  <div class="room-status-page" :class="`layout-${layoutMode}`">
    <!-- 无感刷新指示器 -->
    <div v-if="refreshing" class="refresh-indicator">
      <n-spin size="small" />
      <span>{{ t('roomStatus.refreshing') }}</span>
    </div>

    <!-- 内容容器 - 包含左侧边栏和主内容区域 -->
    <div class="content-container" :class="{ 'with-sidebar': layoutMode === 'left' }">
      <!-- 左侧筛选面板 (仅在左侧布局时显示) -->
    <div v-if="layoutMode === 'left'" class="left-sidebar">
      <div class="sidebar-content">
        <!-- 筛选器 -->
        <div class="sidebar-filters">
          <div class="sidebar-header">
            <h4 class="sidebar-title">筛选条件</h4>
            <!-- 布局切换按钮 -->
            <n-dropdown
              :options="layoutOptions"
              @select="handleLayoutChange"
              trigger="hover"
              placement="bottom-start"
            >
              <n-button size="tiny" quaternary class="layout-switch-btn">
                <template #icon>
                  <i class="i-material-symbols:view-module text-16"></i>
                </template>
                布局
              </n-button>
            </n-dropdown>
          </div>
          <div class="filter-group">
            <label>楼栋</label>
            <n-select
              v-model:value="selectedBuilding"
              :placeholder="t('roomStatus.filters.building')"
              :options="buildingOptions"
              @update:value="handleBuildingChange"
              clearable
              size="small"
            />
          </div>
          <div class="filter-group">
            <label>楼层</label>
            <n-select
              v-model:value="selectedFloor"
              :placeholder="$t('roomStatus.filters.floor')"
              :options="floorOptions"
              @update:value="handleFloorChange"
              clearable
              size="small"
            />
          </div>
          <div class="filter-group">
            <label>房型</label>
            <n-select
              v-model:value="selectedRoomType"
              :placeholder="$t('roomStatus.filters.roomType')"
              :options="roomTypeOptions"
              @update:value="handleRoomTypeChange"
              clearable
              size="small"
            />
          </div>
          <div class="filter-group">
            <label>状态</label>
            <n-select
              v-model:value="selectedStatus"
              :placeholder="$t('roomStatus.filters.status')"
              :options="statusOptions"
              @update:value="handleStatusChange"
              clearable
              size="small"
            />
          </div>
          <div class="filter-group">
            <label>清洁</label>
            <n-select
              v-model:value="selectedCleanStatus"
              :placeholder="$t('roomStatus.filters.cleanStatus')"
              :options="cleanStatusOptions"
              @update:value="handleCleanStatusChange"
              clearable
              size="small"
            />
          </div>
        </div>

        <!-- 左侧统计面板 -->
        <div class="sidebar-stats">
          <h4 class="sidebar-title">房态统计</h4>
          <div class="grid-stats">
            <!-- 业务状态 -->
            <div
              class="stat-item-grid clickable"
              :class="{ active: isStatusActive('stay') }"
              :style="getStatItemStyle('occupied')"
              @click="handleStatClick('stay')"
            >
              <span class="stat-number">{{ roomStats.occupied }}</span>
              <span class="stat-label">已住</span>
            </div>
            <div
              class="stat-item-grid clickable"
              :class="{ active: isStatusActive('vacant') }"
              :style="getStatItemStyle('available')"
              @click="handleStatClick('vacant')"
            >
              <span class="stat-number">{{ roomStats.available }}</span>
              <span class="stat-label">空房</span>
            </div>
            <div
              class="stat-item-grid clickable"
              :class="{ active: isStatusActive('reserved') }"
              :style="getStatItemStyle('reserved')"
              @click="handleStatClick('reserved')"
            >
              <span class="stat-number">{{ roomStats.reserved }}</span>
              <span class="stat-label">预订</span>
            </div>

            <!-- 清洁状态 -->
            <div
              class="stat-item-grid clickable clean-status"
              :class="{ active: selectedCleanStatus === 2 }"
              :style="getStatItemStyle('dirty')"
              @click="handleCleanStatClick(2)"
            >
              <span class="stat-number">{{ roomStats.dirty }}</span>
              <span class="stat-label">脏房</span>
            </div>
            <div
              v-show="roomStats.cleaning > 0"
              class="stat-item-grid clickable clean-status"
              :class="{ active: selectedCleanStatus === 3 }"
              :style="getStatItemStyle('cleaning')"
              @click="handleCleanStatClick(3)"
            >
              <span class="stat-number">{{ roomStats.cleaning }}</span>
              <span class="stat-label">清洁中</span>
            </div>
            <div
              v-show="roomStats.maintenance > 0"
              class="stat-item-grid clickable clean-status"
              :class="{ active: selectedCleanStatus === 4 }"
              :style="getStatItemStyle('maintenance')"
              @click="handleCleanStatClick(4)"
            >
              <span class="stat-number">{{ roomStats.maintenance }}</span>
              <span class="stat-label">维修</span>
            </div>
          </div>
        </div>

        <!-- 左侧状态图例 -->
        <div class="sidebar-legend">
          <h4 class="sidebar-title">状态图例</h4>
          <!-- 清洁状态图例 -->
          <div class="legend-section">
            <span class="legend-title">清洁:</span>
            <div class="legend-dots">
              <div
                v-for="statusConfig in cleanStatusConfig"
                :key="'clean-' + statusConfig.id"
                :class="['legend-dot', { active: selectedCleanStatus === statusConfig.id }]"
                @click="handleCleanStatusLegendClick(statusConfig.id)"
                :title="`${statusConfig.name}: ${statusConfig.count}间`"
                :style="{ backgroundColor: statusConfig.color }"
              >
                <span class="dot-char">{{ statusConfig.name.charAt(0) }}</span>
                <span class="dot-count">{{ statusConfig.count }}</span>
              </div>
            </div>
          </div>
          <!-- 业务状态图例 -->
          <div class="legend-section">
            <span class="legend-title">业务:</span>
            <div class="legend-dots">
              <div
                v-for="statusConfig in roomStatusConfig"
                :key="'business-' + statusConfig.id"
                :class="['legend-dot', { active: selectedStatus === statusConfig.sign }]"
                @click="handleBusinessStatusLegendClick(statusConfig.sign)"
                :title="`${statusConfig.name}: ${statusConfig.count}间`"
                :style="{ backgroundColor: statusConfig.color }"
              >
                <span class="dot-char">{{ statusConfig.name.charAt(0) }}</span>
                <span class="dot-count">{{ statusConfig.count }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 左侧卡片大小控制 -->
        <div class="sidebar-view-controls">
          <h4 class="sidebar-title">卡片大小</h4>
          <div class="vertical-view-controls">
            <n-button
              :type="cardSize === 'large' ? 'primary' : 'default'"
              @click="setCardSize('large')"
              size="small"
              class="view-control-btn"
            >
              <i class="i-material-symbols:view-module"></i>
              大卡片
            </n-button>
            <n-button
              :type="cardSize === 'medium' ? 'primary' : 'default'"
              @click="setCardSize('medium')"
              size="small"
              class="view-control-btn"
            >
              <i class="i-material-symbols:view-comfy"></i>
              中卡片
            </n-button>
            <n-button
              :type="cardSize === 'small' ? 'primary' : 'default'"
              @click="setCardSize('small')"
              size="small"
              class="view-control-btn"
            >
              <i class="i-material-symbols:view-compact"></i>
              小卡片
            </n-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content" :class="{ 'with-sidebar': layoutMode === 'left' }">
      <!-- 顶部筛选工具栏 (仅在顶部布局时显示) -->
      <div v-if="layoutMode === 'top'" class="room-filter-bar">
      <div class="filter-left">
        <n-select
          v-model:value="selectedBuilding"
          :placeholder="t('roomStatus.filters.building')"
          :options="buildingOptions"
          style="width: 100px"
          @update:value="handleBuildingChange"
          clearable
          size="small"
        />
        <n-select
          v-model:value="selectedFloor"
          :placeholder="$t('roomStatus.filters.floor')"
          :options="floorOptions"
          style="width: 100px"
          @update:value="handleFloorChange"
          clearable
          size="small"
        />
        <n-select
          v-model:value="selectedRoomType"
          :placeholder="$t('roomStatus.filters.roomType')"
          :options="roomTypeOptions"
          style="width: 100px"
          @update:value="handleRoomTypeChange"
          clearable
          size="small"
        />
        <n-select
          v-model:value="selectedStatus"
          :placeholder="$t('roomStatus.filters.status')"
          :options="statusOptions"
          style="width: 100px"
          @update:value="handleStatusChange"
          clearable
          size="small"
        />
        <n-select
          v-model:value="selectedCleanStatus"
          :placeholder="$t('roomStatus.filters.cleanStatus')"
          :options="cleanStatusOptions"
          style="width: 100px"
          @update:value="handleCleanStatusChange"
          clearable
          size="small"
        />
      </div>

      <!-- 极简状态图例 - 显示状态首字和数量 -->
      <div class="status-mini-bar">
        <!-- 清洁状态 -->
        <div class="status-group">
          <span class="group-label">{{ t('roomStatus.cleanStatusLegend') }}:</span>
          <div
            v-for="statusConfig in cleanStatusConfig"
            :key="'clean-' + statusConfig.id"
            :class="['status-dot', { active: selectedCleanStatus === statusConfig.id }]"
            @click="handleCleanStatusLegendClick(statusConfig.id)"
            :title="`${statusConfig.name}: ${statusConfig.count}${t('roomStatus.roomUnit')}`"
            :style="{ backgroundColor: statusConfig.color }"
          >
            <span class="status-char">{{ statusConfig.name.charAt(0) }}</span>
            <span class="status-count">{{ statusConfig.count }}</span>
          </div>
        </div>

        <!-- 分隔符 -->
        <div class="separator">|</div>

        <!-- 业务状态 -->
        <div class="status-group">
          <span class="group-label">{{ t('roomStatus.businessStatusLegend') }}:</span>
          <div
            v-for="statusConfig in roomStatusConfig"
            :key="'business-' + statusConfig.id"
            :class="['status-dot', { active: selectedStatus === statusConfig.sign }]"
            @click="handleBusinessStatusLegendClick(statusConfig.sign)"
            :title="`${statusConfig.name}: ${statusConfig.count}${t('roomStatus.roomUnit')}`"
            :style="{ backgroundColor: statusConfig.color }"
          >
            <span class="status-char">{{ statusConfig.name.charAt(0) }}</span>
            <span class="status-count">{{ statusConfig.count }}</span>
          </div>
        </div>
      </div>

      <div class="filter-right">
        <!-- 布局切换按钮 -->
        <div class="layout-switcher-top">
          <n-dropdown
            :options="layoutOptions"
            @select="handleLayoutChange"
            trigger="hover"
            placement="bottom-end"
          >
            <n-button size="small" quaternary class="layout-switch-btn">
              <template #icon>
                <i class="i-material-symbols:view-module text-16"></i>
              </template>
              布局
            </n-button>
          </n-dropdown>
        </div>

        <div class="view-controls">
          <n-button-group>
            <n-button
              :type="cardSize === 'large' ? 'primary' : 'default'"
              @click="setCardSize('large')"
              size="small"
            >
              <i class="i-material-symbols:view-module"></i>
              {{ $t('roomStatus.cardSize.large') }}
            </n-button>
            <n-button
              :type="cardSize === 'medium' ? 'primary' : 'default'"
              @click="setCardSize('medium')"
              size="small"
            >
              <i class="i-material-symbols:grid-view"></i>
              {{ $t('roomStatus.cardSize.medium') }}
            </n-button>
            <n-button
              :type="cardSize === 'small' ? 'primary' : 'default'"
              @click="setCardSize('small')"
              size="small"
            >
              <i class="i-material-symbols:apps"></i>
              {{ $t('roomStatus.cardSize.small') }}
            </n-button>
          </n-button-group>
        </div>

        <n-input
          v-model:value="searchKeyword"
          :placeholder="t('roomStatus.searchPlaceholder')"
          clearable
          style="width: 160px"
          size="small"
          @input="handleSearch"
        >
          <template #prefix>
            <i class="i-material-symbols:search"></i>
          </template>
        </n-input>
      </div>
    </div>

    <!-- 房态网格 - 按楼栋楼层分组 -->
    <div class="room-grid-container">
      <!-- 骨架屏加载状态 -->
      <div v-if="loading" class="skeleton-container">
        <div class="skeleton-building" v-for="i in 2" :key="i">
          <div class="skeleton-building-header">
            <div class="skeleton-building-title"></div>
            <div class="skeleton-building-stats"></div>
          </div>
          <div class="skeleton-floor" v-for="j in 3" :key="j">
            <div class="skeleton-floor-header">
              <div class="skeleton-floor-title"></div>
              <div class="skeleton-floor-stats"></div>
            </div>
            <div :class="['skeleton-room-grid', `skeleton-grid-${cardSize}`]">
              <div
                v-for="k in (cardSize === 'large' ? 6 : cardSize === 'medium' ? 12 : 20)"
                :key="k"
                :class="['skeleton-room-card', `skeleton-card-${cardSize}`]"
              >
                <div class="skeleton-room-content">
                  <div class="skeleton-room-number"></div>
                  <div class="skeleton-room-type"></div>
                  <div v-if="cardSize === 'large'" class="skeleton-guest-info">
                    <div class="skeleton-guest-name"></div>
                    <div class="skeleton-guest-phone"></div>
                  </div>
                  <div v-if="cardSize !== 'small'" class="skeleton-room-status"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <n-alert type="error" :title="error">
          <template #action>
            <n-button size="small" @click="fullDataRefresh">
              {{ t('roomStatus.retryFetch') }}
            </n-button>
          </template>
        </n-alert>
      </div>

      <!-- 无数据状态 -->
      <div v-else-if="groupedRooms.length === 0" class="empty-container">
        <n-empty :description="t('roomStatus.noRoomData')">
          <template #extra>
            <n-button @click="fullDataRefresh">{{ t('roomStatus.refreshData') }}</n-button>
          </template>
        </n-empty>
      </div>

      <!-- 房间数据 -->
      <div
        v-else-if="groupedRooms && groupedRooms.length > 0"
        v-for="building in groupedRooms"
        :key="building.buildingName"
        class="building-section"
      >
        <div class="building-header">
          <h3 class="building-title">{{ building.buildingName }}</h3>
          <span class="building-stats">共{{ building.totalRooms }}间房</span>
        </div>

        <div
          v-for="floor in building.floors"
          :key="floor.floorNumber"
          class="floor-section"
        >
          <div class="floor-header">
            <h4 class="floor-title">{{ floor.floorNumber }}楼</h4>
            <div class="floor-stats">
              <span class="floor-room-count">{{ floor.rooms.length }}间</span>
              <div class="floor-status-summary">
                <span
                  v-for="(count, status) in floor.statusSummary"
                  :key="status"
                  :class="['status-dot', `status-${status}`]"
                  :title="`${statusTextMap[status] || '未知'}: ${count}间`"
                >
                  {{ count }}
                </span>
              </div>
            </div>
          </div>

          <div :class="['room-grid', `grid-${cardSize}`]">
            <div
              v-for="room in floor.rooms"
              :key="`room-${room.roomNumber || room.room_number}-${room.id || room.room_id || 'empty'}-${forceRerenderKey}`"
              :class="['room-card', `card-${cardSize}` , { 'connect-room-hovered': hoveredConnectCode && room.connectCode && hoveredConnectCode === room.connectCode }]"
              :style="getRoomCardStyle(room)"
              :data-connect-code="room.isConnectedRoom && room.connectCode ? room.connectCode : null"
              @click="handleRoomClick(room)"
              @contextmenu.prevent="handleRoomRightClick(room, $event)"
              @mouseenter="handleRoomMouseEnter(room)"
              @mouseleave="handleRoomMouseLeave(room)"
            >
                <!-- 大卡片模式 -->
                <template v-if="cardSize === 'large'">
            <!-- 顶部信息行：房间号 + 房型 + 联房标识 -->
            <div class="room-header-large">
              <div class="room-basic-info">
                <span class="room-number-large">{{ room.roomNumber }}</span>
                <span class="room-type-large">{{ room.roomType }}</span>
              </div>
              <!-- 联房标识 -->
              <div
                v-if="room.isConnectedRoom && room.connectCode"
                class="connect-room-badge-large"
                :title="`联房组: ${room.connectCode}`"
              >
                <i class="i-mdi:link-variant"></i>
              </div>
            </div>

            <!-- 主要信息：客人信息 -->
            <div v-if="hasGuestInfo(room)" class="guest-main-info">
              <div class="guest-primary">
                <div class="guest-name-with-badges">
                  <span class="guest-name-large">{{ room.guestName }}</span>
                  <!-- 会员等级和渠道来源标签（紧跟姓名） -->
                  <div v-if="room.memberGrade || room.channelSource" class="guest-badges-inline">
                    <span v-if="room.memberGrade" class="member-badge-inline">{{ room.memberGrade }}</span>
                    <span v-if="room.channelSource" class="channel-badge-inline">{{ room.channelSource }}</span>
                  </div>
                </div>
                <div v-if="room.checkInTime || room.checkOutTime" class="time-info-large">
                  <span v-if="room.checkInTime" class="checkin-time-large">
                    入住: {{ formatTime(room.checkInTime) }}
                  </span>
                  <span v-if="room.checkOutTime" class="checkout-time-large">
                    退房: {{ formatTime(room.checkOutTime) }}
                  </span>
                </div>
              </div>
              <div v-if="room.guestPhone" class="guest-secondary">
                {{ room.guestPhone }}
              </div>
            </div>

            <!-- 预订信息 -->
            <div v-else-if="hasReservationInfo(room)" class="reservation-main-info">
              <div class="reservation-primary">
                <div class="guest-name-large">{{ room.guestName || '预订客人' }}</div>
                <div v-if="getArrivalTime(room) || getExpectedArrival(room)" class="arrival-info-large">
                  <span v-if="getArrivalTime(room)">预计: {{ formatTime(getArrivalTime(room)) }}</span>
                  <span v-if="getExpectedArrival(room)">应到: {{ formatTime(getExpectedArrival(room)) }}</span>
                </div>
              </div>
            </div>

            <!-- 维修信息 -->
            <div v-if="room.maintenance" class="maintenance-info">
              <div class="maintenance-issue">{{ room.maintenance.issue }}</div>
              <div class="maintenance-assignee">负责人: {{ room.maintenance.assignedTo }}</div>
            </div>

            <!-- 清洁信息 -->
            <div v-if="room.housekeeping && (room.status === 'cleaning' || room.status === 'inspecting')" class="housekeeping-info">
              <div v-if="room.housekeeping.assignedTo" class="hk-assignee">
                清洁员: {{ room.housekeeping.assignedTo }}
              </div>
              <div v-if="room.housekeeping.inspector" class="hk-inspector">
                查房员: {{ room.housekeeping.inspector }}
              </div>
              <div v-if="room.housekeeping.estimatedCompletion" class="hk-eta">
                预计完成: {{ formatTime(room.housekeeping.estimatedCompletion) }}
              </div>
            </div>

            <!-- 底部信息行：订单信息 + 状态标识 -->
            <div class="room-bottom-info">
              <!-- 订单信息（简化显示） -->
              <div v-if="getTodayOrders(room.futureOrders).length > 0 || getFutureOrders(room.futureOrders).length > 0" class="orders-summary">
                <span v-if="getTodayOrders(room.futureOrders).length > 0" class="today-orders-summary">
                  <i class="i-mdi:calendar-today"></i>
                  今日{{ getTodayOrders(room.futureOrders).length }}单
                </span>
                <span v-if="getFutureOrders(room.futureOrders).length > 0" class="future-orders-summary">
                  <i class="i-mdi:calendar-clock"></i>
                  未来{{ getFutureOrders(room.futureOrders).length }}单
                </span>
              </div>

            </div>

            <!-- 底部状态标签区域 -->
            <div class="bottom-status-area">
              <!-- 左侧：房间状态标签 -->
              <div class="status-badges-left">
                <!-- 优先显示业务状态，如果是空房或净房则显示清洁状态 -->
                <div
                  v-if="room.roomStatusName && !shouldShowCleanStatus(room)"
                  class="room-status-badge-large"
                  :style="getRoomStatusBadgeStyle(room)"
                >
                  <i :class="getStatusIcon(room.status)"></i>
                  <span>{{ room.roomStatusName }}</span>
                </div>
                <div
                  v-else-if="room.cleanStatusName"
                  class="room-clean-badge-large"
                  :class="getCleanStatusClass(room)"
                  :style="getCleanStatusBadgeStyle(room)"
                >
                  <i :class="getCleanStatusIcon(room)"></i>
                  <span>{{ room.cleanStatusName }}</span>
                </div>

                <!-- 退房提醒（如果有的话） -->
                <div v-if="isCheckoutOverdue(room)" class="checkout-alert-large overdue">
                  <i class="i-mdi:alert-circle"></i>
                  <span>超时未退房</span>
                </div>
                <div v-else-if="isCheckoutSoon(room)" class="checkout-alert-large soon">
                  <i class="i-mdi:clock-alert"></i>
                  <span>即将退房</span>
                </div>
              </div>

              <!-- 右侧：离店提醒和欠费标签 -->
              <div class="status-badges-right">
                <div v-if="room.checkoutReminderInfo" class="checkout-reminder-large" :title="room.checkoutReminderInfo.fullText">
                  <i class="i-mdi:clock-alert-outline"></i>
                  <span>{{ room.checkoutReminderInfo.text }}</span>
                </div>
                <div v-if="room.debtInfo" class="debt-reminder-large" :title="room.debtInfo.fullText">
                  <i class="i-mdi:currency-cny"></i>
                  <span>{{ room.debtInfo.debtText }}</span>
                </div>
              </div>
            </div>
          </template>

          <!-- 中卡片模式 -->
          <template v-else-if="cardSize === 'medium'">
            <div class="room-header">
              <div class="room-number">{{ room.roomNumber }}</div>
              <!-- 清洁状态标识（中卡片右上角显示，仅首字） -->
              <div
                v-if="room.cleanStatusName && room.cleanStatusName !== '净'"
                class="room-clean-text-corner"
                :class="getCleanStatusClass(room)"
                :style="getCleanStatusBadgeStyle(room)"
                :title="room.cleanStatusName"
              >
                {{ room.cleanStatusName.charAt(0) }}
              </div>
            </div>

            <div class="room-type">{{ room.roomType }}</div>

            <div v-if="hasGuestInfo(room)" class="guest-info-compact">
              <div class="guest-name-with-badges-medium">
                <span class="guest-name">{{ room.guestName }}</span>
                <!-- 会员等级和渠道来源标签（中卡片，简化显示） -->
                <div v-if="room.memberGrade || room.channelSource" class="guest-badges-medium-simple">
                  <span v-if="room.memberGrade" class="member-badge-simple" :title="room.memberGrade">{{ room.memberGrade.charAt(0) }}</span>
                  <span v-if="room.channelSource" class="channel-badge-simple" :title="room.channelSource">{{ room.channelSource.charAt(0) }}</span>
                </div>
              </div>
            </div>

            <!-- 离店提醒标签（中卡片） -->
            <div v-if="room.checkoutReminderInfo" class="checkout-reminder-medium">
              <i class="i-mdi:clock-alert-outline"></i>
              <span>{{ room.checkoutReminderInfo.remainingHours }}h离店</span>
            </div>

            <!-- 今日订单信息（中卡片模式） -->
            <div v-if="getTodayOrders(room.futureOrders).length > 0" class="today-orders-compact">
              <div class="today-indicator">
                <i class="i-mdi:calendar-today"></i>
                <span>{{ getTodayOrders(room.futureOrders).length }}个今日订单</span>
              </div>
              <!-- 订单信息（中卡片） -->
              <div v-if="getTodayOrders(room.futureOrders)[0]" class="order-info-medium">
                <span v-if="getBillInfoField(getTodayOrders(room.futureOrders)[0], 'bill_source_name')" class="order-source-medium">
                  {{ getBillInfoField(getTodayOrders(room.futureOrders)[0], 'bill_source_name') }}
                </span>
                <span v-if="getBillInfoField(getTodayOrders(room.futureOrders)[0], 'tempGrade_name')" class="member-type-medium">
                  {{ getBillInfoField(getTodayOrders(room.futureOrders)[0], 'tempGrade_name') }}
                </span>
              </div>
            </div>

            <!-- 未来订单信息（中卡片模式） -->
            <div v-if="getFutureOrders(room.futureOrders).length > 0" class="future-orders-compact">
              <div class="future-indicator">
                <i class="i-mdi:calendar-clock"></i>
                <span>{{ getFutureOrders(room.futureOrders).length }}个未来订单</span>
              </div>
              <!-- 订单信息（中卡片） -->
              <div v-if="getFutureOrders(room.futureOrders)[0]" class="order-info-medium">
                <span v-if="getBillInfoField(getFutureOrders(room.futureOrders)[0], 'bill_source_name')" class="order-source-medium">
                  {{ getBillInfoField(getFutureOrders(room.futureOrders)[0], 'bill_source_name') }}
                </span>
                <span v-if="getBillInfoField(getFutureOrders(room.futureOrders)[0], 'tempGrade_name')" class="member-type-medium">
                  {{ getBillInfoField(getFutureOrders(room.futureOrders)[0], 'tempGrade_name') }}
                </span>
              </div>
            </div>

            <!-- 底部标签组（中卡片） -->
            <div class="bottom-right-badges-medium">
              <!-- 左侧：联房标识 -->
              <div
                v-if="room.isConnectedRoom && room.connectCode"
                class="connect-room-badge-medium"
                :title="`联房组: ${room.connectCode}`"
              >
                <i class="i-mdi:link-variant"></i>
              </div>
              <div v-else></div> <!-- 占位元素，保持两端分布 -->

              <!-- 右侧：欠费标识 -->
              <div
                v-if="room.debtInfo"
                class="debt-badge-medium"
                :title="room.debtInfo.fullText"
              >
                欠
              </div>
            </div>

            <div v-if="room.tags && room.tags.length > 0" class="room-tags-compact">
              <span
                v-for="tag in room.tags"
                :key="tag"
                :class="['tag-dot', `tag-${tag}`]"
              ></span>
            </div>
          </template>

          <!-- 小方块模式 -->
          <template v-else>
            <div class="room-mini">
              <!-- 顶部：房间号 -->
              <div class="room-number-mini">{{ room.roomNumber }}</div>

              <!-- 房型信息 -->
              <div class="room-type-mini">{{ room.roomType }}</div>

              <!-- 客人姓名（如果有） -->
              <div v-if="hasGuestInfo(room)" class="guest-name-mini" :title="room.guestName">
                {{ room.guestName.charAt(0) }}
              </div>

              <!-- 底部：标签区域 -->
              <div class="room-badges-mini">
                <!-- 欠费提醒标签（小卡片，最高优先级） -->
                <div v-if="room.debtInfo" class="debt-reminder-mini" :title="room.debtInfo.fullText">
                  ¥
                </div>
                <!-- 离店提醒标签（小卡片，第二优先级） -->
                <div v-else-if="room.checkoutReminderInfo" class="checkout-reminder-mini" :title="room.checkoutReminderInfo.fullText">
                  {{ room.checkoutReminderInfo.remainingHours }}
                </div>
                <!-- 会员等级标签 -->
                <div v-else-if="room.memberGrade" class="member-badge-mini" :title="room.memberGrade">
                  {{ room.memberGrade.charAt(0) }}
                </div>
                <!-- 渠道来源标签 -->
                <div v-else-if="room.channelSource" class="channel-badge-mini" :title="room.channelSource">
                  {{ room.channelSource.charAt(0) }}
                </div>
                <!-- 如果没有客人信息，显示未来订单 -->
                <div v-else-if="!hasGuestInfo(room) && room.futureOrders && room.futureOrders.length > 0" class="future-indicator-mini">
                  <i class="i-mdi:calendar-clock"></i>
                  <span class="future-count-mini">{{ room.futureOrders.length }}</span>
                </div>
              </div>

              <!-- 清洁状态文字（小卡片下方显示） -->
              <div
                v-if="room.cleanStatusName"
                class="clean-status-text-mini"
                :class="getCleanStatusClass(room)"
                :style="{ color: getCleanStatusColor(room) }"
              >
                {{ room.cleanStatusName.charAt(0) }}
              </div>

              <!-- 联房标识（小卡片） -->
              <div
                v-if="room.isConnectedRoom && room.connectCode"
                class="connect-room-badge-mini"
                :title="`联房组: ${room.connectCode}`"
              >
                <i class="i-mdi:link-variant"></i>
              </div>

                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 房间操作右键菜单 -->
    <n-dropdown
      placement="bottom-start"
      trigger="manual"
      :x="contextMenuX"
      :y="contextMenuY"
      :options="contextMenuOptions"
      :show="showContextMenu"
      :on-clickoutside="closeContextMenu"
      @select="handleContextMenuSelect"
    />

    <!-- 办理入住弹窗 -->
    <CheckInModal
      v-model:show="showCheckInModal"
      :room-data="selectedRoomData"
      @success="handleModalSuccess"
    />

    <!-- 办理预订弹窗 -->
    <ReservationModal
      v-model:show="showReservationModal"
      :room-data="selectedRoomData"
      @success="handleModalSuccess"
    />

    <!-- 房间详情弹窗 -->
    <RoomDetailModal
      v-if="selectedRoomData"
      v-model:show="showRoomDetailModal"
      :room-data="selectedRoomData"
      @reservation="handleReservationFromDetail"
      @checkin="handleCheckInFromDetail"
      @checkout="handleCheckOutFromDetail"
      @room-service="handleRoomServiceFromDetail"
      @confirm-checkin="handleConfirmCheckInFromDetail"
      @cancel-reservation="handleCancelReservationFromDetail"
      @bill-detail="handleBillDetailFromDetail"
      @view-full-order="handleViewFullOrderFromDetail"
      @room-maintenance="handleRoomMaintenanceFromDetail"
      @room-cleaning="handleRoomCleaningFromDetail"
    />
    </div>
    </div> <!-- 关闭内容容器 -->

    <!-- 底部筛选栏 (仅在底部布局时显示) -->
    <div v-if="layoutMode === 'bottom'" class="bottom-toolbar">
      <div class="bottom-content">
        <!-- 底部筛选器 -->
        <div class="bottom-filters">
          <n-select
            v-model:value="selectedBuilding"
            :placeholder="t('roomStatus.filters.building')"
            :options="buildingOptions"
            style="width: 100px"
            @update:value="handleBuildingChange"
            clearable
            size="small"
          />
          <n-select
            v-model:value="selectedFloor"
            :placeholder="$t('roomStatus.filters.floor')"
            :options="floorOptions"
            style="width: 100px"
            @update:value="handleFloorChange"
            clearable
            size="small"
          />
          <n-select
            v-model:value="selectedRoomType"
            :placeholder="$t('roomStatus.filters.roomType')"
            :options="roomTypeOptions"
            style="width: 100px"
            @update:value="handleRoomTypeChange"
            clearable
            size="small"
          />
          <n-select
            v-model:value="selectedStatus"
            :placeholder="$t('roomStatus.filters.status')"
            :options="statusOptions"
            style="width: 100px"
            @update:value="handleStatusChange"
            clearable
            size="small"
          />
          <n-select
            v-model:value="selectedCleanStatus"
            :placeholder="$t('roomStatus.filters.cleanStatus')"
            :options="cleanStatusOptions"
            style="width: 100px"
            @update:value="handleCleanStatusChange"
            clearable
            size="small"
          />
          </div>

        <!-- 底部统计面板 - 简化版 -->
        <div class="bottom-stats-compact">
          <div
            class="stat-compact clickable"
            :class="{ active: isStatusActive('stay') }"
            :style="getStatItemStyle('occupied')"
            @click="handleStatClick('stay')"
          >
            <span class="stat-num">{{ roomStats.occupied }}</span>
            <span class="stat-text">住</span>
          </div>
          <div
            class="stat-compact clickable"
            :class="{ active: isStatusActive('vacant') }"
            :style="getStatItemStyle('available')"
            @click="handleStatClick('vacant')"
          >
            <span class="stat-num">{{ roomStats.available }}</span>
            <span class="stat-text">空</span>
          </div>
          <div
            class="stat-compact clickable"
            :class="{ active: isStatusActive('reserved') }"
            :style="getStatItemStyle('reserved')"
            @click="handleStatClick('reserved')"
          >
            <span class="stat-num">{{ roomStats.reserved }}</span>
            <span class="stat-text">订</span>
          </div>

        </div>

        <!-- 底部状态图例 -->
        <div class="bottom-legend">
          <!-- 清洁状态图例 -->
          <div class="bottom-legend-group">
            <span class="bottom-legend-title">清洁:</span>
            <div class="bottom-legend-dots">
              <div
                v-for="statusConfig in cleanStatusConfig"
                :key="'clean-' + statusConfig.id"
                :class="['bottom-legend-dot', { active: selectedCleanStatus === statusConfig.id }]"
                @click="handleCleanStatusLegendClick(statusConfig.id)"
                :title="`${statusConfig.name}: ${statusConfig.count}间`"
                :style="{ backgroundColor: statusConfig.color }"
              >
                <span class="bottom-dot-char">{{ statusConfig.name.charAt(0) }}</span>
                <span class="bottom-dot-count">{{ statusConfig.count }}</span>
              </div>
            </div>
          </div>
          <!-- 业务状态图例 -->
          <div class="bottom-legend-group">
            <span class="bottom-legend-title">业务:</span>
            <div class="bottom-legend-dots">
              <div
                v-for="statusConfig in roomStatusConfig"
                :key="'business-' + statusConfig.id"
                :class="['bottom-legend-dot', { active: selectedStatus === statusConfig.sign }]"
                @click="handleBusinessStatusLegendClick(statusConfig.sign)"
                :title="`${statusConfig.name}: ${statusConfig.count}间`"
                :style="{ backgroundColor: statusConfig.color }"
              >
                <span class="bottom-dot-char">{{ statusConfig.name.charAt(0) }}</span>
                <span class="bottom-dot-count">{{ statusConfig.count }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部卡片大小控制 -->
        <div class="bottom-view-controls">
          <n-button-group>
            <n-button
              :type="cardSize === 'large' ? 'primary' : 'default'"
              @click="setCardSize('large')"
              size="small"
            >
              <i class="i-material-symbols:view-module"></i>
            </n-button>
            <n-button
              :type="cardSize === 'medium' ? 'primary' : 'default'"
              @click="setCardSize('medium')"
              size="small"
            >
              <i class="i-material-symbols:view-comfy"></i>
            </n-button>
            <n-button
              :type="cardSize === 'small' ? 'primary' : 'default'"
              @click="setCardSize('small')"
              size="small"
            >
              <i class="i-material-symbols:view-compact"></i>
            </n-button>
          </n-button-group>
        </div>

        <!-- 底部刷新控制 -->
        <div class="bottom-refresh-control">
          <div class="auto-refresh-control">
            <n-switch
              v-model:value="autoRefreshEnabled"
              @update:value="toggleAutoRefresh"
            >
              <template #checked>{{ $t('roomStatus.autoRefresh0') }}</template>
              <template #unchecked>{{ $t('roomStatus.manualRefresh') }}</template>
            </n-switch>
            <n-select
              v-model:value="refreshInterval"
              :options="refreshIntervalOptions"
              style="width: 100px"
              size="small"
              @update:value="updateRefreshInterval"
            />
            <span class="refresh-countdown" v-if="autoRefreshEnabled">
              {{ refreshCountdown }}s
            </span>
          </div>
          <n-button @click="silentRefresh" :loading="refreshing" size="small">
            <i class="i-material-symbols:refresh"></i>
            {{ refreshing ? $t('roomStatus.updating') : $t('roomStatus.refreshStatus') }}
          </n-button>
        </div>

        <!-- 底部布局切换按钮 -->
        <div class="layout-switcher-bottom">
          <n-dropdown
            :options="layoutOptions"
            @select="handleLayoutChange"
            trigger="hover"
            placement="top-end"
          >
            <n-button size="small" quaternary class="layout-switch-btn">
              <template #icon>
                <i class="i-material-symbols:view-module text-16"></i>
              </template>
              布局
            </n-button>
          </n-dropdown>
        </div>
      </div>
    </div>

    <!-- 右键菜单 -->
    <RoomContextMenu
      v-model:show="showContextMenu"
      :x="contextMenuX"
      :y="contextMenuY"
      :room-data="contextMenuRoom"
      @action="handleContextMenuAction"
    />

    <!-- 订单详情弹窗 -->
    <OrderDetailModal
      v-model:show="showOrderDetail"
      :order-data="selectedOrderData"
      :bill-id="selectedBillId"
      @print="handleOrderPrint"
      @export="handleOrderExport"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, h, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import { useI18n } from 'vue-i18n'
import { useUserStore, useAppStore } from '@/store'
import { setupVisibilityLoginCheck } from '@/utils/auth-check'

import {
  getUsableRoomBuildingList,
  getUsableRoomFloorList,
  getUsableRoom,
  getRoomClearStatus,
  getRoomRecordStatus,
  getRoomBillDetail
} from './api'
import CheckInModal from '@/components/hotel/CheckInModal.vue'
import ReservationModal from '@/components/hotel/ReservationModal.vue'
import RoomDetailModal from '@/components/hotel/RoomDetailModal.vue'
import RoomContextMenu from '@/components/hotel/RoomContextMenu.vue'
import OrderDetailModal from '@/components/hotel/OrderDetailModal.vue'
import { useRoomSelectionStore } from '@/store'

const router = useRouter()
const $message = useMessage()
const { t } = useI18n()
const userStore = useUserStore()
const appStore = useAppStore()
const roomSelectionStore = useRoomSelectionStore()

// 开发模式影子对比（不影响 UI/行为）
const devShadow = import.meta.env.DEV && (location.search.includes('shadow=1') || localStorage.getItem('roomStatusShadow') === '1')

// 仅定义，实际在 onMounted 再注册，避免变量尚未初始化的访问
function setupShadowWatch() {
  if (!(import.meta.env.DEV && devShadow)) return
  try {
    watch([
      () => roomStats?.value,
      () => filteredRooms?.value?.length,
      () => groupedRooms?.value?.length,
    ], ([stats, lenFiltered, lenGrouped]) => {
      try {
        console.debug('[shadow] roomStats:', JSON.stringify(stats))
        console.debug('[shadow] filteredRooms.length:', lenFiltered)
        console.debug('[shadow] groupedRooms.length:', lenGrouped)
      } catch (e) {
        console.warn('[shadow] log failed', e)
      }
    }, { immediate: false })
  } catch (e) {
    console.warn('[shadow] setup failed', e)
  }
}

// 基线快照（仅开发 + 影子模式）
function logBaselineSnapshot(label = 'snapshot') {
  if (!(import.meta.env.DEV && devShadow)) return
  try {
    const floorsCount = Array.isArray(groupedRooms?.value)
      ? groupedRooms.value.reduce((acc, b) => acc + (Array.isArray(b.floors) ? b.floors.length : 0), 0)
      : 0
    const sampleKeys = Array.isArray(rooms?.value)
      ? rooms.value.slice(0, 10).map(r => r.id || r.roomNumber || r.room_number)
      : []
    const payload = {
      label,
      filters: {
        building: selectedBuilding?.value ?? null,
        floor: selectedFloor?.value ?? null,
        roomType: selectedRoomType?.value ?? null,
        status: selectedStatus?.value ?? null,
        cleanStatus: selectedCleanStatus?.value ?? null,
        keyword: searchKeyword?.value ?? ''
      },
      stats: roomStats?.value || {},
      counts: {
        rooms: rooms?.value?.length || 0,
        filteredRooms: filteredRooms?.value?.length || 0,
        buildings: groupedRooms?.value?.length || 0,
        floors: floorsCount
      },
      sampleRoomKeys: sampleKeys
    }
    console.debug('[shadow][baseline]', payload)
    // 标记一次基线日志，用于首屏与筛选后的校验锚点
    console.debug('[shadow][anchor]', label)
  } catch (e) {
    console.warn('[shadow][baseline] failed', e)
  }
}

// 响应式数据

// 布局模式
const layoutMode = ref('top') // 'top', 'left', 'bottom'

// API数据状态
const loading = ref(false)
const error = ref(null)
const refreshing = ref(false)  // 无感刷新状态

// 楼栋数据
const buildings = ref([])
const buildingsLoading = ref(false)

// 楼层数据
const floors = ref([])
const floorsLoading = ref(false)

// 房间数据
const rooms = ref([])
const roomsLoading = ref(false)

// 房间状态数据（保留占位，后续可接入实际接口）
const roomStatuses = ref([])
const statusLoading = ref(false)

// 状态配置数据
const cleanStatusConfig = ref([])  // 清洁状态配置
const roomStatusConfig = ref([])   // 业务状态配置
const selectedFloor = ref(null)
const selectedRoomType = ref(null)
const selectedStatus = ref(null)
const selectedCleanStatus = ref(null)
const selectedBuilding = ref(null)
const searchKeyword = ref('')

// 弹窗状态
const showCheckInModal = ref(false)
const showReservationModal = ref(false)
const showRoomDetailModal = ref(false)
const selectedRoomData = ref(null) // 改为null，避免空对象导致的问题

// 状态文本映射计算属性 - 性能优化，避免重复函数调用
const statusTextMap = computed(() => STATUS_TEXT_MAP)

// 布局选项
const layoutOptions = computed(() => [
  {
    label: '顶部布局',
    key: 'top',
    icon: () => h('i', { class: 'i-material-symbols:view-agenda text-14' })
  },
  {
    label: '左侧布局',
    key: 'left',
    icon: () => h('i', { class: 'i-material-symbols:view-sidebar text-14' })
  },
  {
    label: '底部布局',
    key: 'bottom',
    icon: () => h('i', { class: 'i-material-symbols:bottom-panel-close text-14' })
  }
])

// 获取当前布局图标
function getCurrentLayoutIcon() {
  const iconMap = {
    top: 'i-material-symbols:view-agenda',
    left: 'i-material-symbols:view-sidebar',
    bottom: 'i-material-symbols:bottom-panel-close'
  }
  return iconMap[layoutMode.value] || iconMap.top
}

// 获取当前布局标签
function getCurrentLayoutLabel() {
  const labelMap = {
    top: '顶部布局',
    left: '左侧布局',
    bottom: '底部布局'
  }
  return labelMap[layoutMode.value] || labelMap.top
}

// 处理布局切换
function handleLayoutChange(key) {
  layoutMode.value = key
  // 保存到本地存储
  localStorage.setItem('roomStatus_layoutMode', key)
  $message.success(`已切换到${getCurrentLayoutLabel()}`)
}

// 右键菜单
const showContextMenu = ref(false)
const contextMenuX = ref(0)
const contextMenuY = ref(0)
const contextMenuRoom = ref({})

// 订单详情弹窗
const showOrderDetail = ref(false)
const selectedOrderData = ref({})
const selectedBillId = ref(null)

// 联房悬浮效果
const hoveredConnectCode = ref('')

// 筛选参数
const filterParams = ref({
  room_clear_status: [],      // 清洁状态筛选
  room_record_status: [],     // 业务状态筛选
  floor_id: "",               // 楼层ID
  building_id: "",            // 楼栋ID
  room_type_id: "",           // 房型ID
  room_number: "",            // 房间号
  room_sale_type: "",         // 房间销售类型
  grade_id: "",               // 等级ID
  intermediaries_id: "",      // 中介ID
  start_time: "",             // 开始时间
  end_time: ""                // 结束时间
})

// 自动刷新相关
const autoRefreshEnabled = ref(true)
const refreshInterval = ref(30) // 默认30秒（秒）
const refreshTimer = ref(null)
// isRefreshing 已被 refreshing 替代

// 独立的倒计时显示（不影响数据渲染）
const refreshCountdown = ref(30)
const countdownTimer = ref(null)

// 性能优化 - 数据变化检测
const lastDataHash = ref('')

// 卡片大小控制
const cardSize = ref('medium') // large, medium, small

// 酒店切换相关
const showHotelSwitcher = ref(false)
const currentHotel = ref({
  id: 1,
  name: '智慧酒店总店',
  type: '总店',
  address: '北京市朝阳区xxx路xxx号',
  online: true
})

// 入住办理相关（使用统一的弹窗状态）
const memberSearch = ref('')
const guestInfo = ref(null)
const selectedRooms = ref([])
const selectedPackages = ref([])

// 酒店列表
const hotelList = ref([
  {
    id: 1,
    name: '智慧酒店总店',
    type: '总店',
    address: '北京市朝阳区xxx路xxx号',
    online: true
  },
  {
    id: 2,
    name: '智慧酒店分店A',
    type: '分店',
    address: '北京市海淀区xxx路xxx号',
    online: true
  },
  {
    id: 3,
    name: '智慧酒店分店B',
    type: '分店',
    address: '北京市西城区xxx路xxx号',
    online: false
  }
])

// 套餐列表
const packageList = ref([
  { id: 1, name: '早餐套餐', price: 68 },
  { id: 2, name: '接送服务', price: 50 },
  { id: 3, name: '洗衣服务', price: 30 },
  { id: 4, name: '健身房', price: 40 }
])

// 房间统计数据 - 基于接口数据的计算属性
const roomStats = computed(() => {
  // 🚀 性能优化：缓存配置数据长度，避免重复访问
  const roomConfigLength = roomStatusConfig.value.length
  const cleanConfigLength = cleanStatusConfig.value.length

  // 🚀 性能优化：如果配置为空，直接返回默认值
  if (roomConfigLength === 0 && cleanConfigLength === 0) {
    return {
      occupied: 0, available: 0, checkout: 0, reserved: 0,
      dirty: 0, cleaning: 0, maintenance: 0, blocked: 0, inspecting: 0, noshow: 0
    }
  }

  // 🚀 性能优化：预分配对象，避免动态属性添加
  const businessStats = {
    occupied: 0, available: 0, checkout: 0, reserved: 0,
    maintenance: 0, blocked: 0, inspecting: 0, noshow: 0
  }
  const cleanStats = {
    dirty: 0, cleaning: 0, maintenance: 0
  }

  // 🚀 性能优化：使用for循环替代forEach，性能更好
  for (let i = 0; i < roomConfigLength; i++) {
    const status = roomStatusConfig.value[i]
    const mappedStatus = mapRoomStatusBySign_legacy(status.sign)
    if (businessStats.hasOwnProperty(mappedStatus)) {
      businessStats[mappedStatus] += status.count
    }
  }

  for (let i = 0; i < cleanConfigLength; i++) {
    const status = cleanStatusConfig.value[i]
    const cleanKey = getCleanStatusKey(status.name)
    if (cleanKey && cleanStats.hasOwnProperty(cleanKey)) {
      cleanStats[cleanKey] = status.count
    }
  }

  return {
    // 主要业务状态
    occupied: businessStats.occupied,
    available: businessStats.available,
    checkout: businessStats.checkout,
    reserved: businessStats.reserved,

    // 清洁和维护状态
    dirty: cleanStats.dirty,
    cleaning: cleanStats.cleaning,
    maintenance: businessStats.maintenance || cleanStats.maintenance,

    // 其他状态
    blocked: businessStats.blocked,
    inspecting: businessStats.inspecting,
    noshow: businessStats.noshow
  }
})

// API获取的房间数据将在这里初始化
// rooms 变量已在上面的API数据状态部分定义

// API数据获取函数
async function fetchBuildingList() {
  buildingsLoading.value = true
  try {
    const response = await getUsableRoomBuildingList()
    if (response && response.data) {
      buildings.value = Array.isArray(response.data) ? response.data : []
    }
  } catch (error) {
    $message.error('获取楼栋列表失败: ' + error.message)
  } finally {
    buildingsLoading.value = false
  }
}

async function fetchFloorList(buildingId) {
  floorsLoading.value = true
  try {
    const response = await getUsableRoomFloorList({ building_id: buildingId })
    if (response && response.data) {
      floors.value = Array.isArray(response.data) ? response.data : []
    }
  } catch (error) {
    $message.error('获取楼层列表失败: ' + error.message)
  } finally {
    floorsLoading.value = false
  }
}

async function fetchRoomList(params = {}) {
  roomsLoading.value = true
  try {
    // 使用正确的默认参数
    const defaultParams = {
      bill_source: "",
      building_id: "",
      floor_id: "",
      room_clear_status: [],
      room_number: "",
      room_record_status: [],
      room_type_id: "",
      sell_type_id: "",
      tag_type: "",
      user_name: ""
    }

    const requestParams = { ...defaultParams, ...params }

    const response = await getUsableRoom(requestParams)

    if (response && response.data) {

      // 处理嵌套的楼栋-楼层-房间结构
      let allRooms = []
      let buildingData = []
      let floorData = []

      // 提取状态统计数据
      let cleanStatusCount = []
      let roomStatusCount = []

      if (Array.isArray(response.data)) {
        // 数据是楼栋数组
        buildingData = response.data
      } else if (response.data.list && Array.isArray(response.data.list)) {
        buildingData = response.data.list
      }

      // 从第一个楼栋中提取状态统计数据（通常状态数据在根级别或第一个楼栋中）
      if (buildingData.length > 0) {
        const firstBuilding = buildingData[0]
        cleanStatusCount = firstBuilding.clean_status_count || response.data.clean_status_count || []
        roomStatusCount = firstBuilding.room_status_count || response.data.room_status_count || []

        // 保存状态配置数据
        saveStatusConfig(cleanStatusCount, roomStatusCount)
      }

      // 提取楼栋信息
      buildings.value = buildingData.map(building => ({
        id: building.building_number || building.id,
        building_name: building.building || building.building_name,
        building_number: building.building_number,
        room_count: building.floor_list ?
          building.floor_list.reduce((total, floor) => total + (floor.room_count || 0), 0) : 0
      }))

      // 提取楼层和房间信息
      buildingData.forEach(building => {
        if (building.floor_list && Array.isArray(building.floor_list)) {
          building.floor_list.forEach(floor => {
            // 添加楼层信息
            floorData.push({
              id: floor.floor_number || floor.id,
              floor_number: floor.floor_number,
              floor: floor.floor || floor.floor_name,
              building_id: building.building_number || building.id,
              room_count: floor.room_count || 0
            })

            // 提取房间信息
            if (floor.room_list && Array.isArray(floor.room_list)) {
              const roomsInFloor = floor.room_list.map(room => ({
                ...room,
                building_name: building.building || building.building_name,
                building_number: building.building_number,
                floor_name: floor.floor || floor.floor_name,
                floor_number: floor.floor_number
              }))
              allRooms = allRooms.concat(roomsInFloor)
            }
          })
        }
      })

      // 更新楼层数据
      floors.value = floorData

      // 直接使用API返回的真实房间数据，不进行状态多样化处理
      rooms.value = transformRoomData(allRooms)

    } else {
      rooms.value = []
    }
  } catch (error) {

    $message.error('获取房间列表失败: ' + error.message)
    rooms.value = []
  } finally {
    roomsLoading.value = false
  }
}

// 获取房间业务状态配置
async function fetchRoomStatusConfig() {
  try {
    const response = await getRoomRecordStatus()

    if (response && response.data) {
      let statusData = []

      if (Array.isArray(response.data)) {
        statusData = response.data
      } else if (response.data.list && Array.isArray(response.data.list)) {
        statusData = response.data.list
      } else if (response.data.data && Array.isArray(response.data.data)) {
        statusData = response.data.data
      }

      // 保存业务状态配置
      roomStatusConfig.value = statusData.map(status => ({
        id: status.id,
        name: status.status_name,
        sign: status.sign,
        color: status.color,
        count: 0 // 初始计数为0
      }))

    }
  } catch (error) {

    $message.error('获取房间业务状态配置失败: ' + error.message)
  }
}

// 获取房间清洁状态配置
async function fetchCleanStatusConfig() {
  try {
    const response = await getRoomClearStatus()

    if (response && response.data) {
      let statusData = []

      if (Array.isArray(response.data)) {
        statusData = response.data
      } else if (response.data.list && Array.isArray(response.data.list)) {
        statusData = response.data.list
      } else if (response.data.data && Array.isArray(response.data.data)) {
        statusData = response.data.data
      }

      // 保存清洁状态配置
      cleanStatusConfig.value = statusData.map(status => ({
        id: status.id,
        name: status.status_name,
        sign: status.sign,
        color: status.color,
        count: 0 // 初始计数为0
      }))

    }
  } catch (error) {

    $message.error('获取房间清洁状态配置失败: ' + error.message)
  }
}

// 更新状态计数（从房间数据中获取的统计）
function updateStatusCounts(cleanStatusCount, roomStatusCount) {
  // 更新清洁状态计数
  cleanStatusCount.forEach(statusCount => {
    const statusConfig = cleanStatusConfig.value.find(s => s.id === (statusCount.clear_id || statusCount.id))
    if (statusConfig) {
      statusConfig.count = statusCount.room_count || 0
    }
  })

  // 更新业务状态计数
  roomStatusCount.forEach(statusCount => {
    const statusConfig = roomStatusConfig.value.find(s => s.id === statusCount.id)
    if (statusConfig) {
      statusConfig.count = statusCount.room_count || 0
    }
  })

}

// 保存状态配置数据（兼容旧版本，现在只更新计数）
function saveStatusConfig(cleanStatusCount, roomStatusCount) {
  updateStatusCounts(cleanStatusCount, roomStatusCount)
}

// 根据ID获取清洁状态信息
function getCleanStatusInfo(cleanStatusId) {
  const status = cleanStatusConfig.value.find(s => s.id === cleanStatusId)
  return status || { name: '未知', color: '#999999' }
}

// 根据ID获取业务状态信息
function getRoomStatusInfo(roomStatusId) {
  const status = roomStatusConfig.value.find(s => s.id === roomStatusId)
  return status || { name: '未知', color: '#999999', sign: 'unknown' }
}


// 数据转换函数 - 处理getUsableRoom接口返回的嵌套数据结构
function transformRoomData(roomData) {
  if (!Array.isArray(roomData) || roomData.length === 0) {
    return []
  }

  const transformedRooms = roomData.map((room) => {
    // 获取清洁状态和业务状态信息
    const cleanStatusInfo = getCleanStatusInfo(room.clean_status)
    const roomStatusInfo = getRoomStatusInfo(room.room_status_record_id)

    // 处理getUsableRoom接口返回的房间数据结构
    const transformedRoom = {
      // 房间基本信息
      id: room.id,
      roomNumber: room.room_number,
      roomType: room.room_type_name || room.room_type_simple_name || getRoomTypeName(room.room_type_id),
      roomTypeId :room.room_type_id,
      // 房间状态信息（结合清洁状态和业务状态）
      status: mapRoomStatusBySign_legacy(roomStatusInfo.sign), // 使用业务状态的sign字段

      // 清洁状态信息
      cleanStatus: room.clean_status,
      cleanStatusName: cleanStatusInfo.name,
      cleanStatusColor: cleanStatusInfo.color,

      // 业务状态信息
      roomStatus: room.room_status_record_id,
      roomStatusName: roomStatusInfo.name,
      roomStatusColor: roomStatusInfo.color,
      roomStatusSign: roomStatusInfo.sign,
      room_status_record_id: room.room_status_record_id,

      // 保留原始字段名（用于样式和详情弹窗）
      room_status_color: room.room_status_color || roomStatusInfo.color,
      room_status_name: room.room_status_name || roomStatusInfo.name,
      clean_status_color: room.clean_status_color || cleanStatusInfo.color,
      clear_color: room.clear_color || cleanStatusInfo.color,
      clear_status_name: room.clear_status_name || cleanStatusInfo.name,

      // 保留原有字段（兼容性）
      clearStatusName: room.clear_status_name || cleanStatusInfo.name,
      clearColor: room.clear_color || cleanStatusInfo.color,

      // 楼栋楼层信息
      buildingId: room.building_id,
      buildingName: room.building_name,
      buildingNumber: room.building_number,
      floorId: room.floor_id,
      floorName: room.floor_name,
      floorNumber: room.floor_number,

      // 客人和订单信息
      guest: room.bill_info ? {
        name: room.bill_info.link_man,
        phone: room.bill_info.phone || '',
        company: room.bill_info.company || '',
        vipLevel: room.bill_info.vip_level || ''
      } : null,

      // 预计算的客人信息（避免模板中重复调用函数）
      guestName: getDisplayGuestName(room.bill_info),
      guestPhone: room.bill_info?.link_phone || room.bill_info?.phone || '',
      guestVipLevel: room.bill_info?.vip_level || '',
      guestCompany: room.bill_info?.company || '',

      // 会员等级和渠道来源信息
      memberGrade: room.bill_info?.tempGrade_name || '',
      channelSource: room.bill_info?.bill_source_name || '',

      // 联房信息
      connectCode: room.bill_info?.connect_code || '',
      isConnectedRoom: !!(room.bill_info?.connect_code),

      // 离店提醒信息
      isCheckoutSoon: false, // 先设为false，后面会重新计算
      checkoutReminderInfo: null, // 离店提醒详细信息

      // 欠费信息
      debtInfo: null, // 欠费详细信息

      // 保留完整的 bill_info 对象供模板和函数使用
      bill_info: room.bill_info || null,
      billInfo: room.bill_info || null, // 保持向后兼容

      // 时间信息
      checkInTime: room.start_time_plan ? new Date(room.start_time_plan * 1000).toISOString() : null,
      checkOutTime: room.bill_info?.leave_time_plan || null,

      // 订单信息
      billId: room.bill_id,
      billInfo: room.bill_info,
      reservationId: room.bill_id,

      // 房间详细信息
      roomCode: room.room_code,
      shopId: room.shop_id,
      sort: room.sort,

      // 未来预订
      futureOrders: room.room_status_record_future || [],

      // 房间状态信息
      tags: [],
      housekeeping: {
        status: room.clear_status_name || 'clean',
        lastCleaned: room.update_time ? new Date(room.update_time * 1000).toISOString() : null
      },
      rate: 388, // 默认房价，可以从其他接口获取
      notes: '',

      // 保留原始数据用于调试
      _raw: room
    }

    return transformedRoom
  })

  // 重新计算离店提醒状态和欠费信息
  transformedRooms.forEach(room => {
    const reminderInfo = getCheckoutReminderInfo(room)
    room.isCheckoutSoon = reminderInfo ? reminderInfo.shouldShow : false
    room.checkoutReminderInfo = reminderInfo

    // 计算欠费信息
    room.debtInfo = getDebtInfo(room)
  })

  return transformedRooms
}

function getRoomTypeName(typeId) {
  const typeMap = {
    1: '标准间',
    2: '豪华间',
    3: '套房',
    4: '总统套房'
  }
  return typeMap[typeId] || '标准间'
}

function mergeRoomStatusData(statusData) {

  if (!Array.isArray(statusData) || statusData.length === 0) {
    return
  }

  if (rooms.value.length === 0) {
    return
  }

  const statusMap = new Map()

  statusData.forEach((status) => {
    // 处理getRoomClearStatus接口返回的状态数据
    const roomId = status.room_id || status.id
    const roomNumber = status.room_number || status.roomNumber || status.number

    if (roomId || roomNumber) {
      const key = roomNumber || roomId
      const statusInfo = {
        // 房间状态
        status: mapApiStatusToLocal(status.status || status.room_status_record_id || status.clear_status),
        cleanStatus: status.clean_status || status.cleanStatus,
        businessStatus: status.business_status || status.businessStatus,

        // 客人信息
        guest: status.guest_name || status.guestName ? {
          name: status.guest_name || status.guestName,
          phone: status.guest_phone || status.guestPhone || '',
          company: status.guest_company || status.guestCompany || '',
          vipLevel: status.tempGrade_name || status.tempGrade_name || ''
        } : null,

        // 入住信息
        checkInTime: status.check_in_time || status.checkIn || status.checkin_time || null,
        checkOutTime: status.check_out_time || status.checkOut || status.checkout_time || null,
        reservationId: status.order_no || status.orderNo || status.reservation_id || null,

        // 清洁信息
        housekeeping: {
          lastCleaned: status.last_cleaned || status.lastCleaned || status.clean_time,
          cleanedBy: status.cleaned_by || status.cleanedBy || status.cleaner,
          status: status.clean_status || status.cleanStatus || 'clean'
        },

        // 其他信息
        notes: status.notes || status.remarks || status.memo || ''
      }

      statusMap.set(key, statusInfo)
    }
  })

  // 合并状态到房间数据 - 使用安全的更新方式
  const updatedRooms = rooms.value.map(room => {
    const statusInfo = statusMap.get(room.roomNumber) || statusMap.get(room.id)
    if (statusInfo) {
      mergedCount++
      return { ...room, ...statusInfo }
    }
    return room
  })

  // 一次性更新，避免响应式系统混乱
  rooms.value = updatedRooms

  // 更新房间统计
  updateRoomStats()
}

function mapApiStatusToLocal(apiStatus) {
  const statusMap = {
    // 房间状态映射（基于实际数据：1=在住）
    0: 'available',    // 空闲
    1: 'occupied',     // 在住
    2: 'reserved',     // 已预订
    3: 'checkout',     // 待退房
    4: 'dirty',        // 脏房
    5: 'cleaning',     // 清洁中
    6: 'maintenance',  // 维修中
    7: 'blocked',      // 封锁
    8: 'inspecting',   // 查房中

    // 字符串状态映射
    'vacant': 'available',
    'occupied': 'occupied',
    'reserved': 'reserved',
    'dirty': 'dirty',
    'cleaning': 'cleaning',
    'maintenance': 'maintenance',
    'locked': 'blocked',
    'to_inspect': 'inspecting',
    'checkout': 'checkout',

    // 数字字符串映射
    '0': 'available',
    '1': 'occupied',
    '2': 'reserved',
    '3': 'checkout',
    '4': 'dirty',
    '5': 'cleaning',
    '6': 'maintenance',
    '7': 'blocked',
    '8': 'inspecting'
  }

  return statusMap[apiStatus] || 'available'
}

// 根据业务状态的sign字段映射到本地状态
function mapRoomStatusBySign_legacy(sign) {
  const signMap = {
    'stay': 'occupied',        // 在住
    'vacant': 'available',     // 空闲
    'reserved': 'reserved',    // 已预订
    'checkout': 'checkout',    // 待退房
    'dirty': 'dirty',          // 脏房
    'cleaning': 'cleaning',    // 清洁中
    'maintenance': 'maintenance', // 维修中
    'blocked': 'blocked',      // 封锁
    'inspecting': 'inspecting', // 查房中
    'noshow': 'noshow',        // 未到店

    // 其他可能的sign值
    'available': 'available',
    'occupied': 'occupied',
    'ooo': 'maintenance',      // Out of Order
    'oos': 'blocked'           // Out of Service
  }
  return signMap[sign] || 'available'
}

// 新实现（影子对比用，不接线）
function mapRoomStatusBySign(sign) {
  const signMap = {
    'stay': 'occupied',        // 在住
    'vacant': 'available',     // 空闲
    'reserved': 'reserved',    // 已预订
    'checkout': 'checkout',    // 待退房
    'dirty': 'dirty',          // 脏房
    'cleaning': 'cleaning',    // 清洁中
    'maintenance': 'maintenance', // 维修中
    'blocked': 'blocked',      // 封锁
    'inspecting': 'inspecting', // 查房中
    'noshow': 'noshow',        // 未到店

    // 其他可能的sign值
    'available': 'available',
    'occupied': 'occupied',
    'ooo': 'maintenance',      // Out of Order
    'oos': 'blocked'           // Out of Service
  }
  return signMap[sign] || 'available'
}

// 影子对比：状态映射一致性
function shadowCompareStatusMapping() {
  if (!(import.meta.env.DEV && devShadow)) return
  try {
    const signs = Array.isArray(roomStatusConfig.value)
      ? roomStatusConfig.value.map(s => s.sign).filter(Boolean)
      : []
    const uniqueSigns = Array.from(new Set(signs))
    const diffs = []
    for (const sign of uniqueSigns) {
      const legacyVal = mapRoomStatusBySign_legacy(sign)
      const newVal = mapRoomStatusBySign(sign)
      if (legacyVal !== newVal) diffs.push({ sign, legacy: legacyVal, next: newVal })
    }
    if (diffs.length) {
      console.warn('[shadow][compare][mapRoomStatusBySign] diffs:', diffs)
    } else {
      console.debug('[shadow][compare][mapRoomStatusBySign] ok, count=', uniqueSigns.length)
    }
  } catch (e) {
    console.warn('[shadow][compare][mapRoomStatusBySign] failed', e)
  }
}


// 根据清洁状态名称映射到统计键
function getCleanStatusKey(statusName) {
  const cleanStatusMap = {
    '净': null,                // 净房不需要特殊统计
    '脏': 'dirty',             // 脏房
    '清洁中': 'cleaning',       // 清洁中
    '维修': 'maintenance',      // 维修中
    '维修中': 'maintenance',    // 维修中（备用）
    '封锁': 'blocked',         // 封锁
    '查房': 'inspecting',      // 查房中
    '查房中': 'inspecting'     // 查房中（备用）
  }

  return cleanStatusMap[statusName] || null
}

// 完整数据刷新（初始加载）
async function fullDataRefresh() {
  loading.value = true
  error.value = null

  try {
    // 1. 首先获取状态配置数据
    await Promise.all([
      fetchRoomStatusConfig(),
      fetchCleanStatusConfig()
    ])

    // 2. 获取楼栋列表
    await fetchBuildingList()

    // 3. 获取楼层列表（如果有选中的楼栋）
    if (selectedBuilding.value) {
      await fetchFloorList(selectedBuilding.value)
    }

    // 4. 获取房间列表（包含楼栋、楼层、房间的完整信息）
    await fetchRoomList()

    // 如果没有房间数据，使用模拟数据
    if (rooms.value.length === 0) {
      $message.warning('未获取到房间数据')
    }

    $message.success(`数据刷新完成：${buildings.value.length}个楼栋，${floors.value.length}个楼层，${rooms.value.length}个房间`)

  } catch (error) {
    error.value = error.message || '获取房间数据失败'
    $message.error('数据刷新失败，显示模拟数据: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 生成数据哈希值用于变化检测
function generateDataHash(data) {
  return JSON.stringify(data).length + '_' + Date.now()
}

// 无感刷新（后台更新数据）
async function silentRefresh() {
  // 检查组件是否仍然挂载
  if (!isMounted.value) {

    return
  }

  refreshing.value = true

  try {
    // 后台获取最新数据，使用当前筛选参数
    const response = await getUsableRoom(filterParams.value)

    // 再次检查组件是否仍然挂载（异步操作后）
    if (!isMounted.value) {

      return
    }

    if (response && response.data) {
      // 生成新数据的哈希值
      const newDataHash = generateDataHash(response.data)

      // 如果数据没有变化，跳过更新
      if (newDataHash === lastDataHash.value) {
        return
      }

      lastDataHash.value = newDataHash

      // 处理嵌套的楼栋-楼层-房间结构
      let allRooms = []
      let buildingData = []
      let floorData = []

      // 提取状态统计数据
      let cleanStatusCount = []
      let roomStatusCount = []

      if (Array.isArray(response.data)) {
        buildingData = response.data
      } else if (response.data.list && Array.isArray(response.data.list)) {
        buildingData = response.data.list
      }

      // 从第一个楼栋中提取状态统计数据
      if (buildingData.length > 0) {
        const firstBuilding = buildingData[0]
        cleanStatusCount = firstBuilding.clean_status_count || response.data.clean_status_count || []
        roomStatusCount = firstBuilding.room_status_count || response.data.room_status_count || []

        // 更新状态配置数据
        saveStatusConfig(cleanStatusCount, roomStatusCount)
      }

      // 提取楼栋信息
      const newBuildings = buildingData.map(building => ({
        id: building.building_number || building.id,
        building_name: building.building || building.building_name,
        building_number: building.building_number,
        room_count: building.floor_list ?
          building.floor_list.reduce((total, floor) => total + (floor.room_count || 0), 0) : 0
      }))

      // 提取楼层和房间信息
      buildingData.forEach(building => {
        if (building.floor_list && Array.isArray(building.floor_list)) {
          building.floor_list.forEach(floor => {
            // 添加楼层信息
            floorData.push({
              id: floor.floor_number || floor.id,
              floor_number: floor.floor_number,
              floor: floor.floor || floor.floor_name,
              building_id: building.building_number || building.id,
              room_count: floor.room_count || 0
            })

            // 提取房间信息
            if (floor.room_list && Array.isArray(floor.room_list)) {
              const roomsInFloor = floor.room_list.map(room => ({
                ...room,
                building_name: building.building || building.building_name,
                building_number: building.building_number,
                floor_name: floor.floor || floor.floor_name,
                floor_number: floor.floor_number
              }))
              allRooms = allRooms.concat(roomsInFloor)
            }
          })
        }
      })

      // 直接使用API返回的真实房间数据，不进行状态多样化处理
      // 静默更新数据（不显示加载状态）
      buildings.value = newBuildings
      floors.value = floorData
      rooms.value = transformRoomData(allRooms)

      // 静默更新，不显示提示信息（避免频繁打扰用户）
      // $message.info('房态数据已更新', { duration: 2000 })

      // 手动刷新时重置倒计时
      if (autoRefreshEnabled.value) {
        refreshCountdown.value = refreshInterval.value
      }

    } else {
      $message.error('房态数据获取失败')
    }

  } catch (error) {
    // 静默失败，不显示错误消息，避免打扰用户
  } finally {
    refreshing.value = false
  }
}

// 筛选选项
// 楼栋选项
const buildingOptions = computed(() => {
  return buildings.value.map(building => ({
    label: building.building_name || `${building.building_number}栋`,
    value: building.id || building.building_number
  }))
})

// 楼层选项
const floorOptions = computed(() => {
  let availableFloors = floors.value

  // 如果选择了楼栋，只显示该楼栋的楼层
  if (selectedBuilding.value) {
    availableFloors = floors.value.filter(floor =>
      floor.building_id === selectedBuilding.value
    )
  }

  return availableFloors.map(floor => ({
    label: floor.floor || `${floor.floor_number}楼`,
    value: floor.id || floor.floor_number
  }))
})

// 房型选项
const roomTypeOptions = computed(() => {
  const types = [...new Set(rooms.value.map(room => room.roomType).filter(Boolean))]
  return types.map(type => ({ label: type, value: type }))
})

// 业务状态选项 - 基于API返回的状态配置动态生成
const statusOptions = computed(() => {
  const options = [{ label: '全部状态', value: null }]

  // 添加业务状态选项
  roomStatusConfig.value.forEach(status => {
    options.push({
      label: status.name,
      value: status.sign  // 使用sign作为选择值，在updateFilterParams中转换为ID
    })
  })

  return options
})

// 清洁状态选项 - 基于API返回的状态配置动态生成
const cleanStatusOptions = computed(() => {
  const options = [{ label: '全部清洁状态', value: null }]

  // 添加清洁状态选项
  cleanStatusConfig.value.forEach(status => {
    options.push({
      label: status.name,
      value: status.id  // 直接使用ID作为选择值
    })
  })

  return options
})

// 刷新间隔选项
const refreshIntervalOptions = ref([
  { label: '10秒', value: 10 },
  { label: '30秒', value: 30 },
  { label: '60秒', value: 60 },
  { label: '2分钟', value: 120 },
  { label: '5分钟', value: 300 }
])

// 右键菜单选项
const contextMenuOptions = computed(() => {
  if (!contextMenuRoom.value) return []

  const options = [
    { label: '查看详情', key: 'detail', icon: () => h('i', { class: 'i-material-symbols:info' }) },
    { label: '办理入住', key: 'checkin', icon: () => h('i', { class: 'i-material-symbols:person-add' }) },
    { label: '办理退房', key: 'checkout', icon: () => h('i', { class: 'i-material-symbols:person-remove' }) },
    { label: '房间清洁', key: 'clean', icon: () => h('i', { class: 'i-material-symbols:cleaning-services' }) },
    { label: '维修报修', key: 'maintenance', icon: () => h('i', { class: 'i-material-symbols:build' }) }
  ]

  return options.filter(option => {
    // 根据房间状态过滤可用操作
    switch (contextMenuRoom.value.status) {
      case 'available':
        return ['detail', 'checkin', 'clean', 'maintenance'].includes(option.key)
      case 'occupied':
        return ['detail', 'checkout'].includes(option.key)
      case 'cleaning':
        return ['detail', 'checkin'].includes(option.key)
      case 'maintenance':
        return ['detail'].includes(option.key)
      default:
        return true
    }
  })
})

// 房间列表 - 直接使用API返回的数据，无需前端筛选
const filteredRooms = computed(() => {
  // 所有筛选逻辑都通过API参数实现：
  // - 楼栋筛选：building_id
  // - 楼层筛选：floor_id
  // - 房型筛选：room_type_id
  // - 业务状态筛选：room_record_status (数字ID数组)
  // - 清洁状态筛选：room_clear_status (数字ID数组)
  // - 房间号搜索：room_number

  // 直接返回API筛选后的数据，避免重复筛选
  return rooms.value
})

// 按楼栋楼层分组的房间 - 性能优化版本
const groupedRooms = computed(() => {
  const roomsData = filteredRooms.value

  // 🚀 性能优化：如果房间数据为空，直接返回
  if (!roomsData || roomsData.length === 0) {
    return []
  }

  // 🚀 性能优化：使用Map提高查找性能，避免频繁的对象属性查找
  const buildingMap = new Map()
  const floorMap = new Map()

  // 🚀 性能优化：使用for循环替代forEach，性能更好
  for (let i = 0; i < roomsData.length; i++) {
    const room = roomsData[i]
    // 过滤无效房间，避免模板里再用 v-if
    if (!room || !(room.roomNumber || room.room_number)) continue

    const buildingName = room.buildingName || `楼栋${room.buildingId || '未知'}`
    const floorNumber = room.floorNumber || Math.max(1, Math.floor(parseInt(room.roomNumber || room.room_number) / 100) || 1)
    const buildingKey = buildingName
    const floorKey = `${buildingName}-${floorNumber}`

    // 初始化楼栋
    if (!buildingMap.has(buildingKey)) {
      buildingMap.set(buildingKey, {
        buildingName,
        buildingId: room.buildingId,
        totalRooms: 0,
        floors: []
      })
    }

    // 初始化楼层
    if (!floorMap.has(floorKey)) {
      const floorData = {
        floorNumber,
        floorId: room.floorId,
        rooms: [],
        statusSummary: {}
      }
      floorMap.set(floorKey, floorData)
      buildingMap.get(buildingKey).floors.push(floorData)
    }

    // 添加房间和统计
    const floorData = floorMap.get(floorKey)
    floorData.rooms.push(room)
    buildingMap.get(buildingKey).totalRooms++

    // 状态统计 - 优化：避免重复查找
    const status = room.status
    if (status) {
      floorData.statusSummary[status] = (floorData.statusSummary[status] || 0) + 1
    }
  }

  // 🚀 性能优化：转换为数组并排序（减少中间对象创建）
  return Array.from(buildingMap.values())
    .map(building => ({
      ...building,
      floors: building.floors.sort((a, b) => a.floorNumber - b.floorNumber)
    }))
    .sort((a, b) => (a.buildingId || 0) - (b.buildingId || 0))
})

// 方法

function getStatusIcon(status) {
  const icons = {
    occupied: 'i-material-symbols:person',              // 已入住
    available: 'i-material-symbols:check-circle',       // 可入住
    checkout: 'i-material-symbols:logout',              // 待退房
    dirty: 'i-material-symbols:warning',                // 脏房
    cleaning: 'i-material-symbols:cleaning-services',   // 清洁中
    inspecting: 'i-material-symbols:search',            // 查房中
    maintenance: 'i-material-symbols:build',            // 维修中
    blocked: 'i-material-symbols:block',                // 房间封锁
    reserved: 'i-material-symbols:event',               // 已预订
    noshow: 'i-material-symbols:person-off'             // 未到店
  }
  return icons[status] || 'i-material-symbols:help'
}

// 状态文本映射常量 - 避免重复创建对象
const STATUS_TEXT_MAP = {
  occupied: '已入住',      // OCC
  available: '可入住',     // VAC
  checkout: '待退房',      // CO
  dirty: '脏房',          // VD
  cleaning: '清洁中',      // Clean
  inspecting: '查房中',    // INS
  maintenance: '维修中',   // OOO
  blocked: '封锁',        // BLK
  reserved: '已预订',      // RES
  noshow: '未到店'        // NS
}

// 获取显示的客人姓名（优先显示入住人，其次显示联系人）
function getDisplayGuestName(billInfo) {
  if (!billInfo) return ''

  // 优先检查 room_user 入住人数组
  if (billInfo.room_user && Array.isArray(billInfo.room_user) && billInfo.room_user.length > 0) {
    // 获取第一个入住人的姓名
    const firstGuest = billInfo.room_user[0]
    if (firstGuest && firstGuest.name) {
      // 如果有多个入住人，显示第一个人的姓名 + 人数
      if (billInfo.room_user.length > 1) {
        return `${firstGuest.name} 等${billInfo.room_user.length}人`
      } else {
        return firstGuest.name
      }
    }
  }

  // 如果没有入住人信息，使用联系人信息
  return billInfo.link_man || ''
}

// 解析时间字符串的辅助函数
function parseTimeString(timeStr) {
  if (!timeStr) return null

  try {
    // 处理不同的时间格式
    let date

    if (typeof timeStr === 'number') {
      // 时间戳格式
      date = new Date(timeStr * 1000)
    } else if (typeof timeStr === 'string') {
      // 字符串格式
      if (timeStr.includes('T') && timeStr.includes('Z')) {
        // ISO格式：2025-01-15T12:00:00.000Z
        date = new Date(timeStr)
      } else if (timeStr.includes(' ')) {
        // 标准格式：2025-01-15 12:00:00
        date = new Date(timeStr)
      } else {
        // 其他格式
        date = new Date(timeStr)
      }
    } else {
      return null
    }

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return null
    }

    return date
  } catch (error) {

    return null
  }
}

// 获取离店提醒信息（6小时内）
function getCheckoutReminderInfo(room) {
  // 从房间的 bill_info 中获取 leave_time_plan 字段
  if (!room.bill_info || !room.bill_info.leave_time_plan) return null

  const leaveTimePlan = room.bill_info.leave_time_plan
  const leaveTime = parseTimeString(leaveTimePlan)

  if (!leaveTime) {

    return null
  }

  const now = new Date()
  const timeDiff = leaveTime.getTime() - now.getTime()
  const hoursDiff = timeDiff / (1000 * 60 * 60)

  // 6小时内且还未到离店时间
  if (hoursDiff > 0 && hoursDiff <= 6) {
    const remainingHours = Math.ceil(hoursDiff)
    return {
      shouldShow: true,
      remainingHours,
      text: `${remainingHours}h离店`,
      fullText: `还剩${remainingHours}小时离店`
    }
  }

  return null
}

// 判断是否应该显示清洁状态而不是业务状态
function shouldShowCleanStatus(room) {
  // 如果没有清洁状态，不显示
  if (!room.cleanStatusName) return false

  // 如果是净房，不需要特别显示清洁状态
  if (room.cleanStatusName === '净') return false

  // 如果房间状态是空房，优先显示清洁状态
  if (room.roomStatusName === '空房') return true

  // 如果清洁状态是需要关注的状态（脏、清洁中、维修等），显示清洁状态
  const importantCleanStatus = ['脏', '清洁中', '维修', '维修中', '封锁', '查房', '查房中']
  return importantCleanStatus.includes(room.cleanStatusName)
}

// 获取欠费信息
function getDebtInfo(room) {
  if (!room.bill_info) return null

  const billInfo = room.bill_info
  const connectConsumeAmount = parseFloat(billInfo.connect_consume_amount) || 0  // 联房欠费
  const connectPaymentAmount = parseFloat(billInfo.connect_payment_amount) || 0  // 联房支付
  const consumeAmount = parseFloat(billInfo.consume_amount) || 0                 // 本房间欠费

  // 计算总欠费：本房间欠费 + 联房欠费 - 联房支付
  const totalDebt = consumeAmount + connectConsumeAmount - connectPaymentAmount

  if (totalDebt > 0) {
    return {
      hasDebt: true,
      totalDebt,
      consumeAmount,
      connectConsumeAmount,
      connectPaymentAmount,
      debtText: totalDebt >= 1000 ? `${(totalDebt / 1000).toFixed(1)}k` : totalDebt.toFixed(0),
      fullText: `欠费${totalDebt.toFixed(2)}元`
    }
  }

  return null
}

// 联房悬浮效果处理
function handleConnectRoomHover(connectCode, isHover) {
  // 只处理有效的联房码
  if (!connectCode || typeof connectCode !== 'string' || connectCode.trim() === '') {
    return
  }

  // 响应式实现：仅记录当前悬浮的联房码，其余效果由样式类绑定实现
  hoveredConnectCode.value = isHover ? connectCode : ''
}

function formatTime(timeStr) {
  if (!timeStr) return ''

  try {
    let date

    // 处理不同的时间格式
    if (typeof timeStr === 'number') {
      // 时间戳格式
      date = new Date(timeStr * 1000)
    } else if (typeof timeStr === 'string') {
      // 字符串格式
      if (timeStr.includes('T') && timeStr.includes('Z')) {
        // ISO格式：2025-07-28T06:23:08.000Z
        date = new Date(timeStr)
      } else if (timeStr.includes(' ')) {
        // 标准格式：2025-07-28 14:30:00
        date = new Date(timeStr)
      } else {
        // 其他格式
        date = new Date(timeStr)
      }
    } else {
      return ''
    }

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      // 如果无法解析，尝试简单的字符串分割
      const parts = timeStr.split(' ')
      return parts.length > 1 ? parts[1].substring(0, 5) : timeStr
    }

    // 转换为本地时间
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const targetDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())

    // 计算日期差
    const diffDays = Math.floor((targetDate - today) / (1000 * 60 * 60 * 24))

    // 格式化时间（本地时间）
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    const timeDisplay = `${hours}:${minutes}`

    // 根据日期差显示不同格式
    if (diffDays === 0) {
      return `今日 ${timeDisplay}`
    } else if (diffDays === 1) {
      return `明日 ${timeDisplay}`
    } else if (diffDays === -1) {
      return `昨日 ${timeDisplay}`
    } else if (diffDays > 1 && diffDays <= 7) {
      return `${diffDays}日后 ${timeDisplay}`
    } else if (diffDays < -1 && diffDays >= -7) {
      return `${Math.abs(diffDays)}日前 ${timeDisplay}`
    } else {
      // 超过一周的显示具体日期
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      return `${month}/${day} ${timeDisplay}`
    }
  } catch (error) {

    // 如果出错，尝试提取时间部分
    if (typeof timeStr === 'string') {
      if (timeStr.includes('T')) {
        // ISO格式，提取时间部分
        const timePart = timeStr.split('T')[1]
        if (timePart) {
          const time = timePart.split('.')[0] || timePart.split('Z')[0]
          return time.substring(0, 5) // 只取 HH:MM
        }
      } else if (timeStr.includes(' ')) {
        // 标准格式，提取时间部分
        const parts = timeStr.split(' ')
        if (parts.length > 1) {
          return parts[1].substring(0, 5)
        }
      }
    }
    return timeStr
  }
}

// 获取今日订单
function getTodayOrders(orders) {
  if (!orders || !Array.isArray(orders)) return []

  const today = new Date()
  today.setHours(0, 0, 0, 0)
  const tomorrow = new Date(today)
  tomorrow.setDate(tomorrow.getDate() + 1)

  return orders.filter(order => {
    const timestamp = order.enter_time || order.enter_time_plan || order.start_time_plan
    if (!timestamp) return false

    const orderDate = new Date(timestamp * 1000)
    return orderDate >= today && orderDate < tomorrow
  })
}

// 获取未来订单（明日及以后）
function getFutureOrders(orders) {
  if (!orders || !Array.isArray(orders)) return []

  const tomorrow = new Date()
  tomorrow.setHours(0, 0, 0, 0)
  tomorrow.setDate(tomorrow.getDate() + 1)

  return orders.filter(order => {
    const timestamp = order.enter_time || order.enter_time_plan || order.start_time_plan
    if (!timestamp) return false

    const orderDate = new Date(timestamp * 1000)
    return orderDate >= tomorrow
  })
}

// 获取bill_info中的字段
function getBillInfoField(order, field) {
  if (!order) return ''

  // 直接从order中获取
  if (order[field]) return order[field]

  // 从bill_info中获取
  if (order.bill_info && order.bill_info[field]) {
    return order.bill_info[field]
  }

  return ''
}

// 判断是否有客人信息
function hasGuestInfo(room) {
  if (!room) return false

  // 优先检查bill_info中的客人信息
  if (room.bill_info && room.bill_info.link_man) {
    return true
  }

  // 检查guest对象
  if (room.guest && room.guest.name) {
    return true
  }

  return false
}

// 判断是否有预订信息
function hasReservationInfo(room) {
  if (!room) return false

  // 房间状态为预订相关
  const statusName = room.roomStatusName || room.room_status_name || ''
  if (statusName.includes('预订') || statusName.includes('预定')) {
    return true
  }

  // 或者有预订相关的状态标识
  const status = room.status || ''
  if (status === 'reserved' || status === 'noshow') {
    return true
  }

  return false
}

// 获取到店时间
function getArrivalTime(room) {

  if (!room) return null

  // 从bill_info获取
  if (room.bill_info && room.bill_info.arrival_time) {
    return room.bill_info.arrival_time
  }

  // 从guest对象获取
  if (room.guest && room.guest.arrivalTime) {
    return room.guest.arrivalTime
  }

  return null
}

// 获取预期到店时间
function getExpectedArrival(room) {
  if (!room) return null

  // 从bill_info获取
  if (room.bill_info && room.bill_info.expected_arrival) {
    return room.bill_info.expected_arrival
  }

  // 从guest对象获取
  if (room.guest && room.guest.expectedArrival) {
    return room.guest.expectedArrival
  }

  return null
}

// 房间详情缓存
const roomDetailCache = new Map()

// 预加载请求序列，用于丢弃过期响应
let preloadSeq = 0

// 预加载房间详情（带过期响应丢弃与组件存活校验）
function preloadRoomDetail(room) {
  if (!room?.billId && !room?.bill_id) return

  const billId = room.billId || room.bill_id
  const cacheKey = `room_detail_${billId}`

  // 如果已有缓存或正在加载，跳过
  if (roomDetailCache.has(cacheKey)) return

  // 标记为正在加载
  roomDetailCache.set(cacheKey, { loading: true })

  // 记录本次请求序号
  const requestId = ++preloadSeq

  getRoomBillDetail({ bill_id: billId })
    .then(response => {
      // 丢弃过期响应或组件已卸载
      if (!isMounted.value || requestId !== preloadSeq) return

      if (response && response.data) {
        roomDetailCache.set(cacheKey, {
          loading: false,
          data: response.data,
          timestamp: Date.now()
        })
      } else {
        roomDetailCache.delete(cacheKey)
      }
    })
    .catch(() => {
      // 丢弃过期响应或组件已卸载
      if (!isMounted.value || requestId !== preloadSeq) return
      roomDetailCache.delete(cacheKey)
    })
}

// 清理过期缓存
function cleanExpiredCache() {
  const now = Date.now()
  const maxAge = 5 * 60 * 1000 // 5分钟过期

  for (const [key, value] of roomDetailCache.entries()) {
    if (value.timestamp && (now - value.timestamp) > maxAge) {
      roomDetailCache.delete(key)
    }
  }
}

// 定期清理缓存
setInterval(cleanExpiredCache, 60000) // 每分钟清理一次

// 添加组件挂载状态检查
const isMounted = ref(false)
const isDestroyed = ref(false)

async function handleRoomClick(room) {
  // 防护措施：检查参数和组件状态
  if (!room || !isMounted.value || isDestroyed.value) {

    return
  }

  // 验证房间数据的完整性
  if (!room.id && !room.room_id) {

    $message.error('房间数据无效，请刷新页面重试')
    return
  }

  try {
    // 记住当前点击的房间信息（用于快速打开订单详情）
    roomSelectionStore.setFromRoom(room)

    // 先关闭弹窗，避免状态冲突
    showRoomDetailModal.value = false

    // 等待DOM更新完成
    await nextTick()

    // 创建房间数据的深拷贝，避免响应式问题
    const roomDataCopy = {
      ...room,
      // 确保关键字段存在
      id: room.id || room.room_id,
      roomNumber: room.roomNumber || room.room_number,
      roomType: room.roomType || room.room_type_name,
      // 添加时间戳，用于调试
      _clickTime: Date.now()
    }

    // 设置房间数据
    selectedRoomData.value = roomDataCopy

    // 再次等待DOM更新
    await nextTick()

    // 显示弹窗
    showRoomDetailModal.value = true

  } catch (error) {

    $message.error('打开房间详情失败：' + error.message)

    // 重置状态
    selectedRoomData.value = null
    showRoomDetailModal.value = false
    return
  }
}

// 鼠标进入房间卡片 - 预加载数据
function handleRoomMouseEnter(room) {
  // 处理联房悬浮效果 - 只有真正的联房才触发
  if (room.isConnectedRoom && room.connectCode) {
    handleConnectRoomHover(room.connectCode, true)
  }

  // 预加载房间详情数据
  if (room.billId || room.bill_id) {
    preloadRoomDetail(room)
  }
}

// 鼠标离开房间卡片
function handleRoomMouseLeave(room) {
  // 处理联房悬浮效果 - 只有真正的联房才触发
  if (room.isConnectedRoom && room.connectCode) {
    handleConnectRoomHover(room.connectCode, false)
  }
}

// 判断房间是否可以办理入住
function isRoomAvailableForCheckIn(room) {
  if (!room) return false

  // 检查房间状态
  const roomStatusInfo = getRoomStatusInfo(room.room_status_record_id)
  if (!roomStatusInfo) return false

  // 可入住的房间状态：空闲(4)、空净(5)
  const availableStatusIds = [4, 5]
  const isStatusAvailable = availableStatusIds.includes(roomStatusInfo.id)

  // 修改：允许脏房直接入住，不再限制清洁状态
  // 原来的限制：const isCleanStatusOk = room.cleanStatus !== 4
  // 现在允许所有清洁状态，包括脏房
  const isCleanStatusOk = true

  // 检查是否已有订单（有订单的房间不能重复办理入住）
  const hasExistingOrder = !!(room.billId || room.bill_id)

  // 房间状态可用 && 清洁状态OK（现在总是true） && 没有现有订单
  return isStatusAvailable && isCleanStatusOk && !hasExistingOrder
}

// 判断房间是否可以尝试办理入住（包括状态异常的情况）
function canAttemptCheckIn(room) {
  if (!room) return false

  // 没有订单的房间都可以尝试办理入住
  const hasExistingOrder = !!(room.billId || room.bill_id)
  return !hasExistingOrder
}

function handleRoomRightClick(room, event) {

  // 判断房间是否可以办理入住
  const isAvailable = isRoomAvailableForCheckIn(room)

  // 判断房间是否可以尝试办理入住
  const canAttempt = canAttemptCheckIn(room)

  // 判断房间是否有订单
  const hasOrder = !!(room.billId || room.bill_id)

  // 优先级1：房间完全可以办理入住
  if (isAvailable) {
    selectedRoomData.value = room
    showCheckInModal.value = true
    $message.success(`正在为房间 ${room.roomNumber || room.room_number} 办理入住`)
  }
  // 优先级2：没有订单的房间，允许尝试办理入住（即使状态不完全符合）
  else if (canAttempt) {
    selectedRoomData.value = room
    showCheckInModal.value = true

    // 根据具体情况给出不同提示
    const roomStatusInfo = getRoomStatusInfo(room.room_status_record_id)
    if (room.cleanStatus === 4) {
      // 修改：脏房可以直接入住，改为提示信息而不是警告
      $message.info(`房间 ${room.roomNumber || room.room_number} 为脏房状态，正在办理入住`)
    } else if (roomStatusInfo && ![4, 5].includes(roomStatusInfo.id)) {
      $message.warning(`房间 ${room.roomNumber || room.room_number} 状态为"${roomStatusInfo.name}"，请确认是否可以入住`)
    } else {
      $message.info(`正在为房间 ${room.roomNumber || room.room_number} 办理入住`)
    }
  }
  // 优先级3：有订单的房间，显示右键菜单（保留原有功能）
  else {
    // 右键时也记住当前房间
    roomSelectionStore.setFromRoom(room)
    contextMenuRoom.value = room
    contextMenuX.value = event.clientX
    contextMenuY.value = event.clientY
    showContextMenu.value = true
  }
}

function closeContextMenu() {
  showContextMenu.value = false
}

function handleContextMenuSelect(key) {
  showContextMenu.value = false

  switch (key) {
    case 'detail':
      selectedRoomData.value = contextMenuRoom.value
      showRoomDetailModal.value = true
      break
    case 'checkin':
      selectedRoomData.value = contextMenuRoom.value
      showCheckInModal.value = true
      break
    case 'reservation':
      selectedRoomData.value = contextMenuRoom.value
      showReservationModal.value = true
      break
    case 'checkout':
      handleQuickCheckout(contextMenuRoom.value)
      break
    case 'clean':
      handleRoomCleaning(contextMenuRoom.value)
      break
    case 'maintenance':
      handleRoomMaintenance(contextMenuRoom.value)
      break
  }
}

// 入住
function handleCheckin() {
  $message.info('正在跳转到入住办理页面...')
  // 这里可以打开入住弹窗或跳转到入住页面
  showCheckInModal.value = true
}

// 快速预订
function handleReservation() {
  $message.info('正在跳转到预订办理页面...')
  // 这里可以打开预订弹窗或跳转到预订页面
  showReservationModal.value = true
  // router.push('/hotel/reservation')
}

function handleQuickCheckout(room) {
  $message.info(`正在为房间 ${room.roomNumber} 办理退房...`)
}

function handleRoomCleaning(room) {
  $message.info(`正在安排房间 ${room.roomNumber} 清洁...`)
}

function handleRoomMaintenance(room) {
  $message.info(`正在为房间 ${room.roomNumber} 报修...`)
}

// refreshRoomStatus 函数已被 silentRefresh 替代，提供更好的用户体验

// 更新房间统计
function updateRoomStats() {
  const stats = {
    occupied: 0,        // 已入住
    available: 0,       // 可入住
    checkout: 0,        // 待退房
    dirty: 0,           // 脏房
    cleaning: 0,        // 清洁中
    maintenance: 0,     // 维修中
    inspecting: 0,      // 查房中
    blocked: 0,         // 房间封锁
    reserved: 0,        // 已预订
    noshow: 0           // 未到店
  }

  rooms.value.forEach(room => {
    if (stats[room.status] !== undefined) {
      stats[room.status]++
    }
  })

  roomStats.value = stats
}

// 切换自动刷新
function toggleAutoRefresh(enabled) {
  if (enabled) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

// 开始自动刷新
function startAutoRefresh() {
  stopAutoRefresh() // 先停止现有的定时器

  // 初始化倒计时
  refreshCountdown.value = refreshInterval.value

  // 数据刷新定时器 - 按间隔执行数据刷新
  refreshTimer.value = setInterval(() => {
    silentRefresh()
    // 刷新后重置倒计时
    refreshCountdown.value = refreshInterval.value
  }, refreshInterval.value * 1000) // 转换为毫秒

  // 独立的倒计时定时器 - 只负责UI显示，不影响数据渲染
  countdownTimer.value = setInterval(() => {
    if (refreshCountdown.value > 0) {
      refreshCountdown.value--
    }
  }, 1000)
}

// 停止自动刷新
function stopAutoRefresh() {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }
}

// 更新刷新间隔
function updateRefreshInterval(newInterval) {
  refreshInterval.value = newInterval
  if (autoRefreshEnabled.value) {
    startAutoRefresh() // 重新启动定时器
  }
}

// 设置卡片大小
function setCardSize(size) {
  cardSize.value = size
  $message.info(`已切换到${size === 'large' ? '大卡片' : size === 'medium' ? '中卡片' : '小方块'}模式`)
}

// 处理清洁状态图例点击
function handleCleanStatusLegendClick(cleanStatusId) {
  selectedCleanStatus.value = selectedCleanStatus.value === cleanStatusId ? null : cleanStatusId
  updateFilterParams()
}

// 处理业务状态图例点击
function handleBusinessStatusLegendClick(statusSign) {
  selectedStatus.value = selectedStatus.value === statusSign ? null : statusSign
  updateFilterParams()
}

// 处理楼栋变化
function handleBuildingChange(buildingId) {
  selectedBuilding.value = buildingId
  selectedFloor.value = null // 重置楼层选择

  if (buildingId) {
    fetchFloorList(buildingId)
  }

  updateFilterParams()
}

// 处理楼层变化
function handleFloorChange(floorId) {
  selectedFloor.value = floorId
  updateFilterParams()
}

// 处理房型变化
function handleRoomTypeChange(roomTypeId) {
  selectedRoomType.value = roomTypeId
  updateFilterParams()
}

// 处理状态变化
function handleStatusChange(status) {
  selectedStatus.value = status
  updateFilterParams()
}

// 处理清洁状态变化
function handleCleanStatusChange(cleanStatusId) {
  selectedCleanStatus.value = cleanStatusId
  updateFilterParams()
}

// 判断统计卡片状态是否激活
function isStatusActive(statusKey) {
  if (!selectedStatus.value) return false

  // 直接查找匹配的状态配置
  let targetSign = null

  if (statusKey === 'vacant') {
    // 查找空房相关的配置
    const config = roomStatusConfig.value.find(s =>
      s.name === '空房' || s.name === '可用' || s.name === '空闲' ||
      s.sign === 'vacant' || s.sign === 'available' || s.sign === 'empty'
    )
    targetSign = config ? config.sign : null
  } else if (statusKey === 'stay') {
    // 查找入住相关的配置
    const config = roomStatusConfig.value.find(s =>
      s.name === '已住' || s.name === '入住' || s.name === '在住' ||
      s.sign === 'stay' || s.sign === 'occupied' || s.sign === 'checkin'
    )
    targetSign = config ? config.sign : null
  } else if (statusKey === 'reserved') {
    // 查找预订相关的配置
    const config = roomStatusConfig.value.find(s =>
      s.name === '预订' || s.name === '已预订' || s.name === '预定' ||
      s.sign === 'reserved' || s.sign === 'booking'
    )
    targetSign = config ? config.sign : null
  } else if (statusKey === 'checkout') {
    // 查找退房相关的配置
    const config = roomStatusConfig.value.find(s =>
      s.name === '退房' || s.name === '待退房' || s.name === '离店' ||
      s.sign === 'checkout' || s.sign === 'departure'
    )
    targetSign = config ? config.sign : null
  }

  return selectedStatus.value === targetSign
}

// 处理统计卡片点击（业务状态）
function handleStatClick(statusKey) {

  // 直接查找匹配的状态配置
  let targetSign = null

  if (statusKey === 'vacant') {
    // 查找空房相关的配置
    const config = roomStatusConfig.value.find(s =>
      s.name === '空房' || s.name === '可用' || s.name === '空闲' ||
      s.sign === 'vacant' || s.sign === 'available' || s.sign === 'empty'
    )
    targetSign = config ? config.sign : null

  } else if (statusKey === 'stay') {
    // 查找入住相关的配置
    const config = roomStatusConfig.value.find(s =>
      s.name === '已住' || s.name === '入住' || s.name === '在住' ||
      s.sign === 'stay' || s.sign === 'occupied' || s.sign === 'checkin'
    )
    targetSign = config ? config.sign : null

  } else if (statusKey === 'reserved') {
    // 查找预订相关的配置
    const config = roomStatusConfig.value.find(s =>
      s.name === '预订' || s.name === '已预订' || s.name === '预定' ||
      s.sign === 'reserved' || s.sign === 'booking'
    )
    targetSign = config ? config.sign : null

  } else if (statusKey === 'checkout') {
    // 查找退房相关的配置
    const config = roomStatusConfig.value.find(s =>
      s.name === '退房' || s.name === '待退房' || s.name === '离店' ||
      s.sign === 'checkout' || s.sign === 'departure'
    )
    targetSign = config ? config.sign : null

  }

  if (targetSign) {
    // 切换选中状态
    selectedStatus.value = selectedStatus.value === targetSign ? null : targetSign
    updateFilterParams()
  } else {

  }
}

// 处理清洁统计卡片点击
function handleCleanStatClick(cleanStatusId) {
  selectedCleanStatus.value = selectedCleanStatus.value === cleanStatusId ? null : cleanStatusId
  updateFilterParams()
}

// 处理弹窗成功
function handleModalSuccess() {
  // 刷新房间数据
  fetchRoomList(filterParams.value)
  $message.success('操作成功')
}

// 处理右键菜单操作
function handleContextMenuAction(data) {
  switch (data.action) {
    case 'detail':
      selectedRoomData.value = data.roomData
      showRoomDetailModal.value = true
      break
    case 'checkin':
      selectedRoomData.value = data.roomData
      showCheckInModal.value = true
      break
    case 'reservation':
      selectedRoomData.value = data.roomData
      showReservationModal.value = true
      break
    case 'checkout':
      handleQuickCheckout(data.roomData)
      break
    case 'start-cleaning':
    case 'complete-cleaning':
      handleRoomCleaning(data.roomData)
      break
    case 'maintenance':
    case 'complete-maintenance':
      handleRoomMaintenance(data.roomData)
      break
    case 'toggle-block':
      handleToggleBlock(data.roomData)
      break
    case 'history':
      selectedRoomData.value = data.roomData
      showRoomDetailModal.value = true
      break
    case 'refresh':
      fetchRoomList(filterParams.value)
      break
  }
}

// 处理房间详情弹窗的按钮事件
function handleReservationFromDetail(roomData) {
  showRoomDetailModal.value = false
  selectedRoomData.value = roomData
  showReservationModal.value = true
}

function handleCheckInFromDetail(roomData) {
  showRoomDetailModal.value = false
  selectedRoomData.value = roomData
  showCheckInModal.value = true
}

function handleCheckOutFromDetail(roomData) {
  showRoomDetailModal.value = false
  handleQuickCheckout(roomData)
}

function handleRoomServiceFromDetail(roomData) {
  showRoomDetailModal.value = false
  $message.info(`房间 ${roomData.room_number} 房间服务`)
  // 这里可以添加房间服务的具体逻辑
}

function handleConfirmCheckInFromDetail(roomData) {
  showRoomDetailModal.value = false
  $message.info(`确认房间 ${roomData.room_number} 入住`)
  // 这里可以添加确认入住的具体逻辑
}

function handleCancelReservationFromDetail(roomData) {
  showRoomDetailModal.value = false
  $message.info(`取消房间 ${roomData.roomNumber} 预订`)
  // 这里可以添加取消预订的具体逻辑
}

function handleBillDetailFromDetail(roomData) {
  showRoomDetailModal.value = false

  // 优先从房间数据中直接获取 bill_id
  const billId = roomData.bill_id || roomData.billId

  if (billId) {

    // 设置订单数据
    selectedOrderData.value = {
      id: billId,
      bill_id: billId,
      room_number: roomData.room_number || roomData.roomNumber,
      room_type_name: roomData.room_type_name,
      // 如果有 bill_info，使用其中的数据
      ...(roomData.bill_info || roomData.billInfo || {})
    }
    selectedBillId.value = billId
    showOrderDetail.value = true

  } else {

    $message.warning(`房间 ${roomData.roomNumber || roomData.room_number} 暂无订单信息`)
  }
}

function handleViewFullOrderFromDetail(roomData) {
  // 不关闭房间详情弹窗，直接打开订单详情

  // 优先从房间数据中直接获取 bill_id
  const billId = roomData.bill_id || roomData.billId

  if (billId) {

    // 设置订单数据
    selectedOrderData.value = {
      id: billId,
      bill_id: billId,
      room_number: roomData.room_number || roomData.roomNumber,
      room_type_name: roomData.room_type_name,
      // 如果有 bill_info，使用其中的数据
      ...(roomData.bill_info || roomData.billInfo || {})
    }
    selectedBillId.value = billId
    showOrderDetail.value = true

  } else {

    $message.warning(`房间 ${roomData.roomNumber || roomData.room_number} 暂无订单信息`)
  }
}

// 订单详情抽屉事件处理
function handleOrderPrint(orderData) {
  $message.info('准备打印订单...')
  // 这里可以添加打印订单的具体逻辑

}

function handleOrderExport(orderData) {
  $message.info('准备导出订单详情...')
  // 这里可以添加导出订单的具体逻辑

}

function handleRoomMaintenanceFromDetail(roomData) {
  showRoomDetailModal.value = false
  $message.info(`房间 ${roomData.roomNumber} 维修管理`)
  // 这里可以添加房间维修管理的具体逻辑
}

function handleRoomCleaningFromDetail(roomData) {
  showRoomDetailModal.value = false
  $message.info(`开始清洁房间 ${roomData.roomNumber}`)
  // 这里可以添加房间清洁的具体逻辑
}

// 处理房间封锁切换
function handleToggleBlock(room) {
  const action = room.is_blocked ? '解除封锁' : '封锁'
  $message.info(`房间 ${room.room_number} ${action}操作`)
  // 这里可以调用封锁API
}

// 处理搜索
function handleSearch() {
  // 更新房间号筛选参数
  filterParams.value.room_number = searchKeyword.value || ""

  // 重新获取房间数据，传递筛选参数
  fetchRoomList(filterParams.value)
}

// 防抖定时器
let filterUpdateTimer = null

// 更新筛选参数 - 性能优化版本
function updateFilterParams() {
  // 🚀 性能优化：使用防抖避免频繁API调用
  if (filterUpdateTimer) {
    clearTimeout(filterUpdateTimer)
  }

  filterUpdateTimer = setTimeout(() => {
    // 清洁状态筛选 - 使用数字ID
    filterParams.value.room_clear_status = selectedCleanStatus.value ? [selectedCleanStatus.value] : []

    // 业务状态筛选 - 需要将sign转换为ID
    if (selectedStatus.value) {
      const statusConfig = roomStatusConfig.value.find(status => status.sign === selectedStatus.value)
      filterParams.value.room_record_status = statusConfig ? [statusConfig.id] : []
    } else {
      filterParams.value.room_record_status = []
    }

    // 楼栋筛选
    filterParams.value.building_id = selectedBuilding.value || ""

    // 楼层筛选
    filterParams.value.floor_id = selectedFloor.value || ""

    // 房型筛选
    filterParams.value.room_type_id = selectedRoomType.value || ""

    // 房间号搜索
    filterParams.value.room_number = searchKeyword.value || ""

    // 🚀 性能优化：防抖后执行API调用
    fetchRoomList(filterParams.value)
    // 记录筛选后的基线快照（仅dev+shadow）
    logBaselineSnapshot('filters-updated')
  }, 300) // 300ms防抖延迟
}

// 获取用户角色
function getUserRole() {
  // 从用户store或登录数据中获取角色信息
  const userStore = useUserStore()
  return userStore.userInfo?.role || 'staff'
}

// 检查房间是否快到退房时间
function isCheckoutSoon(room) {
  if (!room.checkoutTime && !room.checkout_time) return false

  const checkoutTime = new Date(room.checkoutTime || room.checkout_time)
  const now = new Date()
  const timeDiff = checkoutTime.getTime() - now.getTime()
  const hoursDiff = timeDiff / (1000 * 60 * 60)

  // 如果在2小时内需要退房，返回true
  return hoursDiff > 0 && hoursDiff <= 2
}

// 检查房间是否已超时未退房
function isCheckoutOverdue(room) {
  if (!room.checkoutTime && !room.checkout_time) return false

  const checkoutTime = new Date(room.checkoutTime || room.checkout_time)
  const now = new Date()

  // 如果已过退房时间，返回true
  return now > checkoutTime
}

// 移除样式缓存机制，避免与Vue响应式系统冲突

// 获取房间卡片样式 - 使用接口返回的颜色
function getRoomCardStyle(room) {
  // 直接使用接口返回的颜色
  const primaryColor = room.room_status_color || '#e5e7eb'
  const secondaryColor = room.clear_color || '#f3f4f6'

  // 检查退房提醒
  let alertColor = ''
  if (isCheckoutOverdue(room)) {
    alertColor = '#ef4444' // 红色 - 已超时
  } else if (isCheckoutSoon(room)) {
    alertColor = '#f59e0b' // 橙色 - 即将到期
  }

  // 创建渐变背景，结合业务状态和清洁状态
  // 使用更饱满的颜色透明度
  let gradientBackground = `linear-gradient(135deg, ${colorWithOpacity(primaryColor, 0.35)} 0%, ${colorWithOpacity(primaryColor, 0.15)} 50%, rgba(255, 255, 255, 0.85) 100%)`

  // 如果有退房提醒，添加警告色边框
  if (alertColor) {
    gradientBackground = `linear-gradient(135deg, ${colorWithOpacity(alertColor, 0.4)} 0%, ${colorWithOpacity(primaryColor, 0.35)} 30%, ${colorWithOpacity(primaryColor, 0.15)} 70%, rgba(255, 255, 255, 0.85) 100%)`
  }

  return {
    '--room-primary-color': primaryColor,
    '--room-secondary-color': secondaryColor,
    'border-color': colorWithOpacity(primaryColor, 0.6),
    'background': gradientBackground,
    'box-shadow': `0 2px 12px ${colorWithOpacity(primaryColor, 0.35)}`
  }
}

// 获取房间状态徽章样式 - 使用接口返回的颜色
function getRoomStatusBadgeStyle(room) {
  const color = room.room_status_color || '#e5e7eb'
  return {
    'background': colorWithOpacity(color, 0.25),
    'color': color,
    'border': `1px solid ${colorWithOpacity(color, 0.5)}`
  }
}

// 获取清洁状态徽章样式 - 使用接口返回的颜色
function getCleanStatusBadgeStyle(room) {
  const color = room.clear_color || '#f3f4f6'
  return {
    'background': colorWithOpacity(color, 0.18),
    'color': color,
    'border': `1px solid ${colorWithOpacity(color, 0.4)}`,
    'font-size': '0.65rem',
    'padding': '0.125rem 0.375rem',
    'border-radius': '4px',
    'font-weight': '500'
  }
}

// 获取清洁状态CSS类
function getCleanStatusClass(room) {
  const statusName = room.cleanStatusName || room.clear_status_name || ''
  const classes = []

  if (statusName.includes('脏')) {
    classes.push('status-dirty')
  } else if (statusName.includes('维修')) {
    classes.push('status-maintenance')
  } else if (statusName.includes('清洁')) {
    classes.push('status-cleaning')
  } else if (statusName.includes('查房')) {
    classes.push('status-inspecting')
  } else if (statusName.includes('封锁')) {
    classes.push('status-blocked')
  }

  return classes
}

// 获取清洁状态图标
function getCleanStatusIcon(room) {
  const statusName = room.cleanStatusName || room.clear_status_name || ''

  if (statusName.includes('脏')) {
    return 'i-mdi:delete-sweep'
  } else if (statusName.includes('维修')) {
    return 'i-mdi:tools'
  } else if (statusName.includes('清洁')) {
    return 'i-mdi:broom'
  } else if (statusName.includes('查房')) {
    return 'i-mdi:magnify'
  } else if (statusName.includes('封锁')) {
    return 'i-mdi:block-helper'
  } else {
    return 'i-mdi:check-circle'
  }
}

// 获取实际的CSS变量值
function getCSSVariableValue(variableName) {
  // 优先从documentElement读取（ThemeSelector设置）
  let value = getComputedStyle(document.documentElement).getPropertyValue(variableName).trim()
  // 兼容 ThemeSetting 可能写在 body 的情况
  if (!value) {
    value = getComputedStyle(document.body).getPropertyValue(variableName).trim()
  }

  return value
}

// 将颜色转换为带透明度的rgba格式
function colorWithOpacity(color, opacity) {
  // 如果是十六进制颜色
  if (color.startsWith('#')) {
    const hex = color.replace('#', '')
    const r = parseInt(hex.substr(0, 2), 16)
    const g = parseInt(hex.substr(2, 2), 16)
    const b = parseInt(hex.substr(4, 2), 16)
    return `rgba(${r}, ${g}, ${b}, ${opacity})`
  }

  // 如果是rgb格式
  if (color.startsWith('rgb(')) {
    const values = color.match(/\d+/g)
    if (values && values.length >= 3) {
      return `rgba(${values[0]}, ${values[1]}, ${values[2]}, ${opacity})`
    }
  }

  // 如果是rgba格式，替换透明度
  if (color.startsWith('rgba(')) {
    return color.replace(/,\s*[\d.]+\)$/, `, ${opacity})`)
  }

  // 其他情况，尝试直接添加透明度（兼容性处理）
  return `${color}${Math.round(opacity * 255).toString(16).padStart(2, '0')}`
}

// 获取与主题色搭配的房间状态颜色
function getThemeAwareRoomColor(room) {
  const sign = room.roomStatusSign || room.sign || ''
  const statusName = room.room_status_name || room.roomStatusName || ''
  const originalColor = room.room_status_color || '#e5e7eb'

  // 获取当前主题色的实际值
  const primaryColor = getCSSVariableValue('--primary-color') || '#8A9CFF'
  const primaryColorSuppl = getCSSVariableValue('--primary-color-suppl') || '#A8B5FF'

  let resultColor = originalColor

  // 优先使用标准化的 sign 判断
  switch (sign) {
    case 'available':
    case 'vacant':
      resultColor = primaryColor; break
    case 'reserved':
      resultColor = primaryColorSuppl; break
    case 'occupied':
    case 'maintenance':
      resultColor = '#ef4444'; break
    case 'checkout':
      resultColor = '#f59e0b'; break
    case 'blocked':
      resultColor = '#6b7280'; break
    default:
      // 如果没有 sign，则回退到名称判断
      if (statusName.includes('可') || statusName.includes('空') || /available|vacant/i.test(statusName)) {
        resultColor = primaryColor
      } else if (statusName.includes('预') || /reserve/i.test(statusName)) {
        resultColor = primaryColorSuppl
      } else if (statusName.includes('住') || /occupy/i.test(statusName)) {
        resultColor = '#ef4444'
      } else if (statusName.includes('退') || /checkout/i.test(statusName)) {
        resultColor = '#f59e0b'
      } else if (statusName.includes('修') || /maint/i.test(statusName)) {
        resultColor = '#ef4444'
      } else if (statusName.includes('封') || /block/i.test(statusName)) {
        resultColor = '#6b7280'
      } else {
        resultColor = isNeutralStatus(statusName) ? primaryColor : originalColor
      }
  }

  return resultColor
}

// 获取与主题色搭配的清洁状态颜色
function getThemeAwareCleanColor(room) {
  const statusName = room.cleanStatusName || room.clear_status_name || ''
  const originalColor = room.clean_status_color || room.clear_color || '#f3f4f6'

  // 获取当前主题色的实际值
  const primaryColor = getCSSVariableValue('--primary-color') || '#8A9CFF'
  const primaryColorSuppl = getCSSVariableValue('--primary-color-suppl') || '#A8B5FF'

  if (statusName.includes('脏')) {
    return '#ef4444' // 红色 - 保持警告色
  } else if (statusName.includes('维修')) {
    return '#f59e0b' // 橙色 - 保持警告色
  } else if (statusName.includes('清洁')) {
    return primaryColor // 主题色
  } else if (statusName.includes('查房')) {
    return primaryColorSuppl // 主题色辅助色
  } else if (statusName.includes('封锁')) {
    return '#6b7280' // 灰色 - 保持中性色
  } else {
    // 其他清洁状态使用主题色
    return isNeutralStatus(statusName) ? primaryColor : originalColor
  }
}

// 判断是否为中性状态（可以使用主题色的状态）
function isNeutralStatus(statusName) {
  const neutralKeywords = ['正常', '完成', '可用', '空闲', '待用', '准备', '就绪']
  return neutralKeywords.some(keyword => statusName.includes(keyword))
}

// 获取清洁状态颜色 - 使用接口返回的颜色
function getCleanStatusColor(room) {
  return room.clear_color || '#f3f4f6'
}

// 获取统计项样式（使用接口返回的颜色）
function getStatItemStyle(statusType) {
  let color = '#6b7280' // 默认颜色

  // 根据状态类型查找对应的颜色
  if (['occupied', 'available', 'reserved', 'checkout', 'maintenance', 'blocked'].includes(statusType)) {
    // 从业务状态配置中查找颜色
    const businessStatus = roomStatusConfig.value.find(status => {
      const mappedStatus = mapRoomStatusBySign_legacy(status.sign)
      return mappedStatus === statusType
    })
    if (businessStatus) {
      color = businessStatus.color
    }
  } else if (['dirty', 'cleaning'].includes(statusType)) {
    // 从清洁状态配置中查找颜色
    const cleanStatus = cleanStatusConfig.value.find(status => {
      const cleanKey = getCleanStatusKey(status.name)
      return cleanKey === statusType
    })
    if (cleanStatus) {
      color = cleanStatus.color
    }
  }

  // 为维修状态特殊处理（可能来自业务状态或清洁状态）
  if (statusType === 'maintenance') {
    const cleanMaintenanceStatus = cleanStatusConfig.value.find(status => {
      const cleanKey = getCleanStatusKey(status.name)
      return cleanKey === 'maintenance'
    })
    if (cleanMaintenanceStatus) {
      color = cleanMaintenanceStatus.color
    }
  }

  return {
    'background': `linear-gradient(135deg, ${color}20, rgba(255, 255, 255, 0.95))`,
    'color': color,
    'border': `2px solid ${color}40`,
    'box-shadow': `0 2px 8px ${color}20`
  }
}

// 酒店切换
function switchHotel(hotel) {
  currentHotel.value = hotel
  showHotelSwitcher.value = false
  $message.success(`已切换到${hotel.name}`)
  // 切换酒店后无感刷新房态数据
  silentRefresh()
}

// 搜索会员
function searchMembers() {
  if (memberSearch.value) {
    $message.info('搜索会员功能开发中...')
  }
}

// 刷身份证
function scanIdCard() {
  // 模拟身份证信息
  guestInfo.value = {
    name: '张三',
    idCard: '110101199001011234',
    phone: '138****1234'
  }
  $message.success('身份证信息读取成功')
}

// 添加更多房间
function addMoreRooms() {
  $message.info('选择更多房间功能开发中...')
}

// 移除房间
function removeRoom(room) {
  const index = selectedRooms.value.findIndex(r => r.roomNumber === room.roomNumber)
  if (index > -1) {
    selectedRooms.value.splice(index, 1)
  }
}

// 确认入住
function confirmCheckin() {
  if (!guestInfo.value) {
    $message.error('请先获取客人信息')
    return
  }

  if (selectedRooms.value.length === 0) {
    $message.error('请选择房间')
    return
  }

  $message.success('入住办理成功！')
  showCheckInModal.value = false

  // 重置表单
  guestInfo.value = null
  selectedRooms.value = []
  selectedPackages.value = []
  memberSearch.value = ''
}

// 监听顶部刷新事件
function handleTopRefresh() {
  silentRefresh()
}

// 强制重新渲染房间卡片（用于主题切换时）
const forceRerenderKey = ref(0)

function forceRerender() {
  forceRerenderKey.value++

}


// 监听主题变化
watch(() => appStore.currentTheme, (newTheme, oldTheme) => {
  if (newTheme !== oldTheme) {

    // 延迟一点确保CSS变量已更新
    setTimeout(() => {
      forceRerender()
    }, 150)
  }
}, { immediate: false })

// 生命周期
onMounted(() => {
  // 设置组件为已挂载状态
  isMounted.value = true
  isDestroyed.value = false

  // 初始化布局模式
  const savedLayout = localStorage.getItem('roomStatus_layoutMode')
  if (savedLayout && ['top', 'left', 'bottom'].includes(savedLayout)) {
    layoutMode.value = savedLayout
  }

  // 监听顶部刷新事件
  window.addEventListener('refresh-room-status', handleTopRefresh)

  // 获取房态数据
  fullDataRefresh().finally(() => {
    // 首屏基线日志（仅dev+shadow）
    setupShadowWatch()
    logBaselineSnapshot('initial-load')
  })

  // 启动自动刷新（仅数据刷新）
  if (autoRefreshEnabled.value) {
    startAutoRefresh()
  }

  // 启用页面可见性检查
  setupVisibilityLoginCheck({
    needTip: false,
    autoRedirect: true
  })
})

onUnmounted(() => {
  // 设置组件为已卸载状态
  isDestroyed.value = true
  isMounted.value = false

  // 清理响应式数据（保持数据 shape 稳定）
  selectedRoomData.value = {}
  showRoomDetailModal.value = false

  // 清理事件监听器
  window.removeEventListener('refresh-room-status', handleTopRefresh)

  // 清理所有定时器
  stopAutoRefresh() // 这会清理 refreshTimer 和 countdownTimer
})
</script>

<style scoped>
.room-status-page {
  height: 100%;
  max-height: 100%;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
}

/* 无感刷新指示器 */
.refresh-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  color: #666;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 骨架屏样式 */
.skeleton-container {
  padding: 20px;
}

.skeleton-building {
  margin-bottom: 32px;
}

.skeleton-building-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.skeleton-building-title {
  width: 120px;
  height: 24px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.skeleton-building-stats {
  width: 80px;
  height: 20px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.skeleton-floor {
  margin-bottom: 24px;
}

.skeleton-floor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.skeleton-floor-title {
  width: 80px;
  height: 20px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.skeleton-floor-stats {
  width: 60px;
  height: 16px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.skeleton-room-grid {
  display: grid;
  gap: 12px;
}

.skeleton-grid-large {
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
}

.skeleton-grid-medium {
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}

.skeleton-grid-small {
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
}

.skeleton-room-card {
  border-radius: 8px;
  background: #fafafa;
  border: 1px solid #f0f0f0;
  overflow: hidden;
}

.skeleton-card-large {
  height: 160px;
  padding: 16px;
}

.skeleton-card-medium {
  height: 120px;
  padding: 12px;
}

.skeleton-card-small {
  height: 80px;
  padding: 8px;
}

.skeleton-room-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.skeleton-room-number {
  width: 60px;
  height: 18px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.skeleton-room-type {
  width: 80px;
  height: 14px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.skeleton-guest-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-top: 8px;
}

.skeleton-guest-name {
  width: 100px;
  height: 16px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.skeleton-guest-phone {
  width: 120px;
  height: 14px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.skeleton-room-status {
  width: 50px;
  height: 12px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
  margin-top: auto;
}

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 加载和错误状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  margin: 20px;
  backdrop-filter: blur(10px);
}

.error-container {
  margin: 20px;
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  margin: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  font-size: 0.6rem; /* 从1.5rem减少到1.2rem */
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.page-title i {
  font-size: 1.4rem; /* 从1.8rem减少到1.4rem */
  color: var(--primary-color);
}

/* 快速统计 */
.quick-stats {
  display: flex;
  gap: 0.75rem;
}

.quick-stats.compact {
  gap: 0.5rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  min-width: 50px;
  transition: all 0.3s ease;
  position: relative;
  /* 颜色现在通过动态样式设置 */
}

.stat-item.clickable {
  cursor: pointer;
}

.stat-item.clickable:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-item.active {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.8);
}

.stat-item.active::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid currentColor;
  border-radius: 10px;
  opacity: 0.3;
}

.stat-number {
  font-size: 1.25rem;
  font-weight: 700;
  line-height: 1;
}

.stat-label {
  font-size: 0.65rem;
  font-weight: 500;
  margin-top: 0.2rem;
  opacity: 0.9;
}

/* 头部操作按钮 */
.header-actions {
  display: flex;
  gap: 0.75rem;
}

/* 自动刷新控制 */
.auto-refresh-control {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 1rem;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 8px;
  border: 1px solid rgba(226, 232, 240, 0.6);
}

.refresh-countdown {
  font-size: 0.8rem;
  color: var(--primary-color);
  font-weight: 600;
  font-family: 'Courier New', monospace;
  min-width: 30px;
  text-align: center;
}

/* 筛选工具栏 */
.room-filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.1rem 2rem;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  border-bottom: 1px solid rgba(var(--primary-color-rgb), 0.15);
  flex-shrink: 0; /* 防止被压缩 */
}

.filter-left {
  display: flex;
  gap: 0.6rem; /* 从1rem减少到0.6rem */
}

.filter-right {
  display: flex;
  align-items: center;
  gap: 0.8rem; /* 从1rem减少到0.8rem */
}

.view-controls {
  display: flex;
  align-items: center;
}

/* 优化视图控制按钮样式 - 让按钮更紧凑 */
.view-controls .n-button {
  padding: 0.25rem 0.5rem !important;
  font-size: 0.75rem !important;
  min-width: auto !important;
  height: 28px !important;
}

.view-controls .n-button i {
  font-size: 0.9rem !important;
  margin-right: 0.25rem !important;
}

/* 只在小屏幕下隐藏按钮文字，只显示图标 */
@media (max-width: 1200px) {
  .view-controls .n-button {
    padding: 0.25rem 0.4rem !important;
    min-width: 32px !important;
  }

  /* 隐藏按钮文字，只保留图标 */
  .view-controls .n-button .n-button__content > span:not(.n-button__icon) {
    display: none !important;
  }

  .view-controls .n-button i {
    margin-right: 0 !important;
  }
}

/* 酒店切换按钮 */
.hotel-switch-section {
  margin-right: 2rem;
}

.hotel-switch-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 500;
}

/* 状态图例 */
.status-legend {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0;
}

.status-legend.compact {
  padding: 0.25rem 0;
}

.legend-section {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.legend-title {
  font-size: 0.8rem;
  font-weight: 600;
  color: #374151;
  margin-right: 0.25rem;
}

.legend-items {
  display: flex;
  gap: 0.3rem; /* 从0.5rem减少到0.3rem */
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.25rem; /* 从0.375rem减少到0.25rem */
  padding: 0.15rem 0.4rem; /* 从0.2rem 0.5rem减少到0.15rem 0.4rem */
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
  font-size: 0.7rem; /* 从0.75rem减少到0.7rem */
}

.legend-item:hover {
  background: rgba(var(--primary-color-rgb), 0.12);
  border-color: rgba(var(--primary-color-rgb), 0.35);
}

.legend-item.active {
  background: rgba(var(--primary-color-rgb), 0.1);
  border-color: rgba(var(--primary-color-rgb), 0.3);
  font-weight: 600;
}

.legend-color {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: 1px solid rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.legend-text {
  font-size: 0.75rem;
}

.legend-count {
  font-size: 0.7rem;
  opacity: 0.8;
  font-weight: 500;
}

.legend-color.status-occupied {
  background: #dc2626; /* 保持红色警告 */
}

.legend-color.status-available {
  background: var(--primary-color); /* 使用主题色 */
}

.legend-color.status-checkout {
  background: #d97706; /* 保持橙色警告 */
}

.legend-color.status-dirty {
  background: #dc2626; /* 保持红色警告 */
}

.legend-color.status-cleaning {
  background: var(--primary-color); /* 使用主题色 */
}

.legend-color.status-inspecting {
  background: var(--primary-color-suppl); /* 使用主题色辅助色 */
}

.legend-color.status-maintenance {
  background: #d97706; /* 保持橙色警告 */
}

.legend-color.status-reserved {
  background: var(--primary-color-suppl); /* 使用主题色辅助色 */
}

.legend-text {
  font-size: 0.8rem;
  color: rgba(0,0,0,0.72);
  font-weight: 500;
}

.legend-count {
  font-size: 0.7rem;
  color: #6b7280;
  background: rgba(107, 114, 128, 0.1);
  padding: 0.1rem 0.4rem;
  border-radius: 4px;
  min-width: 20px;
  text-align: center;
}

.legend-icon {
  font-size: 0.75rem;
  margin-right: 0.125rem;
}

/* 状态标识说明（紧凑版） */
.status-indicators-guide.compact {
  margin-top: 0.25rem;
  padding: 0.375rem 0.5rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
}

.guide-items-inline {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

.guide-item-mini {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.625rem;
  color: #6b7280;
  white-space: nowrap;
}

.guide-icon-mini {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 0.875rem;
  height: 0.875rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  font-size: 0.5rem;
  font-weight: bold;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.guide-icon-mini.dirty {
  color: #ef4444;
  animation: pulse-dirty 2s infinite;
}

.guide-icon-mini.maintenance {
  color: #f59e0b;
  animation: pulse-maintenance 2s infinite;
}

.guide-icon-mini.cleaning {
  color: var(--primary-color);
  animation: pulse-cleaning 2s infinite;
}

.guide-icon-mini.inspecting {
  color: #8b5cf6;
}

.guide-icon-mini.blocked {
  color: #6b7280;
}

.guide-icon-mini.clean {
  color: #10b981;
}

/* 楼栋楼层样式 */
.building-section {
  margin-bottom: 0.8rem;
}

.building-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 8px 8px 0 0;
  border-bottom: 2px solid var(--primary-color);
  margin-bottom: 0.5rem;
}

.building-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.building-stats {
  font-size: 0.75rem;
  color: #6b7280;
  background: rgba(107, 114, 128, 0.1);
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
}

.floor-section {
  margin-bottom: 1rem;
}

.floor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.4rem 0.8rem;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 6px;
  margin-bottom: 0.6rem;
  border-left: 3px solid var(--primary-color);
}

.floor-title {
  font-size: 0.95rem;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.floor-stats {
  display: flex;
  align-items: center;
  gap: 0.6rem;
}

.floor-room-count {
  font-size: 0.7rem;
  color: #6b7280;
}

.floor-status-summary {
  display: flex;
  gap: 0.3rem;
}

.status-dot {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 600;
  color: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.status-dot.status-occupied {
  background: #dc2626;
}

.status-dot.status-available {
  background: #059669;
}

.status-dot.status-checkout {
  background: #d97706;
}

.status-dot.status-dirty {
  background: #dc2626;
}

.status-dot.status-cleaning {
  background: #7c3aed;
}

.status-dot.status-inspecting {
  background: #0e7490;
}

.status-dot.status-maintenance {
  background: #d97706;
}

.status-dot.status-reserved {
  background: #059669;
}

/* 酒店切换弹窗 */
.hotel-switcher {
  padding: 1rem 0;
}

.hotel-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.hotel-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.hotel-item:hover {
  border-color: var(--primary-color);
  background: rgba(var(--primary-color-rgb), 0.12);
}

.hotel-item.active {
  border-color: var(--primary-color)  ;
  background: rgba(var(--primary-color-rgb), 0.1);
}

.hotel-info {
  flex: 1;
}

.hotel-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.hotel-type {
  font-size: 0.8rem;
  color: #6b7280;
  background: rgba(107, 114, 128, 0.1);
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  display: inline-block;
  margin-bottom: 0.5rem;
}

.hotel-address {
  font-size: 0.9rem;
  color: #9ca3af;
}

.hotel-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-dot.online {
  background: #10b981;
}

.status-dot.offline {
  background: #ef4444;
}

.status-text {
  font-size: 0.8rem;
  font-weight: 500;
}

/* 入住办理弹窗 */
.checkin-modal {
  max-height: 96vh;
  overflow-y: auto;
}

.checkin-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-section {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
}

.form-section h4 {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0.5rem;
}

.member-selection {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.guest-info-display {
  background: #f9fafb;
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.info-item {
  display: flex;
  margin-bottom: 0.5rem;
}

.info-item label {
  font-weight: 600;
  color: #374151;
  min-width: 80px;
}

.room-selection {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.selected-room {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.room-info {
  flex: 1;
  display: flex;
  gap: 0.5rem;
}

.room-number {
  font-weight: 600;
  color: #1f2937;
}

.room-type {
  color: #6b7280;
}

.room-rate {
  width: 120px;
}

.package-selection {
  margin-top: 1rem;
}

.package-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.package-item {
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f9fafb;
}

.package-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.package-name {
  font-weight: 500;
  color: #374151;
}

.package-price {
  font-weight: 600;
  color: var(--primary-color);
}

.checkin-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
  margin-top: 1rem;
}

.total-amount {
  font-size: 1.2rem;
  font-weight: 700;
  color: #1f2937;
}

/* 房态网格容器 */
.room-grid-container {
  flex: 1;
  overflow-y: auto;
  padding: 0.8rem 1.5rem 0.5rem 1.5rem;
  min-height: 0; /* 允许flex子项收缩 */
  max-height: 100%; /* 防止超出容器 */
  box-sizing: border-box;
  /* 减少内边距，增加房态图显示空间 */
}

/* 房态网格 - 支持不同大小 */
.room-grid {
  display: grid;
  gap: 0.8rem;
  transition: all 0.3s ease;
}

/* 大卡片网格 */
.room-grid.grid-large {
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 0.8rem;
}

/* 中卡片网格 */
.room-grid.grid-medium {
  grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));
  gap: 0.5rem;
}

/* 小方块网格 */
.room-grid.grid-small {
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 0.5rem;
}

/* 房间卡片基础样式 */
.room-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid;
  position: relative;
  display: flex;
  flex-direction: column;
}

.room-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

/* 大卡片样式 */
.room-card.card-large {
  padding: 0.6rem;
  min-height: 120px;
  max-height: 135px;
  gap: 0.3rem;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

/* 大卡片顶部信息行 */
.room-header-large {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.1rem;
}

.room-basic-info {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  flex: 1;
  min-width: 0;
}

.room-number-large {
  font-size: 1rem;
  font-weight: 800;
  color: #1f2937;
  line-height: 1;
}

.room-type-large {
  font-size: 0.7rem;
  color: #6b7280;
  line-height: 1;
}

.connect-room-badge-large {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  border-radius: 50%;
  font-size: 0.7rem;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.connect-room-badge-large:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 10px rgba(139, 92, 246, 0.5);
}

/* 大卡片主要信息 */
.guest-main-info,
.reservation-main-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
  margin: 0.15rem 0 0.2rem 0;
  min-height: 0;
  overflow: visible;
}

.guest-primary,
.reservation-primary {
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
  font-size: 0.8rem;
  line-height: 1.3;
}

.guest-name-large {
  font-weight: 600;
  color: #374151;
  line-height: 1.4;
  margin-bottom: 0.1rem;
}

/* 大卡片标签组 */
.guest-badges-large {
  display: flex;
  flex-wrap: wrap;
  gap: 0.2rem;
  margin-top: 0.1rem;
}

.vip-badge-large {
  font-size: 0.6rem;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  padding: 0.15rem 0.3rem;
  border-radius: 3px;
  font-weight: 500;
  line-height: 1;
}

.member-badge-large {
  font-size: 0.6rem;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  padding: 0.15rem 0.3rem;
  border-radius: 3px;
  font-weight: 500;
  line-height: 1;
}

.channel-badge-large {
  font-size: 0.6rem;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  padding: 0.15rem 0.3rem;
  border-radius: 3px;
  font-weight: 500;
  line-height: 1;
}

/* 大卡片内联标签 */
.guest-name-with-badges {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  flex-wrap: wrap;
}

.guest-badges-inline {
  display: flex;
  gap: 0.2rem;
}

.member-badge-inline {
  font-size: 0.55rem;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  padding: 0.1rem 0.25rem;
  border-radius: 2px;
  font-weight: 500;
  line-height: 1;
}

.channel-badge-inline {
  font-size: 0.55rem;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  padding: 0.1rem 0.25rem;
  border-radius: 2px;
  font-weight: 500;
  line-height: 1;
}

/* 中卡片标签组 */
.guest-badges-medium {
  display: flex;
  flex-wrap: wrap;
  gap: 0.15rem;
  margin-top: 0.1rem;
}

.vip-badge-medium {
  font-size: 0.55rem;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  padding: 0.1rem 0.25rem;
  border-radius: 2px;
  font-weight: 500;
  line-height: 1;
}

.member-badge-medium {
  font-size: 0.55rem;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  padding: 0.1rem 0.25rem;
  border-radius: 2px;
  font-weight: 500;
  line-height: 1;
}

.channel-badge-medium {
  font-size: 0.55rem;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  padding: 0.1rem 0.25rem;
  border-radius: 2px;
  font-weight: 500;
  line-height: 1;
}

/* 中卡片简化标签 */
.guest-name-with-badges-medium {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.2rem;
}

.guest-badges-medium-simple {
  display: flex;
  gap: 0.1rem;
  flex-shrink: 0;
}

.member-badge-simple {
  font-size: 0.6rem;
  background: #059669;
  color: white;
  padding: 0;
  border-radius: 50%;
  font-weight: 700;
  line-height: 1;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.channel-badge-simple {
  font-size: 0.6rem;
  background: #2563eb;
  color: white;
  padding: 0;
  border-radius: 50%;
  font-weight: 700;
  line-height: 1;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.time-info-large,
.arrival-info-large {
  font-size: 0.65rem;
  color: #6b7280;
  display: flex;
  flex-direction: column;
  gap: 0.05rem;
  margin-top: 0.1rem;
}

.checkin-time-large,
.checkout-time-large {
  white-space: nowrap;
  font-size: 0.65rem;
  line-height: 1.2;
  padding: 0.1rem 0;
}

.guest-secondary {
  font-size: 0.7rem;
  color: #6b7280;
  line-height: 1.3;
  margin-top: 0.1rem;
  padding: 0.05rem 0;
}

/* 大卡片底部信息 */
.room-bottom-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 0.25rem;
  border-top: 1px solid rgba(229, 231, 235, 0.5);
  min-height: 24px;
}

.orders-summary {
  display: flex;
  gap: 0.4rem;
  font-size: 0.65rem;
  color: #6b7280;
}

.today-orders-summary,
.future-orders-summary {
  display: flex;
  align-items: center;
  gap: 0.15rem;
}

.today-orders-summary i,
.future-orders-summary i {
  font-size: 0.75rem;
}

.status-badges {
  display: flex;
  gap: 0.25rem;
}

.room-status-badge-large,
.room-clean-badge-large {
  display: flex;
  align-items: center;
  gap: 0.2rem;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-size: 0.65rem;
  font-weight: 500;
  line-height: 1.2;
  white-space: nowrap;
  flex-shrink: 0;
}

.checkout-alert-large {
  position: absolute;
  top: 0.2rem;
  right: 0.2rem;
  display: flex;
  align-items: center;
  gap: 0.1rem;
  padding: 0.1rem 0.2rem;
  border-radius: 3px;
  font-size: 0.6rem;
  font-weight: 500;
  z-index: 10;
}

.checkout-alert-large.overdue {
  background: rgba(239, 68, 68, 0.9);
  color: white;
}

.checkout-alert-large.soon {
  background: rgba(245, 158, 11, 0.9);
  color: white;
}

/* 离店提醒标签 */
.checkout-reminder-large {
  display: flex;
  align-items: center;
  gap: 0.2rem;
  padding: 0.15rem 0.4rem;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  border-radius: 4px;
  font-size: 0.65rem;
  font-weight: 500;
  margin-top: 0.2rem;
  box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
  animation: checkoutPulse 2s ease-in-out infinite;
}

.checkout-reminder-medium {
  display: flex;
  align-items: center;
  gap: 0.15rem;
  padding: 0.1rem 0.3rem;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  border-radius: 3px;
  font-size: 0.6rem;
  font-weight: 500;
  margin-top: 0.15rem;
  box-shadow: 0 1px 3px rgba(245, 158, 11, 0.3);
  animation: checkoutPulse 2s ease-in-out infinite;
}

.checkout-reminder-mini {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 14px;
  height: 14px;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  border-radius: 50%;
  font-size: 0.55rem;
  font-weight: 700;
  box-shadow: 0 1px 2px rgba(245, 158, 11, 0.3);
  animation: checkoutPulse 2s ease-in-out infinite;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
}

/* 大卡片底部状态区域 */
.bottom-status-area {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-top: 0.3rem;
  gap: 0.5rem;
}

.status-badges-left {
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
}

.status-badges-right {
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
  align-items: flex-end;
}

/* 欠费提醒标签 */
.debt-reminder-large {
  display: flex;
  align-items: center;
  gap: 0.2rem;
  padding: 0.15rem 0.4rem;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border-radius: 4px;
  font-size: 0.65rem;
  font-weight: 500;
  margin-top: 0.2rem;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
  animation: debtPulse 2s ease-in-out infinite;
}

.debt-reminder-mini {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 14px;
  height: 14px;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border-radius: 50%;
  font-size: 0.55rem;
  font-weight: 700;
  box-shadow: 0 1px 2px rgba(239, 68, 68, 0.3);
  animation: debtPulse 2s ease-in-out infinite;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
}

/* 欠费提醒脉冲动画 */
@keyframes debtPulse {
  0% {
    box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
  }
  50% {
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.6), 0 0 0 3px rgba(239, 68, 68, 0.2);
  }
  100% {
    box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
  }
}

/* 离店提醒脉冲动画 */
@keyframes checkoutPulse {
  0% {
    box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
  }
  50% {
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.6), 0 0 0 3px rgba(245, 158, 11, 0.2);
  }
  100% {
    box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
  }
}

/* 中卡片样式 */
.room-card.card-medium {
  padding: 0.4rem;
  min-height: 55px;
  gap: 0.15rem;
}

/* 中卡片无订单模式 - 增加高度 */
.room-card.card-medium:not(.has-orders) {
  min-height: 65px;
}

/* 中卡片有订单模式 - 保持紧凑 */
.room-card.card-medium.has-orders {
  min-height: 45px;
  padding: 0.3rem;
}

/* 小方块样式 */
.room-card.card-small {
  padding: 0.4rem;
  min-height: 80px;
  max-height: 80px;
  border-radius: 8px;
  gap: 0.2rem;
  position: relative; /* 为联房标识提供定位基准 */
}

/* 房间状态样式 - 跟随主题色 */
.room-card.status-occupied {
  border-color: rgba(239, 68, 68, 0.3);
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.room-card.status-available {
  border-color: rgba(var(--primary-color-rgb), 0.5);
  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.15) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.room-card.status-cleaning {
  border-color: rgba(var(--primary-color-rgb), 0.5);
  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.15) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.room-card.status-checkout {
  border-color: rgba(245, 158, 11, 0.3);
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.room-card.status-dirty {
  border-color: rgba(239, 68, 68, 0.3);
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.room-card.status-cleaning {
  border-color: rgba(139, 92, 246, 0.3);
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.room-card.status-inspecting {
  border-color: rgba(var(--primary-color-rgb), 0.3);
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.room-card.status-maintenance {
  border-color: rgba(245, 158, 11, 0.3);
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.room-card.status-blocked {
  border-color: rgba(107, 114, 128, 0.3);
  background: linear-gradient(135deg, rgba(107, 114, 128, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.room-card.status-reserved {
  border-color: rgba(16, 185, 129, 0.3);
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.room-card.status-noshow {
  border-color: rgba(156, 163, 175, 0.3);
  background: linear-gradient(135deg, rgba(156, 163, 175, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
}

/* 房间号 */
.room-number {
  font-size: 1.1rem;
  font-weight: 700;
  color: #1f2937;
  text-align: center;
  margin-bottom: 0.3rem;
}

/* 房型 */
.room-type {
  font-size: 0.7rem;
  color: #6b7280;
  text-align: center;
  background: rgba(107, 114, 128, 0.1);
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  align-self: center;
}

/* 客人信息 */
.guest-info {
  background: rgba(248, 250, 252, 0.8);
  padding: 0.3rem;
  border-radius: 4px;
  border-left: 3px solid var(--primary-color);
}

/* 紧凑客人信息布局 */
.guest-info-compact {
  background: rgba(248, 250, 252, 0.8);
  padding: 0.25rem;
  border-radius: 4px;
  border-left: 3px solid var(--primary-color);
  font-size: 0.75rem;
  line-height: 1.3;
}

.guest-main-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.1rem;
}

.guest-details-line {
  display: flex;
  gap: 0.5rem;
  font-size: 0.7rem;
  color: #6b7280;
}

.time-info-inline {
  font-size: 0.65rem;
  color: #6b7280;
  display: flex;
  gap: 0.3rem;
}

.checkin-time-inline,
.checkout-time-inline {
  white-space: nowrap;
}

.guest-phone-inline,
.guest-company-inline {
  white-space: nowrap;
}

/* 紧凑预订信息布局 */
.reservation-info-compact {
  background: rgba(254, 243, 199, 0.8);
  padding: 0.25rem;
  border-radius: 4px;
  border-left: 3px solid #f59e0b;
  font-size: 0.75rem;
  line-height: 1.3;
}

.reservation-main-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.arrival-info-inline {
  font-size: 0.65rem;
  color: #6b7280;
  display: flex;
  gap: 0.3rem;
}

.arrival-time-inline,
.expected-arrival-inline {
  white-space: nowrap;
}

/* 紧凑订单样式 */
.today-orders-compact,
.future-orders-compact {
  background: rgba(239, 246, 255, 0.8);
  padding: 0.2rem;
  border-radius: 4px;
  border-left: 3px solid var(--primary-color);
  margin-top: 0.25rem;
  font-size: 0.7rem;
}

.order-header-compact {
  display: flex;
  align-items: center;
  gap: 0.2rem;
  font-weight: 600;
  color: #3b82f6;
  margin-bottom: 0.15rem;
}

.order-header-compact i {
  font-size: 0.8rem;
}

.order-item-compact {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  font-size: 0.65rem;
  line-height: 1.2;
  margin-bottom: 0.1rem;
}

.order-guest-compact {
  font-weight: 500;
  color: #374151;
  min-width: 0;
  flex-shrink: 1;
}

.order-time-compact {
  color: #6b7280;
  white-space: nowrap;
  flex-shrink: 0;
}

.order-source-compact {
  color: #8b5cf6;
  font-size: 0.6rem;
  background: rgba(139, 92, 246, 0.1);
  padding: 0.1rem 0.2rem;
  border-radius: 2px;
  white-space: nowrap;
  flex-shrink: 0;
}

.order-more-compact {
  font-size: 0.6rem;
  color: #6b7280;
  text-align: center;
  margin-top: 0.1rem;
}

.guest-name {
  font-size: 0.8rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.15rem;
}

.guest-phone {
  font-size: 0.65rem;
  color: #6b7280;
}

.guest-company {
  font-size: 0.55rem;
  color: #9ca3af;
  font-style: italic;
}

.vip-badge {
  font-size: 0.6rem;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  padding: 0.1rem 0.3rem;
  border-radius: 4px;
  margin-left: 0.5rem;
  font-weight: 600;
}

/* 预订信息样式 */
.reservation-info {
  background: rgba(16, 185, 129, 0.05);
  padding: 0.5rem;
  border-radius: 6px;
  border-left: 3px solid #10b981;
}

.arrival-time,
.expected-arrival {
  font-size: 0.7rem;
  color: #059669;
  margin-top: 0.25rem;
}

/* 维修信息样式 */
.maintenance-info {
  background: rgba(245, 158, 11, 0.05);
  padding: 0.5rem;
  border-radius: 6px;
  border-left: 3px solid #f59e0b;
}

.maintenance-issue {
  font-size: 0.8rem;
  font-weight: 600;
  color: #d97706;
  margin-bottom: 0.25rem;
}

.maintenance-assignee {
  font-size: 0.7rem;
  color: #92400e;
}

/* 清洁信息样式 */
.housekeeping-info {
  background: rgba(139, 92, 246, 0.05);
  padding: 0.5rem;
  border-radius: 6px;
  border-left: 3px solid #8b5cf6;
}

.hk-assignee,
.hk-inspector,
.hk-eta {
  font-size: 0.7rem;
  color: #7c3aed;
  margin-bottom: 0.2rem;
}

.hk-eta {
  font-weight: 600;
}

/* 时间信息 */
.time-info {
  font-size: 0.7rem;
  color: #6b7280;
  line-height: 1.4;
}

.checkin-time {
  color: #059669;
}

.checkout-time {
  color: #dc2626;
}

/* 房间状态标识 */
.room-status-badge {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.7rem;
  font-weight: 600;
  margin-top: auto;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  align-self: flex-start;
}

/* 清洁状态标识 */
.room-clean-badge {
  display: inline-flex;
  align-items: center;
  font-size: 0.625rem;
  font-weight: 500;
  padding: 0.125rem 0.25rem;
  border-radius: 3px;
  margin-top: 0.125rem;
  align-self: flex-start;
  gap: 0.1875rem;
  line-height: 1;
}

.room-clean-badge i {
  font-size: 0.625rem;
}

/* 中卡片右上角状态标识 */
.room-clean-badge-corner {
  margin-top: 0;
  margin-left: auto;
  flex-shrink: 0;
  max-width: 60px; /* 限制最大宽度 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.room-clean-badge-corner span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 中卡片清洁状态文字（仅首字，不显示图标） */
.room-clean-text-corner {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  margin-left: auto;
  flex-shrink: 0;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.6rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
  /* 背景颜色通过内联样式设置，与清洁状态颜色保持一致 */
}

.room-clean-text-corner:hover {
  transform: scale(1.1);
}

/* 清洁状态指示器样式 */
.room-status-indicators {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.clean-status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 0.875rem;
  height: 0.875rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  font-size: 0.625rem;
  font-weight: bold;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.clean-status-text-medium {
  font-size: 0.625rem;
  font-weight: 500;
  margin-top: 0.125rem;
  padding: 0.125rem 0.25rem;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.8);
  text-align: center;
  line-height: 1;
}

.room-indicators-mini {
  display: flex;
  flex-wrap: wrap;
  gap: 0.125rem;
  margin-top: 0.125rem;
}

.clean-status-text-mini {
  font-size: 0.5rem;
  font-weight: 500;
  margin-top: 0.25rem;
  padding: 0.125rem 0.25rem;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.9);
  text-align: center;
  line-height: 1;
  display: inline-block;
  width: fit-content;
  align-self: center;
}

/* 订单信息样式 */
/* 大卡片订单信息 */
.order-info-large {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin-top: 0.25rem;
  font-size: 0.625rem;
}

.order-source,
.member-type,
.channel-name {
  display: inline-flex;
  align-items: center;
  gap: 0.125rem;
  padding: 0.125rem 0.25rem;
  border-radius: 3px;
  font-weight: 500;
  line-height: 1;
}

.order-source {
  background: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  border: 1px solid rgba(var(--primary-color-rgb), 0.2);
}

.member-type {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.channel-name {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

/* 中卡片订单信息 */
.order-info-medium {
  display: flex;
  align-items: center;
  gap: 0.1875rem;
  margin-top: 0.1875rem;
  font-size: 0.5rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.order-source-medium,
.member-type-medium {
  padding: 0.0625rem 0.1875rem;
  border-radius: 2px;
  font-weight: 500;
  line-height: 1;
}

.order-source-medium {
  background: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
}

.member-type-medium {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

/* 小卡片订单信息 */
.order-info-mini {
  display: flex;
  flex-wrap: wrap;
  gap: 0.125rem;
  margin-top: 0.125rem;
  font-size: 0.375rem;
}

.order-source-mini,
.member-type-mini {
  padding: 0.0625rem 0.125rem;
  border-radius: 2px;
  font-weight: 500;
  line-height: 1;
  background: rgba(255, 255, 255, 0.9);
  width: fit-content;
}

.order-source-mini {
  color: var(--primary-color);
}

.member-type-mini {
  color: #f59e0b;
}

.clean-status-mini {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 0.625rem;
  height: 0.625rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  font-size: 0.375rem;
  font-weight: bold;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 特殊状态样式 */
.status-dirty {
  animation: pulse-dirty 2s infinite;
}

.status-maintenance {
  animation: pulse-maintenance 2s infinite;
}

.status-cleaning {
  animation: pulse-cleaning 2s infinite;
}

/* 动画效果 */
@keyframes pulse-dirty {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 0 0.25rem rgba(239, 68, 68, 0);
  }
}

@keyframes pulse-maintenance {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 0 0.25rem rgba(245, 158, 11, 0);
  }
}

@keyframes pulse-cleaning {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 0 0.25rem rgba(59, 130, 246, 0);
  }
}

.status-occupied .room-status-badge {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
}

.status-available .room-status-badge {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
}

.status-cleaning .room-status-badge {
  background: rgba(139, 92, 246, 0.1);
  color: #7c3aed;
}

.status-checkout .room-status-badge {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
}

.status-dirty .room-status-badge {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
}

.status-cleaning .room-status-badge {
  background: rgba(139, 92, 246, 0.1);
  color: #7c3aed;
}

.status-inspecting .room-status-badge {
  background: rgba(14, 116, 144, 0.1);
  color: #0e7490;
}

.status-maintenance .room-status-badge {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
}

.status-blocked .room-status-badge {
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
}

.status-reserved .room-status-badge {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
}

.status-noshow .room-status-badge {
  background: rgba(156, 163, 175, 0.1);
  color: #9ca3af;
}

/* 联房标识 */
.connect-room-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  border-radius: 50%;
  font-size: 0.7rem;
  font-weight: 600;
  margin-top: 0.25rem;
  box-shadow: 0 2px 4px rgba(139, 92, 246, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.connect-room-badge:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.5);
}

.connect-room-badge i {
  font-size: 0.8rem;
}

/* 中卡片联房标识 */
.connect-room-badge-medium {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  border-radius: 50%;
  font-size: 0.6rem;
  font-weight: 600;
  margin-top: 0.2rem;
  box-shadow: 0 1px 3px rgba(139, 92, 246, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.connect-room-badge-medium:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 8px rgba(139, 92, 246, 0.5);
}

.connect-room-badge-medium i {
  font-size: 0.7rem;
}

/* 中卡片底部标签组 */
.bottom-right-badges-medium {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.3rem;
  min-height: 20px; /* 确保即使只有一个标签也有足够高度 */
}

/* 确保标签水平居中对齐 */
.bottom-right-badges-medium > div {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 中卡片欠费圆形标签 */
.debt-badge-medium {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border-radius: 50%;
  font-size: 0.7rem;
  font-weight: 700;
  line-height: 1;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
  box-shadow: 0 1px 3px rgba(239, 68, 68, 0.3);
  animation: debtPulse 2s ease-in-out infinite;
  cursor: pointer;
  transition: all 0.3s ease;
}

.debt-badge-medium:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 8px rgba(239, 68, 68, 0.5);
}

/* 小卡片联房标识 */
.connect-room-badge-mini {
  position: absolute;
  top: 4px;
  right: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  border-radius: 50%;
  font-size: 0.5rem;
  font-weight: 600;
  box-shadow: 0 1px 3px rgba(139, 92, 246, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
}

.connect-room-badge-mini:hover {
  transform: scale(1.2);
  box-shadow: 0 2px 6px rgba(139, 92, 246, 0.5);
}

.connect-room-badge-mini i {
  font-size: 0.6rem;
}

/* 房间卡片过渡动画 */
.room-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

/* 联房卡片基础样式增强 */
.room-card[data-connect-code] {
  cursor: pointer;
}

.room-card[data-connect-code]:not(.connect-room-hovered) {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 联房悬浮效果 */
.room-card.connect-room-hovered {
  transform: translateY(-3px) scale(1.03) !important;
  box-shadow: 0 12px 30px rgba(139, 92, 246, 0.5), 0 0 0 3px rgba(139, 92, 246, 0.3) !important;
  border: 2px solid #8b5cf6 !important;
  z-index: 30 !important;
}

/* 联房脉冲动画 */
@keyframes connectRoomPulse {
  0% {
    box-shadow: 0 12px 30px rgba(139, 92, 246, 0.5), 0 0 0 3px rgba(139, 92, 246, 0.3);
  }
  50% {
    box-shadow: 0 15px 35px rgba(139, 92, 246, 0.6), 0 0 0 5px rgba(139, 92, 246, 0.4);
  }
  100% {
    box-shadow: 0 12px 30px rgba(139, 92, 246, 0.5), 0 0 0 3px rgba(139, 92, 246, 0.3);
  }
}

/* 联房标识悬浮增强效果 */
.connect-room-badge-large:hover,
.connect-room-badge-medium:hover,
.connect-room-badge-mini:hover {
  transform: scale(1.2) !important;
  box-shadow: 0 0 15px rgba(139, 92, 246, 0.8) !important;
  background: linear-gradient(135deg, #a855f7, #8b5cf6) !important;
}

/* 内联订单渠道信息样式 */
.order-source-inline,
.member-type-inline {
  font-size: 0.65rem;
  color: #6b7280;
  font-weight: 400;
  margin-left: 0.25rem;
}

.order-source-inline-medium {
  font-size: 0.6rem;
  color: #6b7280;
  font-weight: 400;
  margin-left: 0.2rem;
}

/* 房间标签 */
.room-tags {
  display: flex;
  gap: 0.25rem;
  flex-wrap: wrap;
  margin-top: 0.25rem;
}

.tag {
  font-size: 0.6rem;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-weight: 600;
  text-transform: uppercase;
}

.tag-vip {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

.tag-cleaning {
  background: rgba(var(--primary-color-rgb), 0.20);
  color: var(--primary-color);
}

.tag-maintenance {
  background: rgba(245, 158, 11, 0.2);
  color: #d97706;
}

/* 中卡片模式专用样式 */
.room-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.15rem;
}

.room-status-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-size: 0.9rem;
}

.card-medium .room-number {
  font-size: 0.9rem;
  font-weight: 700;
}

.card-medium .room-type {
  font-size: 0.6rem;
  padding: 0.1rem 0.25rem;
  margin-bottom: 0.2rem;
}

.guest-info-compact {
  background: rgba(248, 250, 252, 0.6);
  padding: 0.15rem 0.3rem;
  border-radius: 2px;
  border-left: 2px solid var(--primary-color);
  margin-bottom: 0.2rem;
}

/* 有订单模式的客人信息 - 更紧凑 */
.room-card.has-orders .guest-info-compact {
  padding: 0.1rem 0.25rem;
  margin-bottom: 0.1rem;
}

.guest-info-compact .guest-name {
  font-size: 0.65rem;
  font-weight: 600;
  color: #374151;
}

.room-tags-compact {
  display: flex;
  gap: 0.1rem;
  margin-top: auto;
}

.tag-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.tag-dot.tag-vip {
  background: #f59e0b;
}

.tag-dot.tag-cleaning {
  background: #7c3aed;
}

.tag-dot.tag-maintenance {
  background: #d97706;
}

/* 小方块模式专用样式 */
.room-mini {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  gap: 0.15rem;
  text-align: center;
  padding: 0.1rem;
}

.room-number-mini {
  font-size: 0.85rem;
  font-weight: 800;
  color: #1f2937;
  line-height: 1;
  margin-bottom: 0.1rem;
}

.room-type-mini {
  font-size: 0.5rem;
  font-weight: 500;
  color: #6b7280;
  line-height: 1.1;
  text-align: center;
  margin-bottom: 0.1rem;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 小卡片客人姓名 */
.guest-name-mini {
  font-size: 0.65rem;
  font-weight: 600;
  color: #374151;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

/* 小卡片底部标签区域 */
.room-badges-mini {
  display: flex;
  gap: 0.15rem;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}

/* 小卡片会员标签 */
.member-badge-mini {
  font-size: 0.5rem;
  font-weight: 700;
  background: #059669;
  color: white;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
}

/* 小卡片渠道标签 */
.channel-badge-mini {
  font-size: 0.5rem;
  font-weight: 700;
  background: #2563eb;
  color: white;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
}

.room-status-mini {
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.guest-indicator {
  font-size: 0.8rem;
  color: var(--primary-color);
  line-height: 1;
  display: flex;
  align-items: center;
  gap: 1px;
}

.guest-text-mini {
  font-size: 6px;
  font-weight: 600;
  line-height: 1;
}

.future-count-mini {
  font-size: 6px;
  font-weight: 600;
  line-height: 1;
  color: #06B6D4;
}

.tag-text-mini {
  font-size: 6px;
  font-weight: 600;
  line-height: 1;
  color: #d97706;
}

.tags-indicator {
  display: flex;
  gap: 0.2rem;
  justify-content: center;
}

.tag-icon {
  font-size: 0.7rem;
  line-height: 1;
}

.tag-icon.i-material-symbols-star {
  color: var(--primary-color-suppl);
}

.tag-icon.i-material-symbols-cleaning-services {
  color: var(--primary-color);
}

.tag-icon.i-material-symbols-build {
  color: #d97706;
}

/* 小方块模式下的状态颜色 */
.card-small.status-occupied .room-status-mini {
  color: #dc2626;
}

.card-small.status-available .room-status-mini {
  color: var(--primary-color);
}

.card-small.status-checkout .room-status-mini {
  color: #d97706;
}

.card-small.status-dirty .room-status-mini {
  color: #dc2626;
}

.card-small.status-cleaning .room-status-mini {
  color: var(--primary-color-suppl);
}

.card-small.status-inspecting .room-status-mini {
  color: var(--primary-color-suppl);
}

.card-small.status-maintenance .room-status-mini {
  color: #d97706;
}

.card-small.status-blocked .room-status-mini {
  color: #6b7280;
}

.card-small.status-reserved .room-status-mini {
  color: #059669;
}

.card-small.status-noshow .room-status-mini {
  color: #9ca3af;
}

/* 小屏幕优化 - 针对1366x768等小尺寸屏幕 */
@media (max-width: 1440px) and (max-height: 900px) {

  .quick-stats {
    gap: 0.5rem !important;
  }

  .stat-item {
    padding: 0.4rem 0.8rem !important;
    min-width: 70px !important;
  }

  .stat-number {
    font-size: 1rem !important;
  }

  .stat-label {
    font-size: 0.7rem !important;
  }

  /* 压缩筛选工具栏 */
  .room-filter-bar {
    padding: 0.6rem 1.5rem !important;
    gap: 0.8rem !important;
  }

  .filter-left {
    gap: 0.6rem !important;
  }

  .filter-left .n-select {
    min-width: 100px !important;
    width: 100px !important;
  }

  /* 压缩状态图例 */
  .status-legend.compact {
    padding: 0.2rem 0 !important;
  }

  .legend-items {
    gap: 0.4rem !important;
  }

  .legend-item {
    padding: 0.2rem 0.4rem !important;
    font-size: 0.7rem !important;
  }

  .legend-title {
    font-size: 0.7rem !important;
  }

  /* 压缩视图控制按钮 */
  .view-controls .n-button {
    padding: 0.3rem 0.6rem !important;
    font-size: 0.7rem !important;
  }

  /* 增加房态网格容器的空间利用率 */
  .room-grid-container {
    padding: 0.8rem 1.5rem 1rem 1.5rem !important;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {

  .room-filter-bar {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .filter-left,
  .filter-right {
    flex-wrap: wrap;
    justify-content: center;
  }

  .view-controls {
    order: -1;
    margin-bottom: 0.5rem;
  }

  /* 移动端网格优化 */
  .room-grid.grid-large {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 1rem;
  }

  .room-grid.grid-medium {
    grid-template-columns: repeat(auto-fill, minmax(85px, 1fr));
    gap: 0.4rem;
  }

  .room-grid.grid-small {
    grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
    gap: 0.5rem;
  }

  .room-grid-container {
    padding: 1rem;
  }

  /* 移动端卡片优化 */
  .card-large {
    padding: 1rem;
    min-height: 140px;
  }

  .card-medium {
    padding: 0.3rem;
    min-height: 50px;
  }

  .card-small {
    padding: 0.4rem;
    min-height: 70px;
    max-height: 70px;
  }

  .room-number-mini {
    font-size: 0.9rem;
  }
}

/* 暗色主题 */
.dark .room-status-page {
  background: #000000;
}

/* 暗色通用：近纯黑风格覆盖 */
.dark .refresh-indicator {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.08);
}
.dark .loading-container {
  background: rgba(0, 0, 0, 0.6);
}
.dark .auto-refresh-control {
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.08);
}
.dark .room-filter-bar {
  background: rgba(0, 0, 0, 0.6);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.dark .status-indicators-guide.compact {
  background: #0a0a0a;
  border: 1px solid rgba(255, 255, 255, 0.08);
}
.dark .legend-count {
  background: rgba(255, 255, 255, 0.06);
  color: #d1d5db;
}
.dark .building-header {
  background: #0d0d0d;
}
.dark .building-stats {
  background: rgba(255, 255, 255, 0.06);
  color: #9ca3af;
}
.dark .floor-header {
  background: rgba(0, 0, 0, 0.5);
}
.dark .hotel-item.active {
  background: rgba(255, 255, 255, 0.06);
}
.dark .guest-info,
.dark .guest-info-compact {
  background: rgba(0, 0, 0, 0.45);
  border-left: 3px solid var(--primary-color);
}
.dark .reservation-info,
.dark .maintenance-info,
.dark .housekeeping-info {
  background: rgba(0, 0, 0, 0.45);
}
.dark .today-orders,
.dark .today-orders-compact,
.dark .future-orders,
.dark .future-orders-compact {
  background: rgba(0, 0, 0, 0.45);
}
.dark .dot-char {
  background: rgba(255, 255, 255, 0.12);
}
.dark .dot-count {
  background: rgba(255, 255, 255, 0.08);
  color: #e5e7eb;
}
.dark .room-type,
.dark .building-stats,
.dark .hotel-type {
  background: rgba(255, 255, 255, 0.06);
  color: #cbd5e1;
}
.dark .order-source,
.dark .order-source-medium {
  background: rgba(var(--primary-color-rgb), 0.2);
  border: 1px solid rgba(var(--primary-color-rgb), 0.4);
}
.dark .member-type,
.dark .channel-name {
  background: rgba(255, 255, 255, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.12);
  color: #e5e7eb;
}
.dark .bottom-toolbar {
  background: #000000;
  border-top: 1px solid rgba(255, 255, 255, 0.08);
}
.dark .left-sidebar {
  background: #0a0a0a;
  border-right: 1px solid rgba(255, 255, 255, 0.08);
}
.dark .layout-switch-btn {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.04) 0%, rgba(255, 255, 255, 0.06) 100%) !important;
  color: #e5e7eb !important;
  border: 1px solid rgba(255, 255, 255, 0.12) !important;
}

.dark .room-filter-bar {
  background: rgba(31, 41, 55, 0.95);
  border-color: rgba(75, 85, 99, 0.8);
}

.dark .room-card {
  background: rgba(31, 41, 55, 0.95);
  border-color: rgba(75, 85, 99, 0.6);
}

.dark .page-title,
.dark .current-date,
.dark .room-number {
  color: #f9fafb;
}

.dark .guest-info {
  background: rgba(55, 65, 81, 0.8);
}

/* 退房提醒样式 */
.checkout-alert {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
  margin-top: 8px;
  animation: pulse 2s infinite;
}

.checkout-alert.overdue {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.checkout-alert.soon {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.checkout-alert i {
  font-size: 0.875rem;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.dark .checkout-alert.overdue {
  background: rgba(239, 68, 68, 0.2);
  color: #fca5a5;
}

.dark .checkout-alert.soon {
  background: rgba(245, 158, 11, 0.2);
  color: #fcd34d;
}

/* 今日订单样式 */
.today-orders {
  margin-top: 6px;
  padding: 6px;
  background: rgba(16, 185, 129, 0.05);
  border-radius: 4px;
  border-left: 3px solid var(--primary-color);
}

/* 有订单模式的今日订单 - 更紧凑 */
.room-card.has-orders .today-orders {
  margin-top: 4px;
  padding: 4px;
}

.today-order-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 4px;
  font-size: 0.75rem;
  color: #10b981;
  font-weight: 600;
  margin-bottom: 6px;
}

.today-badge {
  padding: 2px 6px;
  background: #10b981;
  color: white;
  border-radius: 8px;
  font-size: 0.6rem;
  font-weight: 700;
  text-transform: uppercase;
}

.today-order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 4px 0;
  font-size: 0.75rem;
}

.today-guest {
  color: #374151;
  font-weight: 500;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.today-time {
  color: #10b981;
  font-size: 0.7rem;
  margin-left: 8px;
  font-weight: 600;
}

.today-more {
  font-size: 0.7rem;
  color: #10b981;
  text-align: center;
  margin-top: 4px;
  font-style: italic;
}

/* 未来订单样式 */
.future-orders {
  margin-top: 6px;
  padding: 6px;
  background: rgba(6, 182, 212, 0.05);
  border-radius: 4px;
  border-left: 3px solid var(--primary-color);
}

/* 有订单模式的未来订单 - 更紧凑 */
.room-card.has-orders .future-orders {
  margin-top: 4px;
  padding: 4px;
}

.future-order-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 4px;
  font-size: 0.75rem;
  color: #06B6D4;
  font-weight: 600;
  margin-bottom: 6px;
}

.future-badge {
  padding: 2px 6px;
  background: #06B6D4;
  color: white;
  border-radius: 8px;
  font-size: 0.6rem;
  font-weight: 700;
  text-transform: uppercase;
}

.future-order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 4px 0;
  font-size: 0.75rem;
}

.future-guest {
  color: #374151;
  font-weight: 500;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.future-time {
  color: #6b7280;
  font-size: 0.7rem;
  margin-left: 8px;
}

.future-more {
  font-size: 0.7rem;
  color: #6b7280;
  text-align: center;
  margin-top: 4px;
  font-style: italic;
}

/* 中卡片模式的今日订单 */
.today-orders-compact {
  margin-top: 4px;
}

/* 有订单模式的中卡片今日订单 - 更紧凑 */
.room-card.has-orders .today-orders-compact {
  margin-top: 2px;
}

.today-indicator {
  display: flex;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  align-items: center;
  gap: 4px;
  font-size: 0.7rem;
  color: #10b981;
  background: rgba(16, 185, 129, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
}

/* 中卡片模式的未来订单 */
.future-orders-compact {
  margin-top: 4px;
}

/* 有订单模式的中卡片未来订单 - 更紧凑 */
.room-card.has-orders .future-orders-compact {
  margin-top: 2px;
}

.future-indicator {
  display: flex;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  align-items: center;
  gap: 4px;
  font-size: 0.7rem;
  color: #06B6D4;
  background: rgba(6, 182, 212, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
}

/* 小卡片模式的未来订单指示器 */
.future-indicator-mini {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  background: #06B6D4;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 8px;
}

.future-indicator-mini i {
  font-size: 8px;
}

/* 极简状态图例 - 最小化高度 */
.status-mini-bar {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 0.2rem 0;
  font-size: 0.7rem;
  line-height: 1;
  height: 1.8rem; /* 固定高度，确保紧凑 */
}

.status-group {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.group-label {
  font-size: 0.65rem;
  color: #6b7280;
  font-weight: 500;
  white-space: nowrap;
}

.status-dot {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 1.4rem;
  border-radius: 0.7rem;
  font-size: 0.55rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
  cursor: pointer;
  transition: all 0.15s ease;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  gap: 0.05rem;
  line-height: 1;
}

.status-dot:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.status-dot.active {
  transform: scale(1.15);
  box-shadow: 0 0 0 2px rgba(6, 182, 212, 0.4);
  border-color: rgba(6, 182, 212, 0.6);
}

.status-char {
  font-size: 0.65rem;
  font-weight: 700;
  line-height: 1;
}

.status-count {
  font-size: 0.5rem;
  font-weight: 500;
  opacity: 0.9;
  line-height: 1;
}

.separator {
  color: #d1d5db;
  font-weight: 300;
  margin: 0 0.2rem;
}

/* 响应式 - 保持极简 */
@media (max-width: 768px) {
  .status-mini-bar {
    gap: 0.5rem;
    padding: 0.15rem 0;
    height: 1.6rem;
  }

  .group-label {
    font-size: 0.6rem;
  }

  .status-dot {
    width: 1.6rem;
    height: 1.2rem;
    border-radius: 0.6rem;
  }

  .status-char {
    font-size: 0.6rem;
  }

  .status-count {
    font-size: 0.45rem;
  }
}

/* ==================== 布局模式样式 ==================== */

/* 布局切换器 */
.layout-switch-btn {
  font-size: 12px !important;
  padding: 6px 10px !important;
  height: 32px !important;
  border: 1px solid var(--primary-color) !important;
  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.06) 0%, rgba(var(--primary-color-rgb), 0.12) 100%) !important;
  color: var(--primary-color) !important;
  transition: all 0.2s ease !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
  box-shadow: 0 1px 3px rgba(var(--primary-color-rgb), 0.2) !important;
}

.layout-switch-btn:hover {
  border-color: var(--primary-color) !important;
  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.12) 0%, rgba(var(--primary-color-rgb), 0.18) 100%) !important;
  color: var(--primary-color) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 6px rgba(var(--primary-color-rgb), 0.3) !important;
}

.layout-switch-btn:active {
  transform: translateY(0) !important;
  box-shadow: 0 1px 3px rgba(6, 182, 212, 0.2) !important;
}

.layout-switcher-top {
  margin-right: 12px;
}

.layout-switcher-bottom {
  flex-shrink: 0;
}

.bottom-view-controls {
  flex-shrink: 0;
}

/* 底部刷新控制 */
.bottom-refresh-control {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-shrink: 0;
}

.bottom-refresh-control .auto-refresh-control {
  padding: 0.4rem 0.8rem;
  background: rgba(248, 250, 252, 0.9);
  border-radius: 6px;
  border: 1px solid rgba(226, 232, 240, 0.8);
  gap: 0.5rem;
}

/* 底部状态图例 */
.bottom-legend {
  display: flex;
  gap: 6px;
  align-items: center;
  flex-shrink: 0;
}

.bottom-legend-group {
  display: flex;
  align-items: center;
  gap: 2px;
}

.bottom-legend-title {
  font-size: 9px;
  color: #6b7280;
  font-weight: 500;
  white-space: nowrap;
}

.bottom-legend-dots {
  display: flex;
  gap: 1px;
}

.bottom-legend-dot {
  width: 16px;
  height: 12px;
  border-radius: 2px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 1px solid transparent;
}

.bottom-legend-dot:hover {
  transform: scale(1.05);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.bottom-legend-dot.active {
  border-color: rgba(var(--primary-color-rgb), 0.9);
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.5);
}

.bottom-dot-char {
  font-size: 6px;
  font-weight: 700;
  color: white;
  line-height: 1;
}

.bottom-dot-count {
  font-size: 5px;
  color: white;
  opacity: 0.9;
  line-height: 1;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

/* 布局切换过渡动画 */
.room-status-page {
  transition: all 0.3s ease;
}

/* 内容容器 */
.content-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0; /* 允许flex子项收缩 */
  max-height: 100%; /* 防止超出父容器 */
}

.content-container.with-sidebar {
  flex-direction: row;
  height: 100%; /* 使用父容器的全部高度 */
  max-height: 100%; /* 防止超出父容器 */
}

/* 左侧布局 */
.room-status-page.layout-left {
  display: flex;
  flex-direction: column;
  height: 100%; /* 使用父容器的全部高度 */
  max-height: 100%; /* 防止超出父容器 */
  overflow: hidden;
}

.left-sidebar {
  width: 240px;
  background: #f8fafc;
  border-right: 1px solid rgba(var(--primary-color-rgb), 0.12);
  overflow-y: auto;
  flex-shrink: 0;
  transform: translateX(0);
  transition: transform 0.3s ease, opacity 0.3s ease;
  opacity: 1;
  height: 100%;
}

.sidebar-content {
  padding: 16px;
}

.sidebar-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(var(--primary-color-rgb), 0.12);
}

.sidebar-filters {
  margin-bottom: 24px;
}

.filter-group {
  margin-bottom: 12px;
}

.filter-group label {
  display: block;
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
  font-weight: 500;
}

.sidebar-stats {
  margin-top: 24px;
}

.sidebar-view-controls {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid rgba(var(--primary-color-rgb), 0.12);
}

.vertical-view-controls {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.view-control-btn {
  width: 100% !important;
  justify-content: flex-start !important;
  font-size: 12px !important;
  padding: 8px 12px !important;
}

.view-control-btn .n-button__icon {
  margin-right: 8px !important;
}

/* 左侧状态图例 */
.sidebar-legend {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid rgba(var(--primary-color-rgb), 0.12);
}

.legend-section {
  margin-bottom: 8px;
}

.legend-title {
  font-size: 10px;
  color: #6b7280;
  font-weight: 500;
  margin-right: 4px;
}

.legend-dots {
  display: flex;
  gap: 3px;
  flex-wrap: wrap;
  margin-top: 2px;
}

.legend-dot {
  width: 24px;
  height: 20px;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 1px solid transparent;
}

.legend-dot:hover {
  transform: scale(1.05);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.legend-dot.active {
  border-color: #ffffff;
  box-shadow: 0 0 0 2px rgba(6, 182, 212, 0.5);
}

.dot-char {
  font-size: 8px;
  font-weight: 700;
  color: white;
  line-height: 1;
}

.dot-count {
  font-size: 7px;
  color: white;
  opacity: 0.9;
  line-height: 1;
}

.grid-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6px;
}

.stat-item-grid {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 6px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  text-align: center;
}

.stat-item-grid:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-item-grid.active {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.2);
}

.stat-item-grid .stat-number {
  font-size: 16px;
  font-weight: 700;
  line-height: 1;
}

.stat-item-grid .stat-label {
  font-size: 10px;
  opacity: 0.8;
  margin-top: 2px;
  line-height: 1;
}

.main-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 允许flex子项收缩 */
}

.main-content.with-sidebar {
  margin-left: 0;
  flex: 1;
  overflow: hidden;
}

/* 左侧布局时的房间网格容器 */
.content-container.with-sidebar .room-grid-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  height: 100%;
}

/* 左侧布局时的主内容区域 */
.content-container.with-sidebar .main-content {
  flex: 1;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 底部布局 */
.room-status-page.layout-bottom {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 100%;
  position: relative; /* 为底部工具栏的绝对定位提供参考 */
}

.room-status-page.layout-bottom .main-content {
  flex: 1;
  overflow: hidden;
  min-height: 0; /* 允许flex子项收缩 */
  height: calc(100% - 80px); /* 增加底部工具栏预留空间 */
}

/* 底部布局时的房态图容器 */
.room-status-page.layout-bottom .room-grid-container {
  padding-bottom: 3rem; /* 添加底部内边距，防止房卡被工具栏遮挡 */
}

.bottom-toolbar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  border-top: 1px solid rgba(var(--primary-color-rgb), 0.12);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 100;
  padding: 4px 12px;
  transform: translateY(0);
  transition: transform 0.3s ease, opacity 0.3s ease;
  opacity: 1;
  flex-shrink: 0; /* 防止被压缩 */
}

.bottom-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  max-width: 1400px;
  margin: 0 auto;
}

.bottom-filters {
  display: flex;
  gap: 3px;
  align-items: center;
  flex-shrink: 0;
}

.bottom-stats-compact {
  display: flex;
  gap: 2px;
  align-items: center;
  flex-shrink: 0;
}

.stat-compact {
  display: flex;
  align-items: center;
  gap: 1px;
  padding: 2px 4px;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  min-width: 32px;
  justify-content: center;
}

.stat-compact:hover {
  transform: translateY(-1px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-compact.active {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.2);
}

.stat-num {
  font-size: 12px;
  font-weight: 700;
  line-height: 1;
}

.stat-text {
  font-size: 9px;
  opacity: 0.8;
  line-height: 1;
}

/* 顶部布局（默认） */
.room-status-page.layout-top .main-content {
  padding: 0;
  flex: 1;
  min-height: 0;
  max-height: 100%; /* 防止超出容器 */
  overflow: hidden; /* 防止内容溢出 */
}

/* 顶部布局时的房态图容器 */
.room-status-page.layout-top .room-grid-container {
  padding-bottom: 0.5rem; /* 减少底部内边距 */
}

/* 响应式适配 */
@media (max-width: 768px) {
  .left-sidebar {
    width: 200px;
  }

  .bottom-content {
    flex-wrap: wrap;
    gap: 2px;
    justify-content: center;
  }

  .bottom-filters {
    flex-wrap: wrap;
    justify-content: center;
    gap: 2px;
  }

  .bottom-stats-compact {
    flex-wrap: wrap;
    justify-content: center;
    gap: 1px;
  }

  .bottom-legend {
    flex-wrap: wrap;
    gap: 2px;
  }

  .bottom-legend-group {
    justify-content: center;
  }

  .room-status-page.layout-bottom .main-content {
    padding-bottom: 60px; /* 移动端底部工具栏调整 */
  }
}

/* 高饱和度主题的房态页面增强样式 */
:root[style*="--primary-color: #10B981"] .room-card.status-available,
:root[style*="--primary-color: #8B5CF6"] .room-card.status-available,
:root[style*="--primary-color: #EF4444"] .room-card.status-available,
:root[style*="--primary-color: #F59E0B"] .room-card.status-available {
  border-color: rgba(var(--primary-color-rgb), 0.8) !important;
  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.25) 0%, rgba(255, 255, 255, 0.95) 100%) !important;
  box-shadow: 0 2px 12px rgba(var(--primary-color-rgb), 0.4) !important;
}

:root[style*="--primary-color: #10B981"] .room-card.status-cleaning,
:root[style*="--primary-color: #8B5CF6"] .room-card.status-cleaning,
:root[style*="--primary-color: #EF4444"] .room-card.status-cleaning,
:root[style*="--primary-color: #F59E0B"] .room-card.status-cleaning {
  border-color: rgba(var(--primary-color-rgb), 0.8) !important;
  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.25) 0%, rgba(255, 255, 255, 0.95) 100%) !important;
  box-shadow: 0 2px 12px rgba(var(--primary-color-rgb), 0.4) !important;
}

:root[style*="--primary-color: #10B981"] .legend-item:hover,
:root[style*="--primary-color: #8B5CF6"] .legend-item:hover,
:root[style*="--primary-color: #EF4444"] .legend-item:hover,
:root[style*="--primary-color: #F59E0B"] .legend-item:hover {
  background: rgba(var(--primary-color-rgb), 0.2) !important;
  border-color: rgba(var(--primary-color-rgb), 0.6) !important;
}

:root[style*="--primary-color: #10B981"] .hotel-item:hover,
:root[style*="--primary-color: #8B5CF6"] .hotel-item:hover,
:root[style*="--primary-color: #EF4444"] .hotel-item:hover,
:root[style*="--primary-color: #F59E0B"] .hotel-item:hover {
  background: rgba(var(--primary-color-rgb), 0.2) !important;
}

:root[style*="--primary-color: #10B981"] .tag-cleaning,
:root[style*="--primary-color: #8B5CF6"] .tag-cleaning,
:root[style*="--primary-color: #EF4444"] .tag-cleaning,
:root[style*="--primary-color: #F59E0B"] .tag-cleaning {
  background: rgba(var(--primary-color-rgb), 0.3) !important;
  font-weight: 600 !important;
}

/* 高饱和度主题的筛选工具栏增强 */
:root[style*="--primary-color: #10B981"] .room-filter-bar,
:root[style*="--primary-color: #8B5CF6"] .room-filter-bar,
:root[style*="--primary-color: #EF4444"] .room-filter-bar,
:root[style*="--primary-color: #F59E0B"] .room-filter-bar {
  border-bottom: 1px solid rgba(var(--primary-color-rgb), 0.25) !important;
}

/* 高饱和度主题的侧栏增强 */
:root[style*="--primary-color: #10B981"] .left-sidebar,
:root[style*="--primary-color: #8B5CF6"] .left-sidebar,
:root[style*="--primary-color: #EF4444"] .left-sidebar,
:root[style*="--primary-color: #F59E0B"] .left-sidebar {
  border-right: 1px solid rgba(var(--primary-color-rgb), 0.2) !important;
}

:root[style*="--primary-color: #10B981"] .sidebar-title,
:root[style*="--primary-color: #8B5CF6"] .sidebar-title,
:root[style*="--primary-color: #EF4444"] .sidebar-title,
:root[style*="--primary-color: #F59E0B"] .sidebar-title {
  border-bottom: 1px solid rgba(var(--primary-color-rgb), 0.2) !important;
}

:root[style*="--primary-color: #10B981"] .sidebar-legend,
:root[style*="--primary-color: #8B5CF6"] .sidebar-legend,
:root[style*="--primary-color: #EF4444"] .sidebar-legend,
:root[style*="--primary-color: #F59E0B"] .sidebar-legend {
  border-top: 1px solid rgba(var(--primary-color-rgb), 0.2) !important;
}

:root[style*="--primary-color: #10B981"] .bottom-toolbar,
:root[style*="--primary-color: #8B5CF6"] .bottom-toolbar,
:root[style*="--primary-color: #EF4444"] .bottom-toolbar,
:root[style*="--primary-color: #F59E0B"] .bottom-toolbar {
  border-top: 1px solid rgba(var(--primary-color-rgb), 0.2) !important;
}

/* 暗夜黑主题的超深黑房态页面 */
:root[style*="--primary-color: #1F2937"] .room-card {
  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), var(--ultra-dark-light)) 0%, rgba(var(--primary-color-rgb), var(--ultra-dark-subtle)) 100%) !important;
  border: 1px solid rgba(var(--primary-color-rgb), var(--ultra-border-opacity)) !important;
  box-shadow:
    0 4px 16px rgba(var(--primary-color-rgb), 0.45),
    0 0 0 1px rgba(255, 255, 255, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.18) !important;
  color: #FFFFFF !important;
}

:root[style*="--primary-color: #1F2937"] .room-card:hover {
  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), var(--ultra-dark-medium)) 0%, rgba(var(--primary-color-rgb), var(--ultra-dark-light)) 100%) !important;
  border-color: rgba(var(--primary-color-rgb), 0.7) !important;
  box-shadow:
    0 8px 24px rgba(var(--primary-color-rgb), 0.55),
    0 0 0 1px rgba(255, 255, 255, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.22) !important;
  transform: translateY(-2px) !important;
}

:root[style*="--primary-color: #1F2937"] .room-card.status-available {
  border-color: rgba(var(--primary-color-rgb), 0.8) !important;
  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), var(--ultra-dark-medium)) 0%, rgba(var(--primary-color-rgb), var(--ultra-dark-light)) 100%) !important;
  box-shadow:
    0 8px 24px rgba(var(--primary-color-rgb), 0.55),
    0 0 0 1px rgba(255, 255, 255, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.22) !important;
}

:root[style*="--primary-color: #1F2937"] .room-card.status-cleaning {
  border-color: rgba(var(--primary-color-rgb), 0.8) !important;
  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), var(--ultra-dark-medium)) 0%, rgba(var(--primary-color-rgb), var(--ultra-dark-light)) 100%) !important;
  box-shadow:
    0 8px 24px rgba(var(--primary-color-rgb), 0.55),
    0 0 0 1px rgba(255, 255, 255, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.22) !important;
}

:root[style*="--primary-color: #1F2937"] .legend-item {
  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), var(--ultra-dark-subtle)) 0%, rgba(var(--primary-color-rgb), 0.12) 100%) !important;
  border: 1px solid rgba(var(--primary-color-rgb), var(--ultra-border-opacity)) !important;
  box-shadow:
    0 4px 12px rgba(var(--primary-color-rgb), 0.35),
    0 0 0 1px rgba(255, 255, 255, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.12) !important;
  color: #FFFFFF !important;
}

:root[style*="--primary-color: #1F2937"] .legend-item:hover {
  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), var(--ultra-dark-light)) 0%, rgba(var(--primary-color-rgb), var(--ultra-dark-subtle)) 100%) !important;
  border-color: rgba(var(--primary-color-rgb), 0.7) !important;
  box-shadow:
    0 8px 24px rgba(var(--primary-color-rgb), 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.18) !important;
  transform: translateY(-1px) !important;
}

:root[style*="--primary-color: #1F2937"] .hotel-item {
  background: rgba(var(--primary-color-rgb), 0.05) !important;
  border: 1px solid rgba(var(--primary-color-rgb), 0.12) !important;
  box-shadow: 0 1px 3px rgba(var(--primary-color-rgb), 0.1) !important;
}

:root[style*="--primary-color: #1F2937"] .hotel-item:hover {
  background: rgba(var(--primary-color-rgb), 0.08) !important;
  border-color: rgba(var(--primary-color-rgb), 0.2) !important;
  box-shadow: 0 2px 6px rgba(var(--primary-color-rgb), 0.2) !important;
}

:root[style*="--primary-color: #1F2937"] .tag-cleaning {
  background: rgba(var(--primary-color-rgb), 0.15) !important;
  border: 1px solid rgba(var(--primary-color-rgb), 0.25) !important;
  box-shadow: 0 1px 2px rgba(var(--primary-color-rgb), 0.2) !important;
}

:root[style*="--primary-color: #1F2937"] .room-filter-bar {
  background: rgba(var(--primary-color-rgb), 0.03) !important;
  border-bottom: 1px solid rgba(var(--primary-color-rgb), 0.12) !important;
  box-shadow: 0 1px 3px rgba(var(--primary-color-rgb), 0.1) !important;
}

:root[style*="--primary-color: #1F2937"] .left-sidebar {
  background: rgba(var(--primary-color-rgb), 0.02) !important;
  border-right: 1px solid rgba(var(--primary-color-rgb), 0.12) !important;
  box-shadow: 2px 0 6px rgba(var(--primary-color-rgb), 0.1) !important;
}

:root[style*="--primary-color: #1F2937"] .sidebar-title {
  background: rgba(var(--primary-color-rgb), 0.05) !important;
  border-bottom: 1px solid rgba(var(--primary-color-rgb), 0.15) !important;
}

:root[style*="--primary-color: #1F2937"] .sidebar-legend {
  border-top: 1px solid rgba(var(--primary-color-rgb), 0.12) !important;
}

:root[style*="--primary-color: #1F2937"] .bottom-toolbar {
  background: rgba(var(--primary-color-rgb), 0.03) !important;
  border-top: 1px solid rgba(var(--primary-color-rgb), 0.12) !important;
  box-shadow: 0 -1px 3px rgba(var(--primary-color-rgb), 0.1) !important;
}

:root[style*="--primary-color: #1F2937"] .stat-item-grid {
  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), var(--apple-dark-subtle)) 0%, rgba(var(--primary-color-rgb), 0.1) 100%) !important;
  border: 1px solid rgba(var(--primary-color-rgb), var(--apple-border-opacity)) !important;
  box-shadow:
    0 2px 6px rgba(var(--primary-color-rgb), 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.08) !important;
  color: #FFFFFF !important;
}

:root[style*="--primary-color: #1F2937"] .stat-item-grid:hover {
  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), var(--apple-dark-light)) 0%, rgba(var(--primary-color-rgb), var(--apple-dark-subtle)) 100%) !important;
  border-color: rgba(var(--primary-color-rgb), 0.5) !important;
  box-shadow:
    0 4px 12px rgba(var(--primary-color-rgb), 0.35),
    0 0 0 1px rgba(255, 255, 255, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.12) !important;
  transform: translateY(-1px) !important;
}

:root[style*="--primary-color: #1F2937"] .stat-item-grid.active {
  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), var(--apple-dark-medium)) 0%, rgba(var(--primary-color-rgb), var(--apple-dark-light)) 100%) !important;
  border-color: rgba(var(--primary-color-rgb), 0.6) !important;
  box-shadow:
    0 4px 16px rgba(var(--primary-color-rgb), 0.45),
    0 0 0 1px rgba(255, 255, 255, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.18),
    inset 0 0 20px rgba(var(--primary-color-rgb), 0.2) !important;
  transform: translateY(-2px) !important;
  color: #FFFFFF !important;
  font-weight: 600 !important;
}
</style>
