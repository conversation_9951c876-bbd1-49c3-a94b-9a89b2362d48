<template>
  <n-modal
    v-model:show="visible"
    :mask-closable="false"
    preset="card"
    title="办理预订"
    class="reservation-modal"
    style="width: 1200px; max-height: 90vh;"
    :segmented="true"
  >
    <template #header>
      <div class="modal-header">
        <div class="header-title">
          <i class="i-mdi:calendar-plus"></i>
          <span>办理预订</span>
        </div>
        <div class="header-info">
          <n-tag type="info" size="small">预订编号: {{ reservationNumber }}</n-tag>
        </div>
      </div>
    </template>

    <template #header-extra>
      <n-button quaternary circle @click="handleClose">
        <template #icon>
          <i class="i-mdi:close"></i>
        </template>
      </n-button>
    </template>

    <div class="reservation-form">
      <n-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        label-width="100px"
        require-mark-placement="right-hanging"
      >
        <!-- 预订信息 -->
        <div class="form-section">
          <div class="section-title">
            <i class="i-mdi:information-outline"></i>
            <span>预订信息</span>
          </div>

          <n-grid :cols="4" :x-gap="16" :y-gap="12">
            <n-grid-item>
              <n-form-item label="预订编号">
                <n-input v-model:value="reservationNumber" readonly />
              </n-form-item>
            </n-grid-item>

            <n-grid-item>
              <n-form-item label="预订时间">
                <n-date-picker
                  v-model:value="formData.reservation_date"
                  type="datetime"
                  placeholder="预订时间"
                  style="width: 100%"
                />
              </n-form-item>
            </n-grid-item>

            <n-grid-item>
              <n-form-item label="入住人数" path="guest_count">
                <n-input-number
                  v-model:value="formData.guest_count"
                  :min="1"
                  :max="10"
                  style="width: 100%"
                />
              </n-form-item>
            </n-grid-item>

            <n-grid-item>
              <n-form-item label="联系人" path="guest_name">
                <n-input
                  v-model:value="formData.guest_name"
                  placeholder="请输入联系人姓名"
                />
              </n-form-item>
            </n-grid-item>

            <n-grid-item>
              <n-form-item label="入住时间" path="checkin_date">
                <n-date-picker
                  v-model:value="formData.checkin_date"
                  type="datetime"
                  placeholder="请选择入住时间"
                  style="width: 100%"
                />
              </n-form-item>
            </n-grid-item>

            <n-grid-item>
              <n-form-item label="退房时间" path="checkout_date">
                <n-date-picker
                  v-model:value="formData.checkout_date"
                  type="datetime"
                  placeholder="请选择退房时间"
                  style="width: 100%"
                />
              </n-form-item>
            </n-grid-item>

            <n-grid-item>
              <n-form-item label="对接人">
                <n-input
                  v-model:value="formData.contact_person"
                  placeholder="请输入对接人"
                />
              </n-form-item>
            </n-grid-item>

            <n-grid-item>
              <n-form-item label="备注">
                <n-input
                  v-model:value="formData.remark"
                  placeholder="备注信息"
                />
              </n-form-item>
            </n-grid-item>
          </n-grid>
        </div>

        <!-- 房间信息表格 -->
        <div class="form-section">
          <div class="section-title">
            <i class="i-mdi:bed"></i>
            <span>房间信息</span>
            <n-button
              size="small"
              type="primary"
              @click="showRoomSelector = true"
              style="margin-left: auto;"
            >
              <template #icon>
                <i class="i-mdi:plus"></i>
              </template>
              添加房间
            </n-button>
          </div>

          <n-data-table
            :columns="roomColumns"
            :data="selectedRooms"
            :pagination="false"
            :bordered="true"
            size="small"
            class="room-table"
          />

          <div v-if="selectedRooms.length === 0" class="empty-rooms">
            <n-empty description="暂无选择房间" size="small">
              <template #extra>
                <n-button size="small" @click="showRoomSelector = true">
                  选择房间
                </n-button>
              </template>
            </n-empty>
          </div>
        </div>

        <!-- 客人信息 -->
        <div class="form-section">
          <div class="section-title">
            <i class="i-mdi:account"></i>
            <span>客人信息</span>
          </div>

          <n-grid :cols="4" :x-gap="16" :y-gap="12">
            <n-grid-item>
              <n-form-item label="联系电话" path="guest_phone">
                <n-input
                  v-model:value="formData.guest_phone"
                  placeholder="请输入联系电话"
                />
              </n-form-item>
            </n-grid-item>

            <n-grid-item>
              <n-form-item label="身份证号" path="guest_id_card">
                <n-input
                  v-model:value="formData.guest_id_card"
                  placeholder="请输入身份证号"
                />
              </n-form-item>
            </n-grid-item>

            <n-grid-item>
              <n-form-item label="会员卡号">
                <n-input
                  v-model:value="formData.member_card"
                  placeholder="请输入会员卡号（可选）"
                />
              </n-form-item>
            </n-grid-item>

            <n-grid-item>
              <n-form-item label="入住天数">
                <n-input-number
                  v-model:value="stayDays"
                  :min="1"
                  readonly
                  style="width: 100%"
                />
              </n-form-item>
            </n-grid-item>
          </n-grid>
        </div>

        <!-- 费用汇总 -->
        <div class="form-section">
          <div class="section-title">
            <i class="i-mdi:calculator"></i>
            <span>费用汇总</span>
          </div>

          <div class="cost-summary">
            <div class="summary-row">
              <span class="label">房费小计:</span>
              <span class="value">¥{{ totalRoomAmount.toFixed(2) }}</span>
            </div>
            <div class="summary-row">
              <span class="label">优惠金额:</span>
              <span class="value discount">-¥{{ formData.discount_amount.toFixed(2) }}</span>
            </div>
            <div class="summary-row total">
              <span class="label">应收金额:</span>
              <span class="value">¥{{ totalAmount.toFixed(2) }}</span>
            </div>
            <div class="summary-row">
              <span class="label">实收金额:</span>
              <n-input-number
                v-model:value="formData.paid_amount"
                :precision="2"
                :min="0"
                style="width: 150px"
              >
                <template #prefix>¥</template>
              </n-input-number>
            </div>
          </div>
        </div>
      </n-form>
    </div>

    <template #footer>
      <div class="modal-footer">
        <n-space>
          <n-button @click="handleClose">取消</n-button>
          <n-button type="primary" @click="handleSubmit" :loading="submitting">
            确认预订
          </n-button>
        </n-space>
      </div>
    </template>

    <!-- 房间选择器 -->
    <n-modal
      v-model:show="showRoomSelector"
      preset="card"
      title="选择房间"
      style="width: 800px"
    >
      <div class="room-selector">
        <n-data-table
          :columns="availableRoomColumns"
          :data="availableRooms"
          :pagination="false"
          :max-height="400"
          :row-props="getRowProps"
          size="small"
        />
      </div>

      <template #footer>
        <n-space justify="end">
          <n-button @click="showRoomSelector = false">取消</n-button>
          <n-button type="primary" @click="confirmRoomSelection">确认选择</n-button>
        </n-space>
      </template>
    </n-modal>
  </n-modal>
</template>

<script setup>
import { ref, computed, watch, onMounted, h } from 'vue'
import { useMessage } from 'naive-ui'
import { createReservation, getRoomTypeList } from '@/views/hotel/roomStatus/api'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  roomData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:show', 'success'])

const message = useMessage()
const formRef = ref()
const visible = ref(false)
const submitting = ref(false)
const showRoomSelector = ref(false)

// 生成预订编号
const generateReservationNumber = () => {
  const now = new Date()
  const year = now.getFullYear().toString().slice(-2)
  const month = (now.getMonth() + 1).toString().padStart(2, '0')
  const day = now.getDate().toString().padStart(2, '0')
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0')
  return `YD${year}${month}${day}${random}`
}

const reservationNumber = ref(generateReservationNumber())

// 表单数据
const formData = ref({
  reservation_number: reservationNumber.value,
  guest_name: '',
  guest_phone: '',
  guest_id_card: '',
  member_card: '',
  guest_count: 1,
  contact_person: '',
  reservation_date: Date.now(),
  checkin_date: Date.now(),
  checkout_date: Date.now() + 24 * 60 * 60 * 1000,
  discount_amount: 0,
  paid_amount: 0,
  remark: ''
})

// 选中的房间列表
const selectedRooms = ref([])
// 可用房间列表
const availableRooms = ref([])
// 临时选中的房间（用于房间选择器）
const tempSelectedRooms = ref([])

// 房间信息表格列定义
const roomColumns = [
  {
    title: '房号',
    key: 'room_number',
    width: 80,
    align: 'center'
  },
  {
    title: '价格类型',
    key: 'price_type',
    width: 100,
    render: (row) => row.price_type || '标准'
  },
  {
    title: '价格',
    key: 'price',
    width: 80,
    render: (row) => `¥${row.price}`
  },
  {
    title: '住客',
    key: 'guest_count',
    width: 60,
    render: (row) => `${row.guest_count || 1}人`
  },
  {
    title: '天数',
    key: 'days',
    width: 60,
    render: (row) => `${row.days || stayDays.value}天`
  },
  {
    title: '应收金额',
    key: 'should_amount',
    width: 100,
    render: (row) => `¥${((row.price || 0) * (row.days || stayDays.value)).toFixed(2)}`
  },
  {
    title: '实收金额',
    key: 'actual_amount',
    width: 100,
    render: (row) => `¥${(row.actual_amount || 0).toFixed(2)}`
  },
  {
    title: '房态',
    key: 'status',
    width: 80,
    render: (row) => h('n-tag', {
      type: row.status === 'available' ? 'success' : 'warning',
      size: 'small'
    }, row.status_name || '可用')
  },
  {
    title: '操作',
    key: 'actions',
    width: 80,
    render: (row, index) => h('n-button', {
      size: 'small',
      type: 'error',
      onClick: () => removeRoom(index)
    }, '删除')
  }
]

// 可用房间表格列定义
const availableRoomColumns = [
  {
    title: '房号',
    key: 'room_number',
    width: 80
  },
  {
    title: '房型',
    key: 'room_type_name',
    width: 120
  },
  {
    title: '价格',
    key: 'price',
    width: 80,
    render: (row) => `¥${row.price}/晚`
  },
  {
    title: '楼层',
    key: 'floor',
    width: 60
  },
  {
    title: '状态',
    key: 'status',
    width: 80,
    render: (row) => h('n-tag', {
      type: 'success',
      size: 'small'
    }, '可用')
  }
]

// 表单验证规则
const rules = {
  guest_name: { required: true, message: '请输入联系人姓名', trigger: 'blur' },
  guest_phone: { required: true, message: '请输入联系电话', trigger: 'blur' },
  guest_id_card: { required: true, message: '请输入身份证号', trigger: 'blur' },
  guest_count: { required: true, message: '请输入入住人数', trigger: 'blur' },
  checkin_date: { required: true, message: '请选择入住时间', trigger: 'blur' },
  checkout_date: { required: true, message: '请选择退房时间', trigger: 'blur' }
}

// 计算属性
const stayDays = computed(() => {
  if (formData.value.checkin_date && formData.value.checkout_date) {
    const days = Math.ceil((formData.value.checkout_date - formData.value.checkin_date) / (24 * 60 * 60 * 1000))
    return days > 0 ? days : 1
  }
  return 1
})

const totalRoomAmount = computed(() => {
  return selectedRooms.value.reduce((total, room) => {
    return total + (room.price || 0) * (room.days || stayDays.value)
  }, 0)
})

const totalAmount = computed(() => {
  return totalRoomAmount.value - (formData.value.discount_amount || 0)
})

// 监听显示状态
watch(() => props.show, (newVal) => {
  visible.value = newVal
  if (newVal) {
    initForm()
  }
})

watch(visible, (newVal) => {
  emit('update:show', newVal)
})

// 初始化表单
function initForm() {
  // 重新生成预订编号
  reservationNumber.value = generateReservationNumber()
  formData.value.reservation_number = reservationNumber.value

  // 如果有传入房间数据，自动添加到选中房间列表
  if (props.roomData.room_number) {
    const roomInfo = {
      room_id: props.roomData.room_id,
      room_number: props.roomData.room_number,
      room_type_name: props.roomData.room_type_name,
      price: props.roomData.price || 0,
      price_type: '标准',
      guest_count: 1,
      days: stayDays.value,
      actual_amount: 0,
      status: 'available',
      status_name: '可用'
    }
    selectedRooms.value = [roomInfo]
  } else {
    selectedRooms.value = []
  }
}

// 房间选择器行属性
function getRowProps(row) {
  return {
    style: 'cursor: pointer;',
    onClick: () => {
      const index = tempSelectedRooms.value.findIndex(r => r.room_id === row.room_id)
      if (index > -1) {
        tempSelectedRooms.value.splice(index, 1)
      } else {
        tempSelectedRooms.value.push({
          ...row,
          price_type: '标准',
          guest_count: 1,
          days: stayDays.value,
          actual_amount: 0,
          status: 'available',
          status_name: '可用'
        })
      }
    }
  }
}

// 确认房间选择
function confirmRoomSelection() {
  selectedRooms.value = [...tempSelectedRooms.value]
  tempSelectedRooms.value = []
  showRoomSelector.value = false
}

// 移除房间
function removeRoom(index) {
  selectedRooms.value.splice(index, 1)
}

// 提交表单
async function handleSubmit() {
  try {
    // 验证是否选择了房间
    if (selectedRooms.value.length === 0) {
      message.warning('请至少选择一个房间')
      return
    }

    await formRef.value?.validate()
    submitting.value = true

    const params = {
      ...formData.value,
      rooms: selectedRooms.value,
      stay_days: stayDays.value,
      total_room_amount: totalRoomAmount.value,
      total_amount: totalAmount.value
    }

    await createReservation(params)
    message.success('预订成功')
    emit('success')
    handleClose()
  } catch (error) {
    message.error('预订失败：' + (error.message || '未知错误'))
  } finally {
    submitting.value = false
  }
}

// 关闭弹窗
function handleClose() {
  visible.value = false
  formRef.value?.restoreValidation()
}

</script>

<style scoped>
.reservation-modal {
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.header-title i {
  font-size: 20px;
  color: #2563eb;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.reservation-form {
  padding: 0 4px;
}

.form-section {
  margin-bottom: 24px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  font-weight: 600;
  color: #374151;
  font-size: 16px;
}

.section-title i {
  font-size: 18px;
  color: #2563eb;
  margin-right: 8px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
}

/* 房间表格样式 */
.room-table {
  margin-top: 12px;
}

.empty-rooms {
  padding: 40px 0;
  text-align: center;
}

/* 费用汇总样式 */
.cost-summary {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.summary-row:last-child {
  border-bottom: none;
}

.summary-row.total {
  font-weight: 600;
  font-size: 16px;
  color: #1f2937;
  border-top: 2px solid #e5e7eb;
  margin-top: 8px;
  padding-top: 12px;
}

.summary-row .label {
  color: #6b7280;
  font-size: 14px;
}

.summary-row .value {
  font-weight: 500;
  color: #1f2937;
}

.summary-row .value.discount {
  color: #dc2626;
}

/* 房间选择器样式 */
.room-selector {
  max-height: 400px;
  overflow-y: auto;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .reservation-modal {
    width: 95vw !important;
    max-width: none !important;
  }

  .form-section {
    padding: 16px;
  }

  .cost-summary {
    padding: 16px;
  }
}
</style>
