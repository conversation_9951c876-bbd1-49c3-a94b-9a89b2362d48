import { ref, computed, watch } from 'vue'

/**
 * 房间筛选逻辑组合函数
 * 负责筛选条件管理和房间数据过滤
 */
export function useRoomFilters(roomData, buildingList, floorList, roomTypeList, roomStatusConfig, cleanStatusConfig) {
  // 筛选条件
  const filters = ref({
    building: null,
    floor: null,
    roomType: null,
    status: null,
    cleanStatus: null,
    keyword: '',
    dateRange: null
  })

  // 选项数据
  const buildingOptions = computed(() => {
    // 确保 buildingList.value 是数组
    if (!Array.isArray(buildingList.value)) {
      return []
    }
    return buildingList.value.map(building => ({
      label: building.name || building.building_name,
      value: building.id || building.building_id
    }))
  })

  const floorOptions = computed(() => {
    // 确保 floorList.value 是数组
    if (!Array.isArray(floorList.value)) {
      return []
    }

    // 如果选择了楼栋，只显示该楼栋的楼层
    let availableFloors = floorList.value

    if (filters.value.building && Array.isArray(buildingList.value)) {
      const selectedBuilding = buildingList.value.find(b =>
        (b.id || b.building_id) === filters.value.building
      )
      if (selectedBuilding && Array.isArray(selectedBuilding.floors)) {
        availableFloors = selectedBuilding.floors
      }
    }

    // 确保 availableFloors 是数组
    if (!Array.isArray(availableFloors)) {
      return []
    }

    return availableFloors.map(floor => ({
      label: `${floor}楼`,
      value: floor
    }))
  })

  const roomTypeOptions = computed(() => {
    // 确保 roomTypeList.value 是数组
    if (!Array.isArray(roomTypeList.value)) {
      return []
    }
    return roomTypeList.value.map(type => ({
      label: type,
      value: type
    }))
  })

  const statusOptions = computed(() => {
    // 确保 roomStatusConfig.value 是数组
    if (!Array.isArray(roomStatusConfig.value)) {
      return []
    }
    return roomStatusConfig.value.map(status => ({
      label: status.name || status.status_name,
      value: status.sign || status.status_sign,
      color: status.color || status.status_color
    }))
  })

  const cleanStatusOptions = computed(() => {
    // 确保 cleanStatusConfig.value 是数组
    if (!Array.isArray(cleanStatusConfig.value)) {
      return []
    }
    return cleanStatusConfig.value.map(status => ({
      label: status.name || status.status_name,
      value: status.id || status.status_id,
      color: status.color || status.status_color
    }))
  })

  // 过滤后的房间数据
  const filteredRoomData = computed(() => {
    if (!roomData.value || !Array.isArray(roomData.value)) {
      return []
    }

    let filtered = [...roomData.value]
    console.log('开始筛选，原始数据数量:', filtered.length)
    console.log('当前筛选条件:', filters.value)

    // 楼栋筛选
    if (filters.value.building) {
      const beforeCount = filtered.length
      filtered = filtered.filter(room => {
        const roomBuildingId = room.building_id || room.buildingId
        return roomBuildingId === filters.value.building
      })
      console.log('楼栋筛选:', beforeCount, '->', filtered.length)
    }

    // 楼层筛选
    if (filters.value.floor) {
      const beforeCount = filtered.length
      filtered = filtered.filter(room => {
        const roomFloor = room.floor_number || room.floorNumber
        return roomFloor === filters.value.floor
      })
      console.log('楼层筛选:', beforeCount, '->', filtered.length)
    }

    // 房型筛选
    if (filters.value.roomType) {
      const beforeCount = filtered.length
      filtered = filtered.filter(room => {
        const roomType = room.room_type_name || room.roomType
        return roomType === filters.value.roomType
      })
      console.log('房型筛选:', beforeCount, '->', filtered.length)
    }

    // 房间状态筛选
    if (filters.value.status) {
      const beforeCount = filtered.length
      filtered = filtered.filter(room => {
        // 根据筛选值匹配不同的状态
        switch (filters.value.status) {
          case 'empty':
            // 空房：status为available
            return room.status === 'available'
          case 'stay':
            // 在住：status为occupied
            return room.status === 'occupied'
          case 'locked':
            // 锁房：room_status_sign为locked
            return room.room_status_sign === 'locked'
          case 'closed':
            // 关房：room_status_sign为closed
            return room.room_status_sign === 'closed'
          case 'book':
            // 预定：room_status_sign为book
            return room.room_status_sign === 'book'
          case 'checkout':
            // 申请退房：room_status_sign为checkout
            return room.room_status_sign === 'checkout'
          default:
            // 其他情况使用room_status_sign匹配
            return room.room_status_sign === filters.value.status
        }
      })
      console.log('房间状态筛选:', beforeCount, '->', filtered.length)
    }

    // 清洁状态筛选
    if (filters.value.cleanStatus) {
      filtered = filtered.filter(room => {
        return room.cleanStatus === filters.value.cleanStatus
      })
    }

    // 关键词搜索
    if (filters.value.keyword && filters.value.keyword.trim()) {
      const keyword = filters.value.keyword.trim().toLowerCase()
      filtered = filtered.filter(room => {
        // 搜索房间号
        const roomNumber = (room.room_number || room.roomNumber || '').toString().toLowerCase()
        if (roomNumber.includes(keyword)) return true

        // 搜索客人姓名
        const guestName = getGuestName(room).toLowerCase()
        if (guestName.includes(keyword)) return true

        // 搜索客人电话
        const guestPhone = getGuestPhone(room).toLowerCase()
        if (guestPhone.includes(keyword)) return true

        // 搜索房型
        const roomType = (room.room_type_name || room.roomType || '').toLowerCase()
        if (roomType.includes(keyword)) return true

        return false
      })
    }

    return filtered
  })

  // 获取客人姓名的辅助函数
  const getGuestName = (room) => {
    if (room.bill_info && room.bill_info.link_man) {
      return room.bill_info.link_man
    }
    if (room.guest && room.guest.name) {
      return room.guest.name
    }
    return room.guestName || room.guest_name || ''
  }

  // 获取客人电话的辅助函数
  const getGuestPhone = (room) => {
    if (room.bill_info && room.bill_info.link_phone) {
      return room.bill_info.link_phone
    }
    if (room.guest && room.guest.phone) {
      return room.guest.phone
    }
    return room.guestPhone || room.guest_phone || ''
  }

  // 筛选统计
  const filterStats = computed(() => {
    const total = roomData.value ? roomData.value.length : 0
    const filtered = filteredRoomData.value.length
    const hidden = total - filtered

    return {
      total,
      filtered,
      hidden,
      hasFilters: hasActiveFilters.value
    }
  })

  // 是否有激活的筛选条件
  const hasActiveFilters = computed(() => {
    return !!(
      filters.value.building ||
      filters.value.floor ||
      filters.value.roomType ||
      filters.value.status ||
      filters.value.cleanStatus ||
      (filters.value.keyword && filters.value.keyword.trim())
    )
  })

  // 更新筛选条件
  const updateFilter = (key, value) => {
    filters.value[key] = value

    // 联动逻辑：选择楼栋时清空楼层选择
    if (key === 'building') {
      filters.value.floor = null
    }
  }

  // 批量更新筛选条件
  const updateFilters = (newFilters) => {
    Object.assign(filters.value, newFilters)
  }

  // 清空所有筛选条件
  const clearFilters = () => {
    filters.value = {
      building: null,
      floor: null,
      roomType: null,
      status: null,
      cleanStatus: null,
      keyword: '',
      dateRange: null
    }
  }

  // 清空单个筛选条件
  const clearFilter = (key) => {
    if (key in filters.value) {
      if (typeof filters.value[key] === 'string') {
        filters.value[key] = ''
      } else {
        filters.value[key] = null
      }
    }
  }

  // 预设筛选条件
  const applyPresetFilter = (preset) => {
    switch (preset) {
      case 'occupied':
        updateFilter('status', 'stay')
        break
      case 'available':
        updateFilter('status', 'vacant')
        break
      case 'checkout':
        updateFilter('status', 'checkout')
        break
      case 'dirty':
        updateFilter('cleanStatus', 2) // 假设2是脏房状态ID
        break
      case 'cleaning':
        updateFilter('cleanStatus', 3) // 假设3是清洁中状态ID
        break
      case 'maintenance':
        updateFilter('cleanStatus', 4) // 假设4是维修状态ID
        break
      default:
        console.warn('未知的预设筛选条件:', preset)
    }
  }

  // 获取筛选条件的显示文本
  const getFilterDisplayText = () => {
    const texts = []

    if (filters.value.building) {
      const building = buildingList.value.find(b =>
        (b.id || b.building_id) === filters.value.building
      )
      if (building) {
        texts.push(`楼栋: ${building.name || building.building_name}`)
      }
    }

    if (filters.value.floor) {
      texts.push(`楼层: ${filters.value.floor}楼`)
    }

    if (filters.value.roomType) {
      texts.push(`房型: ${filters.value.roomType}`)
    }

    if (filters.value.status) {
      const status = roomStatusConfig.value.find(s =>
        (s.sign || s.status_sign) === filters.value.status
      )
      if (status) {
        texts.push(`状态: ${status.name || status.status_name}`)
      }
    }

    if (filters.value.cleanStatus) {
      const cleanStatus = cleanStatusConfig.value.find(s =>
        (s.id || s.status_id) === filters.value.cleanStatus
      )
      if (cleanStatus) {
        texts.push(`清洁: ${cleanStatus.name || cleanStatus.status_name}`)
      }
    }

    if (filters.value.keyword && filters.value.keyword.trim()) {
      texts.push(`关键词: ${filters.value.keyword.trim()}`)
    }

    return texts.join(', ')
  }

  // 监听楼栋变化，自动更新楼层选项
  watch(() => filters.value.building, (newBuilding) => {
    if (newBuilding && filters.value.floor) {
      // 检查当前选择的楼层是否在新楼栋中
      const selectedBuilding = buildingList.value.find(b =>
        (b.id || b.building_id) === newBuilding
      )
      if (selectedBuilding && selectedBuilding.floors) {
        if (!selectedBuilding.floors.includes(filters.value.floor)) {
          filters.value.floor = null
        }
      }
    }
  })

  return {
    // 状态
    filters,
    filteredRoomData,
    filterStats,
    hasActiveFilters,

    // 选项
    buildingOptions,
    floorOptions,
    roomTypeOptions,
    statusOptions,
    cleanStatusOptions,

    // 方法
    updateFilter,
    updateFilters,
    clearFilters,
    clearFilter,
    applyPresetFilter,
    getFilterDisplayText
  }
}
