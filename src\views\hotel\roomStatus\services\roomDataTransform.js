/**
 * 房间数据转换服务
 * 负责原始API数据到前端展示数据的转换
 */

// 房间状态映射配置
const ROOM_STATUS_MAP = {
  // 数字状态映射（基于实际数据：1=在住）
  0: 'available',    // 空闲
  1: 'occupied',     // 在住
  2: 'reserved',     // 已预订
  3: 'checkout',     // 待退房
  4: 'dirty',        // 脏房
  5: 'cleaning',     // 清洁中
  6: 'maintenance',  // 维修中
  7: 'blocked',      // 封锁
  8: 'inspecting',   // 查房中

  // 字符串状态映射
  'vacant': 'available',
  'occupied': 'occupied',
  'reserved': 'reserved',
  'dirty': 'dirty',
  'cleaning': 'cleaning',
  'maintenance': 'maintenance',
  'locked': 'blocked',
  'to_inspect': 'inspecting',
  'checkout': 'checkout',
  'stay': 'occupied',        // 在住
  'blocked': 'blocked',      // 封锁
  'inspecting': 'inspecting', // 查房中
  'noshow': 'noshow',        // 未到店

  // 数字字符串映射
  '0': 'available',
  '1': 'occupied',
  '2': 'reserved',
  '3': 'checkout',
  '4': 'dirty',
  '5': 'cleaning',
  '6': 'maintenance',
  '7': 'blocked',
  '8': 'inspecting'
}

// 状态文本映射常量
const STATUS_TEXT_MAP = {
  occupied: '已入住',      // OCC
  available: '可入住',     // VAC
  checkout: '待退房',      // CO
  dirty: '脏房',          // VD
  cleaning: '清洁中',      // Clean
  inspecting: '查房中',    // INS
  maintenance: '维修中',   // OOO
  blocked: '封锁',        // BLK
  reserved: '已预订',      // RES
  noshow: '未到店'        // NS
}

export const roomDataTransform = {
  /**
   * 映射API状态到本地状态
   * @param {string|number} apiStatus - API返回的状态
   * @returns {string} 本地状态
   */
  mapApiStatusToLocal(apiStatus) {
    return ROOM_STATUS_MAP[apiStatus] || 'available'
  },

  /**
   * 根据sign字段映射房间状态
   * @param {string} sign - 状态标识
   * @returns {string} 本地状态
   */
  getRealRoomStatus(room) {
    // 根据房间是否有客人来确定真实状态
    // 检查多个可能的客人信息来源
    let hasGuest = false
    let guestName = ''



    // 1. 从bill_info中检查（主要数据源）
    if (room.bill_info && (room.bill_info.link_man || room.bill_info.guest_name)) {
      guestName = room.bill_info.link_man || room.bill_info.guest_name
      hasGuest = true
    }

    // 2. 从guest对象中检查（备用）
    if (!hasGuest && room.guest && room.guest.name) {
      guestName = room.guest.name
      hasGuest = true
    }

    // 3. 直接从房间对象中检查（兼容性）
    if (!hasGuest && (room.guest_name || room.guestName)) {
      guestName = room.guest_name || room.guestName
      hasGuest = true
    }

    // 确保客人姓名不为空
    if (hasGuest && guestName && guestName.trim() !== '') {
      // 有客人，状态为在住
      return 'occupied'
    } else {
      // 没有客人，状态为空房
      return 'available'
    }
  },

  /**
   * 将房间状态ID映射为状态标识
   * @param {Object} room - 原始房间数据
   * @returns {string} 状态标识
   */
  mapRoomStatusToSign(room) {
    // 获取房间状态ID
    const statusId = room.room_status || room.status

    // 根据状态ID映射为状态标识
    const statusIdMap = {
      0: 'empty',       // 空闲
      1: 'stay',        // 在住
      2: 'book',        // 已预订
      3: 'to_check_out', // 待退房
      4: 'locked',      // 锁房
      5: 'closed',      // 关房
      6: 'maintenance', // 维修
      7: 'blocked',     // 封锁
      8: 'inspecting'   // 查房
    }

    return statusIdMap[statusId] || 'vacant'
  },

  mapRoomStatusBySign(sign) {
    const signMap = {
      'stay': 'occupied',        // 在住
      'vacant': 'available',     // 空闲
      'reserved': 'reserved',    // 已预订
      'checkout': 'checkout',    // 待退房
      'dirty': 'dirty',          // 脏房
      'cleaning': 'cleaning',    // 清洁中
      'maintenance': 'maintenance', // 维修中
      'blocked': 'blocked',      // 封锁
      'inspecting': 'inspecting', // 查房中
      'noshow': 'noshow',        // 未到店

      // 其他可能的sign值
      'available': 'available',
      'occupied': 'occupied',
      'ooo': 'maintenance',      // Out of Order
      'oos': 'blocked'           // Out of Service
    }

    // 如果传入的是数字，需要根据状态配置转换为对应的sign
    if (typeof sign === 'number') {
      // 从状态配置中查找对应的sign
      const statusConfig = this.roomStatusConfig || []
      const config = statusConfig.find(config => config.id === sign)
      if (config && config.sign) {
        sign = config.sign
      }
    }

    return signMap[sign] || 'available'
  },

  /**
   * 获取状态显示文本
   * @param {string} status - 状态键
   * @returns {string} 显示文本
   */
  getStatusText(status) {
    return STATUS_TEXT_MAP[status] || '未知'
  },

  /**
   * 转换房间数据
   * @param {Array} rawRoomData - 原始房间数据
   * @param {Object} config - 配置信息
   * @returns {Array} 转换后的房间数据
   */
  transformRoomData(rawRoomData, config = {}) {
    if (!Array.isArray(rawRoomData)) {
      console.warn('房间数据必须是数组格式')
      return []
    }

    const { roomStatusConfig = [], cleanStatusConfig = [] } = config

    return rawRoomData.map(room => {
      try {
        return this.transformSingleRoom(room, { roomStatusConfig, cleanStatusConfig })
      } catch (error) {
        console.error('转换房间数据失败:', error, room)
        return this.createFallbackRoom(room)
      }
    }).filter(Boolean)
  },

  /**
   * 转换单个房间数据
   * @param {Object} room - 原始房间数据
   * @param {Object} config - 配置信息
   * @returns {Object} 转换后的房间数据
   */
  transformSingleRoom(room, config = {}) {
    const { roomStatusConfig = [], cleanStatusConfig = [] } = config

    // 将状态配置保存到实例中，供mapRoomStatusBySign使用
    this.roomStatusConfig = roomStatusConfig

    // 基础房间信息
    const transformedRoom = {
      // 房间基础信息
      id: room.id || room.room_id,
      roomNumber: room.room_number || room.roomNumber,
      roomType: room.room_type_name || room.roomType,
      roomTypeId: room.room_type_id || room.roomTypeId, // 添加房型ID字段
      buildingId: room.building_id || room.buildingId,
      buildingName: room.building_name || room.buildingName,
      floorNumber: room.floor_number || room.floorNumber,

      // 房间状态信息 - 根据是否有客人来确定真实状态
      status: this.getRealRoomStatus(room),
      originalStatus: room.room_status_sign || room.status,
      roomStatusName: room.room_status_name || room.roomStatusName || this.getStatusText(this.mapRoomStatusBySign(room.room_status_sign || room.status)),
      room_status_color: room.room_status_color || this.getStatusColor(room.room_status_sign || room.status, roomStatusConfig),

      // 添加业务状态标识字段，用于筛选
      room_status_sign: this.mapRoomStatusToSign(room),

      // 清洁状态信息
      cleanStatus: room.clean_status || room.clear_status_id || room.cleanStatusId,
      cleanStatusName: room.clear_status_name || room.cleanStatusName,
      clear_color: room.clear_color || this.getCleanStatusColor(room.clean_status || room.clear_status_id || room.cleanStatusId, cleanStatusConfig),

      // 客人信息
      ...this.extractGuestInfo(room),

      // 订单信息
      futureOrders: this.extractFutureOrders(room),

      // 联房信息
      ...this.extractConnectRoomInfo(room),

      // 维修信息
      maintenance: this.extractMaintenanceInfo(room),

      // 清洁信息
      housekeeping: this.extractHousekeepingInfo(room),

      // 提醒信息
      ...this.extractReminderInfo(room),

      // 欠费信息
      debtInfo: this.extractDebtInfo(room),

      // 会员和渠道信息
      memberGrade: room.bill_info?.tempGrade_name || room.bill_info?.member_grade || '',
      channelSource: room.bill_info?.bill_source_name || room.bill_info?.channel_source || '',

      // 保留账单相关字段
      bill_id: room.bill_id || null, // 关键字段！用于调用其他接口
      bill_info: room.bill_info || null,
      billInfo: room.bill_info || null, // 保持向后兼容

      // 原始数据（用于调试和兼容）
      _raw: room
    }

    return transformedRoom
  },

  /**
   * 提取客人信息
   * @param {Object} room - 房间数据
   * @returns {Object} 客人信息
   */
  extractGuestInfo(room) {
    const guestInfo = {}

    // 从bill_info中提取客人信息（主要数据源）
    if (room.bill_info) {
      guestInfo.guestName = room.bill_info.link_man || room.bill_info.guest_name
      guestInfo.guestPhone = room.bill_info.link_phone || room.bill_info.guest_phone
      guestInfo.checkInTime = room.bill_info.enter_time ? new Date(room.bill_info.enter_time * 1000) : null
      guestInfo.checkOutTime = room.bill_info.leave_time ? new Date(room.bill_info.leave_time * 1000) : null
      guestInfo.memberGrade = room.bill_info.member_grade
      guestInfo.channelSource = room.bill_info.channel_source

      // 添加账单相关信息
      guestInfo.billId = room.bill_info.id
      guestInfo.billStatus = room.bill_info.status
      guestInfo.totalAmount = room.bill_info.total_amount
      guestInfo.paidAmount = room.bill_info.paid_amount
      guestInfo.debtAmount = room.bill_info.debt_amount
    }

    // 从guest对象中提取（备用）
    if (room.guest) {
      guestInfo.guestName = guestInfo.guestName || room.guest.name
      guestInfo.guestPhone = guestInfo.guestPhone || room.guest.phone
      guestInfo.checkInTime = guestInfo.checkInTime || (room.guest.checkInTime ? new Date(room.guest.checkInTime) : null)
      guestInfo.checkOutTime = guestInfo.checkOutTime || (room.guest.checkOutTime ? new Date(room.guest.checkOutTime) : null)
    }

    // 直接从房间对象中提取（兼容性）
    guestInfo.guestName = guestInfo.guestName || room.guest_name || room.guestName
    guestInfo.guestPhone = guestInfo.guestPhone || room.guest_phone || room.guestPhone

    // 处理时间格式
    if (guestInfo.checkInTime && typeof guestInfo.checkInTime === 'number') {
      guestInfo.checkInTime = new Date(guestInfo.checkInTime * 1000)
    }
    if (guestInfo.checkOutTime && typeof guestInfo.checkOutTime === 'number') {
      guestInfo.checkOutTime = new Date(guestInfo.checkOutTime * 1000)
    }

    return guestInfo
  },

  /**
   * 提取未来订单信息
   * @param {Object} room - 房间数据
   * @returns {Array} 未来订单列表
   */
  extractFutureOrders(room) {
    if (room.future_orders && Array.isArray(room.future_orders)) {
      return room.future_orders.map(order => ({
        id: order.id || order.order_id,
        guestName: order.guest_name || order.link_man,
        guestPhone: order.guest_phone || order.link_phone,
        enterTime: order.enter_time || order.enter_time_plan,
        leaveTime: order.leave_time || order.leave_time_plan,
        startTimePlan: order.start_time_plan,
        channelSource: order.channel_source,
        orderStatus: order.order_status
      }))
    }
    return []
  },

  /**
   * 提取联房信息
   * @param {Object} room - 房间数据
   * @returns {Object} 联房信息
   */
  extractConnectRoomInfo(room) {
    const connectInfo = {}

    // 优先检查 bill_info 中的 connect_code（主要数据源）
    if (room.bill_info && room.bill_info.connect_code) {
      connectInfo.isConnectedRoom = true
      connectInfo.connectCode = room.bill_info.connect_code
      connectInfo.connectRooms = []
      connectInfo.connectBillsCount = 1
    }
    // 检查 connect_bills 字段
    else if (room.connect_bills && Array.isArray(room.connect_bills) && room.connect_bills.length > 0) {
      connectInfo.isConnectedRoom = true
      // 使用第一个联房账单的 connect_code 作为联房组标识
      connectInfo.connectCode = room.connect_bills[0].connect_code || `connect_${room.connect_bills[0].id}`
      connectInfo.connectRooms = room.connect_bills
      connectInfo.connectBillsCount = room.connect_bills.length
    }
    // 检查 bill_info 中的 connect_bills
    else if (room.bill_info && room.bill_info.connect_bills && Array.isArray(room.bill_info.connect_bills) && room.bill_info.connect_bills.length > 0) {
      connectInfo.isConnectedRoom = true
      connectInfo.connectCode = room.bill_info.connect_bills[0].connect_code || `connect_${room.bill_info.connect_bills[0].id}`
      connectInfo.connectRooms = room.bill_info.connect_bills
      connectInfo.connectBillsCount = room.bill_info.connect_bills.length
    }
    // 回退到旧的数据结构
    else if (room.connect_room_info) {
      connectInfo.isConnectedRoom = true
      connectInfo.connectCode = room.connect_room_info.connect_code
      connectInfo.connectRooms = room.connect_room_info.rooms || []
    } else if (room.isConnectedRoom || room.connectCode) {
      connectInfo.isConnectedRoom = room.isConnectedRoom || false
      connectInfo.connectCode = room.connectCode
    } else {
      connectInfo.isConnectedRoom = false
      connectInfo.connectCode = null
      connectInfo.connectRooms = []
      connectInfo.connectBillsCount = 0
    }

    return connectInfo
  },

  /**
   * 提取维修信息
   * @param {Object} room - 房间数据
   * @returns {Object|null} 维修信息
   */
  extractMaintenanceInfo(room) {
    if (room.maintenance_info) {
      return {
        issue: room.maintenance_info.issue,
        description: room.maintenance_info.description,
        assignedTo: room.maintenance_info.assigned_to,
        reportTime: room.maintenance_info.report_time ? new Date(room.maintenance_info.report_time * 1000) : null,
        expectedCompletion: room.maintenance_info.expected_completion ? new Date(room.maintenance_info.expected_completion * 1000) : null,
        priority: room.maintenance_info.priority
      }
    }
    return null
  },

  /**
   * 提取清洁信息
   * @param {Object} room - 房间数据
   * @returns {Object|null} 清洁信息
   */
  extractHousekeepingInfo(room) {
    if (room.housekeeping_info) {
      return {
        assignedTo: room.housekeeping_info.assigned_to,
        inspector: room.housekeeping_info.inspector,
        startTime: room.housekeeping_info.start_time ? new Date(room.housekeeping_info.start_time * 1000) : null,
        estimatedCompletion: room.housekeeping_info.estimated_completion ? new Date(room.housekeeping_info.estimated_completion * 1000) : null,
        notes: room.housekeeping_info.notes
      }
    }
    return null
  },

  /**
   * 提取提醒信息
   * @param {Object} room - 房间数据
   * @returns {Object} 提醒信息
   */
  extractReminderInfo(room) {
    const reminderInfo = {}

    // 退房提醒 - 基于账单信息计算
    if (room.bill_info && room.bill_info.leave_time) {
      const checkoutTime = new Date(room.bill_info.leave_time * 1000)
      const now = new Date()
      const timeDiff = checkoutTime.getTime() - now.getTime()
      const hoursDiff = timeDiff / (1000 * 60 * 60)

      if (hoursDiff <= 2 && hoursDiff > 0) {
        reminderInfo.checkoutReminderInfo = {
          text: `${Math.ceil(hoursDiff)}小时后退房`,
          fullText: `预计退房时间: ${checkoutTime.toLocaleString('zh-CN')}`,
          urgency: hoursDiff <= 1 ? 'high' : 'medium',
          remainingHours: Math.ceil(hoursDiff)
        }
      } else if (hoursDiff <= 0) {
        const overdueHours = Math.abs(hoursDiff)
        reminderInfo.checkoutReminderInfo = {
          text: `超时${Math.ceil(overdueHours)}小时`,
          fullText: `已超过预计退房时间: ${checkoutTime.toLocaleString('zh-CN')}`,
          urgency: 'critical',
          remainingHours: -Math.ceil(overdueHours)
        }
      }
    }

    // 欠费提醒 - 基于账单信息计算
    if (room.bill_info && room.bill_info.debt_amount && room.bill_info.debt_amount > 0) {
      reminderInfo.debtInfo = {
        amount: room.bill_info.debt_amount,
        debtText: `欠费￥${room.bill_info.debt_amount}`,
        fullText: `总金额: ￥${room.bill_info.total_amount || 0}, 已付: ￥${room.bill_info.paid_amount || 0}, 欠费: ￥${room.bill_info.debt_amount}`
      }
    }

    // 从直接字段提取（兼容性）
    if (room.checkout_reminder) {
      reminderInfo.checkoutReminderInfo = {
        text: room.checkout_reminder.text,
        fullText: room.checkout_reminder.full_text,
        urgency: room.checkout_reminder.urgency
      }
    }

    if (room.debt_info) {
      reminderInfo.debtInfo = {
        amount: room.debt_info.amount,
        debtText: room.debt_info.debt_text,
        fullText: room.debt_info.full_text
      }
    }

    return reminderInfo
  },

  /**
   * 提取欠费信息
   * @param {Object} room - 房间数据
   * @returns {Object|null} 欠费信息
   */
  extractDebtInfo(room) {
    if (!room.bill_info) return null

    const billInfo = room.bill_info
    const connectConsumeAmount = parseFloat(billInfo.connect_consume_amount) || 0  // 联房欠费
    const connectPaymentAmount = parseFloat(billInfo.connect_payment_amount) || 0  // 联房支付
    const consumeAmount = parseFloat(billInfo.consume_amount) || 0                 // 本房间欠费

    // 计算总欠费：本房间欠费 + 联房欠费 - 联房支付
    const totalDebt = consumeAmount + connectConsumeAmount - connectPaymentAmount

    if (totalDebt > 0) {
      return {
        hasDebt: true,
        totalDebt,
        consumeAmount,
        connectConsumeAmount,
        connectPaymentAmount,
        debtText: totalDebt >= 1000 ? `${(totalDebt / 1000).toFixed(1)}k` : totalDebt.toFixed(0),
        fullText: `欠费${totalDebt.toFixed(2)}元`
      }
    }

    return null
  },

  /**
   * 获取状态颜色
   * @param {string} statusSign - 状态标识
   * @param {Array} statusConfig - 状态配置
   * @returns {string} 颜色值
   */
  getStatusColor(statusSign, statusConfig) {
    if (!statusSign || !Array.isArray(statusConfig)) {
      return '#e5e7eb' // 默认颜色
    }

    const config = statusConfig.find(s =>
      (s.sign || s.status_sign) === statusSign
    )
    return config ? (config.color || config.status_color) : '#e5e7eb'
  },

  /**
   * 获取清洁状态颜色
   * @param {number} statusId - 清洁状态ID
   * @param {Array} cleanStatusConfig - 清洁状态配置
   * @returns {string} 颜色值
   */
  getCleanStatusColor(statusId, cleanStatusConfig) {
    if (!statusId || !Array.isArray(cleanStatusConfig)) {
      return '#f3f4f6' // 默认颜色
    }

    const config = cleanStatusConfig.find(s =>
      (s.id || s.status_id) === statusId
    )
    return config ? (config.color || config.status_color) : '#f3f4f6'
  },

  /**
   * 创建备用房间数据（当转换失败时）
   * @param {Object} room - 原始房间数据
   * @returns {Object} 备用房间数据
   */
  createFallbackRoom(room) {
    return {
      id: room.id || room.room_id || Math.random().toString(36).substr(2, 9),
      roomNumber: room.room_number || room.roomNumber || '未知',
      roomType: room.room_type_name || room.roomType || '未知',
      buildingId: room.building_id || room.buildingId,
      buildingName: room.building_name || room.buildingName || '未知楼栋',
      floorNumber: room.floor_number || room.floorNumber || 1,
      status: 'unknown',
      roomStatusName: '未知状态',
      room_status_color: '#e5e7eb',
      cleanStatus: null,
      cleanStatusName: '未知',
      clear_color: '#f3f4f6',
      guestName: '',
      guestPhone: '',
      checkInTime: null,
      checkOutTime: null,
      futureOrders: [],
      isConnectedRoom: false,
      connectCode: null,
      maintenance: null,
      housekeeping: null,
      _raw: room,
      _error: true
    }
  },

  /**
   * 按楼栋和楼层分组房间数据
   * @param {Array} roomData - 房间数据
   * @returns {Array} 分组后的数据
   */
  groupRoomsByBuildingAndFloor(roomData) {
    if (!Array.isArray(roomData)) {
      return []
    }

    // 按楼栋分组
    const buildingGroups = {}

    roomData.forEach(room => {
      const buildingKey = room.buildingId || room.buildingName || 'unknown'
      if (!buildingGroups[buildingKey]) {
        buildingGroups[buildingKey] = {
          buildingId: room.buildingId,
          buildingName: room.buildingName || '未知楼栋',
          floors: {},
          totalRooms: 0
        }
      }

      const floorKey = room.floorNumber || 1
      if (!buildingGroups[buildingKey].floors[floorKey]) {
        buildingGroups[buildingKey].floors[floorKey] = {
          floorNumber: floorKey,
          rooms: [],
          statusSummary: {}
        }
      }

      buildingGroups[buildingKey].floors[floorKey].rooms.push(room)
      buildingGroups[buildingKey].totalRooms++

      // 统计楼层状态
      const status = this.mapRoomStatusForSummary(room)
      const floorSummary = buildingGroups[buildingKey].floors[floorKey].statusSummary
      floorSummary[status] = (floorSummary[status] || 0) + 1
    })

    // 转换为数组格式并排序
    return Object.values(buildingGroups).map(building => ({
      ...building,
      floors: Object.values(building.floors).sort((a, b) => {
        const numA = parseInt(a.floorNumber)
        const numB = parseInt(b.floorNumber)
        if (!isNaN(numA) && !isNaN(numB)) {
          return numA - numB
        }
        return String(a.floorNumber).localeCompare(String(b.floorNumber))
      }).map(floor => ({
        ...floor,
        rooms: floor.rooms.sort((a, b) => {
          const numA = parseInt(a.roomNumber)
          const numB = parseInt(b.roomNumber)
          if (!isNaN(numA) && !isNaN(numB)) {
            return numA - numB
          }
          return String(a.roomNumber).localeCompare(String(b.roomNumber))
        })
      }))
    })).sort((a, b) => a.buildingName.localeCompare(b.buildingName))
  },

  /**
   * 映射房间状态用于汇总
   * @param {Object} room - 房间数据
   * @returns {string} 状态键
   */
  mapRoomStatusForSummary(room) {
    const status = room.status || room.room_status_sign || 'unknown'
    const cleanStatus = room.cleanStatusName || room.clear_status_name || ''

    // 优先显示业务状态，但对于空房要考虑清洁状态
    if (status === 'vacant' || status === 'available') {
      if (cleanStatus === '脏' || cleanStatus === 'dirty') {
        return 'dirty'
      } else if (cleanStatus === '清洁中' || cleanStatus === 'cleaning') {
        return 'cleaning'
      } else if (cleanStatus === '维修' || cleanStatus === '维修中' || cleanStatus === 'maintenance') {
        return 'maintenance'
      } else if (cleanStatus === '查房' || cleanStatus === '查房中' || cleanStatus === 'inspecting') {
        return 'inspecting'
      }
      return 'available'
    }

    // 其他状态直接映射
    const statusMap = {
      'stay': 'occupied',
      'occupied': 'occupied',
      'checkout': 'checkout',
      'reserved': 'reserved',
      'noshow': 'noshow',
      'blocked': 'blocked',
      'maintenance': 'maintenance'
    }

    return statusMap[status] || 'unknown'
  },

  /**
   * 计算房间统计数据
   * @param {Array} roomData - 房间数据
   * @returns {Object} 统计数据
   */
  calculateRoomStats(roomData) {
    if (!Array.isArray(roomData)) {
      return this.getEmptyStats()
    }

    const stats = this.getEmptyStats()
    stats.total = roomData.length

    roomData.forEach(room => {
      const status = room.status || room.room_status_sign || 'unknown'
      const cleanStatus = room.cleanStatusName || room.clear_status_name || ''

      // 业务状态统计
      switch (status) {
        case 'stay':
        case 'occupied':
          stats.occupied++
          break
        case 'vacant':
        case 'available':
          stats.available++
          break
        case 'reserved':
          stats.reserved++
          break
        case 'checkout':
          stats.checkout++
          break
        case 'blocked':
          stats.blocked++
          break
      }

      // 清洁状态统计
      switch (cleanStatus) {
        case '脏':
        case 'dirty':
          stats.dirty++
          break
        case '清洁中':
        case 'cleaning':
          stats.cleaning++
          break
        case '维修':
        case '维修中':
        case 'maintenance':
          stats.maintenance++
          break
      }
    })

    return stats
  },

  /**
   * 获取空的统计数据
   * @returns {Object} 空统计数据
   */
  getEmptyStats() {
    return {
      total: 0,
      occupied: 0,
      available: 0,
      reserved: 0,
      checkout: 0,
      dirty: 0,
      cleaning: 0,
      maintenance: 0,
      blocked: 0
    }
  }
}

export default roomDataTransform
