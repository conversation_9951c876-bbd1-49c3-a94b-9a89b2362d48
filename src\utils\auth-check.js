/**
 * 登录状态检查工具
 *
 * 提供统一的登录状态检查和处理机制
 * 当检测到登录过期（返回码886）时，自动跳转到首页重新登录
 */

import { useAuthStore } from '@/store'
import { useRouter } from 'vue-router'
import { lStorage } from '@/utils'
import api from '@/api'

/**
 * 检查登录状态
 * @param {Object} options 配置选项
 * @param {boolean} options.needTip 是否显示提示信息，默认true
 * @param {boolean} options.autoRedirect 是否自动跳转到登录页，默认true
 * @param {string} options.redirectPath 跳转路径，默认'/login'
 * @returns {Promise<boolean>} 返回登录状态是否有效
 */
export async function checkLoginStatus(options = {}) {
  const {
    needTip = true,
    autoRedirect = true,
    redirectPath = '/login'
  } = options

  try {
    const response = await api.checkLoginStatus({ needTip })

    // 检查响应状态
    if (response && response.code === 0) {
      // 登录状态正常，保存返回的数据到 loginResponseData
      if (response.data && typeof response.data === 'object') {
        try {
          lStorage.set('loginResponseData', response.data)

        } catch (storageError) {

        }
      }

      return true
    }

    // 如果返回码是886，表示登录过期
    if (response && response.code === 886) {
      if (needTip) {

      }
      if (autoRedirect) {
        handleLoginExpired(redirectPath)
      }
      return false
    }

    // 其他错误情况
    if (needTip) {

    }
    return false
  } catch (error) {
    // 如果是886错误（登录过期），在HTTP拦截器中已经处理
    if (error.code === 886) {
      if (needTip) {

      }
      if (autoRedirect) {
        handleLoginExpired(redirectPath)
      }
      return false
    }

    // 其他网络错误等
    if (needTip) {

    }
    return false
  }
}

/**
 * 处理登录过期
 * @param {string} redirectPath 跳转路径
 */
function handleLoginExpired(redirectPath = '/login') {
  const authStore = useAuthStore()

  // 清理登录状态
  authStore.resetLoginState()

  // 跳转到登录页
  if (typeof window !== 'undefined') {

    // 简化跳转逻辑 - 直接使用 window.location.href
    const currentHash = window.location.hash
    const currentRoute = currentHash.replace('#', '') || '/'

    if (redirectPath === '/login') {
      const redirectUrl = currentRoute !== '/login'
        ? `/login?redirect=${encodeURIComponent(currentRoute)}#/login`
        : '/login#/login'

      window.location.href = redirectUrl
    } else {
      window.location.href = `${redirectPath}#${redirectPath}`
    }
  }
}

/**
 * 在组件中使用的登录状态检查
 * @param {Object} options 配置选项
 * @returns {Promise<boolean>} 返回登录状态是否有效
 */
export async function useLoginCheck(options = {}) {
  // 进行登录状态检查
  const result = await checkLoginStatus({
    ...options,
    autoRedirect: false // 在组件中手动处理跳转
  })

  // 如果登录过期且需要自动跳转
  if (!result && options.autoRedirect !== false) {
    const redirectPath = options.redirectPath || '/login'

    try {
      // 尝试使用Vue Router
      const { useRouter } = await import('vue-router')
      const router = useRouter()
      const currentPath = router?.currentRoute?.value?.fullPath || window.location.pathname

      if (redirectPath === '/login') {
        if (router && router.push) {
          router.push({
            path: '/login',
            query: { redirect: currentPath }
          })
        } else {
          // 如果router不可用，使用window.location
          window.location.href = `/login?redirect=${encodeURIComponent(currentPath)}`
        }
      } else {
        if (router && router.push) {
          router.push(redirectPath)
        } else {
          window.location.href = redirectPath
        }
      }
    } catch (error) {

      // 兜底方案：直接使用window.location
      const currentPath = window.location.pathname
      const redirectUrl = redirectPath === '/login'
        ? `/login?redirect=${encodeURIComponent(currentPath)}`
        : redirectPath
      window.location.href = redirectUrl
    }
  }

  return result
}

/**
 * 定期检查登录状态
 * @param {number} interval 检查间隔（毫秒），默认5分钟
 * @param {Object} options 配置选项
 * @returns {Function} 返回停止检查的函数
 */
export function startPeriodicLoginCheck(interval = 5 * 60 * 1000, options = {}) {
  const timer = setInterval(async () => {
    await checkLoginStatus({
      needTip: false, // 定期检查不显示提示
      ...options
    })
  }, interval)

  // 返回停止检查的函数
  return () => {
    clearInterval(timer)
  }
}

/**
 * 在页面可见性变化时检查登录状态
 * 当用户切换回页面时检查登录状态
 */
export function setupVisibilityLoginCheck(options = {}) {
  if (typeof document === 'undefined') return

  let lastCheckTime = Date.now()
  const minInterval = 30 * 1000 // 最小检查间隔30秒

  const handleVisibilityChange = async () => {
    // 只在页面变为可见且距离上次检查超过最小间隔时检查
    if (!document.hidden && Date.now() - lastCheckTime > minInterval) {
      lastCheckTime = Date.now()
      await checkLoginStatus({
        needTip: false, // 可见性检查不显示提示
        ...options
      })
    }
  }

  document.addEventListener('visibilitychange', handleVisibilityChange)

  // 返回清理函数
  return () => {
    document.removeEventListener('visibilitychange', handleVisibilityChange)
  }
}

/**
 * 检查当前是否有有效的登录状态
 * 仅检查本地存储，不发送网络请求
 * @returns {boolean} 是否有有效的登录状态
 */
export function hasValidLocalAuth() {
  const authStore = useAuthStore()
  const token = authStore.accessToken

  // 检查token是否存在且有效
  if (!token || token === 'null' || token === 'undefined' || Object.keys(token).length === 0) {
    return false
  }

  return true
}

/**
 * 强制重新登录
 * 清理所有登录状态并跳转到登录页
 * @param {string} message 提示信息
 * @param {string} redirectPath 跳转路径
 */
export function forceRelogin(message = '登录已过期，请重新登录', redirectPath = '/login') {
  const authStore = useAuthStore()

  // 显示提示信息
  if (message && window.$message) {
    window.$message.warning(message)
  }

  // 清理登录状态
  authStore.resetLoginState()

  // 直接跳转到登录页 - 使用最简单的方式

  try {
    // 对于hash路由，直接设置hash
    const currentRoute = window.location.hash.replace('#', '') || '/'
    const loginUrl = currentRoute !== '/login'
      ? `#/login?redirect=${encodeURIComponent(currentRoute)}`
      : '#/login'

    window.location.hash = loginUrl

    // 如果hash设置后没有触发路由变化，强制刷新
    setTimeout(() => {
      if (!window.location.hash.includes('/login')) {

        window.location.href = `/login#/login`
      }
    }, 200)

  } catch (error) {

    window.location.href = '/login#/login'
  }
}

export default {
  checkLoginStatus,
  useLoginCheck,
  startPeriodicLoginCheck,
  setupVisibilityLoginCheck,
  hasValidLocalAuth,
  forceRelogin,
  handleLoginExpired
}
