<template>
  <div class="room-card-large">
    <!-- 顶部信息行：房间号 + 联房标识 -->
    <div class="room-header-large">
      <div class="room-number-large">{{ room.roomNumber || room.room_number }}</div>
      <!-- 联房标识 -->
      <div
        v-if="room.isConnectedRoom && room.connectCode"
        class="connect-room-badge-large"
        :title="`联房组: ${room.connectCode}`"
      >
        <i class="i-mdi:link-variant"></i>
      </div>
    </div>

    <!-- 房型信息 -->
    <div class="room-type-large">{{ room.roomType || room.room_type_name }}</div>

    <!-- 主要信息：客人信息 -->
    <div v-if="hasGuestInfo(room)" class="guest-main-info">
      <div class="guest-primary">
        <div class="guest-name-with-badges">
          <span class="guest-name-large">{{ room.guestName || getGuestName(room) }}</span>
          <!-- 会员等级和渠道来源标签（紧跟姓名） -->
          <div v-if="room.memberGrade || room.channelSource || getMemberGrade(room) || getChannelSource(room)" class="guest-badges-inline">
            <span v-if="room.memberGrade || getMemberGrade(room)" class="member-badge-inline">{{ room.memberGrade || getMemberGrade(room) }}</span>
            <span v-if="room.channelSource || getChannelSource(room)" class="channel-badge-inline">{{ room.channelSource || getChannelSource(room) }}</span>
          </div>
        </div>
        <div v-if="room.checkInTime || room.checkOutTime || getCheckInTime(room) || getCheckOutTime(room)" class="time-info-large">
          <span v-if="room.checkInTime || getCheckInTime(room)" class="checkin-time-large">
            入住: {{ formatTime(room.checkInTime || getCheckInTime(room)) }}
          </span>
          <span v-if="room.checkOutTime || getCheckOutTime(room)" class="checkout-time-large">
            退房: {{ formatTime(room.checkOutTime || getCheckOutTime(room)) }}
          </span>
        </div>
      </div>
      <div v-if="room.guestPhone" class="guest-secondary">
        {{ room.guestPhone }}
      </div>
    </div>

    <!-- 预订信息 -->
    <div v-else-if="hasReservationInfo(room)" class="reservation-main-info">
      <div class="reservation-primary">
        <div class="guest-name-large">{{ room.guestName || '预订客人' }}</div>
        <div v-if="getArrivalTime(room) || getExpectedArrival(room)" class="arrival-info-large">
          <span v-if="getArrivalTime(room)">预计: {{ formatTime(getArrivalTime(room)) }}</span>
          <span v-if="getExpectedArrival(room)">应到: {{ formatTime(getExpectedArrival(room)) }}</span>
        </div>
      </div>
    </div>

    <!-- 维修信息 -->
    <div v-if="room.maintenance" class="maintenance-info">
      <div class="maintenance-issue">{{ room.maintenance.issue }}</div>
      <div class="maintenance-assignee">负责人: {{ room.maintenance.assignedTo }}</div>
    </div>

    <!-- 清洁信息 -->
    <div v-if="room.housekeeping && (room.status === 'cleaning' || room.status === 'inspecting')" class="housekeeping-info">
      <div v-if="room.housekeeping.assignedTo" class="hk-assignee">
        清洁员: {{ room.housekeeping.assignedTo }}
      </div>
      <div v-if="room.housekeeping.inspector" class="hk-inspector">
        查房员: {{ room.housekeeping.inspector }}
      </div>
      <div v-if="room.housekeeping.estimatedCompletion" class="hk-eta">
        预计完成: {{ formatTime(room.housekeeping.estimatedCompletion) }}
      </div>
    </div>

    <!-- 底部信息行：订单信息 + 状态标识 -->
    <div class="room-bottom-info">
      <!-- 订单信息（简化显示） -->
      <div v-if="getTodayOrders(room.futureOrders).length > 0 || getFutureOrders(room.futureOrders).length > 0" class="orders-summary">
        <span v-if="getTodayOrders(room.futureOrders).length > 0" class="today-orders-summary">
          <i class="i-mdi:calendar-today"></i>
          今日{{ getTodayOrders(room.futureOrders).length }}单
        </span>
        <span v-if="getFutureOrders(room.futureOrders).length > 0" class="future-orders-summary">
          <i class="i-mdi:calendar-clock"></i>
          未来{{ getFutureOrders(room.futureOrders).length }}单
        </span>
      </div>
    </div>

    <!-- 底部状态标签区域 -->
    <div class="bottom-status-area">
      <!-- 左侧：房间状态标签 -->
      <div class="status-badges-left">
        <!-- 优先显示业务状态，如果是空房或净房则显示清洁状态 -->
        <div
          v-if="room.roomStatusName && !shouldShowCleanStatus(room)"
          class="room-status-badge-large"
          :style="getRoomStatusBadgeStyle(room)"
        >
          <i :class="getStatusIcon(room.status)"></i>
          <span>{{ room.roomStatusName }}</span>
        </div>
        <div
          v-else-if="room.cleanStatusName"
          class="room-clean-badge-large"
          :class="getCleanStatusClass(room)"
          :style="getCleanStatusBadgeStyle(room)"
        >
          <i :class="getCleanStatusIcon(room)"></i>
          <span>{{ room.cleanStatusName }}</span>
        </div>

        <!-- 退房提醒（如果有的话） -->
        <div v-if="isCheckoutOverdue(room)" class="checkout-alert-large overdue">
          <i class="i-mdi:alert-circle"></i>
          <span>超时未退房</span>
        </div>
        <div v-else-if="isCheckoutSoon(room)" class="checkout-alert-large soon">
          <i class="i-mdi:clock-alert"></i>
          <span>即将退房</span>
        </div>
      </div>

      <!-- 右侧：离店提醒和欠费标签 -->
      <div class="status-badges-right">
        <div v-if="room.checkoutReminderInfo" class="checkout-reminder-large" :title="room.checkoutReminderInfo.fullText">
          <i class="i-mdi:clock-alert-outline"></i>
          <span>{{ room.checkoutReminderInfo.text }}</span>
        </div>
        <div v-if="room.debtInfo" class="debt-reminder-large" :title="room.debtInfo.fullText">
          <i class="i-mdi:currency-cny"></i>
          <span>{{ room.debtInfo.debtText }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Props
const props = defineProps({
  room: {
    type: Object,
    required: true
  }
})

// 判断是否有客人信息
const hasGuestInfo = (room) => {
  if (!room) return false

  // 优先检查bill_info中的客人信息
  if (room.bill_info && room.bill_info.link_man) {
    return true
  }

  // 检查guest对象
  if (room.guest && room.guest.name) {
    return true
  }

  // 检查转换后的数据
  if (room.guestName) {
    return true
  }

  return false
}

// 获取客人姓名
const getGuestName = (room) => {
  if (!room) return ''

  if (room.bill_info && room.bill_info.link_man) {
    return room.bill_info.link_man
  }

  if (room.guest && room.guest.name) {
    return room.guest.name
  }

  return room.guest_name || ''
}

// 获取会员等级
const getMemberGrade = (room) => {
  if (!room) return ''

  if (room.bill_info && room.bill_info.member_grade) {
    return room.bill_info.member_grade
  }

  return room.member_grade || ''
}

// 获取渠道来源
const getChannelSource = (room) => {
  if (!room) return ''

  if (room.bill_info && room.bill_info.channel_source) {
    return room.bill_info.channel_source
  }

  return room.channel_source || ''
}

// 获取入住时间
const getCheckInTime = (room) => {
  if (!room) return null

  if (room.bill_info && room.bill_info.enter_time) {
    return new Date(room.bill_info.enter_time * 1000)
  }

  if (room.guest && room.guest.checkInTime) {
    return new Date(room.guest.checkInTime)
  }

  return null
}

// 获取退房时间
const getCheckOutTime = (room) => {
  if (!room) return null

  if (room.bill_info && room.bill_info.leave_time) {
    return new Date(room.bill_info.leave_time * 1000)
  }

  if (room.guest && room.guest.checkOutTime) {
    return new Date(room.guest.checkOutTime)
  }

  return null
}

// 判断是否有预订信息
const hasReservationInfo = (room) => {
  if (!room) return false

  // 房间状态为预订相关
  const statusName = room.roomStatusName || room.room_status_name || ''
  if (statusName.includes('预订') || statusName.includes('预定')) {
    return true
  }

  // 或者有预订相关的状态标识
  const status = room.status || ''
  if (status === 'reserved' || status === 'noshow') {
    return true
  }

  return false
}

// 获取到店时间
const getArrivalTime = (room) => {
  if (!room) return null

  // 从bill_info获取
  if (room.bill_info && room.bill_info.arrival_time) {
    return room.bill_info.arrival_time
  }

  // 从guest对象获取
  if (room.guest && room.guest.arrivalTime) {
    return room.guest.arrivalTime
  }

  return null
}

// 获取预期到店时间
const getExpectedArrival = (room) => {
  if (!room) return null

  // 从bill_info获取
  if (room.bill_info && room.bill_info.expected_arrival) {
    return room.bill_info.expected_arrival
  }

  // 从guest对象获取
  if (room.guest && room.guest.expectedArrival) {
    return room.guest.expectedArrival
  }

  return null
}

// 获取今日订单
const getTodayOrders = (orders) => {
  if (!orders || !Array.isArray(orders)) return []

  const today = new Date()
  today.setHours(0, 0, 0, 0)
  const tomorrow = new Date(today)
  tomorrow.setDate(tomorrow.getDate() + 1)

  return orders.filter(order => {
    const timestamp = order.enter_time || order.enter_time_plan || order.start_time_plan
    if (!timestamp) return false

    const orderDate = new Date(timestamp * 1000)
    return orderDate >= today && orderDate < tomorrow
  })
}

// 获取未来订单（明日及以后）
const getFutureOrders = (orders) => {
  if (!orders || !Array.isArray(orders)) return []

  const tomorrow = new Date()
  tomorrow.setHours(0, 0, 0, 0)
  tomorrow.setDate(tomorrow.getDate() + 1)

  return orders.filter(order => {
    const timestamp = order.enter_time || order.enter_time_plan || order.start_time_plan
    if (!timestamp) return false

    const orderDate = new Date(timestamp * 1000)
    return orderDate >= tomorrow
  })
}

// 判断是否应该显示清洁状态而不是业务状态
const shouldShowCleanStatus = (room) => {
  // 如果没有清洁状态，不显示
  if (!room.cleanStatusName) return false

  // 如果是净房，不需要特别显示清洁状态
  if (room.cleanStatusName === '净') return false

  // 如果房间状态是空房，优先显示清洁状态
  if (room.roomStatusName === '空房') return true

  // 如果清洁状态是需要关注的状态（脏、清洁中、维修等），显示清洁状态
  const importantCleanStatus = ['脏', '清洁中', '维修', '维修中', '封锁', '查房', '查房中']
  return importantCleanStatus.includes(room.cleanStatusName)
}

// 其他辅助函数（从原组件中提取）
const formatTime = (timeStr) => {
  // 简化的时间格式化函数
  if (!timeStr) return ''
  try {
    const date = new Date(timeStr)
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } catch {
    return timeStr
  }
}

const getStatusIcon = (status) => {
  const icons = {
    occupied: 'i-material-symbols:person',
    available: 'i-material-symbols:check-circle',
    checkout: 'i-material-symbols:logout',
    dirty: 'i-material-symbols:warning',
    cleaning: 'i-material-symbols:cleaning-services',
    inspecting: 'i-material-symbols:search',
    maintenance: 'i-material-symbols:build',
    blocked: 'i-material-symbols:block',
    reserved: 'i-material-symbols:event',
    noshow: 'i-material-symbols:person-off'
  }
  return icons[status] || 'i-material-symbols:help'
}

const getRoomStatusBadgeStyle = (room) => {
  const color = room.room_status_color || '#e5e7eb'
  return {
    'background': colorWithOpacity(color, 0.25),
    'color': color,
    'border': `1px solid ${colorWithOpacity(color, 0.5)}`
  }
}

const getCleanStatusBadgeStyle = (room) => {
  const color = room.clear_color || '#f3f4f6'
  return {
    'background': colorWithOpacity(color, 0.18),
    'color': color,
    'border': `1px solid ${colorWithOpacity(color, 0.4)}`,
    'font-size': '0.65rem',
    'padding': '0.125rem 0.375rem',
    'border-radius': '4px',
    'font-weight': '500'
  }
}

const getCleanStatusClass = (room) => {
  const statusName = room.cleanStatusName || room.clear_status_name || ''
  const classes = []

  if (statusName.includes('脏')) {
    classes.push('status-dirty')
  } else if (statusName.includes('维修')) {
    classes.push('status-maintenance')
  } else if (statusName.includes('清洁')) {
    classes.push('status-cleaning')
  } else if (statusName.includes('查房')) {
    classes.push('status-inspecting')
  } else if (statusName.includes('封锁')) {
    classes.push('status-blocked')
  }

  return classes
}

const getCleanStatusIcon = (room) => {
  const statusName = room.cleanStatusName || room.clear_status_name || ''

  if (statusName.includes('脏')) {
    return 'i-mdi:delete-sweep'
  } else if (statusName.includes('维修')) {
    return 'i-mdi:tools'
  } else if (statusName.includes('清洁')) {
    return 'i-mdi:broom'
  } else if (statusName.includes('查房')) {
    return 'i-mdi:magnify'
  } else if (statusName.includes('封锁')) {
    return 'i-mdi:block-helper'
  } else {
    return 'i-mdi:check-circle'
  }
}

const isCheckoutSoon = (room) => {
  if (!room.checkoutTime && !room.checkout_time) return false

  const checkoutTime = new Date(room.checkoutTime || room.checkout_time)
  const now = new Date()
  const timeDiff = checkoutTime.getTime() - now.getTime()
  const hoursDiff = timeDiff / (1000 * 60 * 60)

  return hoursDiff > 0 && hoursDiff <= 2
}

const isCheckoutOverdue = (room) => {
  if (!room.checkoutTime && !room.checkout_time) return false

  const checkoutTime = new Date(room.checkoutTime || room.checkout_time)
  const now = new Date()

  return now > checkoutTime
}

const colorWithOpacity = (color, opacity) => {
  if (color.startsWith('#')) {
    const hex = color.replace('#', '')
    const r = parseInt(hex.substr(0, 2), 16)
    const g = parseInt(hex.substr(2, 2), 16)
    const b = parseInt(hex.substr(4, 2), 16)
    return `rgba(${r}, ${g}, ${b}, ${opacity})`
  }
  return color
}
</script>

<style scoped>
.room-card-large {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  overflow: hidden;
}

/* 顶部信息行 */
.room-header-large {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.05rem;
}

.room-number-large {
  font-size: 1.3rem;
  font-weight: 800;
  color: #1f2937;
  line-height: 1;
  flex: 1;
}

/* 空房的房号更大 */
.room-card-large:not(:has(.guest-main-info)):not(:has(.reservation-main-info)) .room-number-large {
  font-size: 1.6rem;
  font-weight: 900;
}

.room-type-large {
  font-size: 0.85rem;
  color: #4b5563;
  line-height: 1;
  margin-bottom: 0.15rem;
  font-weight: 500;
}

.connect-room-badge-large {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  border-radius: 50%;
  font-size: 0.7rem;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.connect-room-badge-large:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 10px rgba(139, 92, 246, 0.5);
}

/* 主要信息 */
.guest-main-info,
.reservation-main-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.15rem;
  margin: 0.1rem 0 0.15rem 0;
  min-height: 0;
  overflow: hidden;
}

.guest-primary,
.reservation-primary {
  display: flex;
  flex-direction: column;
  gap: 0.15rem;
  font-size: 0.75rem;
  line-height: 1.2;
  overflow: hidden;
}

.guest-name-large {
  font-weight: 600;
  color: #374151;
  line-height: 1.2;
  margin-bottom: 0.05rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.guest-name-with-badges {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  flex-wrap: wrap;
}

.guest-badges-inline {
  display: flex;
  gap: 0.2rem;
}

.member-badge-inline {
  font-size: 0.55rem;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  padding: 0.1rem 0.25rem;
  border-radius: 2px;
  font-weight: 500;
  line-height: 1;
}

.channel-badge-inline {
  font-size: 0.55rem;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  padding: 0.1rem 0.25rem;
  border-radius: 2px;
  font-weight: 500;
  line-height: 1;
}

.time-info-large {
  font-size: 0.65rem;
  color: #6b7280;
  display: flex;
  flex-direction: column;
  gap: 0.05rem;
  overflow: hidden;
}

.guest-secondary {
  font-size: 0.75rem;
  color: #6b7280;
}

/* 维修和清洁信息 */
.maintenance-info,
.housekeeping-info {
  font-size: 0.65rem;
  color: #6b7280;
  background: rgba(0, 0, 0, 0.05);
  padding: 0.25rem;
  border-radius: 4px;
  margin: 0.1rem 0;
  overflow: hidden;
  flex-shrink: 0;
}

/* 底部信息 */
.room-bottom-info {
  margin-top: auto;
  flex-shrink: 0;
}

.orders-summary {
  display: flex;
  gap: 0.5rem;
  font-size: 0.65rem;
  color: #6b7280;
}

.today-orders-summary,
.future-orders-summary {
  display: flex;
  align-items: center;
  gap: 0.2rem;
}

/* 底部状态区域 */
.bottom-status-area {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-top: 0.2rem;
  gap: 0.25rem;
  flex-shrink: 0;
  min-height: 20px;
}

.status-badges-left,
.status-badges-right {
  display: flex;
  gap: 0.2rem;
  flex-wrap: wrap;
}

.room-status-badge-large,
.room-clean-badge-large {
  display: flex;
  align-items: center;
  gap: 0.2rem;
  padding: 0.15rem 0.3rem;
  border-radius: 4px;
  font-size: 0.65rem;
  font-weight: 500;
}

.checkout-alert-large {
  display: flex;
  align-items: center;
  gap: 0.2rem;
  padding: 0.15rem 0.3rem;
  border-radius: 4px;
  font-size: 0.6rem;
  font-weight: 500;
}

.checkout-alert-large.overdue {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.checkout-alert-large.soon {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.checkout-reminder-large,
.debt-reminder-large {
  display: flex;
  align-items: center;
  gap: 0.2rem;
  padding: 0.15rem 0.3rem;
  border-radius: 4px;
  font-size: 0.6rem;
  font-weight: 500;
  background: rgba(0, 0, 0, 0.1);
  color: #374151;
}
</style>
