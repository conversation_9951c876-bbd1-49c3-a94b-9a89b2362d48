import api from '@/api'
import { basePermissions } from '@/settings'
import { lStorage, throttle } from '@/utils'
export async function getUserInfo() {
  const res = await api.getUser()
  return res.data
}
export async function getPermissions() {
  let asyncPermissions = []
  try {
    // 优先从本地缓存获取auth_list
    let authList = lStorage.get('auth_list') || localStorage.getItem('auth_list')
    if (typeof authList === 'string') {
      try { authList = JSON.parse(authList) } catch {}
    }
    asyncPermissions = Array.isArray(authList) ? authList : []
  }
  catch (error) {
  }
  return basePermissions.concat(asyncPermissions)
}
