<template>
  <div class="theme-preview-container">
    <div class="theme-grid">
      <!-- 明亮主题 -->
      <div class="theme-category">
        <h3 class="category-title">
          <i class="i-material-symbols:wb-sunny text-16 mr-8"></i>
          明亮主题
        </h3>
        <div class="theme-items">
          <div
            v-for="theme in lightThemes"
            :key="theme.key"
            class="theme-item"
            :class="{ active: currentTheme.key === theme.key }"
            @click="selectTheme(theme)"
          >
            <div class="theme-preview" :style="getThemePreviewStyle(theme)">
              <div class="preview-header" :style="{ background: theme.primaryColor }"></div>
              <div class="preview-content">
                <div class="preview-card" :style="{ borderColor: theme.primaryColor + '40' }"></div>
                <div class="preview-card" :style="{ borderColor: theme.primaryColor + '40' }"></div>
              </div>
            </div>
            <div class="theme-info">
              <i :class="theme.icon" :style="{ color: theme.primaryColor }"></i>
              <span class="theme-name">{{ theme.label }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 暗色主题 -->
      <div class="theme-category">
        <h3 class="category-title">
          <i class="i-material-symbols:dark-mode text-16 mr-8"></i>
          暗色主题
        </h3>
        <div class="theme-items">
          <div
            v-for="theme in darkThemes"
            :key="theme.key"
            class="theme-item"
            :class="{ active: currentTheme.key === theme.key }"
            @click="selectTheme(theme)"
          >
            <div class="theme-preview dark-preview" :style="getThemePreviewStyle(theme)">
              <div class="preview-header" :style="{ background: theme.primaryColor }"></div>
              <div class="preview-content">
                <div class="preview-card" :style="{ borderColor: theme.primaryColor + '60' }"></div>
                <div class="preview-card" :style="{ borderColor: theme.primaryColor + '60' }"></div>
              </div>
            </div>
            <div class="theme-info">
              <i :class="theme.icon" :style="{ color: theme.primaryColor }"></i>
              <span class="theme-name">{{ theme.label }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useAppStore } from '@/store'

const appStore = useAppStore()

// 主题配置（与ThemeSelector保持一致，包含 hover/pressed/suppl）
const themes = [
  // 明亮主题
  { key: 'default', label: '默认蓝色', icon: 'i-material-symbols:palette', primaryColor: '#8A9CFF', primaryColorHover: '#6E8AF8', primaryColorPressed: '#4C73E6', primaryColorSuppl: '#A8B5FF', category: 'light' },
  { key: 'green', label: '清新绿色', icon: 'i-material-symbols:eco', primaryColor: '#10B981', primaryColorHover: '#059669', primaryColorPressed: '#047857', primaryColorSuppl: '#34D399', category: 'light' },
  { key: 'purple', label: '优雅紫色', icon: 'i-material-symbols:auto-awesome', primaryColor: '#8B5CF6', primaryColorHover: '#7C3AED', primaryColorPressed: '#6D28D9', primaryColorSuppl: '#A78BFA', category: 'light' },
  { key: 'orange', label: '活力橙色', icon: 'i-material-symbols:wb-sunny', primaryColor: '#F59E0B', primaryColorHover: '#D97706', primaryColorPressed: '#B45309', primaryColorSuppl: '#FCD34D', category: 'light' },
  { key: 'red', label: '热情红色', icon: 'i-material-symbols:favorite', primaryColor: '#EF4444', primaryColorHover: '#DC2626', primaryColorPressed: '#B91C1C', primaryColorSuppl: '#F87171', category: 'light' },

  // 暗色主题
  { key: 'dark-default', label: '暗夜黑', icon: 'i-material-symbols:dark-mode', primaryColor: '#1F2937', primaryColorHover: '#374151', primaryColorPressed: '#4B5563', primaryColorSuppl: '#6B7280', category: 'dark' },
  { key: 'dark-purple', label: '神秘紫', icon: 'i-material-symbols:psychology', primaryColor: '#581C87', primaryColorHover: '#6B21A8', primaryColorPressed: '#7C2D12', primaryColorSuppl: '#8B5CF6', category: 'dark' },
  { key: 'dark-green', label: '森林绿', icon: 'i-material-symbols:forest', primaryColor: '#14532D', primaryColorHover: '#166534', primaryColorPressed: '#15803D', primaryColorSuppl: '#22C55E', category: 'dark' },
  { key: 'dark-red', label: '深红酒', icon: 'i-material-symbols:wine-bar', primaryColor: '#7F1D1D', primaryColorHover: '#991B1B', primaryColorPressed: '#B91C1C', primaryColorSuppl: '#EF4444', category: 'dark' },
  { key: 'dark-orange', label: '暖炉橙', icon: 'i-material-symbols:fireplace', primaryColor: '#9A3412', primaryColorHover: '#C2410C', primaryColorPressed: '#EA580C', primaryColorSuppl: '#F97316', category: 'dark' },
]

const lightThemes = computed(() => themes.filter(theme => theme.category === 'light'))
const darkThemes = computed(() => themes.filter(theme => theme.category === 'dark'))
const currentTheme = computed(() => themes.find(theme => theme.key === appStore.currentTheme) || themes[0])

function getThemePreviewStyle(theme) {
  return {
    '--preview-primary': theme.primaryColor,
    '--preview-primary-light': theme.primaryColor + '20'
  }
}

function selectTheme(theme) {
  // 更新 store 当前主题
  appStore.setCurrentTheme(theme.key)

  // 同步更新 Naive UI 主题
  appStore.updateThemeOverrides({
    common: {
      primaryColor: theme.primaryColor,
      primaryColorHover: theme.primaryColorHover || theme.primaryColor,
      primaryColorPressed: theme.primaryColorPressed || theme.primaryColor,
      primaryColorSuppl: theme.primaryColorSuppl || theme.primaryColor,
    }
  })

  // 同步更新 CSS 变量
  const root = document.documentElement
  root.style.setProperty('--primary-color', theme.primaryColor)
  root.style.setProperty('--primary-color-hover', theme.primaryColorHover || theme.primaryColor)
  root.style.setProperty('--primary-color-pressed', theme.primaryColorPressed || theme.primaryColor)
  root.style.setProperty('--primary-color-suppl', theme.primaryColorSuppl || theme.primaryColor)

  // 更新 RGB
  const hex = theme.primaryColor.replace('#', '')
  const r = parseInt(hex.substring(0, 2), 16)
  const g = parseInt(hex.substring(2, 4), 16)
  const b = parseInt(hex.substring(4, 6), 16)
  root.style.setProperty('--primary-color-rgb', `${r}, ${g}, ${b}`)
}
</script>

<style scoped>
.theme-preview-container {
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

.theme-category {
  margin-bottom: 32px;
}

.category-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color-1);
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color);
}

.theme-items {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 16px;
}

.theme-item {
  cursor: pointer;
  border-radius: 12px;
  padding: 12px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.theme-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.theme-item.active {
  border-color: var(--primary-color);
  box-shadow: 0 4px 16px rgba(var(--primary-color-rgb), 0.2);
  position: relative;
}

.theme-item.active::after {
  content: '已选';
  position: absolute;
  top: 8px;
  right: 8px;
  font-size: 10px;
  color: #fff;
  background: var(--primary-color);
  border-radius: 8px;
  padding: 2px 6px;
}

.theme-preview {
  width: 100%;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
  border: 1px solid #e5e7eb;
  margin-bottom: 8px;
}

.dark-preview {
  background: #000000 !important;
  border-color: #111827 !important;
}

.preview-header {
  height: 20px;
  width: 100%;
}

.preview-content {
  padding: 8px;
  display: flex;
  gap: 4px;
}

.preview-card {
  flex: 1;
  height: 24px;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.02);
  border: 1px solid;
}

.dark-preview .preview-card {
  background: rgba(255, 255, 255, 0.05);
}

.theme-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.theme-name {
  font-size: 12px;
  color: var(--text-color-2);
  font-weight: 500;
}

.theme-item.active .theme-name {
  color: var(--primary-color);
  font-weight: 600;
}
</style>
