/**
 * 酒店营业日状态管理
 */
import { defineStore } from 'pinia'
import businessDateApi from '@/api/hotel/businessDate'

export const useBusinessDateStore = defineStore('businessDate', {
  state: () => ({
    // 当前营业日
    currentDate: null,
    // 是否正在加载
    loading: false,
    // 最后更新时间
    lastUpdateTime: null,
    // 错误信息
    error: null
  }),

  getters: {
    // 格式化的营业日显示
    formattedDate: (state) => {
      if (!state.currentDate) return '获取中...'
      
      try {
        const date = new Date(state.currentDate)
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const weekdays = ['日', '一', '二', '三', '四', '五', '六']
        const weekday = weekdays[date.getDay()]
        
        return `${year}-${month}-${day} 星期${weekday}`
      } catch (error) {

        return state.currentDate
      }
    },

    // 是否有有效的营业日
    hasValidDate: (state) => {
      return !!state.currentDate && !state.error
    }
  },

  actions: {
    // 获取营业日
    async fetchBusinessDate() {
      this.loading = true
      this.error = null
      
      try {

        const response = await businessDateApi.getShopDate()
        
        if (response && response.data && response.data.date) {
          this.currentDate = response.data.date
          this.lastUpdateTime = new Date().toISOString()

        } else {
          throw new Error('营业日数据格式错误')
        }
      } catch (error) {

        this.error = error.message || '获取营业日失败'
        
        // 如果获取失败，使用当前日期作为备用
        if (!this.currentDate) {
          this.currentDate = new Date().toISOString().split('T')[0]

        }
      } finally {
        this.loading = false
      }
    },

    // 重置状态
    reset() {
      this.currentDate = null
      this.loading = false
      this.lastUpdateTime = null
      this.error = null
    },

    // 手动设置营业日（用于测试或特殊情况）
    setBusinessDate(date) {
      this.currentDate = date
      this.lastUpdateTime = new Date().toISOString()
      this.error = null

    }
  },

  persist: {
    // 持久化营业日信息，避免频繁请求
    pick: ['currentDate', 'lastUpdateTime'],
    storage: sessionStorage // 使用sessionStorage，关闭浏览器后重新获取
  }
})
