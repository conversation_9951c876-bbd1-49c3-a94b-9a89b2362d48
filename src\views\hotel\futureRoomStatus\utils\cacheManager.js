/**
 * 智能缓存管理器
 * 支持多级缓存、过期时间、LRU淘汰策略
 */
class CacheManager {
  constructor(options = {}) {
    this.maxSize = options.maxSize || 100 // 最大缓存条目数
    this.defaultTTL = options.defaultTTL || 5 * 60 * 1000 // 默认5分钟过期
    this.cache = new Map()
    this.accessTimes = new Map() // 记录访问时间，用于LRU
    this.expireTimes = new Map() // 记录过期时间
    
    // 定期清理过期缓存
    this.cleanupInterval = setInterval(() => {
      this.cleanup()
    }, 60 * 1000) // 每分钟清理一次
  }

  /**
   * 生成缓存键
   */
  generateKey(prefix, params) {
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((result, key) => {
        result[key] = params[key]
        return result
      }, {})
    
    return `${prefix}:${JSON.stringify(sortedParams)}`
  }

  /**
   * 设置缓存
   */
  set(key, value, ttl = this.defaultTTL) {
    // 如果缓存已满，删除最久未访问的项
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      this.evictLRU()
    }

    const now = Date.now()
    this.cache.set(key, value)
    this.accessTimes.set(key, now)
    this.expireTimes.set(key, now + ttl)

    return this
  }

  /**
   * 获取缓存
   */
  get(key) {
    if (!this.cache.has(key)) {
      return null
    }

    const now = Date.now()
    const expireTime = this.expireTimes.get(key)

    // 检查是否过期
    if (expireTime && now > expireTime) {
      this.delete(key)
      return null
    }

    // 更新访问时间
    this.accessTimes.set(key, now)
    return this.cache.get(key)
  }

  /**
   * 删除缓存
   */
  delete(key) {
    this.cache.delete(key)
    this.accessTimes.delete(key)
    this.expireTimes.delete(key)
    return this
  }

  /**
   * 检查缓存是否存在且未过期
   */
  has(key) {
    if (!this.cache.has(key)) {
      return false
    }

    const now = Date.now()
    const expireTime = this.expireTimes.get(key)

    if (expireTime && now > expireTime) {
      this.delete(key)
      return false
    }

    return true
  }

  /**
   * 清空所有缓存
   */
  clear() {
    this.cache.clear()
    this.accessTimes.clear()
    this.expireTimes.clear()
    return this
  }

  /**
   * LRU淘汰策略
   */
  evictLRU() {
    let oldestKey = null
    let oldestTime = Date.now()

    for (const [key, time] of this.accessTimes.entries()) {
      if (time < oldestTime) {
        oldestTime = time
        oldestKey = key
      }
    }

    if (oldestKey) {
      this.delete(oldestKey)
    }
  }

  /**
   * 清理过期缓存
   */
  cleanup() {
    const now = Date.now()
    const expiredKeys = []

    for (const [key, expireTime] of this.expireTimes.entries()) {
      if (now > expireTime) {
        expiredKeys.push(key)
      }
    }

    expiredKeys.forEach(key => this.delete(key))
    
    console.log(`缓存清理完成，删除了 ${expiredKeys.length} 个过期项`)
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: this.hitCount / (this.hitCount + this.missCount) || 0,
      hitCount: this.hitCount || 0,
      missCount: this.missCount || 0
    }
  }

  /**
   * 预热缓存
   */
  async warmup(keys, fetchFunction) {
    const promises = keys.map(async (key) => {
      if (!this.has(key)) {
        try {
          const data = await fetchFunction(key)
          this.set(key, data)
        } catch (error) {
          console.warn(`预热缓存失败: ${key}`, error)
        }
      }
    })

    await Promise.allSettled(promises)
  }

  /**
   * 销毁缓存管理器
   */
  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
    }
    this.clear()
  }
}

/**
 * 创建缓存管理器实例
 */
export function createCacheManager(options) {
  return new CacheManager(options)
}

/**
 * 全局缓存管理器实例
 */
export const globalCache = createCacheManager({
  maxSize: 200,
  defaultTTL: 10 * 60 * 1000 // 10分钟
})

/**
 * 房态数据专用缓存
 */
export const roomStatusCache = createCacheManager({
  maxSize: 50,
  defaultTTL: 5 * 60 * 1000 // 5分钟
})

/**
 * 基础数据缓存（房间列表、楼栋列表等）
 */
export const basicDataCache = createCacheManager({
  maxSize: 20,
  defaultTTL: 30 * 60 * 1000 // 30分钟
})

/**
 * 缓存装饰器
 */
export function withCache(cacheManager, keyPrefix, ttl) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value

    descriptor.value = async function(...args) {
      const key = cacheManager.generateKey(keyPrefix, args[0] || {})
      
      // 尝试从缓存获取
      const cached = cacheManager.get(key)
      if (cached !== null) {
        return cached
      }

      // 缓存未命中，执行原方法
      try {
        const result = await originalMethod.apply(this, args)
        cacheManager.set(key, result, ttl)
        return result
      } catch (error) {
        // 不缓存错误结果
        throw error
      }
    }

    return descriptor
  }
}

export default CacheManager
