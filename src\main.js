import { createApp } from 'vue'
import App from './App.vue'
import { setupRouter } from './router'
import { setupStore } from './store'
import { setupNaiveDiscreteApi } from './utils'
import { request } from './utils/http'
import { initEventOptimization } from './utils/eventOptimization'
import i18n from './i18n'
import 'uno.css'
// 将request挂载到全局，方便调试
window.$http = request

async function bootstrap() {
  try {
    const app = createApp(App)
    // 设置Store
    setupStore(app)
    // 设置多语言
    app.use(i18n)
    // 设置路由
    await setupRouter(app)
    app.mount('#app')

    // 设置Naive UI离散API
    setupNaiveDiscreteApi()

    // 初始化事件优化
    initEventOptimization()

    // 触发应用挂载完成事件，隐藏初始加载动画
    setTimeout(() => {
      window.dispatchEvent(new CustomEvent('app-mounted'))
      console.log('Vue应用挂载完成，初始加载动画将被隐藏')
    }, 100) // 稍微延迟确保DOM完全渲染
  } catch (error) {
    // 如果Vue应用失败，回退到原生JS
    document.getElementById('app').innerHTML = `
      <div style="padding: 2rem; text-align: center; color: red;">
        <h1>Vue应用初始化失败</h1>
        <p>错误信息: ${error.message}</p>
        <p>请检查浏览器控制台获取详细信息</p>
        <pre style="text-align: left; background: #f5f5f5; padding: 1rem; margin-top: 1rem;">${error.stack}</pre>
      </div>
    `
  }
}

bootstrap()
