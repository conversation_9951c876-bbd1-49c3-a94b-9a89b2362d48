<template>
  <n-modal
    v-model:show="visible"
    :mask-closable="true"
    preset="card"
    title="客房服务"
    class="room-service-modal"
    style="width: 800px; max-height: 90vh;"
    :segmented="true"
  >
    <template #header>
      <div class="modal-header">
        <div class="room-title">
          <i class="i-mdi:room-service"></i>
          <span>房间 {{ roomInfo.roomNumber }} - 客房服务</span>
        </div>
        <div class="room-status-badge" :style="{ backgroundColor: roomInfo.statusColor }">
          {{ roomInfo.statusName }}
        </div>
      </div>
    </template>

    <div class="room-service-content" v-if="roomInfo.id">
      <!-- 房间基本信息 -->
      <div class="room-info-section">
        <div class="section-title">
          <i class="i-mdi:information-outline"></i>
          <span>房间信息</span>
        </div>
        <div class="room-basic-info">
          <div class="info-item">
            <span class="label">房间号:</span>
            <span class="value">{{ roomInfo.roomNumber }}</span>
          </div>
          <div class="info-item">
            <span class="label">房型:</span>
            <span class="value">{{ roomInfo.roomType }}</span>
          </div>
          <div class="info-item" v-if="roomInfo.guestName">
            <span class="label">客人:</span>
            <span class="value">{{ roomInfo.guestName }}</span>
          </div>
        </div>
      </div>

      <!-- 服务类型选择 -->
      <div class="service-section">
        <div class="section-title">
          <i class="i-mdi:format-list-bulleted"></i>
          <span>服务类型</span>
        </div>
        <div class="service-types">
          <n-radio-group v-model:value="selectedServiceType" name="serviceType">
            <n-space vertical>
              <n-radio value="cleaning" label="客房清洁" />
              <n-radio value="maintenance" label="设施维修" />
              <n-radio value="amenities" label="用品补充" />
              <n-radio value="laundry" label="洗衣服务" />
              <n-radio value="food" label="送餐服务" />
              <n-radio value="other" label="其他服务" />
            </n-space>
          </n-radio-group>
        </div>
      </div>

      <!-- 服务详情 -->
      <div class="service-details-section">
        <div class="section-title">
          <i class="i-mdi:text-box-outline"></i>
          <span>服务详情</span>
        </div>
        <n-input
          v-model:value="serviceDescription"
          type="textarea"
          placeholder="请详细描述所需的服务内容..."
          :rows="4"
          :maxlength="500"
          show-count
        />
      </div>

      <!-- 优先级设置 -->
      <div class="priority-section">
        <div class="section-title">
          <i class="i-mdi:flag-outline"></i>
          <span>优先级</span>
        </div>
        <n-radio-group v-model:value="selectedPriority" name="priority">
          <n-space>
            <n-radio value="low">
              <n-tag type="info" size="small">低</n-tag>
            </n-radio>
            <n-radio value="normal">
              <n-tag type="default" size="small">普通</n-tag>
            </n-radio>
            <n-radio value="high">
              <n-tag type="warning" size="small">高</n-tag>
            </n-radio>
            <n-radio value="urgent">
              <n-tag type="error" size="small">紧急</n-tag>
            </n-radio>
          </n-space>
        </n-radio-group>
      </div>

      <!-- 预计完成时间 -->
      <div class="time-section">
        <div class="section-title">
          <i class="i-mdi:clock-outline"></i>
          <span>预计完成时间</span>
        </div>
        <n-date-picker
          v-model:value="expectedCompletionTime"
          type="datetime"
          placeholder="选择预计完成时间"
          style="width: 100%"
        />
      </div>
    </div>

    <template #action>
      <div class="modal-actions">
        <n-button @click="handleCancel">取消</n-button>
        <n-button 
          type="primary" 
          @click="handleSubmit"
          :loading="submitting"
          :disabled="!canSubmit"
        >
          提交服务请求
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useMessage } from 'naive-ui'

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  roomData: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:show', 'submit'])

// Reactive data
const visible = ref(false)
const submitting = ref(false)
const selectedServiceType = ref('cleaning')
const serviceDescription = ref('')
const selectedPriority = ref('normal')
const expectedCompletionTime = ref(null)

const message = useMessage()

// Computed
const roomInfo = computed(() => {
  const room = props.roomData || {}
  return {
    id: room.id || room.room_id,
    roomNumber: room.roomNumber || room.room_number || '未知',
    roomType: room.roomType || room.room_type_name || '未知',
    guestName: room.guestName || room.guest_name,
    statusName: room.roomStatusName || room.status_name || '未知',
    statusColor: room.statusColor || '#666'
  }
})

const canSubmit = computed(() => {
  return selectedServiceType.value && 
         serviceDescription.value.trim().length > 0 && 
         selectedPriority.value
})

// Watch props
watch(() => props.show, (newVal) => {
  visible.value = newVal
})

watch(visible, (newVal) => {
  emit('update:show', newVal)
  if (!newVal) {
    resetForm()
  }
})

// Methods
const resetForm = () => {
  selectedServiceType.value = 'cleaning'
  serviceDescription.value = ''
  selectedPriority.value = 'normal'
  expectedCompletionTime.value = null
  submitting.value = false
}

const handleCancel = () => {
  visible.value = false
}

const handleSubmit = async () => {
  if (!canSubmit.value) {
    message.warning('请填写完整的服务信息')
    return
  }

  submitting.value = true
  
  try {
    const serviceRequest = {
      roomId: roomInfo.value.id,
      roomNumber: roomInfo.value.roomNumber,
      serviceType: selectedServiceType.value,
      description: serviceDescription.value.trim(),
      priority: selectedPriority.value,
      expectedCompletionTime: expectedCompletionTime.value,
      requestTime: new Date().toISOString(),
      status: 'pending'
    }

    // Emit the service request
    emit('submit', serviceRequest)
    
    message.success('客房服务请求已提交')
    visible.value = false
  } catch (error) {
    console.error('提交客房服务请求失败:', error)
    message.error('提交失败，请重试')
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.room-service-modal {
  --n-color: #fff;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.room-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
}

.room-title i {
  font-size: 18px;
  color: #2080f0;
}

.room-status-badge {
  padding: 4px 12px;
  border-radius: 12px;
  color: white;
  font-size: 12px;
  font-weight: 500;
}

.room-service-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.section-title i {
  font-size: 16px;
  color: #2080f0;
}

.room-basic-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-item .label {
  font-weight: 500;
  color: #666;
}

.info-item .value {
  color: #333;
}

.service-types {
  padding: 8px 0;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
