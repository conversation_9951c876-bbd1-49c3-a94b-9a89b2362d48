/**
 * 权限相关的组合式函数
 */
import { usePermissionStore } from '@/store'

export function usePermission() {
  const permissionStore = usePermissionStore()

  /**
   * 检查是否有指定权限
   * @param {string} authMark - 权限标识
   * @returns {boolean}
   */
  const hasPermission = (authMark) => {
    return permissionStore.hasPermission(authMark)
  }

  /**
   * 检查是否有页面权限
   * @param {string} routerUrl - 路由地址
   * @returns {boolean}
   */
  const hasPagePermission = (routerUrl) => {
    return permissionStore.hasPagePermission(routerUrl)
  }

  /**
   * 检查是否有元素权限
   * @param {string} authMark - 权限标识
   * @returns {boolean}
   */
  const hasElementPermission = (authMark) => {
    return permissionStore.hasElementPermission(authMark)
  }

  /**
   * 获取当前页面的所有元素权限
   * @param {string} routerUrl - 路由地址
   * @returns {Array}
   */
  const getPageElementPermissions = (routerUrl) => {
    return permissionStore.getPageElementPermissions(routerUrl)
  }

  /**
   * 检查多个权限，只要有一个权限即可
   * @param {Array<string>} authMarks - 权限标识数组
   * @returns {boolean}
   */
  const hasAnyPermission = (authMarks) => {
    if (!Array.isArray(authMarks) || authMarks.length === 0) return true
    return authMarks.some(authMark => hasPermission(authMark))
  }

  /**
   * 检查多个权限，需要全部权限
   * @param {Array<string>} authMarks - 权限标识数组
   * @returns {boolean}
   */
  const hasAllPermissions = (authMarks) => {
    if (!Array.isArray(authMarks) || authMarks.length === 0) return true
    return authMarks.every(authMark => hasPermission(authMark))
  }

  return {
    hasPermission,
    hasPagePermission,
    hasElementPermission,
    getPageElementPermissions,
    hasAnyPermission,
    hasAllPermissions
  }
}
