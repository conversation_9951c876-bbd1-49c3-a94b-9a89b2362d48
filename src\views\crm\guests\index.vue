<template>
  <div class="guests-page">
    <n-card title="客人档案" size="small">
      <template #header-extra>
        <n-button type="primary" @click="handleAdd">
          <template #icon>
            <n-icon><i-material-symbols:add /></n-icon>
          </template>
          新增客人
        </n-button>
      </template>

      <!-- 搜索区域 -->
      <n-space class="mb-4">
        <n-input
          v-model:value="searchForm.keyword"
          placeholder="搜索客人姓名、手机号"
          style="width: 300px;"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <n-icon><i-material-symbols:search /></n-icon>
          </template>
        </n-input>
        <n-button type="primary" @click="handleSearch">搜索</n-button>
        <n-button @click="handleReset">重置</n-button>
      </n-space>

      <!-- 数据表格 -->
      <n-data-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        :row-key="row => row.id"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </n-card>

    <!-- 新增/编辑弹窗 -->
    <n-modal v-model:show="showModal" preset="dialog" title="客人信息">
      <n-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-placement="left"
        label-width="100px"
      >
        <n-form-item label="姓名" path="name">
          <n-input v-model:value="formData.name" placeholder="请输入客人姓名" />
        </n-form-item>
        <n-form-item label="手机号" path="phone">
          <n-input v-model:value="formData.phone" placeholder="请输入手机号" />
        </n-form-item>
        <n-form-item label="身份证号" path="idCard">
          <n-input v-model:value="formData.idCard" placeholder="请输入身份证号" />
        </n-form-item>
        <n-form-item label="会员等级" path="memberGrade">
          <n-select
            v-model:value="formData.memberGrade"
            :options="memberGradeOptions"
            placeholder="请选择会员等级"
          />
        </n-form-item>
        <n-form-item label="备注" path="remark">
          <n-input
            v-model:value="formData.remark"
            type="textarea"
            placeholder="请输入备注信息"
            :rows="3"
          />
        </n-form-item>
      </n-form>
      <template #action>
        <n-space>
          <n-button @click="showModal = false">取消</n-button>
          <n-button type="primary" @click="handleSubmit">确定</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useMessage } from 'naive-ui'

const message = useMessage()

// 搜索表单
const searchForm = reactive({
  keyword: ''
})

// 表格数据
const tableData = ref([])
const loading = ref(false)

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100]
})

// 表格列定义
const columns = [
  {
    title: '姓名',
    key: 'name',
    width: 120
  },
  {
    title: '手机号',
    key: 'phone',
    width: 140
  },
  {
    title: '身份证号',
    key: 'idCard',
    width: 180,
    render: (row) => {
      if (row.idCard) {
        return row.idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
      }
      return '-'
    }
  },
  {
    title: '会员等级',
    key: 'memberGrade',
    width: 120
  },
  {
    title: '创建时间',
    key: 'createTime',
    width: 160
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    render: (row) => {
      return [
        h('n-button', {
          size: 'small',
          type: 'primary',
          style: 'margin-right: 8px;',
          onClick: () => handleEdit(row)
        }, '编辑'),
        h('n-button', {
          size: 'small',
          type: 'error',
          onClick: () => handleDelete(row)
        }, '删除')
      ]
    }
  }
]

// 弹窗相关
const showModal = ref(false)
const formRef = ref(null)
const formData = reactive({
  id: null,
  name: '',
  phone: '',
  idCard: '',
  memberGrade: null,
  remark: ''
})

// 会员等级选项
const memberGradeOptions = ref([
  { label: '普通会员', value: 'normal' },
  { label: '银卡会员', value: 'silver' },
  { label: '金卡会员', value: 'gold' },
  { label: '钻石会员', value: 'diamond' }
])

// 表单验证规则
const formRules = {
  name: { required: true, message: '请输入客人姓名', trigger: 'blur' },
  phone: { required: true, message: '请输入手机号', trigger: 'blur' }
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 这里应该调用实际的API
    // const response = await api.getGuestList({
    //   page: pagination.page,
    //   pageSize: pagination.pageSize,
    //   keyword: searchForm.keyword
    // })
    
    // 模拟数据
    await new Promise(resolve => setTimeout(resolve, 500))
    tableData.value = [
      {
        id: 1,
        name: '张三',
        phone: '13800138001',
        idCard: '110101199001011234',
        memberGrade: '金卡会员',
        createTime: '2024-01-01 10:00:00'
      },
      {
        id: 2,
        name: '李四',
        phone: '13800138002',
        idCard: '110101199002021234',
        memberGrade: '银卡会员',
        createTime: '2024-01-02 11:00:00'
      }
    ]
    pagination.itemCount = 2
  } catch (error) {

    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadData()
}

// 重置
const handleReset = () => {
  searchForm.keyword = ''
  pagination.page = 1
  loadData()
}

// 分页变化
const handlePageChange = (page) => {
  pagination.page = page
  loadData()
}

const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  loadData()
}

// 新增
const handleAdd = () => {
  Object.assign(formData, {
    id: null,
    name: '',
    phone: '',
    idCard: '',
    memberGrade: null,
    remark: ''
  })
  showModal.value = true
}

// 编辑
const handleEdit = (row) => {
  Object.assign(formData, { ...row })
  showModal.value = true
}

// 删除
const handleDelete = (row) => {
  // 这里应该调用删除API
  message.success(`删除客人 ${row.name} 成功`)
  loadData()
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    // 这里应该调用保存API
    // if (formData.id) {
    //   await api.updateGuest(formData)
    // } else {
    //   await api.createGuest(formData)
    // }
    
    message.success(formData.id ? '更新成功' : '新增成功')
    showModal.value = false
    loadData()
  } catch (error) {

  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.guests-page {
  padding: 16px;
}
</style>
