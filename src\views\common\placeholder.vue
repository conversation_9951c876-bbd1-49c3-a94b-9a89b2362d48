<template>
  <div class="placeholder-page">
    <div class="placeholder-content">
      <div class="placeholder-icon">
        <i :class="iconClass"></i>
      </div>
      <h2 class="placeholder-title">{{ title }}</h2>
      <p class="placeholder-description">{{ description }}</p>
      <div class="placeholder-info">
        <div class="info-item">
          <span class="info-label">模块路径:</span>
          <span class="info-value">{{ $route.path }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">页面名称:</span>
          <span class="info-value">{{ $route.name }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">开发状态:</span>
          <span class="info-value status-pending">规划中</span>
        </div>
      </div>
      <div class="placeholder-actions">
        <n-button type="primary" @click="goBack">
          <i class="i-fe:arrow-left mr-2"></i>
          返回上级
        </n-button>
        <n-button @click="goHome">
          <i class="i-fe:home mr-2"></i>
          回到首页
        </n-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

const props = defineProps({
  title: {
    type: String,
    default: '功能开发中'
  },
  description: {
    type: String,
    default: '该功能模块正在开发中，敬请期待。'
  },
  icon: {
    type: String,
    default: 'i-fe:settings'
  }
})

const iconClass = computed(() => `${props.icon} text-48`)

const title = computed(() => {
  return route.meta?.title || props.title
})

const description = computed(() => {
  const moduleDescriptions = {
    '房间状态': '客房状态管理和清洁任务分配功能',
    '清洁任务': '客房清洁工作流程和任务管理',
    '维修工单': '客房设施维修和保养管理',
    '账单管理': '客户账单生成和管理功能',
    '收款管理': '收款记录和财务对账功能',
    '对账管理': '财务对账和结算管理',
    '收益分析': '酒店收益数据分析和报表',
    '入住分析': '客房入住率和趋势分析',
    '客户分析': '客户行为和偏好分析',
    '营销活动': '营销活动策划和执行管理',
    '系统配置': '系统参数和基础配置管理',
    '操作日志': '系统操作记录和审计日志'
  }

  return moduleDescriptions[title.value] || props.description
})

function goBack() {
  router.go(-1)
}

function goHome() {
  router.push('/dashboard/overview')
}
</script>

<style scoped lang="less">
.placeholder-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

.placeholder-content {
  background: white;
  border-radius: 16px;
  padding: 48px;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  width: 100%;
}

.placeholder-icon {
  margin-bottom: 24px;
  color: #2A5DAA;

  i {
    font-size: 48px;
  }
}

.placeholder-title {
  font-size: 28px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 12px;
}

.placeholder-description {
  font-size: 16px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 32px;
}

.placeholder-info {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 32px;
  text-align: left;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

.info-label {
  font-weight: 500;
  color: #666;
}

.info-value {
  font-family: 'Monaco', 'Menlo', monospace;
  color: #333;

  &.status-pending {
    background: #fff3cd;
    color: #856404;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
  }
}

.placeholder-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .placeholder-content {
    padding: 32px 24px;
  }

  .placeholder-title {
    font-size: 24px;
  }

  .placeholder-actions {
    flex-direction: column;
  }
}
</style>