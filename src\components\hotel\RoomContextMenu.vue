<template>
  <n-dropdown
    :show="show"
    :x="x"
    :y="y"
    :options="menuOptions"
    @select="handleSelect"
    @clickoutside="handleClickOutside"
    placement="bottom-start"
    trigger="manual"
    :show-arrow="false"
  />
</template>

<script setup>
import { computed, h } from 'vue'
import { useMessage } from 'naive-ui'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  x: {
    type: Number,
    default: 0
  },
  y: {
    type: Number,
    default: 0
  },
  roomData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:show', 'action', 'close'])

const message = useMessage()

// 根据房间状态生成菜单选项
const menuOptions = computed(() => {
  console.log('RoomContextMenu: 计算菜单选项', {
    show: props.show,
    roomData: props.roomData,
    x: props.x,
    y: props.y
  })

  const options = []

  // 安全检查：确保roomData存在
  if (!props.roomData) {
    console.warn('RoomContextMenu: roomData为空')
    return options
  }

  // 兼容多种可能的状态字段名
  const roomStatus = props.roomData.roomStatusSign || props.roomData.room_status || props.roomData.status
  const cleanStatus = props.roomData.cleanStatusId || props.roomData.clean_status || props.roomData.cleanStatus

  // 查看详情 - 所有状态都可以
  options.push({
    key: 'detail',
    label: '查看详情',
    icon: () => h('i', { class: 'i-mdi:information-outline text-blue-500' }),
    props: {
      class: 'context-menu-item primary'
    }
  })

  // 根据房间状态添加相应操作
  if (roomStatus === 'vacant') {
    // 空闲房间
    options.push({
      key: 'checkin',
      label: '办理入住',
      icon: () => h('i', { class: 'i-mdi:login text-green-500' }),
      props: {
        class: 'context-menu-item success'
      }
    })

    options.push({
      key: 'reservation',
      label: '办理预订',
      icon: () => h('i', { class: 'i-mdi:calendar-plus text-orange-500' }),
      props: {
        class: 'context-menu-item warning'
      }
    })
  } else if (roomStatus === 'stay') {
    // 已入住房间
    options.push({
      key: 'checkout',
      label: '办理退房',
      icon: () => h('i', { class: 'i-mdi:logout text-red-500' }),
      props: {
        class: 'context-menu-item danger'
      }
    })

    options.push({
      key: 'extend',
      label: '延期退房',
      icon: () => h('i', { class: 'i-mdi:clock-plus text-blue-500' }),
      props: {
        class: 'context-menu-item info'
      }
    })

    options.push({
      key: 'change-room',
      label: '换房',
      icon: () => h('i', { class: 'i-mdi:swap-horizontal text-purple-500' }),
      props: {
        class: 'context-menu-item'
      }
    })

    options.push({
      key: 'room-service',
      label: '客房服务',
      icon: () => h('i', { class: 'i-mdi:room-service text-blue-500' }),
      props: {
        class: 'context-menu-item info'
      }
    })
  } else if (roomStatus === 'reserved') {
    // 已预订房间
    options.push({
      key: 'checkin',
      label: '确认入住',
      icon: () => h('i', { class: 'i-mdi:check-circle text-green-500' }),
      props: {
        class: 'context-menu-item success'
      }
    })

    options.push({
      key: 'cancel-reservation',
      label: '取消预订',
      icon: () => h('i', { class: 'i-mdi:cancel text-red-500' }),
      props: {
        class: 'context-menu-item danger'
      }
    })
  } else if (roomStatus === 'checkout') {
    // 待退房
    options.push({
      key: 'confirm-checkout',
      label: '确认退房',
      icon: () => h('i', { class: 'i-mdi:check-circle text-green-500' }),
      props: {
        class: 'context-menu-item success'
      }
    })
  }

  // 分隔线
  if (options.length > 1) {
    options.push({
      key: 'divider1',
      type: 'divider'
    })
  }

  // 清洁状态操作
  if (cleanStatus === 2) {
    // 脏房
    options.push({
      key: 'start-cleaning',
      label: '开始清洁',
      icon: () => h('i', { class: 'i-mdi:broom text-blue-500' }),
      props: {
        class: 'context-menu-item info'
      }
    })
  } else if (cleanStatus === 3) {
    // 清洁中
    options.push({
      key: 'complete-cleaning',
      label: '清洁完成',
      icon: () => h('i', { class: 'i-mdi:check-circle text-green-500' }),
      props: {
        class: 'context-menu-item success'
      }
    })
  }

  // 维修操作
  if (cleanStatus !== 4) {
    options.push({
      key: 'maintenance',
      label: '报修',
      icon: () => h('i', { class: 'i-mdi:wrench text-orange-500' }),
      props: {
        class: 'context-menu-item warning'
      }
    })
  } else {
    options.push({
      key: 'complete-maintenance',
      label: '维修完成',
      icon: () => h('i', { class: 'i-mdi:check-circle text-green-500' }),
      props: {
        class: 'context-menu-item success'
      }
    })
  }

  // 房间封锁
  options.push({
    key: 'toggle-block',
    label: props.roomData.is_blocked ? '解除封锁' : '封锁房间',
    icon: () => h('i', {
      class: props.roomData.is_blocked
        ? 'i-mdi:lock-open text-green-500'
        : 'i-mdi:lock text-red-500'
    }),
    props: {
      class: `context-menu-item ${props.roomData.is_blocked ? 'success' : 'danger'}`
    }
  })

  // 分隔线
  options.push({
    key: 'divider2',
    type: 'divider'
  })

  // 其他操作
  options.push({
    key: 'history',
    label: '查看历史',
    icon: () => h('i', { class: 'i-mdi:history text-gray-500' }),
    props: {
      class: 'context-menu-item'
    }
  })

  options.push({
    key: 'refresh',
    label: '刷新状态',
    icon: () => h('i', { class: 'i-mdi:refresh text-blue-500' }),
    props: {
      class: 'context-menu-item info'
    }
  })

  console.log('RoomContextMenu: 生成的菜单选项', options)
  return options
})

// 处理菜单选择
function handleSelect(key) {
  emit('action', {
    action: key,
    roomData: props.roomData
  })
  handleClickOutside()
}

// 处理点击外部
function handleClickOutside() {
  emit('update:show', false)
  emit('close')
}

</script>

<style>
/* 右键菜单样式 */
.n-dropdown-menu {
  min-width: 180px !important;
  border-radius: 12px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  backdrop-filter: blur(10px) !important;
  background: rgba(255, 255, 255, 0.95) !important;
}

.n-dropdown-option {
  border-radius: 8px !important;
  margin: 4px 8px !important;
  padding: 10px 12px !important;
  transition: all 0.2s ease !important;
}

.context-menu-item {
  font-weight: 500 !important;
}

.context-menu-item.primary:hover {
  background: rgba(59, 130, 246, 0.1) !important;
  color: #3b82f6 !important;
}

.context-menu-item.success:hover {
  background: rgba(16, 185, 129, 0.1) !important;
  color: #10b981 !important;
}

.context-menu-item.warning:hover {
  background: rgba(245, 158, 11, 0.1) !important;
  color: #f59e0b !important;
}

.context-menu-item.danger:hover {
  background: rgba(239, 68, 68, 0.1) !important;
  color: #ef4444 !important;
}

.context-menu-item.info:hover {
  background: rgba(var(--primary-color-rgb), 0.1) !important;
  color: var(--primary-color) !important;
}

.n-dropdown-option:hover {
  transform: translateX(2px) !important;
}

.n-dropdown-option .n-dropdown-option-body {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

.n-dropdown-option .n-dropdown-option-body__prefix {
  font-size: 16px !important;
}

.n-dropdown-divider {
  margin: 8px 12px !important;
  background: rgba(var(--primary-color-rgb), 0.12) !important;
}

/* 动画效果 */
.n-dropdown-menu {
  animation: contextMenuSlideIn 0.2s ease-out !important;
}

@keyframes contextMenuSlideIn {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 图标颜色 */
.text-blue-500 {
  color: var(--primary-color) !important;
}

.text-green-500 {
  color: #10b981 !important;
}

.text-orange-500 {
  color: #f59e0b !important;
}

.text-red-500 {
  color: #ef4444 !important;
}

.text-purple-500 {
  color: var(--primary-color-suppl) !important;
}

.text-gray-500 {
  color: #6b7280 !important;
}
</style>
