<template>
  <div>
    <n-card title="会员卡管理">
      <template #header-extra>
        <n-button type="primary" @click="handleAdd">
          新增会员卡
        </n-button>
      </template>

      <!-- 搜索区域 -->
      <n-form
        inline
        :label-width="80"
        :model="searchForm"
        label-placement="left"
        class="mb-4"
      >
        <n-form-item label="卡号">
          <n-input v-model:value="searchForm.cardNo" placeholder="请输入卡号" />
        </n-form-item>
        <n-form-item label="会员名称">
          <n-input v-model:value="searchForm.memberName" placeholder="请输入会员名称" />
        </n-form-item>
        <n-form-item label="状态">
          <n-select
            v-model:value="searchForm.status"
            placeholder="请选择状态"
            :options="statusOptions"
            clearable
          />
        </n-form-item>
        <n-form-item>
          <n-button type="primary" @click="handleSearch">
            查询
          </n-button>
          <n-button class="ml-2" @click="resetSearch">
            重置
          </n-button>
        </n-form-item>
      </n-form>

      <!-- 表格区域 -->
      <n-data-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        @update:page="handlePageChange"
      />
    </n-card>

    <!-- 新增/编辑会员卡弹窗 -->
    <n-modal
      v-model:show="showModal"
      :title="formTitle"
      preset="card"
      :style="{ width: '600px' }"
    >
      <n-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        :label-width="100"
      >
        <n-form-item label="卡号" path="cardNo">
          <n-input v-model:value="formData.cardNo" placeholder="请输入卡号" />
        </n-form-item>
        <n-form-item label="会员" path="memberId">
          <n-select
            v-model:value="formData.memberId"
            placeholder="请选择会员"
            :options="memberOptions"
            filterable
          />
        </n-form-item>
        <n-form-item label="卡类型" path="type">
          <n-select
            v-model:value="formData.type"
            placeholder="请选择卡类型"
            :options="cardTypeOptions"
          />
        </n-form-item>
        <n-form-item label="有效期" path="validDate">
          <n-date-picker
            v-model:value="formData.validDate"
            type="date"
            placeholder="请选择有效期"
            clearable
          />
        </n-form-item>
        <n-form-item label="备注" path="remark">
          <n-input
            v-model:value="formData.remark"
            placeholder="请输入备注"
            type="textarea"
            :rows="3"
          />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="showModal = false">取消</n-button>
          <n-button type="primary" @click="handleSubmit">确定</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import { h, ref, reactive, computed, onMounted } from 'vue'
import { useMessage } from 'naive-ui'
import { getCardList, createCard, updateCard, deleteCard, activateCard, reportLossCard, unlockCard } from '@/api/hotel'

const message = useMessage()

// 状态选项
const statusOptions = [
  { label: '未激活', value: 0 },
  { label: '正常', value: 1 },
  { label: '挂失', value: 2 },
  { label: '过期', value: 3 },
]

// 卡类型选项
const cardTypeOptions = [
  { label: '普通卡', value: 1 },
  { label: '银卡', value: 2 },
  { label: '金卡', value: 3 },
  { label: '钻石卡', value: 4 },
]

// 会员选项（实际应该从API获取）
const memberOptions = ref([
  { label: '张三', value: 1 },
  { label: '李四', value: 2 },
  { label: '王五', value: 3 },
])

// 搜索表单
const searchForm = reactive({
  cardNo: '',
  memberName: '',
  status: null,
})

// 重置搜索
const resetSearch = () => {
  searchForm.cardNo = ''
  searchForm.memberName = ''
  searchForm.status = null
  handleSearch()
}

// 表格数据
const tableData = ref([])
const loading = ref(false)
const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 30, 40],
})

// 表格列配置
const columns = [
  {
    title: '卡号',
    key: 'cardNo',
  },
  {
    title: '会员名称',
    key: 'memberName',
  },
  {
    title: '卡类型',
    key: 'type',
    render(row) {
      const option = cardTypeOptions.find(item => item.value === row.type)
      return option ? option.label : '未知'
    }
  },
  {
    title: '状态',
    key: 'status',
    render(row) {
      const statusMap = {
        0: { text: '未激活', type: 'default' },
        1: { text: '正常', type: 'success' },
        2: { text: '挂失', type: 'warning' },
        3: { text: '过期', type: 'error' },
      }
      const status = statusMap[row.status] || { text: '未知', type: 'default' }
      return h(
        'n-tag',
        {
          type: status.type,
          size: 'small',
        },
        { default: () => status.text }
      )
    }
  },
  {
    title: '有效期',
    key: 'validDate',
  },
  {
    title: '创建时间',
    key: 'createTime',
  },
  {
    title: '操作',
    key: 'actions',
    render(row) {
      const actions = []

      // 编辑按钮
      actions.push(
        h(
          'a',
          {
            style: {
              marginRight: '10px',
            },
            onClick: () => handleEdit(row),
          },
          '编辑'
        )
      )

      // 根据状态显示不同的操作按钮
      if (row.status === 0) {
        // 未激活状态可以激活
        actions.push(
          h(
            'a',
            {
              style: {
                marginRight: '10px',
                color: 'green',
              },
              onClick: () => handleActivate(row),
            },
            '激活'
          )
        )
      } else if (row.status === 1) {
        // 正常状态可以挂失
        actions.push(
          h(
            'a',
            {
              style: {
                marginRight: '10px',
                color: 'orange',
              },
              onClick: () => handleReportLoss(row),
            },
            '挂失'
          )
        )
      } else if (row.status === 2) {
        // 挂失状态可以解挂
        actions.push(
          h(
            'a',
            {
              style: {
                marginRight: '10px',
                color: 'blue',
              },
              onClick: () => handleUnlock(row),
            },
            '解挂'
          )
        )
      }

      // 删除按钮
      actions.push(
        h(
          'a',
          {
            style: {
              color: 'red',
            },
            onClick: () => handleDelete(row),
          },
          '删除'
        )
      )

      return h('div', actions)
    },
  },
]

// 查询会员卡列表
const fetchCardList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...searchForm,
    }
    // 实际应该调用API获取数据
    // const { data } = await getCardList(params)
    // tableData.value = data.list
    // pagination.itemCount = data.total

    // 模拟数据
    setTimeout(() => {
      tableData.value = [
        {
          id: 1,
          cardNo: 'VIP10001',
          memberId: 1,
          memberName: '张三',
          type: 3,
          status: 1,
          validDate: '2024-12-31',
          createTime: '2023-01-15 10:30:00',
          remark: '金卡会员',
        },
        {
          id: 2,
          cardNo: 'VIP10002',
          memberId: 2,
          memberName: '李四',
          type: 2,
          status: 2,
          validDate: '2024-10-15',
          createTime: '2023-02-20 14:20:00',
          remark: '银卡会员',
        },
        {
          id: 3,
          cardNo: 'VIP10003',
          memberId: 3,
          memberName: '王五',
          type: 1,
          status: 0,
          validDate: '2024-08-01',
          createTime: '2023-03-05 09:15:00',
          remark: '普通会员',
        },
      ]
      pagination.itemCount = 3
      loading.value = false
    }, 500)
  } catch (error) {
    message.error('获取会员卡列表失败')
    loading.value = false
  }
}

// 处理查询
const handleSearch = () => {
  pagination.page = 1
  fetchCardList()
}

// 处理分页变化
const handlePageChange = (page) => {
  pagination.page = page
  fetchCardList()
}

// 表单相关
const formRef = ref(null)
const showModal = ref(false)
const isEdit = ref(false)
const formData = reactive({
  id: null,
  cardNo: '',
  memberId: null,
  type: null,
  validDate: null,
  remark: '',
})

// 表单标题
const formTitle = computed(() => isEdit.value ? '编辑会员卡' : '新增会员卡')

// 表单校验规则
const rules = {
  cardNo: {
    required: true,
    message: '请输入卡号',
    trigger: 'blur',
  },
  memberId: {
    required: true,
    message: '请选择会员',
    trigger: 'change',
  },
  type: {
    required: true,
    message: '请选择卡类型',
    trigger: 'change',
  },
}

// 新增会员卡
const handleAdd = () => {
  isEdit.value = false
  Object.assign(formData, {
    id: null,
    cardNo: '',
    memberId: null,
    type: null,
    validDate: null,
    remark: '',
  })
  showModal.value = true
}

// 编辑会员卡
const handleEdit = (row) => {
  isEdit.value = true
  Object.assign(formData, {
    id: row.id,
    cardNo: row.cardNo,
    memberId: row.memberId,
    type: row.type,
    validDate: row.validDate,
    remark: row.remark,
  })
  showModal.value = true
}

// 提交表单
const handleSubmit = () => {
  formRef.value?.validate(async (errors) => {
    if (errors) return

    try {
      if (isEdit.value) {
        // 编辑
        await updateCard(formData)
        message.success('编辑成功')
      } else {
        // 新增
        await createCard(formData)
        message.success('新增成功')
      }
      showModal.value = false
      fetchCardList()
    } catch (error) {
      message.error(isEdit.value ? '编辑失败' : '新增失败')
    }
  })
}

// 删除会员卡
const handleDelete = async (row) => {
  try {
    await deleteCard(row.id)
    message.success('删除成功')
    fetchCardList()
  } catch (error) {
    message.error('删除失败')
  }
}

// 激活会员卡
const handleActivate = async (row) => {
  try {
    await activateCard(row.id)
    message.success('激活成功')
    fetchCardList()
  } catch (error) {
    message.error('激活失败')
  }
}

// 挂失会员卡
const handleReportLoss = async (row) => {
  try {
    await reportLossCard(row.id)
    message.success('挂失成功')
    fetchCardList()
  } catch (error) {
    message.error('挂失失败')
  }
}

// 解挂会员卡
const handleUnlock = async (row) => {
  try {
    await unlockCard(row.id)
    message.success('解挂成功')
    fetchCardList()
  } catch (error) {
    message.error('解挂失败')
  }
}

// 初始化
onMounted(() => {
  fetchCardList()
})
</script>

<style scoped>
.mb-4 {
  margin-bottom: 16px;
}
.ml-2 {
  margin-left: 8px;
}
</style>
