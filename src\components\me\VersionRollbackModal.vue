<template>

    <n-modal
    v-model:show="show"
    class="modal-box"
    :style="{ width: modalOptions.width, ...modalOptions.modalStyle }"
    :preset="undefined"
    size="huge">
    <n-card class="card">
    <n-space vertical>
      <n-alert type="info" title="版本回退提示">
        <template #icon>
          <n-icon>
            <i class="i-material-symbols:info-outline"></i>
          </n-icon>
        </template>
        选择一个历史版本进行回退操作，回退后将覆盖当前版本。请谨慎操作！
      </n-alert>

      <n-data-table
        :columns="columns"
        :data="versionList"
        :pagination="pagination"
        :row-key="row => row.key"
      />

      <div class="flex justify-end mt-4">
        <n-button type="primary" :disabled="!selectedVersion" @click="handleConfirm">
          确认回退
        </n-button>
      </div>
    </n-space>
  </n-card>
  </n-modal>

</template>

<script setup>
import { ref, computed } from 'vue'
import dayjs from 'dayjs'

const props = defineProps({
  appid: {
    type: String,
    default: ''
  }
})

const show = ref(false);
// 声明一个modalOptions变量，用于存储模态框的配置信息
const modalOptions = ref({width:'1200px',modalStyle:{height:'600px'}})
async function open(options = {}) {
  // 将props和options合并赋值给modalOptions
  modalOptions.value = { ...props, ...options }
  // 如果modalOptions中没有title属性，则将props中的title属性赋值给modalOptions的title属性
  if (!modalOptions.value.title)
    modalOptions.value.title = props.title
  // 将show的值设置为true
  show.value = true
  await nextTick()
  initDrag(
    Array.prototype.at.call(document.querySelectorAll('.modal-header'), -1),
    Array.prototype.at.call(document.querySelectorAll('.modal-box'), -1),
  )
}

// 表格列定义
const columns = [
  {
    type: 'selection'
  },
  {
    title: '小程序版本',
    key: 'app_version'
  },
  {
    title: '模板版本号',
    key: 'user_version'
  },
  {
    title: '版本描述',
    key: 'user_desc',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '更新时间',
    key: 'commit_time',
    render(row) {
      return dayjs(row.commit_time * 1000).format('YYYY-MM-DD HH:mm:ss')
    }
  }
]

// 分页配置
const pagination = ref({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 30, 40],
  onChange: (page) => {
    pagination.value.page = page
  },
  onUpdatePageSize: (pageSize) => {
    pagination.value.pageSize = pageSize
    pagination.value.page = 1
  }
})

// 版本列表数据
const versionList = ref([])
// 选中的行keys
const checkedRowKeys = ref([])
// 选中的版本
const selectedVersion = computed(() => {
  if (checkedRowKeys.value.length === 0) return null
  return versionList.value.find(item => item.key === checkedRowKeys.value[0])
})

// 处理选择行变化
function handleCheckedRowKeysChange(keys) {
  // 只允许选择一行
  if (keys.length > 1) {
    checkedRowKeys.value = [keys[keys.length - 1]]
    $message.warning('只能选择一个版本进行回退')
  } else {
    checkedRowKeys.value = keys
  }
}

// 获取版本列表
async function getVersionList() {
  try {
    // 这里需要调用API获取版本列表
    // const response = await api.getVersionHistory({ appid: props.appid })
    // if (response && response.code === 0) {
    //   versionList.value = (response.data || []).map((item, index) => ({
    //     key: index,
    //     ...item
    //   }))
    // }

    // 模拟数据，实际使用时请替换为API调用
    versionList.value = [
      {
        key: 0,
        app_version: '1.0.0',
        user_version: 'v1.0.0',
        user_desc: '初始版本',
        commit_time: Math.floor(Date.now() / 1000) - 86400 * 30
      },
      {
        key: 1,
        app_version: '1.1.0',
        user_version: 'v1.1.0',
        user_desc: '修复已知问题',
        commit_time: Math.floor(Date.now() / 1000) - 86400 * 20
      },
      {
        key: 2,
        app_version: '1.2.0',
        user_version: 'v1.2.0',
        user_desc: '新增功能A和功能B',
        commit_time: Math.floor(Date.now() / 1000) - 86400 * 10
      }
    ]
  } catch (error) {
    $message.error('获取版本列表失败')
  }
}

// 确认回退
function handleConfirm() {
  if (!selectedVersion.value) {
    $message.warning('请选择要回退的版本')
    return
  }

  emit('confirm', selectedVersion.value)
  emit('update:show', false)
}

// 监听show变化，当弹窗显示时获取版本列表
watch(() => props.show, (newVal) => {
  if (newVal) {
    checkedRowKeys.value = []
    getVersionList()
  }
})

// 定义一个defineExpose函数，用于暴露open、close、handleOk、handleCancel函数
defineExpose({
  open,
  close
})
</script>
