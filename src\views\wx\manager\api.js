/**********************************
 * @Author: <PERSON>
 * @LastEditor: <PERSON>
 * @LastEditTime: 2023/12/05 21:29:51
 * @Email: <EMAIL>
 * Copyright © 2023 <PERSON>(大脸怪) | https://isme.top
 **********************************/

import { request } from '@/utils'

export default {
  create: () => request.get('/startAuth'),
  read: (params = {}) => request.get('/user', { params }),
  update: data => request.patch(`/user/${data.id}`, data),
  delete: appid => request.post(`/delMiniprogram`,{appid}),
  resetPwd: (id, data) => request.patch(`/user/password/reset/${id}`, data),
  uploadCode: (params) => request.post(`/wx/uploadCode`, {params}),
  getAllRoles: () => request.get('/role?enable=1'),
  getMiniList :(params)=> request.post('/getprogramlist', {params}),
  getModelList :(params)=> request.get('/wx/getModelList'),
  getPluginStatus: (params) => request.post('wx/getPluginStatus',{ params }),
  applyPluginStatus: (params) => request.post('wx/applyPluginStatus',{ params }),
  updatePluginStatus: (params) => request.post('wx/updatePluginStatus',{ params }),
  // 新增 delPluginStatus 方法，假设后端接口路径为 'wx/delPluginStatus'
  delPluginStatus: (params) => request.post('wx/delPluginStatus', { params }),
  getCommitList: (params) => request.post('wx/getCommitList', { params }),
  getQrCode: (params) => request.post('wx/getQrCode', { params }),
  submitUpload: (params) => request.post('wx/submitUpload', { params }),
  releaseCode: (params) => request.post('wx/releaseCode', { params }),
  getNetLine: (params) => request.post('wx/getNetLine', { params }),
}
